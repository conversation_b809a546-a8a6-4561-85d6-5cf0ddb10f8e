import React from 'react';
import { Provider } from 'react-redux';
import { ReduxOptions } from './types';
import { Store } from '@mmt/redux';

/////////////////////////////////////////////////////////////
// ReduxProvider is needed because migrated routes still use the store instance from react-redux
// and it would be a huge change to migrate all components to mmt-redux
// ToDo: Considering an option to move away from mmt-redux and use react-redux as only library
///////////////////////////////////////////////////////////

export default function withRedux(
  Component: React.ComponentType<any>,
  isMigratedRoute: boolean = false,
  reduxOptions?: ReduxOptions,
): React.ComponentType<any> | React.FunctionComponent<any> {
  if (!reduxOptions) {
    return Component;
  }
  const store: Store<any> = reduxOptions.storeFactory();
  return (props: any) => (
    <Provider store={store} key="provider">
      <Component {...props} store={store} />
    </Provider>
  );
}
