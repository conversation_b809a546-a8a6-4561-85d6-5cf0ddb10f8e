import { Platform } from 'react-native';

type Position = 'bottom' | 'top';
type Theme = 'dark' | 'light';

let ToastAndroid: any;
let NativeModules: any;

if (Platform.OS === 'android') {
  ({ ToastAndroid } = require('react-native'));
} else if (Platform.OS === 'ios') {
  ({ NativeModules } = require('react-native'));
}

const showWebToast = (message: string) => {
  // Implement web-based toast here
  // This is a simple alert for demonstration, replace it with your preferred web toast library
  alert(message);
};

export const showShortToast = (
  message: string,
  position: Position = 'bottom',
  theme: Theme = 'dark',
) => {
  if (Platform.OS === 'ios') {
    NativeModules?.ToastIOS?.show(message, position, theme);
  } else if (Platform.OS === 'android') {
    ToastAndroid?.show(message, ToastAndroid.SHORT);
  } else {
    showWebToast(message);
  }
};

export const showLongToast = (
  message: string,
  position: Position = 'bottom',
  theme: Theme = 'dark',
) => {
  if (Platform.OS === 'ios') {
    NativeModules?.ToastIOS?.show(message, position, theme);
  } else if (Platform.OS === 'android') {
    ToastAndroid?.show(message, ToastAndroid.LONG);
  } else {
    showWebToast(message);
  }
};
