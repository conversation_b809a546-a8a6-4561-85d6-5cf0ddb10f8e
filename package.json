{"name": "src", "version": "0.0.2", "private": true, "scripts": {"danger_setup": "rm -rf Gemfile.lock && bundle install && bundle exec danger --verbose", "start": "npx react-native start --port=8086", "start-without-cache": "npx react-native start --port=8086 --reset-cache", "test": "JEST_ENV='test' jest --coverage=false", "typecheck": "echo 'temporarily skipping typecheck'", "__typecheck": " tsc --project tsconfig.rn.json --noEmit", "lintcheck": "echo 'temporarily skipping lintcheck'", "__lintcheck": "eslint --quiet '{src,apps,packages}/**/*.{ts,tsx,js}'", "bundle": "NODE_ENV=production react-native-nowatch bundle --platform android --dev false --entry-file index.js --bundle-output index.android.bundle --reset-cache", "bundle-android": "node -v && npm -v && NODE_ENV=production node ./scripts/build/buildAndroid.js", "verify": "npm run rn-setup && mkdir build-android && yarn lintcheck && npm run ground-transport-verify && yarn bundle-verify", "bundle-verify": "NODE_ENV=production react-native-nowatch bundle --platform android --dev false --entry-file index.js --bundle-output build-android/main.jsbundle --reset-cache", "ground-transport-verify": "npm run rails-verify && yarn cabs-verify", "fk-ultra": "node ultra-build-integration/bus/build-bus-ultra.js", "publishRNAndroidLibs": "yarn install && NODE_ENV=production node scripts/RN-android-libs-publish/RN-libs-android/upload-android-lib-script.js", "postinstall": "patch-package", "android-dev": "adb reverse tcp:8086 tcp:8086 && npm run start", "rn-setup": "yarn install", "sanitize": "yarn typecheck && yarn lint-staged", "hubble-prettier": "prettier --config apps/hubble/.prettierrc --write 'apps/hubble/src/**/*.{js,ts,tsx}'", "hubble-prettier-fix": "prettier --no-editorconfig --config apps/hubble/.prettierrc --write 'apps/hubble/src/**/*.{js,ts,tsx}'", "hubble-prettier-check": "prettier --no-editorconfig --config apps/hubble/.prettierrc --check 'apps/hubble/src/**/*.{js,ts,tsx}'", "hubble-lint-check": "eslint -c apps/hubble/.eslintrc.js --ignore-path apps/hubble/.eslintignore --no-error-on-unmatched-pattern --cache 'apps/hubble/src/**/*.{js,ts,tsx}'", "hubble-lint-fix": "yarn hubble-lint-check --fix --quiet", "hubble-tsc-silent": "tsc-silent --project ./apps/hubble/tsconfig.json --suppressConfig ./apps/hubble/tsc-silent.config.js", "hubble-verify": "yarn hubble-tsc-silent && yarn hubble-lint-check --quiet && yarn hubble-prettier-check", "ps-eslint": "eslint --quiet 'src/Routes/postsales/flights/**/*.{ts,tsx,js}'", "cabs-pokus-check": "sh scripts/cabs-pokus-check.sh", "cabs-lint-check": "eslint -c ./apps/cabs/.eslintrc.js --no-error-on-unmatched-pattern --fix --cache --quiet 'apps/cabs/**/*.{tsx,ts,js}'", "cabs-prettier-fix": "prettier --no-editorconfig --config ./apps/cabs/.prettierrc --write 'apps/cabs/**/*.{ts,tsx,js}'", "cabs-prettier-check": "prettier --no-editorconfig --config ./apps/cabs/.prettierrc --check 'apps/cabs/**/*.{ts,tsx,js}'", "cabs-tsc": "tsc-silent --project ./apps/cabs/tsconfig.json --suppressConfig ./apps/cabs/tsc-slient.config.js", "cabs-test": "jest -c apps/cabs/__tests__/setup/jest.cab.config.js './apps/cabs/.*\\.test\\.tsx' --coverage", "cabs-verify": "yarn cabs-tsc && yarn cabs-lint-check --quiet && yarn cabs-prettier-check", "rails-lint-check": "node --max-old-space-size=8192 ./node_modules/.bin/eslint -c ./apps/rails/.eslintrc --fix 'apps/rails/**/*.{ts,tsx,js}' --quiet", "rails-verify": "yarn rails-lint-check", "bus-test": "jest -c apps/bus/jest.config.js", "bus-test:coverage": "jest -c apps/bus/jest.config.js --coverage", "bundle-visualizer": "react-native-bundle-visualizer", "enumerateNavRoutes": "python3 scripts/findRouteConfigs.py"}, "husky": {"hooks": {"pre-commit": "lint-staged --allow-empty"}}, "__lint-staged": {"{apps,packages}/**/*.{ts,tsx,js}": [], "apps/hubble/src/**/*.{ts,tsx,js}": ["prettier --config apps/hubble/.prettierrc --write"], "src/Routes/travelMall/**/*.{ts,tsx,js}": ["prettier --config src/Routes/travelMall/.travelMall_prettierrc --write"], "src/Routes/postsales/**/*.{ts,tsx,js}": ["eslint --fix --quiet"]}, "workspaces": ["libraries/*", "packages/*", "apps/*"], "dependencies": {"@bus-fe-commons/xdm": "0.0.6", "@Frontend_Ui_Lib_App/Accordion": "0.0.2", "@Frontend_Ui_Lib_App/BottomBar": "0.0.1", "@Frontend_Ui_Lib_App/BottomBarFilter": "0.0.1", "@Frontend_Ui_Lib_App/BottomSheet": "^0.0.8", "@Frontend_Ui_Lib_App/BottomTabs": "0.0.1", "@Frontend_Ui_Lib_App/Button": "0.0.4", "@Frontend_Ui_Lib_App/CalendarPicker": "0.0.3", "@Frontend_Ui_Lib_App/Carousel": "^0.0.2", "@Frontend_Ui_Lib_App/CheckBox": "0.0.2", "@Frontend_Ui_Lib_App/Counter": "0.0.1", "@Frontend_Ui_Lib_App/DateTimeFooter": "0.0.4", "@Frontend_Ui_Lib_App/Dropdown": "^0.0.5", "@Frontend_Ui_Lib_App/DrumRollCalendar": "^0.0.1", "@Frontend_Ui_Lib_App/FloatingInput": "0.0.6", "@Frontend_Ui_Lib_App/Header": "0.0.1", "@Frontend_Ui_Lib_App/HighlightedText": "^0.0.1", "@Frontend_Ui_Lib_App/HorizontalDatePicker": "^0.0.3", "@Frontend_Ui_Lib_App/InputField": "0.0.4", "@Frontend_Ui_Lib_App/LineLoader": "0.0.1", "@Frontend_Ui_Lib_App/MyraBotCTA": "0.0.2", "@Frontend_Ui_Lib_App/MyraErrorScreen": "0.0.1", "@Frontend_Ui_Lib_App/Persuasion": "0.0.2", "@Frontend_Ui_Lib_App/ProgressBar": "^0.0.1", "@Frontend_Ui_Lib_App/RadioButton": "0.0.3", "@Frontend_Ui_Lib_App/SearchBar": "0.0.3", "@Frontend_Ui_Lib_App/Shimmer": "0.0.1", "@Frontend_Ui_Lib_App/SlideTabPages": "0.0.2", "@Frontend_Ui_Lib_App/SnackBar": "0.0.3", "@Frontend_Ui_Lib_App/Spinner": "^0.0.2", "@Frontend_Ui_Lib_App/SwitchButton": "^0.0.3", "@Frontend_Ui_Lib_App/TabSectionScroller": "^0.0.4", "@Frontend_Ui_Lib_App/Tabs": "0.0.1", "@Frontend_Ui_Lib_App/TabsPager": "0.0.2", "@Frontend_Ui_Lib_App/Tooltip": "0.0.2", "@RN_UI_Lib/BillingAddressRN": "1.1.8", "@RN_UI_Lib/BottomFilterWidget": "1.0.5", "@RN_UI_Lib/BottomSheet": "^1.0.4", "@RN_UI_Lib/Button": "^1.0.4", "@RN_UI_Lib/ButtonLoader": "1.0.4", "@RN_UI_Lib/CalendarCommon": "3.0.0", "@RN_UI_Lib/CustomModal": "1.0.6", "@RN_UI_Lib/CustomToast": "1.0.0", "@RN_UI_Lib/DateAndTimeScroller": "1.1.5", "@RN_UI_Lib/DateTimeFooter": "^1.2.0", "@RN_UI_Lib/DrumRollCalendar": "1.1.0", "@RN_UI_Lib/EditLocationBar": "^1.0.1", "@RN_UI_Lib/FilterBar": "1.0.6", "@RN_UI_Lib/FloatingInput": "^1.2.3", "@RN_UI_Lib/FloatingInputSelector": "1.0.7", "@RN_UI_Lib/FloatingInputV2": "1.0.3", "@RN_UI_Lib/HighlightedText": "^1.0.2", "@RN_UI_Lib/HourlyRentalFilter": "1.0.6", "@RN_UI_Lib/RadioButton": "1.1.0", "@RN_UI_Lib/SearchInfo": "1.0.2", "@RN_UI_Lib/SearchInput": "1.0.8", "@RN_UI_Lib/SearchSuggestions": "1.0.7", "@RN_UI_Lib/SearchWidget": "1.0.2", "@RN_UI_Lib/Shimmer": "^1.0.5", "@RN_UI_Lib/StateSelectionModal": "^1.0.6", "@RN_UI_Lib/mmt-grounds": "1.5.0", "@RN_UI_Lib/mmt-ui": "^1.0.7", "@bus-fe-commons/features-native": "0.0.4", "@bus-fe-commons/store": "0.0.2", "@bus-fe-commons/themes": "0.0.2", "@bus-fe-commons/ui-native": "0.0.2", "@core_app_acme/Card": "1.0.10", "@core_app_cabs/Card": "1.0.13", "@core_app_bus/Card": "1.0.9", "@core_app_common/Cards": "1.0.21", "@core_app_common/RefundTracker": "1.0.3", "@core_app_common/RenderPage": "1.0.22", "@core_app_tripmoney/Card": "1.0.18", "@core_app_common/RenderForwardFlow": "1.1.5", "react-native-loading-placeholder": "0.0.6", "@core_app_common/FormBuilder": "1.0.7", "@core_app_common/RenderStepPage": "1.0.8", "@core_app_common/TravelInsurance": "1.0.5", "@core_app_common/TravelInsuranceCard": "1.0.3", "@core_app_common/TravelInsuranceOld": "1.0.7", "@core_app_cruise/Card": "1.0.5", "@core_app_flight/DetailPageCards": "1.3.6", "@core_app_flight/ReviewPageCards": "0.0.1", "@core_app_flight/SellingPageCards": "1.0.12", "@core_app_flight/SpecialClaim": "1.1.1", "@core_app_holidays/Card": "1.0.17", "@core_app_holidays/Details": "1.0.3", "@core_app_holidays/DetailsVpp": "1.0.1-alpha.1", "@core_app_meals/Card": "1.0.5", "@core_app_visa/Card": "1.0.14", "@forex/rn": "2.7.4", "@gommt-cabs/rn": "1.3.1", "@gorhom/bottom-sheet": "4.6.4", "@growth/tripview": "4.5.9", "@mmt/event-logger": "0.0.5-dev", "@platform/server-driven-ui-react": "1.0.0", "@ptomasroos/react-native-multi-slider": "2.2.2", "@react-native-async-storage/async-storage": "2.1.2", "@ptui/chatbot-ui": "0.0.4-RC.7", "@rails-fe-one/app": "^1.0.75", "@rails-fe-one/config": "^1.0.59", "@rails-fe-one/core": "^1.0.64", "@rails-fe-one/quantum-ui": "^1.0.46", "@react-native-camera-roll/camera-roll": "5.2.0", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^3.4.7", "@react-native-community/masked-view": "0.1.7", "@react-native-community/progress-bar-android": "1.0.5", "@react-native-community/progress-view": "^1.3.0", "@react-native-picker/picker": "2.5.1", "@react-navigation/native": "^5.9.8", "@react-navigation/stack": "5.2.10", "@sentry/react-native": "5.34.0", "@shopify/flash-list": "1.6.4", "@shopify/react-native-performance": "4.1.2", "@shopify/react-native-performance-lists-profiler": "1.1.0", "@shopify/react-native-performance-navigation": "^3.0.0", "@travelplex/react-native": "0.2.7", "@trip-money/mmt-app": "0.0.298", "@turf/point-to-line-distance": "^6.5.0", "@urql/devtools": "^2.0.3", "@urql/exchange-graphcache": "^4.1.2", "@urql/exchange-request-policy": "^0.1.4", "@urql/exchange-retry": "^0.2.1", "MMT-UI": "6.1.93", "ad-react-wrapper": "2.0.8", "base-64": "0.1.0", "community": "0.0.16", "core-rn-ui-factory": "1.0.50", "core-ui-lib": "1.1.7", "create-react-class": "^15.7.0", "crypto-js": "4.2.0", "date-fns": "^4.1.0", "fecha": "2.3.2", "firebase": "9.1.2", "formik": "^2.4.2", "gommt-react-native-firebase": "12.4.0-rc.1", "graphql": "^16.8.1", "graphql-tag": "2.11.0", "immer": "10.0.2", "lodash": "4.17.4", "lottie-react-native": "7.2.2", "memoize-one": "5.1.1", "micromatch": "4.0.5", "mmt-react-native-orientation": "3.1.4", "mmt-react-native-pager-view": "5.4.7-alpha.7", "mobile-acme-react-native": "3.2.95", "mobile-react-native-styles": "1.0.2", "pako": "1.0.6", "prop-types": "15.8.1", "qr.js": "0.0.0", "query-string": "6.2.0", "react": "19.0.0", "react-cookie": "2.2", "react-cookies": "0.1.0", "react-helmet": "5.2.1", "react-native": "0.78.2", "react-native-ad-wrapper": "2.4.0", "react-native-animatable": "1.3.2", "react-native-background-upload": "6.6.0", "react-native-blob-util": "0.19.4", "react-native-calendars": "1.1310.0", "react-native-carousel-view": "0.5.1", "react-native-checkbox": "2.0.0", "react-native-core-ui-module": "1.1.55", "react-native-dash": "0.0.9", "react-native-date-picker": "4.3.7", "react-native-datepicker": "https://github.com/androidIsForVivek/react-native-datepicker.git", "@react-native-documents/picker": "10.1.3", "react-native-dotenv": "2.4.3", "react-native-easy-rating": "0.1.3", "react-native-fast-image": "8.5.2", "react-native-fast-shadow": "^0.1.1", "react-native-fs": "2.19.0", "react-native-gesture-handler": "2.25.0", "react-native-google-analytics": "1.3.2", "react-native-google-mobile-ads": "14.7.2", "react-native-htmlview": "0.17.0", "react-native-image-picker": "7.2.3", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-aware-view": "0.0.14", "react-native-keyboard-spacer": "0.4.1", "react-native-linear-gradient": "2.8.3", "react-native-looped-carousel": "0.1.13", "react-native-maps": "1.18.2", "react-native-markdown-display": "7.0.2", "react-native-masonry-list": "2.16.1", "react-native-mime-types": "^2.3.0", "react-native-modal-dropdown": "^1.0.0", "react-native-nowatch": "1.0.0", "react-native-popup-menu": "^0.16.1", "react-native-progress-circle": "2.0.0", "react-native-reanimated": "3.19.1", "react-native-reanimated-carousel": "3.5.1", "react-native-router-flux": "3.43.0", "react-native-safe-area-context": "3.3.2", "react-native-share": "7.3.2", "react-native-shimmer-placeholder": "1.0.35", "react-native-slider": "0.11.0", "react-native-snap-carousel": "^v3.9.1", "react-native-svg": "12.1.1", "react-native-tab-view": "^3.5.2", "react-native-text-ticker": "^1.12.0", "react-native-toast-message": "^2.2.1", "react-native-triangle": "^0.0.9", "react-native-video": "6.16.1", "react-native-webview": "13.13.5", "react-native-youtube-iframe": "2.3.0", "react-qr-code": "2.0.2", "react-query": "^3.19.0", "react-redux": "7.2.9", "reanimated-bottom-sheet": "1.0.0-alpha.22", "recyclerlistview": "git+https://github.com/mishsom/recyclerlistview.git#prefIssueFix", "redux": "4.2.1", "redux-logger": "3.0.6", "redux-thunk": "2.4.2", "reselect": "3.0.1", "themeprovider": "0.0.16", "url": "0.11.0", "urql": "^2.0.1", "valibot": "^1.1.0", "wonka": "^4.0.15", "zustand": "5.0.7", "zod": "~3.24.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "7.4.4", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@babel/traverse": "^7.20.1", "@mmt/events-schema": "1.0.0", "@platform/server-driven-ui-react": "1.0.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native-community/eslint-config": "^2.0.0", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@shopify/flash-list": "1.6.4", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "12.9.0", "@tsconfig/react-native": "^3.0.0", "@types/jest": "^29.5.14", "@types/lodash": "4.17.0", "@types/react": "^19.0.0", "@types/react-native": "0.65.1", "@types/react-redux": "5.0.28", "@types/react-test-renderer": "^19.0.0", "@types/redux-logger": "^3.0.8", "@typescript-eslint/eslint-plugin": "6.8.0", "@typescript-eslint/parser": "6.7.5", "@welldone-software/why-did-you-render": "10.0.1", "axios": "0.18.0", "babel-plugin-module-resolver": "3.0.0", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-flow": "6.23.0", "babel-preset-stage-0": "6.24.1", "eslint-config-prettier": "^6.11.0", "eslint-config-problems": "6.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-diff": "^2.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-promise": "^6.0.1", "eslint-plugin-react": "7.37.1", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-sonarjs": "^0.15.0", "flow-bin": "0.61.0", "form-data": "2.3.3", "glob": "^8.0.3", "husky": "^4.2.5", "jest": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jetifier": "1.6.3", "jscodeshift": "0.6.4", "lint-staged": "^10.2.13", "moment": "^2.30.1", "patch-package": "^7.0.0", "postinstall-postinstall": "^2.1.0", "react-native-bundle-visualizer": "2.3.0", "react-test-renderer": "19.0.0", "recast": "0.18.1", "redux-mock-store": "1.5.1", "string-hash": "1.1.3", "tsc-silent": "^1.2.2", "typescript": "5.0.4", "yargs": "13.2.4"}, "resolutions": {"@types/react": "^19.0.0", "react-native-linear-gradient": "2.8.3", "@react-native/normalize-colors": "0.78.0", "selenium-webdriver": "4.0.0-rc-1"}, "engines": {"node": ">=18.20.5"}}