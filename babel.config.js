const path = require('path');
const imageResolutionFallbackTransformer = path.resolve(__dirname, 'web/config/ImageResolutionFallbackTransformer.js');
module.exports = {
  presets: [
    ['react-native', { flow: false }],
    '@babel/preset-env'
  ],
  plugins: [
  [
      'module:react-native-dotenv',
      {
        moduleName: '@env',
        path: '.env',
        blacklist: null,
        whitelist: null,
        safe: false,
        allowUndefined: false
      }
    ],
    '@babel/plugin-proposal-nullish-coalescing-operator',
    ['@babel/plugin-proposal-decorators', {legacy: true}],
    '@babel/plugin-transform-runtime',
    ['@babel/plugin-proposal-class-properties', {loose: true}],
    ['@babel/plugin-transform-private-methods', {loose: true}],
    ['@babel/plugin-transform-private-property-in-object', {loose: true}],
    'dynamic-import-webpack',
    'react-native-web'
  ],
  env: {
    production: {
      plugins: [
        'transform-remove-console',
        '@babel/plugin-proposal-export-namespace-from',
        'react-native-reanimated/plugin',
        ['@babel/plugin-proposal-decorators', {legacy: true}]
      ]
    }
  }
};
