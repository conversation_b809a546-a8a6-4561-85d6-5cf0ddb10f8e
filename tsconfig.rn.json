{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"allowJs": false, "checkJs": false, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": false, "jsx": "react", "lib": ["ES6"], "types": ["jest", "googlemaps"], "module": "CommonJS", "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "baseUrl": "./", "typeRoots": ["./types"], "paths": {"src": ["./apps", "./packages"], "react-native-pager-view": ["./node_modules/mmt-react-native-pager-view"], "react-native-orientation": ["./node_modules/mmt-react-native-orientation"]}}, "exclude": ["babel.config.js", "metro.config.js", "jest.config.js", "./node_modules/**", "src", "apps/bus", "apps/crosslob", "**/*.web.js", "**/*.web.ts", "**/*.web.tsx", "./node_modules/redux-thunk/index.d.ts"]}