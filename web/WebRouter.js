import React from 'react';
import {Provider} from 'react-redux';
import {Route, Switch} from 'react-router';
import {ActivityIndicator, View} from 'react-native';
import {Router} from 'react-router-dom';
import Loadable from 'react-loadable';
import {CookiesProvider} from 'react-cookie';

import store, {history} from './webStore';
import WebViewWrapper from '../Mobile-mmt-react-native/packages/legacy-commons/Common/Components/WebViewWrapperNew';
import HolidaysLanding from '../Mobile-mmt-react-native/apps/holidays/src/LandingNew/Containers/HolidayLandingContainerNew';
import './WebModules';
import holidaysLanding from "../Mobile-mmt-react-native/apps/holidays/src/LandingNew/Reducers/HolidayLandingReducers";
import holidaysDetail from '../Mobile-mmt-react-native/apps/holidays/src/PhoenixDetail/Reducers/HolidayDetailReducers';
import {injectAsyncReducer} from "../Mobile-mmt-react-native/packages/legacy-commons/AppState/asyncStore";
import feedbackReducer from '../Mobile-mmt-react-native/apps/holidays/src/Common/Components/Feedback/redux/reducer';
import holidaysCommonOverlays from '../Mobile-mmt-react-native/apps/holidays/src/Common/Components/CommonOverlay/CommonOverlayReducers';


export const withRouterState = Component => props =>
    <Component {...props.location.state} {...props.match.params} {...props} />;

const HolidaysSeoGroupingAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysGrouping" */ '../Mobile-mmt-react-native/apps/holidays/src/Grouping/HolidaysSeoGroupingWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});


const HolidaysGroupingAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysGrouping" */ '../Mobile-mmt-react-native/apps/holidays/src/Grouping/HolidaysGroupingWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysPsmListingAsync = Loadable({
    loader: () => import('../Mobile-mmt-react-native/apps/holidays/src/MimaPreSales/webRoutes/HolidaysPsmListingWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysPresalesDetailAsync = Loadable({
    loader: () => import('../Mobile-mmt-react-native/apps/holidays/src/MimaPreSales/DetailsPage/HolidaysPresalesDetailWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});
const HolidaysPresalesEditDetailAsync =  Loadable({
    loader: () => import('../Mobile-mmt-react-native/apps/holidays/src/MimaPreSales/DetailsPage/HolidaysPresalesModifyWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysPsmCompareAsync = Loadable({
    loader: () => import('../Mobile-mmt-react-native/apps/holidays/src/MimaPreSales/webRoutes/HolidaysPsmCompareWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysDetailAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysDetail" */ '../Mobile-mmt-react-native/apps/holidays/src/Detail/HolidaysDetailWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysSearchWidgetAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysSearchWidget" */ '../Mobile-mmt-react-native/apps/holidays/src/SearchWidget/HolidaysSearchWidgetWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysReviewAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysReview" */ '../Mobile-mmt-react-native/apps/holidays/src/Review/HolidaysReviewWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysThankYouAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysThankYou" */ '../Mobile-mmt-react-native/apps/holidays/src/PostPayment/HolidaysThankYouWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

const HolidaysQueryAsync = Loadable({
    loader: () => import(/* webpackChunkName="holidaysQuery" */ '../Mobile-mmt-react-native/apps/holidays/src/Query/HolidaysQueryWebRoutes'),
    loading() {
        return <View style={styles.webRouterLoader}><ActivityIndicator
            styleAttr="Inverse"
            size="large"
            color="#008cff"
        /></View>;
    }
});

injectAsyncReducer('holidaysLanding', holidaysLanding);
injectAsyncReducer('feedback',feedbackReducer)
injectAsyncReducer('holidaysDetail', holidaysDetail);
injectAsyncReducer('holidaysCommonOverlays', holidaysCommonOverlays);


const WebRouter = () => (
    <CookiesProvider>
        <Provider store={store}>
            <Router history={history} forceRefresh>
                <View style={{flex: 1}}>
                    <Switch>
                        <Route exact path="/holidays/india" component={withRouterState(HolidaysLanding)}/>
                        <Route exact path="/holidays/international" component={withRouterState(HolidaysLanding)}/>
                        <Route exact path="/holidays/mobileIndia" component={withRouterState(HolidaysLanding)}/>
                        <Route exact path="/holidays/mobileInternational" component={withRouterState(HolidaysLanding)}/>
                        <Route path="/holidays-india" component={withRouterState(HolidaysLanding)}/>
                        <Route path="/holidays-international" component={withRouterState(HolidaysLanding)}/>
                        <Route path="/holidays/india/searchWidget" component={HolidaysSearchWidgetAsync}/>
                        <Route path="/holidays/international/searchWidget" component={HolidaysSearchWidgetAsync}/>
                        <Route path="/holidays/india/search" component={HolidaysGroupingAsync}/>
+                        <Route path="/holidays/international/search" component={HolidaysGroupingAsync}/>
                        <Route path="/holidays/india/group" component={HolidaysGroupingAsync}/>
                        <Route path="/holidays/psm/quotes/listing" component={HolidaysPsmListingAsync}/>
                        <Route path="/holidays/psm/quotes/detail" component={HolidaysPresalesDetailAsync}/>
                        <Route path="/holidays/psm/quotes/edit" component={HolidaysPresalesEditDetailAsync}/>
                        <Route path="/holidays/psm/quotes/compare" component={HolidaysPsmCompareAsync}/>
                        <Route path="/holidays/international/group" component={HolidaysGroupingAsync}/>
                        <Route path="/holidays/india/package" component={HolidaysDetailAsync}/>
                        <Route path="/holidays/international/package" component={HolidaysDetailAsync}/>
                        <Route path="/holidays/india/savePackage" component={HolidaysDetailAsync}/>
                        <Route path="/holidays/international/savePackage" component={HolidaysDetailAsync}/>
                        <Route path="/holidays/sendQuery30!packageSendQueryForm" component={HolidaysQueryAsync}/>
                        <Route path="/holidays/mobileSendQuery!packageSendQueryForm" component={HolidaysQueryAsync}/>
                        <Route path="/holidays/reArchBookingReviewAndPaymentAction!openReviewPage" component={HolidaysReviewAsync}/>
                        <Route path="/holidays/reArchBookingReviewAndPaymentAction" component={HolidaysReviewAsync}/>
                        <Route path="/holidays/international/review" component={HolidaysReviewAsync}/>
                        <Route path="/holidays/india/review" component={HolidaysReviewAsync}/>
                        <Route path="/holidays/onlineBookingPaymentThankyouAction" component={HolidaysThankYouAsync}/>
                        <Route path="/holidays/openWebView" component={withRouterState(WebViewWrapper)}/>
                    </Switch>
                </View>
            </Router>
        </Provider>
    </CookiesProvider>
);


const styles = {
    webRouterLoader: {
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center'
    }
}

export default WebRouter;


