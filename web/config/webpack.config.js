/* eslint-disable */
const enableOfflinePlugin = false

const __DEV__ = process.env.NODE_ENV === 'development'
const __OFFLINE__ = enableOfflinePlugin && !__DEV__

const path = require('path')
const glob = require('glob')
const webpack = require('webpack')
const autoprefixer = require('autoprefixer')

const HtmlWebpackPlugin = require('html-webpack-plugin');
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
// const {BundleAnalyzerPlugin} = require('webpack-bundle-analyzer');
const WorkboxPlugin = require('workbox-webpack-plugin');
// const WebpackPwaManifest = require('webpack-pwa-manifest');

const CopyWebpackPlugin = require('copy-webpack-plugin')
const OfflinePlugin = require('offline-plugin')

const outputPath = path.join(__dirname, '../dist/rn')
const publicPath = __DEV__ ? '/' : '//jsak.mmtcdn.com/holidays/rn/'
const staticPath = path.join(__dirname, '../static')
const fontPath = path.join(__dirname, '../fonts')
const imageResolutionFallbackTransformer = path.resolve(__dirname, './ImageResolutionFallbackTransformer.js')
const BrotliPlugin = require('brotli-webpack-plugin');


const prodPlugins = [
  new webpack.optimize.AggressiveMergingPlugin(),
  new CompressionPlugin({
    asset: '[path].gz[query]',
    algorithm: 'gzip',
    test: /\.js$|\.css$|\.html$/,
    threshold: 10240,
    minRatio: 0.8
  }),
  new BrotliPlugin({
    asset: '[path].br[query]',
    test: /\.(js|css|html|svg)$/,
    threshold: 10240,
    minRatio: 0.8
  }),
  // new BundleAnalyzerPlugin()
  new WorkboxPlugin.GenerateSW({
    // these options encourage the ServiceWorkers to get in there fast
    // and not allow any straggling "old" SWs to hang around
    clientsClaim: true,
    skipWaiting: true
  }),
  /*
    new WebpackPwaManifest({
      name: 'MakeMyTrip',
      short_name: 'MakeMyTrip',
      theme_color: '#444444',
      background_color: '#ffffff',
      icons: [
        {
          'src': path.resolve('./web/components/images/mmt_logo.png'),
          'sizes': [96, 128, 192, 256, 384, 512],
          'type': 'image/png'
        }
      ]
    })
  */
]

const plugins = [

  new webpack.DefinePlugin({
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
    __DEV__,
    __OFFLINE__,
  }),
  new HtmlWebpackPlugin({
    filename: __DEV__ ? 'index.html' : 'index.fx',
    template: 'web/templates/index.ejs',
    chunks: ['vendors', 'app']

  }),
  new ScriptExtHtmlWebpackPlugin({
    custom: [
      {
        test: /\.js$/,
        attribute: 'charset',
        value: 'utf-8'
      }
    ]
  }),
  new CopyWebpackPlugin([
    { context: outputPath, from: '*.png', to: 'images/' },
    { context: outputPath, from: staticPath, to: 'static/' },
    { context: outputPath, from: fontPath, to: 'fonts/' },
  ]),
  // new BundleAnalyzerPlugin(),
  ...(__DEV__ ? [] : prodPlugins)
]

// If offline plugin is enabled, it has to come last.
if (__OFFLINE__) plugins.push(new OfflinePlugin())

module.exports = {
  devServer: {
    compress: true,
    hot: true,
    contentBase: outputPath,
    disableHostCheck: true,
    https: false,
    proxy: {
      '/api': {
        target: 'https://holidayz.makemytrip.com',
        secure: false
      },
    },
    historyApiFallback: {
      index: '/'
    }
  },
  entry: {
    app: path.join(__dirname, '../index.web.js')
    //app: path.join(__dirname, '../isolateview.web.js')
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        exclude: /node_modules/,
        loader: "babel-loader",
        options: {
          presets: [
            '@babel/preset-env',
            ['@babel/preset-typescript', {
              allowNamespaces: true,
              allowDeclareFields: true,
              isTSX: true,
              allExtensions: true
            }]
          ],
          plugins: [
            '@babel/plugin-proposal-optional-chaining',
            '@babel/plugin-proposal-nullish-coalescing-operator',
            ['@babel/plugin-proposal-class-properties', { loose: true }],
            ['@babel/plugin-proposal-decorators', { legacy: true }],
            'dynamic-import-webpack'
          ],
          cacheDirectory: true
        }
      },
      {
        test: /\.js$/,
        exclude: /node_modules/,
        // TODO: Set up react-hot-loader during development.
        loaders: 'babel-loader?cacheDirectory=true',
        options: {
          'plugins': ['lodash', imageResolutionFallbackTransformer],
        }
      },
      {
        test: /\.css$/,
        use: [
          { loader: 'style-loader' },
          { loader: 'css-loader' },
          {
            loader: 'postcss-loader',
            options: {
              plugins: () => [autoprefixer()],
            },
          },
          {
            loader: 'sass-loader',
            options: {
              includePaths: ['./node_modules'],
            },
          }
        ],
      },
      {
        test: /\.ttf$/,
        loader: 'url-loader',
        include: path.resolve(__dirname, '../node_modules/react-native-vector-icons')
      },
      {
        // Many react-native libraries do not compile their ES6 JS.
        test: /\.js$/,
        include: /node_modules\/react-native-/,
        // react-native-web is already compiled.
        exclude: /node_modules\/react-native-web\//,
        loader: 'babel-loader',
        query: { cacheDirectory: true }
      },
      {
        test: /\.(png|jpe?g|gif|webp)$/,
        loader: 'react-native-web-image-loader?name=[name].[ext]&scalings[@2x]=2&scalings[@3x]=3',
        options: {
          limit: 8000, // Convert images < 8kb to base64 strings
          name: 'images/[hash:8]_[name].[ext]',
          publicPath: '/rn/'
        }
      },
      {
        test: /\.(mp3|wav)$/,
        loader: 'file-loader',
        query: { name: 'sounds/[name]-[hash:16].[ext]' }
      },
      { // needed for react-native-web-webview
        test: /postMock.html$/,
        include: /node_modules\/react-native-/,
        loader: 'file-loader',
        options: {
          name: '[name].[ext]'
        }
      }
    ]
  },
  output: {
    path: outputPath,
    filename: 'js/[name]-[hash:16].js',
    chunkFilename: 'js/[name]-[hash:16].bundle.js',
    publicPath: publicPath
  },
  optimization: {
    minimize: !__DEV__,
    moduleIds: 'named',
    splitChunks: {
      chunks: 'async',
      name: true,
      cacheGroups: {
        commons: {
          chunks: 'all',
          priority: -10
        },
        default: false
      }
    }
  },
  plugins: plugins,
  resolve: {
    alias: {
      '@rn': path.resolve(__dirname, '../../Mobile-mmt-react-native/src'),
      'src': path.resolve(__dirname, '../../Mobile-mmt-react-native/src'),
      "@mmt/holidays": path.resolve(__dirname, "../../Mobile-mmt-react-native/apps/holidays"),
      "@mmt/legacy-assets": path.resolve(__dirname, "../../Mobile-mmt-react-native/packages/legacy-assets"),
      "@mmt/core": path.resolve(__dirname, "../../Mobile-mmt-react-native/packages/core"),
      "@mmt/location": path.resolve(__dirname, "../../Mobile-mmt-react-native/packages/location"),
      "@mmt/navigation": path.resolve(__dirname, "../../Mobile-mmt-react-native/packages/navigation"),
      "@mmt/legacy-commons": path.resolve(__dirname, "../../Mobile-mmt-react-native/packages/legacy-commons"),
      "@mmt/ui": path.resolve(__dirname, "../../Mobile-mmt-react-native/packages/ui"),
      "apps/holidays": path.resolve(__dirname, "../../Mobile-mmt-react-native/apps/holidays"),
      "@mmt/acme-shared": path.resolve(__dirname, "../../Mobile-mmt-react-native/apps/acme-shared"),
      "@mmt/post-sales-shared": path.resolve(__dirname, "../../Mobile-mmt-react-native/apps/post-sales-shared"),
      "@mmt/post-sales": path.resolve(__dirname, "../../Mobile-mmt-react-native/apps/post-sales"),
      react: path.resolve(__dirname, "../../node_modules/react"),
      'react-art': path.resolve(__dirname, '../dummyReactArt.js'),
      'react-native': 'react-native-web',
      'lottie-react-native': 'react-native-web-lottie',
      'react-native-datepicker': 'react-mobile-datepicker',
      'react-native-linear-gradient': path.resolve(__dirname, '../components/linear-gradient'),
      'react-native-video': 'react-player',
      'WebView': 'react-native-web-webview',
      'react-native-router-flux': path.resolve(__dirname, '../routerFluxMock.js'),
      'react-native-router-flux/index': path.resolve(__dirname, '../routerFluxMock.js'),
      'rn-fetch-blob': path.resolve(__dirname, '../dummyReactArt.js'),
      "react-native-htmlview": path.resolve(__dirname, "../dummyReactArt.js"),
      'react-native-ad-wrapper': path.resolve(__dirname, '../adWrapperMock.js'), // ← ADD THIS
      "react-native-looped-carousel": "nuka-carousel",
      "react-native-snap-carousel": "nuka-carousel",
      'react-native-svg': 'react-native-svg-web'
    },
    modules: [
      path.resolve(__dirname, 'Mobile-mmt-react-native'),
      path.resolve(__dirname, 'Mobile-mmt-react-native/src'),
      path.resolve(__dirname, 'web'),
      'node_modules'
    ],
    extensions: [".web.js", ".js", ".json", ".web.tsx", ".web.ts", ".tsx", ".ts"]
  }
};
