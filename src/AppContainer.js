/* eslint-disable no-console */
import React, { Component, useEffect } from 'react';
import {
  Image,
  Platform,
  SafeAreaView,
  StatusBar,
  Text,
  View,
  NativeModules,
  BackHandler,
} from 'react-native';
import { setCustomSourceTransformer } from 'react-native/Libraries/Image/resolveAssetSource';
import { initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import logAction from '@mmt/legacy-commons/Helpers/actionsLogger';
import Toast from '@mmt/legacy-commons/Common/Components/Toast2';
import reactNavEnabledRef from './reactNavEnabledRef';
import AppRouter from './AppRouter';
// growth routes imports
import spinWinRouter from '@mmt/growth/src/EngagementZone/SpinWin/navigation/spinWinRouteConfig';
import walletRouter from '@mmt/growth/src/Wallet/navigation/walletRouteConfig';
import referAdmin from '@mmt/growth/src/ReferAdminMyBiz/navigation/referAdminRouteConfig';
import spider from '@mmt/growth/src/Spider/navigation/spiderRouteConfig';
// End
import HubbleToast from '@mmt/hubble/src/Common/HubbleToast';
import HubbleRouter, { isHubbleInitialRoute } from '@mmt/hubble/src/Navigation/HubbleRouter';
import PostSalesRouter from '@mmt/post-sales/src/navigation/postSalesRouter';
import { postSalesBookingRoutesSet } from '@mmt/post-sales/src/navigation/postSalesRouteConfig';
import DMSRouter, { RoutesConfig as DMS_ROUTES } from '@mmt/travel-mall/src/navigation';
import { popRootTag, pushRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import bootstrapModule from '@mmt/legacy-commons/Common/ModuleWrapper';
import cabsModule from '@mmt/cabs/src/entry/cabs.module';
import busModule from '@mmt/bus/legacy/bus.module';
import railsModule from '@mmt/rails/src/rails.module';
import visaModule from '@mmt/visa/src/visa.module';
import postSalesModule from '@mmt/post-sales/src/postSales.module';
import { insuranceModuleProvider } from '@trip-money/mmt-app/src';
import { TripMoneyNavigation } from './TripMoneyNavigation';
import { AcmeNavigation } from './AcmeNavigation';
import { HolidayNavigation } from './HolidayNavigation';
import NewRouter from './NewRouter';
import hubbleModule from '@mmt/hubble/src/hubble.module';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { openBlockScreenIfInGDPRRegion } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { COMMON_ACTIONS, COMMON_KEYS } from '@mmt/legacy-commons/Common/constants/AppConstants';
import store from '@mmt/legacy-commons/AppState/Store';
import tripViewRouter from '@growth/tripview/src/navigation/tripViewRouteConfig';
import { logRNPageLoad } from '@mmt/legacy-commons/Native/GenericModule';
import { assembleTripView } from '@growth/tripview/src/utils/assemble';
import { Brand } from '@growth/tripview/src/utils/BrandHolder';
import { TVNavImpl } from './TVNavigationImpl';
import { onPageChange } from '@mmt/legacy-commons/AppState/errorReporter';
import { PerformanceProfilerWrapper } from '@mmt/core/native/PerfLogger';
import { logRenderReportToFirebase } from '@mmt/core/native/PerfLogger/screenProfiler';
import { acmeModuleProvider } from 'mobile-acme-react-native';
import { holidayModuleProvider } from 'mobile-holidays-react-native';

import ForexApp from '@forex/rn';
import forexRouter, { isForexInitialRoute } from '@forex/rn/src/routes/forexRouteConfig';
import { getQueryParamsFromUrl } from '@mmt/legacy-commons/Helpers/misc';
import { assembleForex } from '@forex/rn/src/utils/assemble';
import { ForexNavImpl } from './ForexNavigation';
import { ToastProvider } from '@forex/rn/src/providers/toastProvider';
import { routesConfigSet } from '@mmt/hubble/src/Navigation/hubbleRoutesConfig';

import { MyraChatBotProvider, CHATBOT_STATE } from '@ptui/chatbot-ui';
import { TravelPlexBotNative,TravelPlexBotNativeVariant } from '@travelplex/react-native';
import { hubbleLogRenderReport } from '@mmt/hubble/src/Util/Performance/perfLogger';
import { CommonWishlistToast } from '@mmt/hubble/CommonWishlist/components/CommonWishlistToast';
// ================================================================================================================

class AppContainer extends Component {
  constructor(props) {
    super(props, 'AppContainer');
    if (props.ota_bundle_id) {
      setCustomSourceTransformer((resolver) => resolver.resourceIdentifierWithoutScale());
    }
    if (Text.defaultProps == null) {
      Text.defaultProps = {};
    }
    Text.defaultProps.allowFontScaling = false;
    pushRootTag(props.rootTag);
    this._initLob(props.page);
    reactNavEnabledRef.current = props['@nav/enabledForBus'] || false;
    const tripMoneyModule = insuranceModuleProvider();
    const acmeModule = acmeModuleProvider();
    const holidayModule = holidayModuleProvider();
    const modules = [
      railsModule,
      busModule,
      cabsModule,
      acmeModule,
      holidayModule,
      visaModule,
      postSalesModule,
      tripMoneyModule,
      hubbleModule,
    ];
    modules.forEach((mod) => mod.onBootstrap && mod.onBootstrap());
    tripMoneyModule.injectDependencies?.({
      navigation: TripMoneyNavigation,
      rootTag: props.rootTag,
    });

    acmeModule.injectDependencies?.({
      navigation: AcmeNavigation,
      rootTag: props.rootTag,
    });

    holidayModule.injectDependencies?.({
      navigation: HolidayNavigation,
      rootTag: props.rootTag,
    });

    assembleTripView(TVNavImpl(props.rootTag), Brand.MMT, props.rootTag);
    this.bootstrapRegistry = {};

    assembleForex(ForexNavImpl(props.rootTag), Brand.MMT, props.rootTag);

    const bootstrappedModules = [
      railsModule,
      busModule,
      cabsModule,
      acmeModule,
      holidayModule,
      visaModule,
      tripMoneyModule,
      postSalesModule,
    ].map((mod) => bootstrapModule(mod, this.bootstrapRegistry));

    /** @type {RouteConfig[]} */
    const wrappedRoutes = [];
    bootstrappedModules.forEach((mod) => {
      wrappedRoutes.push(...mod.routeConfig);
      if (mod.routeConfig.find((route) => route.key === props.page)) {
        onPageChange(props.page, mod.id);
      }
    });

    /** @type {RouteConfig[]} */
    const growthRoutes = [
      ...spinWinRouter,
      ...walletRouter,
      ...referAdmin,
      ...spider,
      ...tripViewRouter,
    ];

    if (growthRoutes.find((route) => route.key === props.page)) {
      onPageChange(props.page, 'growth');
    }

    this.ROUTES = [...wrappedRoutes, ...growthRoutes];

    this.ROUTES.map((route) => {
      route.isMigratedRoute = true;
    });
    this.isGDPRBlockRoute = this.ROUTES.find(
      (route) => route.key === this.props.page && route?.initialProp?.gdprBlockRoute,
    );
  }

  componentDidMount() {
    if (this.isGDPRBlockRoute) {
      openBlockScreenIfInGDPRRegion(
        this.isGDPRBlockRoute?.initialProp?.blockLobName,
        this.props?.need_consent,
        this.props?.page,
      );
    }
    Image.resizeMode = {
      cover: 'cover',
      contain: 'contain',
      stretch: 'stretch',
    };
    logAction('Start', this.props.page);
    initAbConfig();
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.page !== nextProps.page) {
      this._initLob(nextProps.page);
    }
  }

  resetReduxState = async (pageKey) => {
    try {
      /*
       * Why this change ? : To meet the requirement of Apple account deletion mandate. if a/c deleted
       * then native iOS deletes all async storage, so we also need to clear the redux state. (in iOS RN
       * instance always stay alive)
       *
       * What it does ? : If no value found in async storage that means a/c deleted (expect first time)
       * so we clear the redux state and update the async storage so that next time it will not be cleared again.
       * But first time it will still clear the state as no value for async storage, but it will not cause any issue.
       */
      const isStateResetDone = await AsyncStorage.getItem(COMMON_KEYS.REDUX_STATE_RESET_DONE);
      if (!isStateResetDone || !JSON.parse(isStateResetDone)) {
        store.dispatch({
          type: COMMON_ACTIONS.RESET_ALL_REDUX_STATE,
        });
        await AsyncStorage.setItem(COMMON_KEYS.REDUX_STATE_RESET_DONE, JSON.stringify(true));
      }
    } catch (e) {
      console.log('Error while resetting the redux state', e);
    }
  };

  _initLob = (pageKey) => {
    console.log('_initLob', pageKey);
    logRNPageLoad(pageKey);
    if (Platform.OS === 'ios') {
      this.resetReduxState(pageKey);
    }
  };

  componentWillUnmount() {
    Object.values(this.bootstrapRegistry).forEach((route) => {
      route && route.onUnmount && route.onUnmount(this.props);
    });
    popRootTag(Platform.OS === 'ios' ? this.props.rootTag : undefined);
  }

  render() {
    const { page, deep_link_intent_url } = this.props;

    const queryParams = deep_link_intent_url ? getQueryParamsFromUrl(deep_link_intent_url) : {};
    const travelPlexPage = queryParams.page || page;
    
    if(travelPlexPage === 'travelplexCbMain') {
      
      // To handle travelplex with auto mounting
      const parentPage = queryParams.parentPage;
        if(parentPage === 'MyAccount' || parentPage === 'BottomBar'){
        return (
          <View style={{ flex: 1, backgroundColor: 'transparent' }} key={this.props.chatInstanceId}>
            <TravelPlexBotNativeVariant  {...this.props} parentPage={parentPage}  />
          </View>
        );
      }

        return (
          <View style={{ flex: 1, backgroundColor: 'transparent' }} key={this.props.chatInstanceId}>
            <TravelPlexBotNative  {...this.props} />
          </View>
        );

      }
 


    const isDMSRoute = DMS_ROUTES.find((route) => route.name === page);
    if (isDMSRoute) {
      return (
        <View style={{ flex: 1 }}>
          <DMSRouter {...this.props} />
        </View>
      );
    }

    if (isHubbleInitialRoute(page)) {
      return (
        <PerformanceProfilerWrapper
          onReportPrepared={hubbleLogRenderReport}
          useRenderTimeouts={false}
        >
          <MyraChatBotProvider parentFunnel={'W2G'}>
            <View style={{ flex: 1 }}>
              <HubbleRouter {...this.props} />
              <Toast />
              <HubbleToast />
              <CommonWishlistToast />
            </View>
          </MyraChatBotProvider>
        </PerformanceProfilerWrapper>
      );
    }

    if (isForexInitialRoute(page)) {
      return <ForexApp {...this.props} />;
    }

    // TODO @rajeshbatth Remove <SafeAreaView/> for useReactNav==true, let individual pages take care of this
    const initialRoute = this.ROUTES.find(
      (route) => route.key === page || (route.matcher && route.matcher(page)),
    );
    const useReactNav = !!initialRoute;

    if (useReactNav) {
      return (
        <View style={{ flex: 1 }}>
          <PerformanceProfilerWrapper
            onReportPrepared={logRenderReportToFirebase}
            useRenderTimeouts={false}
          >
            <ToastProvider>
              <NewRouter {...this.props} routes={this.ROUTES} initialRoute={initialRoute} />
            </ToastProvider>
          </PerformanceProfilerWrapper>

          <Toast />
        </View>
      );
    }

    return (
      <SafeAreaView style={{ flex: 1 }}>
        {Platform.OS === 'ios' && (
          <View>
            <StatusBar backgroundColor="white" barStyle="dark-content" />
          </View>
        )}
        <AppRouter {...this.props} />
        <Toast />
        <HubbleToast />
      </SafeAreaView>
    );
  }
}

export default AppContainer;
