import React from 'react';
import { connect, Provider } from 'react-redux';
import { ActionConst, Actions, Reducer, Router, Scene } from 'react-native-router-flux';
import { animationDirection, animationDuration, animationType } from '@mmt/legacy-commons/Common/utils/AppUtils';
import store from '@mmt/legacy-commons/AppState/Store';
import { allActiveReactPages, onPageFocused } from '@mmt/legacy-commons/Helpers/pageNavHelpers';
import WebViewWrapper from '@mmt/legacy-commons/Common/Components/WebViewWrapper';
import { onRouteUpdated } from '@mmt/legacy-commons/Helpers/actionsLogger';
import HotelFeedbackPage from '@mmt/post-sales/src/hotels-funnel/Container/FeedbackContainer';
import HotelReviewFullSizePage from '@mmt/post-sales/src/hotels-funnel/Components/HotelReviewFullSizePage';
import CommonWebView from '@mmt/legacy-commons/Common/Components/CommonWebView';
import { setIsUsingLegacyNav } from '@mmt/legacy-commons/Native/navigationStateModule';
import { popRouter, pushRouter } from '@mmt/legacy-commons/Common/navigation/routerTypeRef';
import DownloadApp from '@mmt/post-sales/src/selfDrive/components/DetailPage/downloadApp';
import { isHolidaysPage } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import HolidayDataHolder from 'mobile-holidays-react-native/src/utils/HolidayDataHolder';
import { NotFoundPage } from './NotFoundPage';

/**
 * ! =============================== NOTE =============================== !
 * `collapseGroups: true` check has been added because the logs were difficult to read
 * as they were expanded and the amount of data being logged on console made it
 * tricky for eyes to keep track of action names being fired. whereas if we keep
 * collapsed, the console gives a crisp visibility of each action name. the
 * check has been added by `MMT9107`, please let me know if you believe
 * this check should be removed or have a different perspective
 */

if (__DEV__) {
  const whyDidYouRender = require('@welldone-software/why-did-you-render');
  whyDidYouRender(React, { collapseGroups: true });
}

let flightReducerInitHappened = false
const FlightListingLazy = () => {
  function InnerComponent(props) {
    if (!flightReducerInitHappened) {
      const flightListing = require('@mmt/post-sales/src/flights-funnel/Listing/utils/FlightsListingReducers').default;
      store.addReducers({
        flightListing
      });
      flightReducerInitHappened = true;
    }

    const FlightListing = require('@mmt/post-sales/src/flights-funnel/Listing/utils/FlightListingContainer').default;
    return <FlightListing {...props} />;
  }

  return InnerComponent;
};

function lazyContainer(componentFactory, onMount){
  function InnerComponent(props){
    if (onMount) {
      onMount()
    }
    const Cmp = componentFactory()
    return <Cmp {...props}/>
  }
  return InnerComponent
}

const ReduxRouter = connect()(Router);
const createReducer = (params) => {
  const defaultReducer = Reducer(params);
  return (state, action) => {
    if (action.type === ActionConst.FOCUS) {
      const pageName = action.scene.name;

      if (pageName !== 'root') {
        Actions.currentScene = pageName;
        onPageFocused(pageName, action.scene.component);
        if (isHolidaysPage(pageName)) {
          HolidayDataHolder.getInstance().setCurrentPage(action.scene.sceneKey);
        }
      }
    }
    onRouteUpdated(state, action);
    allActiveReactPages(action);
    return defaultReducer(state, action);
  };
};
const AppRouter = ({ page, rootTag, ...otherProps }) => {
  const routerRef = React.useRef(null);
  React.useEffect(() => {
    pushRouter('RNRF', { getCurrentPageKey: () => Actions.currentScene });
    setIsUsingLegacyNav(true);
    return () => {
      popRouter('RNRF');
    };
  }, []);
  return (
    <Provider store={store}>
      <ReduxRouter
        hideNavBar
        getSceneStyle={() => ({
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 0,
        })}
        createReducer={createReducer}
      >
        <Scene ref={routerRef} key="root" duration={animationDuration}>
          <Scene
            key="notFoundPage"
            component={NotFoundPage}
            type={animationType}
            direction={animationDirection}
            duration={animationDuration}
            rootTag={rootTag}
            page={page}
            {...otherProps} />


          <Scene
            key="feedback"
            component={HotelFeedbackPage}
            initial={page === 'feedback'}
            rootTag={rootTag}
            {...otherProps}
          />

          <Scene key="openWebView" component={WebViewWrapper} />
          <Scene key="downloadApp" component={DownloadApp} />



          <Scene key="hotelReviewFullSize" component={HotelReviewFullSizePage} />
          <Scene
            key="feedback"
            component={HotelFeedbackPage}
            initial={page === 'feedback'}
            rootTag={rootTag}
            {...otherProps}
          />

          <Scene key="flightListing" component={lazyContainer(FlightListingLazy)} />

          <Scene
            key="commonWebView"
            component={CommonWebView}
            type={animationType}
            direction={animationDirection}
            duration={animationDuration}
            rootTag={rootTag}
            {...otherProps}
          />
        </Scene>
      </ReduxRouter>
    </Provider>
  );
};
export default AppRouter;
