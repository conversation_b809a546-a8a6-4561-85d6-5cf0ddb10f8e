import { NavigationAdapter as HolidayNavigationAdapter } from 'mobile-acme-react-native';
import { navigation } from '@mmt/navigation';

class HolidayNavigationWrapper implements HolidayNavigationAdapter {
  navigate(key: string, options?: any): void {
    navigation.navigate(key, options);
  }
  push(key: string, options?: any): void {
    navigation.push(key, options);
  }
  popToPage(stackNo: number): void {
    const nav = navigation.getNavigationRef();
    nav.pop(stackNo);
  }
  pop(): void {
    navigation.pop();
  }
  replace(key: string, options?: any): void {
    navigation.replace(key, options);
  }
  goBack(): void {
    navigation.goBack();
  }
  canGoBack(): boolean {
    return navigation.canGoBack();
  }
  reset(routes: { name: string; params?: any }[], index: number): void {
    navigation.resetRoute(routes, index);
  }
  getCurrentRoute = (): string[] | null => {
    const state = navigation?.getNavigationRef()?.dangerouslyGetState();
    const { routes } = state || {};
    let topRoute = null;
    if (routes?.length) {
      topRoute = routes[routes.length - 1];
    }
    return topRoute?.name;
  };
  getPreviousRoute = (): string[] | null => {
    const state = navigation?.getNavigationRef()?.dangerouslyGetState();
    const { routes } = state || {};
    let previousRoute = null;
    if (routes?.length > 1) {
      previousRoute = routes[routes.length - 2];
    }
    return previousRoute?.name;
  }

}

export const HolidayNavigation = new HolidayNavigationWrapper();
