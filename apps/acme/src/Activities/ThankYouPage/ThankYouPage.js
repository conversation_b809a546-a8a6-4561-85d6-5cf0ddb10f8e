import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import _ from 'lodash';
import {connect} from 'react-redux';
import HTMLView from 'react-native-htmlview';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import ACMEModule from '@mmt/legacy-commons/Native/ACMEModule';
import {PAGE_NAME, EVENT_NAME, EVAR_32} from '../../Utils/Analytics/OmnitureConstants';
import {logTuneEvent, TUNE_EVENT} from '../../Utils/Analytics/Tune/TuneTracker';
import backIconAndroid from '@mmt/legacy-assets/src/trip_header_back_icon.webp';
import backIconIos from '@mmt/legacy-assets/src/backIosGrey.webp';
import {
  View, Text, ScrollView, BackHandler,
  Image, ActivityIndicator, Platform, Modal, DeviceEventEmitter
} from 'react-native';
import url from 'url';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { ACME_ROUTE_KEYS, AcmeNavigation } from '../../Navigation';
import QRCode from 'react-qr-code';
import Header from '../../Components/Header';
import {showLongToast, showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import thankYouIcon from '@mmt/legacy-assets/src/Images/thankYouIcon.webp';
import thankYouPendingIcon from '@mmt/legacy-assets/src/pending.webp';
import thankYouFailedIcon from '@mmt/legacy-assets/src/failed.webp';
import {
  API_BASE_URL, BOOKING_CONFIRM_API_ENDPOINT, PAGE_STATUS, LOCAL_STORAGE_FIELDS, UNIT_TYPES,
  UNIT_TEXTS, ERROR_CODES, CUSTOMER_CARE_NO, PLATFORM_IOS, USER_PLATFORM, MY_TRIPS_URL,
  GENERIC_ERROR_MSG, DYNAMIC_FORM_THANKYOU_TEXT, AUTH_ENV, NATIVE_EVENTS
} from '../../Constants';
import FetchApiResponse, {setAuthCredentials} from '../../Utils/ApiUtils';
import styles from './ThankYouPageCss';
import CommonStyles, {gradient} from '../../AcmeCommonCss';
import Footer from '../../Components/Footer';
import MySafetyCard from '../../Components/MySafetyCard';
import MmtBlackCard from '../../Components/MmtBlackCard';
import PaymentSummary from './PaymentSummary';
import {
  getDayText, getShortDate, getTimeString, convertDDMMYYYYtoDate,
  getEndTime, retrieveObjFromAsyncStorage,
  removeKeyFromAsyncStorage, isMMTPayProduct,
  isMySafetyEnabled, isMmtBlackEnabled
} from '../../Utils/CommonUtils';
import ErrorScreen from '../../Components/ErrorScreen';
import {logActionEvent, setOmnitureCxt, logPageLoadEvent, addToOmnitureCxt} from '../../Utils/Analytics/Omniture';
import {isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import NoInternetView from '@mmt/legacy-commons/Common/Components/Error/NoInternetView';
import {logPdtActionEvent, logPdtPageLoadEvent, logPdtPageEntryEvent, logPdtPageExitEvent} from '../../Utils/Analytics/Pdt/PdtTracker';
import {PDT_EVENT_NAME, PDT_PAGE_NAME} from '../../Utils/Analytics/Pdt/PdtConstants';
import {setPDTContextFromCache} from '../../Utils/Analytics/Pdt/PdtContextCache';
import LoadingIndicator from '../../Components/LoadingIndicator';
import TouchableOpacity from '../../Components/TouchableOpacity';
import withRouterWrapper from '../../Web/withRouterWrapper';
import YellowTextView from '../../Components/YellowTextView';
import WhiteButton from '../../Components/WhiteButton';
import DynamicForm from '../../Components/DynamicForm';
import CategoryUtils from '../../Utils/CategoryUtils';
import * as NpsModule from '@mmt/legacy-commons/Native/NpsModule';
import {getTxnCurSymbol} from '../../Utils/CurrencyUtils';
import HolidayDataHolder from '';

const BOOKING_STATE = {
  CONFIRMED: 'confirmed',
  FAILED: 'failed',
  PENDING: 'pending'
};
const POLLING_WAIT_TIME = 10000;

const PLATFORM_STYLE_SUFFIX = Platform.OS === PLATFORM_IOS ? 'Ios' : 'Android';

class ThankYouPage extends BasePage {
  constructor(props) {
    super(props);
    this.paymentResponse = {};
    // default offset top to hide back button
    this.backBtnHideOffsetTop = 130;
    if (props.PAYMENT_RESPONSE_VO) {
      try {
        this.paymentResponse = JSON.parse(props.PAYMENT_RESPONSE_VO);
      } catch (error) {
        // do nothing
      }
    }

    if (USER_PLATFORM.WEB) {
      const urlObj = url.parse(window.location.href, window.location.search);
      const {query} = urlObj;
      if (query && query.ckId) {
        this.paymentResponse = {
          checkoutId: query.ckId
        };
      }
      this.props.history.listen((location, action) => {
        if (action === 'POP' && location.pathname === '/thankyou') {
          AcmeNavigation.reset(ACME_ROUTE_KEYS.LANDING, {fromPage: PAGE_NAME.THANK_YOU});
        }
      });
    }
    this.state = this._getInitialStateObject();
  }

  async componentDidMount() {
    BackHandler.addEventListener('hardwareBackPress', this.backPressHandler);
    DeviceEventEmitter.addListener(NATIVE_EVENTS.NPS_CLOSE_EVENT, this.npsCloseEventListener);
    this._confirmBooking();
    if (USER_PLATFORM.WEB || USER_PLATFORM.IOS) {
      // Set omniture context from local storage
      const omnitureContext = await retrieveObjFromAsyncStorage(LOCAL_STORAGE_FIELDS.OMNITURE_CONTEXT);
      setOmnitureCxt(omnitureContext);

      const categoryName = await retrieveObjFromAsyncStorage(LOCAL_STORAGE_FIELDS.CATEGORY);
      CategoryUtils.setCategoryName(categoryName);

      await setPDTContextFromCache();
      removeKeyFromAsyncStorage(LOCAL_STORAGE_FIELDS.PDT_CONTEXT);
      logPdtPageEntryEvent(PDT_PAGE_NAME.THANK_YOU, this);
    } else {
      logPdtPageEntryEvent(PDT_PAGE_NAME.THANK_YOU, this);
    }
  }

  componentWillUnmount() {
    BackHandler.removeEventListener('hardwareBackPress', this.backPressHandler);
    DeviceEventEmitter.removeListener(NATIVE_EVENTS.NPS_CLOSE_EVENT, this.npsCloseEventListener);
  }

  npsCloseEventListener = () => {
    this.setState({
      npsVisible: false
    });
  };

  render() {
    return (
      <View style={[CommonStyles.flex1]}>
        {this.state.pageStatus === PAGE_STATUS.SUCCESS ||
          this.state.pageStatus === PAGE_STATUS.PENDING ||
          this.state.pageStatus === PAGE_STATUS.CONFIRMING ?
          this.renderThankYouPage() : null}
        {this.state.pageStatus === PAGE_STATUS.FAILED && this._renderBookingFailedPage() }
        {this.state.pageStatus === PAGE_STATUS.LOADING &&
          <LoadingIndicator
            message="Checking booking status..."
            header={this.getHeader()}
          />}
        {this.state.pageStatus === PAGE_STATUS.ERROR && this.renderErrorScreen()}
        {this.state.pageStatus === PAGE_STATUS.NO_INTERNET && this.renderNoInternetScreen() }
      </View>
    );
  }

  renderErrorScreen = () => (
    <View style={CommonStyles.flex1}>
      {this.getHeader()}
      <ErrorScreen desc="" title="" />
    </View>
  );

  getHeader = () => (
    <Header
      backHandler={() => AcmeNavigation.reset(ACME_ROUTE_KEYS.LANDING, {fromPage: PAGE_NAME.THANK_YOU})}
      isSearchVisible={false}
      pageName={PAGE_NAME.THANK_YOU}
    />
  );

  /**
   * Function to render Booking Failed Page
   * @returns {object} Booking Failed page JSX
   */
  _renderBookingFailedPage = () => (
    <View>
      {this.renderBackBtn()}
      {this._getHeaderSection()}
      <View style={styles.failedSection}>
        <Text style={styles.refundText}>{this.state.failMsg || 'In case any amount was deducted, refund will be made out to payment source within 2-4 business days'}</Text>
      </View>
      <View style={[styles.failedSection, {height: '100%'}]}>
        <Text style={styles.contactUsText}>Contact Us</Text>
        <Text style={styles.contactNumberHeading}>CONTACT NO.:</Text>
        <Text style={styles.contactPhoneNumber}>{this.state.custCareNo || CUSTOMER_CARE_NO}</Text>
      </View>
    </View>);

  renderNoInternetScreen = () => (
    <NoInternetView
      onRetry={this.onRetryAfterNoInternet}
      style={{marginTop: 100}}
    />
  );

  onRetryAfterNoInternet = () => {
    this.setState({pageStatus: PAGE_STATUS.LOADING});
    this._confirmBooking(0);
  };

  backPressHandler = (isAppBackPress) => {
    removeKeyFromAsyncStorage(LOCAL_STORAGE_FIELDS.PRIMARY_TAG)
    if (this.state.showPaymentSummary) {
      this.setState({showPaymentSummary: false});
      return true;
    }

    if (this.state.showDynamicForm) {
      this.setState({showDynamicForm: false});
      return true;
    }

    if (this.state.npsVisible && USER_PLATFORM.ANDROID) {
      this.setState({npsVisible: false});
      ACMEModule.hideNps();
      return true;
    }

    if (this.state.npsVisible && USER_PLATFORM.IOS) {
      this.setState({npsVisible: false});
      NpsModule.removeNps();
    }

    if (isAppBackPress) {
      logPdtActionEvent(PDT_EVENT_NAME.APP_BACK_CLICK, this, {pageName: PDT_PAGE_NAME.THANK_YOU});
    } else {
      logPdtActionEvent(PDT_EVENT_NAME.DEVICE_BACK_CLICK, this, {pageName: PDT_PAGE_NAME.THANK_YOU});
    }
    logPdtPageExitEvent(PDT_PAGE_NAME.THANK_YOU, this);
    logPdtPageEntryEvent(PDT_PAGE_NAME.LANDING, this);
    setAuthCredentials(AUTH_ENV.ACME);
    addToOmnitureCxt(EVAR_32, undefined);
    if (USER_PLATFORM.ANDROID || USER_PLATFORM.WEB) {
        AcmeNavigation.reset(ACME_ROUTE_KEYS.LANDING, {
        fromPage: PAGE_NAME.THANK_YOU
      });
    } else {
      ACMEModule.popTillAcmeHome(this.props.rootTag || 1, CategoryUtils.getCategoryName());
    }
    return true;
  };

  /**
   * Function to confirm booking
   * @param {integer} pollingCount No of times polling is to be done to check the status of the booking
   * @param {string} id Booking ID
   */
  _confirmBooking = async (pollingCount = 5) => {
    let {bookingId, checkoutId} = this.paymentResponse;
    const apiUrl = `${API_BASE_URL}${BOOKING_CONFIRM_API_ENDPOINT}/${checkoutId}`;

    let responseBody;
    try {
      responseBody = await FetchApiResponse({
        method: 'GET',
        apiUrl
      });
    } catch (error) {
      console.log(error);
    }

    if (USER_PLATFORM.WEB && responseBody) {
      bookingId = responseBody.booking_id;
    }

    if (responseBody && responseBody.status === BOOKING_STATE.CONFIRMED) {
      logPdtPageLoadEvent(PDT_PAGE_NAME.THANK_YOU, this);
      logActionEvent(EVENT_NAME.THANKYOU_BOOKING_CONFIRMED, this, {bookingId, id: responseBody.product_id, title: responseBody.product_name});
      logPdtActionEvent(PDT_EVENT_NAME.THANK_YOU_BOOKING_STATE, this, {bookingId, bookingState: BOOKING_STATE.CONFIRMED, responseBody});
      // If the booking is confirmed
      if (Platform.OS === 'android') {
        ACMEModule.setMyTripState();
      }
      this._updateThankYouPage(responseBody, bookingId, {isBookingConfirmed: true, isPolling: false});
      logTuneEvent(TUNE_EVENT.ACME_THANK_YOU, this.state);
    } else if (responseBody && responseBody.status === BOOKING_STATE.FAILED) {
      logPdtPageLoadEvent(PDT_PAGE_NAME.THANK_YOU, this);
      logPageLoadEvent(PAGE_NAME.THANK_YOU, this);
      logActionEvent(EVENT_NAME.THANKYOU_BOOKING_ERROR, this);
      logPdtActionEvent(PDT_EVENT_NAME.THANK_YOU_BOOKING_STATE, this, {bookingState: BOOKING_STATE.FAILED, responseBody});
      this.setState({
        pageStatus: PAGE_STATUS.FAILED,
        bookingId,
        failMsg: responseBody.fail_msg || '',
        custCareNo: responseBody.cust_care_no || ''
      });
    } else if (pollingCount !== 0) {
      // Keep trying to confirm booking till Status is Processed or pollingCount isn't 0
      if (responseBody && responseBody.status) {
        this._updateThankYouPage(responseBody, bookingId, {isBookingConfirmed: false, isPolling: true});
      }
      setTimeout(() => {
        if (this._confirmBooking) {
          this._confirmBooking(pollingCount - 1);
        }
      }, POLLING_WAIT_TIME);
    } else if (responseBody && responseBody.error_code === ERROR_CODES.NO_INTERNET_ERROR_CODE) {
      logActionEvent(EVENT_NAME.THANKYOU_BOOKING_ERROR, this);
      this.setState({pageStatus: PAGE_STATUS.NO_INTERNET});
    } else if (!responseBody || responseBody.error) {
      logActionEvent(EVENT_NAME.THANKYOU_BOOKING_ERROR, this);
      this.setState({pageStatus: PAGE_STATUS.ERROR});
    } else {
      logPdtPageLoadEvent(PDT_PAGE_NAME.THANK_YOU, this);
      logPageLoadEvent(PAGE_NAME.THANK_YOU, this);
      logActionEvent(EVENT_NAME.THANKYOU_BOOKING_PENDING, this);
      logPdtActionEvent(PDT_EVENT_NAME.THANK_YOU_BOOKING_STATE, this, {bookingState: BOOKING_STATE.PENDING, responseBody});
      this._updateThankYouPage(responseBody, bookingId, {isBookingConfirmed: false, isPolling: false});
    }
  }

  showNps = (bookingId) => {
    if (USER_PLATFORM.ANDROID) {
      ACMEModule.showNps({
        lob: 'Acme', category: 'Acme_ThankYouPage', bookingId, page: `${PAGE_NAME.THANK_YOU}|${CategoryUtils.getCategoryName()}`
      });
      return;
    }

    NpsModule.showNps(bookingId, NpsModule.NpsParams.ACME);
  }

  /**
   * Function to update the Thank You page with data
   * @param {Object} data Data Object from backend
   * @param {String} bookingId Booking ID
   * @param {Boolean} isBookingConfirmed Flag to tell if booking is confirmed or not
   */
  _updateThankYouPage = async (data, bookingId, statusObj) => {
    const state = _.cloneDeep(this.state);

    if (statusObj.isBookingConfirmed) {
      state.pageStatus = PAGE_STATUS.SUCCESS;
      state.npsVisible = true;
      this.showNps(bookingId);
    } else if (statusObj.isPolling) {
      state.pageStatus = PAGE_STATUS.CONFIRMING;
    } else {
      state.pageStatus = PAGE_STATUS.PENDING;
    }
    state.scanCode = data.scan_code;
    state.showQrCode = (data.show_scan_code && data.scan_code);
    state.emailId = data.cust_email || '';
    state.phone = data.cust_phone || '';
    state.bookingId = bookingId ? bookingId.toString() : '';
    state.activityTitle = data.product_name || '';
    state.localityName = data.locality_name || '';
    state.reportAt = data.product_address;
    state.tourDate = data.start_date ? convertDDMMYYYYtoDate(data.start_date) : null;
    state.startTime = data.start_time || '';
    state.endTime = data.start_time ? getEndTime(data.start_time, data.duration) : '';
    state.unitType = data.unit_type;
    state.unitCount = data.unit_count || 0;
    state.childCount = data.child_count || 0;
    state.infantCount = data.infant_count || 0;
    state.seniorCount = data.senior_count || 0;
    state.youthCount = data.youth_count || 0;
    state.primaryParticipant = `${data.cust_first_name} ${data.cust_last_name}`;
    state.multiplier = data.rpMultiplier || 1;
    state.isAdditionalDataReq = data.is_form_data_req || false;
    state.isFormEditable = data.is_form_editable;
    state.productId = data.product_id;
    state.unitTypesDetails = data.unitTypesDetail;

    const detailObj = await retrieveObjFromAsyncStorage(LOCAL_STORAGE_FIELDS.DETAIL_DATA) || {};
    state.tnc = detailObj && detailObj.tnc ? detailObj.tnc : '';
    state.howToRedeem = detailObj && detailObj.howToRedeem ? detailObj.howToRedeem : '';

    state.activityCharges = data.booking_amount;
    state.discount = data.discount || 0;
    state.wallet = data.wallet_deduction || 0;
    state.convenienceFee = data.convenience_fee || 0;
    state.gstRate = data.tax_percent;
    state.gstAmount = data.extra_amount_tax || 0;
    state.amountPaid = data.amount || 0;
    state.paymentMode = data.transaction_mode;
    state.cardNumber = data.masked_card_number;
    state.categoryId = data.category_id;
    state.isMmtPayProduct = isMMTPayProduct(data.category_id);
    state.rateplanId = data.product_plan_code;
    state.rateInventoryId = data.rate_inventory_id;
    state.txnCurSymbol = getTxnCurSymbol(data.currencyInfo);
    state.safetyAssured = data.safety_assured;
    state.userPersuasions = data.userPersuasions;
    this.setState(state);
  };

  /**
   * Function to get initial state object
   * @returns {Object} Initial State Object
   */
  _getInitialStateObject = () => ({
    pageStatus: PAGE_STATUS.LOADING,
    showPaymentSummary: false,
    emailId: '',
    phone: '',
    bookingId: '',
    activityTitle: '',
    localityName: '',
    reportAt: '',
    tourDate: null,
    startTime: '',
    endTime: '',
    primaryParticipant: '',
    unitCount: 0,
    childCount: 0,
    infantCount: 0,
    seniorCount: 0,
    youthCount: 0,
    activityCharges: 0,
    discount: 0,
    wallet: 0,
    convenienceFee: 0,
    gstRate: 0,
    gstAmount: 0,
    amountPaid: 0,
    paymentMode: '',
    cardNumber: '',
    tnc: '',
    howToRedeem: '',
    failMsg: '',
    custCareNo: '',
    scanCode: '',
    showDynamicForm: false,
    isFormEditable: false,
    product_id: 0, // add to mapping,
    rating: 0,
    comments: '',
    rateplanId: 0,
    rateInventoryId: 0,
    isFloatingBackButtonVisible: true,
    npsVisible: false,
    safetyAssured: 0,
    userPersuasions: []
  });

  _getTourTimings = () => {
    if (!this.state.startTime) {
      return '';
    }
    const startTime = getTimeString(this.state.startTime);
    const endTime = getTimeString(this.state.endTime);
    return `${startTime} to ${endTime}`;
  }
  _getTotalParticipants = () => this.state.unitCount + this.state.childCount + this.state.infantCount + this.state.seniorCount + this.state.youthCount

  _getPersonStr = () => {
    const noOfParticipants = this._getTotalParticipants() * this.state.multiplier;
    if (this.state.unitType === UNIT_TYPES.UNIT) {
      return noOfParticipants > 1 ? `${noOfParticipants} ${UNIT_TEXTS.UNIT.PLURAL}` : `1 ${UNIT_TEXTS.UNIT.SINGULAR}`;
    }
    return noOfParticipants > 1 ? `${noOfParticipants} ${UNIT_TEXTS.PERSON.PLURAL}` : `1 ${UNIT_TEXTS.PERSON.SINGULAR}`;
  };

  _getParticipantStr = () => {
    const noOfParticipants = this._getTotalParticipants() * this.state.multiplier;
    if (noOfParticipants > 1) {
      return `${this.state.primaryParticipant} + ${noOfParticipants - 1}`;
    }
    return this.state.primaryParticipant;
  };

  _keyExtractor = item => item.id;

  onShowSummary = () => {
    logActionEvent(EVENT_NAME.THANKYOU_SUMMARY_DETAILS, this);
    logPdtActionEvent(PDT_EVENT_NAME.THANK_YOU_SUMMARY_OPEN, this);
    this.setState({
      showPaymentSummary: true
    });
  };
  onCloseSummary = () => {
    logActionEvent(EVENT_NAME.THANKYOU_SUMMARY_CLOSE, this);
    logPdtActionEvent(PDT_EVENT_NAME.THANK_YOU_SUMMARY_CLOSE, this);
    this.setState({
      showPaymentSummary: false
    });
  };

  onMyTripsPressed = async () => {
    logActionEvent(EVENT_NAME.THANKYOU_MY_TRIPS, this);
    logPdtActionEvent(PDT_EVENT_NAME.MY_TRIPS_CLICK, this);
    const isLoggedIn = await isUserLoggedIn();
    if (isLoggedIn) {
      logPdtPageExitEvent(PDT_PAGE_NAME.THANK_YOU, this);
      if (USER_PLATFORM.WEB) {
        window.location.href = MY_TRIPS_URL;
      } else {
        ACMEModule.openMyTrips();
      }
    } else {
      showLongToast('Login using the entered email ID and be updated about this booking');
    }
  };

  /**
   * Function to get the Header Section for ThankYou page
   * @returns {object} JSX code for header section
   */
  _getHeaderSection = () => {
    const {pageStatus} = this.state;
    if (pageStatus === PAGE_STATUS.SUCCESS) {
      return this._getSuccessHeaderSection();
    } else if (pageStatus === PAGE_STATUS.CONFIRMING) {
      return this._getConfirmingHeaderSection();
    } else if (pageStatus === PAGE_STATUS.PENDING) {
      return this._getPendingHeaderSection();
    } else if (pageStatus === PAGE_STATUS.FAILED) {
      return this._getFailedHeaderSection();
    }
    return null;
  };

  /**
   * Function to render back button
   * @returns {object} JSX for back button
   */
  renderBackBtn = () => {
    const backIcon = Platform.OS === PLATFORM_IOS ? backIconIos : backIconAndroid;
    return (
      <TouchableOpacity onPress={() => this.backPressHandler(true)} style={styles.backBtn}>
        {this.state.isFloatingBackButtonVisible ?
          <View style={styles.btnShadow} ref={(ref) => { this.topBackBtn = ref; }}>
            <Image source={backIcon} style={styles[`backIcon${PLATFORM_STYLE_SUFFIX}`]} />
          </View> : null}
      </TouchableOpacity>
    );
  };

  /**
   * Function to get the Header section in status success
   * @returns {object} JSX for Header in success state
   */
  _getSuccessHeaderSection = () => (
    <View style={[styles.headerSectionWrapper, styles.successHeaderSectionWrapper]}>
      <LinearGradient
        colors={gradient.purple}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={[styles.successLinearGradient, CommonStyles.makeRelative]}
      >
        <Image source={thankYouIcon} style={styles.thankYouIcon} />
        <View style={styles.header}>
          <Text style={[styles.bookingConfirmText, CommonStyles.basicTextStyle]}>{this.state.isMmtPayProduct ? 'Your Payment is Confirmed!' : 'Your Booking is Confirmed!'}</Text>
          <Text style={[styles.emailPhoneText, CommonStyles.basicTextStyle]}>
            Confirmation sent to {this.state.emailId} & {this.state.phone} via email and SMS
          </Text>
          <View style={CommonStyles.flexRow}>
            <Text style={[styles.bookingIdText, CommonStyles.basicTextStyle]}>BOOKING ID: </Text>
            <Text style={[styles.bookingId, CommonStyles.basicTextStyle]}>{this.state.bookingId}</Text>
          </View>
        </View>
      </LinearGradient>
      {this.state.showQrCode ? this.renderQrCode() : null}
    </View>
  );

  /**
   * Function to render QR Code
   * @returns {object} JSX code for rendering QR Code
   */
  renderQrCode = () => (
    <View>
      <View style={styles.qrCodeOuterContainer}>
        <View style={styles.qrCodeInnerContainer}>
          <QRCode
            value={this.state.scanCode}
            size={86}
            fgColor={colors.black}
            bgColor={colors.white}
          />
        </View>
      </View>
      {/* Empty space to cover bottom white space of barcode */}
      <View style={styles.successBottomWhiteSpace} />
    </View>
  );

  /**
   * Function to get the header section JSX for confirming state
   * @returns {object} JSX code
   */
  _getConfirmingHeaderSection = () => (
    <LinearGradient
      colors={gradient.yellow}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 0}}
      style={[CommonStyles.makeRelative, styles.headerSectionWrapper]}
    >
      <View style={styles.confirmingHeaderContainer}>
        <ActivityIndicator styleAttr="Inverse" size="large" color={colors.white} />
        <Text style={[styles.bookingConfirmingText, CommonStyles.basicTextStyle]}>Checking Booking Status...</Text>
      </View>
    </LinearGradient>
  );

  /**
   * Function to get the header section for pending state
   * @returns {object} JSX code
   */
  _getPendingHeaderSection = () => (
    <LinearGradient
      colors={gradient.yellow}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 0}}
      style={[CommonStyles.makeRelative, styles.headerSectionWrapper]}
    >
      <Image source={thankYouPendingIcon} style={styles.thankYouIcon} />
      <View style={styles.header}>
        <Text style={[styles.bookingConfirmText, CommonStyles.basicTextStyle]}>Your Booking is not yet confirmed!</Text>
        <Text style={[styles.emailPhoneText, CommonStyles.basicTextStyle]}>
          Please check My Trips section after sometime or call our Customer Support
        </Text>
        <View style={CommonStyles.flexRow}>
          <Text style={[styles.bookingIdText, CommonStyles.basicTextStyle]}>BOOKING ID: </Text>
          <Text style={[styles.bookingId, CommonStyles.basicTextStyle]}>{this.state.bookingId}</Text>
        </View>
      </View>
    </LinearGradient>
  );
  /**
   * Function to get Header JSX for failed state
   * @returns {object} JSX code
   */
  _getFailedHeaderSection = () => (
    <LinearGradient
      colors={gradient.red}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 0}}
      style={[CommonStyles.makeRelative, styles.headerSectionWrapper]}
    >
      <Image source={thankYouFailedIcon} style={styles.thankYouIcon} />
      <View style={styles.header}>
        <Text style={[styles.bookingConfirmText, CommonStyles.basicTextStyle]}>We are Sorry</Text>
        <Text style={[styles.bookingFailedMsg, {opacity: 0.7}, CommonStyles.basicTextStyle]}>
          Your Booking has failed!
        </Text>
        <View style={CommonStyles.flexRow}>
          <Text style={[styles.bookingIdText, CommonStyles.basicTextStyle]}>BOOKING ID: </Text>
          <Text style={[styles.bookingId, CommonStyles.basicTextStyle]}>{this.state.bookingId}</Text>
        </View>
      </View>
    </LinearGradient>
  );

  /**
   * Function to handle scrollview scroll functionality
   * @param {object} event Layout object
   */
  handlePageScroll = (event) => {
    let newIsFloatingBackButtonVisible = true;
    if (event.nativeEvent.contentOffset.y > (this.backBtnHideOffsetTop - 35)) {
      newIsFloatingBackButtonVisible = false;
    } else {
      newIsFloatingBackButtonVisible = true;
    }

    // Update the state only if it is changed
    if (this.state.isFloatingBackButtonVisible !== newIsFloatingBackButtonVisible) {
      this.setState({isFloatingBackButtonVisible: newIsFloatingBackButtonVisible});
    }
  };


  /**
   * Function to Calculate offset top to hide back button
   * @param {object} event Layout object
   */
  setBackBtnHideOffset = (event) => {
    this.backBtnHideOffsetTop = event.nativeEvent.layout.height;
  };

  renderAdditionalDetailsSection = () => {
    if (!this.state.isAdditionalDataReq) {
      return null;
    }
    const descriptionText = this.state.isFormEditable ? DYNAMIC_FORM_THANKYOU_TEXT.EDITABLE : DYNAMIC_FORM_THANKYOU_TEXT.NON_EDITABLE;
    const btnText = this.state.isFormEditable ? 'FILL NOW' : 'VIEW DETAILS';
    const bottomContent = (
      <View style={styles.additionalDetailsSection}>
        <WhiteButton
          onPress={() => { this.setState({showDynamicForm: true}); }}
          btnText={btnText}
        />
      </View>
    );

    return (
      <View>
        <Text style={styles.additionalDetailsText}>FILL IN ADDITIONAL DETAILS</Text>
        <YellowTextView
          text={descriptionText}
          bottomContent={bottomContent}
        />
      </View>
    );
  }

  getDynamicFormJSX = () => (
    <Modal visible={this.state.showDynamicForm} onRequestClose={this.backPressHandler}>
      <DynamicForm
        activityName={this.state.activityTitle}
        forDate={this.state.tourDate}
        productId={this.state.productId}
        packageId={this.state.rateplanId}
        onSubmitSuccess={this.onDynamicFormSubmitSuccess}
        onBackPress={() => { this.setState({showDynamicForm: false}); }}
        onFormLoadError={this.onDynamicFormLoadError}
        bookingId={this.state.bookingId}
      />
    </Modal>
  )

  onDynamicFormSubmitSuccess = () => {
    this.setState({showDynamicForm: false});
  }

  onDynamicFormLoadError = () => {
    this.setState({showDynamicForm: false}, () => { showShortToast(GENERIC_ERROR_MSG); });
  }

  renderThankYouPage() {
    const tourTimings = this._getTourTimings();
    const {safetyAssured, userPersuasions, unitCount, childCount, infantCount, seniorCount, youthCount,unitTypesDetails} = this.state;
    const isSafetyAssured = isMySafetyEnabled() && !!(safetyAssured);
    return (
      <View style={CommonStyles.flex1}>
        {this.renderBackBtn()}
        <ScrollView
          style={CommonStyles.flex1}
          showsVerticalScrollIndicator={false}
          stickyHeaderIndices={[0]}
          onScroll={this.handlePageScroll}
          scrollEventThrottle={16}
        >
          <View style={styles.stickyHeader}>
            <Header
              title={this.state.activityTitle}
              isSearchVisible={false}
              pageName={PAGE_NAME.THANK_YOU}
              backHandler={() => this.backPressHandler(true)}
              numberOfLines={2}
            />
          </View>
          <View style={styles.topHeaderSection} onLayout={this.setBackBtnHideOffset}>
            {this._getHeaderSection()}
          </View>
          <View style={styles.contentContainer}>
            <View style={styles.section}>
              <Text style={styles.titleText}>{this.state.activityTitle}</Text>
              <Text style={styles.location}>{this.state.localityName}</Text>
            </View>
            {!this.state.isMmtPayProduct &&
            <View style={[styles.section, CommonStyles.flexRow, styles.flexSpaceBetween]}>
              <View>
                <Text style={styles.caption}>REPORT AT</Text>
              </View>
              <View>
                <Text style={[styles.reportAtAddress, CommonStyles.webEllipsisText]} numberOfLines={4}>{this.state.reportAt}</Text>
              </View>
            </View>}
            {this.state.tourDate && !this.state.isMmtPayProduct ?
              <View style={[styles.section, CommonStyles.flexRow, styles.flexSpaceBetween]}>
                <View>
                  <Text style={styles.caption}>DATE</Text>
                </View>
                <View style={styles.tourDateContainer}>
                  <Text style={styles.boldText}>{getShortDate(this.state.tourDate)}, </Text>
                  <Text style={styles.normalText}>{getDayText(this.state.tourDate)}</Text>
                </View>
              </View> : null}
            { tourTimings.length > 0 && !this.state.isMmtPayProduct ?
              <View style={[styles.section, CommonStyles.flexRow, styles.flexSpaceBetween]}>
                <View>
                  <Text style={styles.caption}>SLOT TIME</Text>
                </View>
                <View>
                  <Text style={styles.boldText}>{tourTimings}</Text>
                </View>
              </View> : null}
            {!this.state.isMmtPayProduct &&
            <View style={[styles.section, CommonStyles.flexRow, styles.flexSpaceBetween]}>
              <View style={{flex: 1}}>
                <Text style={styles.caption}>ADMIT</Text>
              </View>
              <View style={{flex: 1}}>
                {unitTypesDetails ?
                  <View style={{width: '100%'}}>
                    {unitCount > 0 && unitTypesDetails.unit && <Text style={[styles.boldText, styles.admitText]} numberOfLines={1}>{unitCount} {unitTypesDetails.unit.displayName}</Text>}
                    {childCount > 0 && unitTypesDetails.child && <Text style={[styles.boldText, styles.admitText]} numberOfLines={1}>{childCount} {unitTypesDetails.child.displayName}</Text>}
                    {infantCount > 0 && unitTypesDetails.infant && <Text style={[styles.boldText, styles.admitText]} numberOfLines={1}>{infantCount} {unitTypesDetails.infant.displayName}</Text>}
                    {seniorCount > 0 && unitTypesDetails.senior && <Text style={[styles.boldText, styles.admitText]} numberOfLines={1}>{seniorCount} {unitTypesDetails.senior.displayName}</Text>}
                    {youthCount > 0 && unitTypesDetails.youth && <Text style={[styles.boldText, styles.admitText]} numberOfLines={1}>{youthCount} {unitTypesDetails.youth.displayName}</Text>}
                  </View>
                : <Text style={[styles.boldText, styles.admitText]}>{this._getPersonStr()}</Text>}
                {this.state.unitType === UNIT_TYPES.ADULT ?
                  <Text style={[styles.normalText, styles.admitText]}>{this._getParticipantStr()}</Text> : null
                }
              </View>
            </View>}
            <View style={[styles.section, CommonStyles.flexRow, styles.flexSpaceBetween]}>
              <View>
                <Text style={styles.caption}>AMOUNT PAID</Text>
              </View>
              <View>
                <Text style={styles.boldText}>{this.state.txnCurSymbol} {this.state.amountPaid}</Text>
                <TouchableOpacity onPress={this.onShowSummary}>
                  <Text style={CommonStyles.anchorMd}>Summary</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          {this.renderAdditionalDetailsSection()}
          {isSafetyAssured ?
            <View style={styles.mySafetySection}>
              <MySafetyCard />
            </View> : null
          }
          {isMmtBlackEnabled() ?
            <View style={styles.mmtBlackSection}>
              <MmtBlackCard userPersuasions={userPersuasions} />
            </View> : null
          }
          <View style={styles.contentContainer}>
            {this.state.tnc ?
              <View style={[styles.section, styles.flexSpaceBetween]}>
                <Text style={styles.titleText}>Terms and Conditions</Text>
                {USER_PLATFORM.WEB ? <div dangerouslySetInnerHTML={{__html: this.state.tnc}} /> :
                <HTMLView value={this.state.tnc} />}
              </View> : null }
            {this.state.howToRedeem && !this.state.isMmtPayProduct ?
              <View style={[styles.section, styles.flexSpaceBetween]}>
                <Text style={styles.titleText}>How to Redeem</Text>
                {USER_PLATFORM.WEB ? <div dangerouslySetInnerHTML={{__html: this.state.howToRedeem}} /> :
                <HTMLView value={this.state.howToRedeem} />}
              </View> : null
            }
          </View>
        </ScrollView>
        {this.getDynamicFormJSX()}
        <Footer
          actionText="MY TRIPS"
          onActionPress={this.onMyTripsPressed}
        />
        {this.state.showPaymentSummary &&
        <PaymentSummary closeHandler={this.onCloseSummary} data={this.state} />}
      </View>
    );
  }
}

const mapStateToProps = (state, ownProps) => {
  let props = {};
  // add props from location obj (if web).
  if (USER_PLATFORM.WEB) {
    props = {...props, ...ownProps.location.state};
  }
  return props;
};


export default withRouterWrapper(connect(mapStateToProps, null)(ThankYouPage));
