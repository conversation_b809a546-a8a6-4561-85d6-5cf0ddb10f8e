import React from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import styles from '../styles/MembershipCardStyles';
// import DynamicTextWrapper from '../DynamicTextWrapper';
import { noop } from 'lodash';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { getTextStyles } from '../utils/MembershipUtils';
import HighlightedText from '@Frontend_Ui_Lib_App/HighlightedText';

const MembershipCardContent = (props) => {
  const {
    onKnowMorePress = noop,
    cta = '',
    description = [],
    descriptionBulletImageUrl = '',
    headerText = '',
  } = props || {};

  const textColor = cta?.color || holidayColors.black;

  return (
    <>
      {headerText && (
        <View style={{ marginTop: 2 }}>
          <HighlightedText
            str={headerText}
            highlightedTxtStyle={styles.messageTextHighlighted}
            normalTxtStyle={styles.messageText}
            separator="*"
            numberOfLines={3}
          />
          <Pressable onPress={cta ? () => onKnowMorePress(cta) : noop}>
            {cta && (
              <Text style={styles.knowMoreText}>{cta}</Text>
            )}
          </Pressable>
        </View>
      )}

      <View style={headerText.length > 0 ? {} : { marginTop: 0 }}>
        {description.map((item, index) => (
          <View key={index} style={styles.itemContainer}>
            {descriptionBulletImageUrl && (<Image style={styles.bullet} source={{ uri: descriptionBulletImageUrl }} />)}
            <View style={[styles.itemText, !descriptionBulletImageUrl ? { marginLeft: marginStyles.ml2 } : {}]}>
              <HighlightedText
                str={headerText}
                highlightedTxtStyle={styles.messageTextHighlighted}
                normalTxtStyle={styles.messageText}
                separator="*"
                numberOfLines={3}
              />
            </View>
          </View>
        ))}
      </View>

    </>
  );
};

export default MembershipCardContent;
