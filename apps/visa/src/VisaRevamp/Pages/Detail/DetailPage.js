import React, { useEffect, useState, useRef } from 'react';
import {
  Animated,
  StyleSheet,
  View,
  Text,
  ScrollView,
  Image,
  Dimensions,
  DeviceEventEmitter,
  TouchableOpacity,
  Share,
} from 'react-native';
import fecha from 'fecha';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import { connect } from 'react-redux';
import { colors, lightBlueGradient } from '../../Styles/colors';
import { paddingStyles, marginStyles, largeHeightSeperator } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/fonts';
import { addDays, today } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { getDateForCalendar, getVisaTypeCode, processSectionsData } from '../../Utils';
import { VISA_ROUTE_KEYS, VisaNavigation } from '../../Navigation';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { CONTENT_TYPES, EVENTS, PDT_SUB_PAGE_NAMES } from '../../constants';
import { borderRadius } from '../../Styles/borderRadius';
import { getImagePath, IMAGE_KEYS } from '../../Utils/VisaUtils';
import { visaTrackClickEvent } from '../../Tracking/utils';
import { TYPE_OF_EVENTS, NAME_OF_EVENTS } from '../../Tracking/constants';
import { logPDTEvent, visaPdtEventsInitilizer } from '../../Tracking/pdt/logger';
import { PDT_EVENT_TYPES } from '../../Tracking/pdt/constants';
import store from 'packages/legacy-commons/AppState/Store';
import { resetSearchContextAction, updateSearchContextAction, updateToandFromDateTimeAction } from '../../Actions/pdtLoggerActions';
import { generateUUID } from '@mmt/navigation/src/util';
import { ALERTS, STRING_MAP } from '../../textStrings';
/* Actions */
import { setVisaDetail } from '../../Actions/visaActions';
import { updateRecentSearchHistoryForCommon } from '../../Utils/VisaDetailRtUtils';
import { sendPixelTrackingEvent } from '../../Tracking/PixelTrackingUtils';
/* Components */
import ImportantInformation from './Components/ImportantInformation';
import SectionContainer from '../../Components/Common/SectionContainer';
import VisaInformation from './Components/VisaInformation';
import VisaProcessDetails from './Components/VisaProcessDetails';
import BottomBarWpr from '../../Components/Common/BottomBarWpr';
import FAQs from './Components/FAQs';
import ButtonWpr from '../../Components/Common/ButtonWpr';
import Calender from '../../Components/Common/Calender';
import SafetyTips from './Components/PassportSafetyTips/SafetyTips';
import HeaderWpr from '../../Components/Common/HeaderWpr';
import VisaArticles from '../../Components/Common/VisaArticles';
import MyraFloatingCard from '../../MyraChat/MyraFloatingCard';
import MainHeader from './Components/Header/MainHeader';
import StickyHeader from './Components/Header/StickyHeader';
// import MMTAssurance from '../../Components/Common/MMTAssurance';
import MMTAssuranceNew from '../../Components/Common/MMTAssurance/MMTAssurance';
import BottomSheetWpr from '../../Components/Common/BottomSheetWpr';
import AssuranceList from '../../Components/Common/MMTAssurance/AssuranceList';
import PersuationText from '../../Components/Common/PersuationText';
import MMTBlackBottomSheet from '../../Components/Common/Membership/BottomSheet';
import PurposeofTravel from '../../Components/Common/BottomSheetComponents/PurposeofTravel';
import { getIsMyraEnabled } from '../../Utils/VisaPokusUtils';
import { isEmpty } from 'lodash';

const COMPONENTS = {
  faqs: FAQs,
  information: ImportantInformation,
  visaProcess: VisaProcessDetails,
  passportInfo: SafetyTips,
  tips: VisaArticles,
};
const VisaDetailPage = ({
  visaDetailData = {},
  setVisaDetail = () => { },
  handleBackClick = () => { },
  mmtBlackData = {},
  potId = '',
  hasOriginalPotId = false,
  isDirectAccess = false,
}) => {
  const [showCalender, setShowCalender] = useState(false);
  const {
    searchId = '',
    processingTime = '',
    country = {},
    consent = {},
    visaServices = [],
    attributes = [],
    passportInfo = {},
    visaCost = {},
    exception = {},
    visaProcess = {},
    calenderDetails = {},
    chatBotEnabled = '',
    chatBotUrl = '',
    idNameInstruction = '',
    visualPersuasion = {},
    lastNameValidationDescription = {},
    convenienceFeeTitle = '',
  } = visaDetailData || {};

  const modifiedAttributes = country?.persuasionText 
  ? [country.persuasionText, ...attributes]
  : attributes;
  const { name = '', code = '', imgLink = '', backgroundLink = '', persuasionText = '' } = country || {};
  const sectionsData = processSectionsData({ data: visaDetailData });
  const { totalPrice = '', totalPriceDescription = '', totalPriceV2 = '', serviceFeePerPaxV2 = '' } = visaCost || {};

  const visaTagDetails = visaServices?.find(
    (item) => item?.visaType === visaProcess?.visaServiceName,
  );

  const [showSticky, setShowSticky] = useState(false);
  const [mmtAssurance, setMmtAssurance] = useState(false);
  const scrollY = useRef(new Animated.Value(0)).current;
  const stickyOpacity = useRef(new Animated.Value(0)).current;
  const stickyTranslateY = useRef(new Animated.Value(-100)).current;
  const [mmtBlackKnowMore, setMmtBlackKnowMore] = useState(false);

  // Purpose of Travel bottom sheet state
  const [openPOTBottomsheet, setOpenPOTBottomsheet] = useState(false);
  const [selectDestDetails, setSelectedDestDetails] = useState(null);

  const handleOpenMmtAssuranceBottomSheet = (param) => {
    setMmtAssurance(true);
  };
  const handleCloseMmtAssuranceBottomSheet = () => {
    setMmtAssurance(false);
  };

  const handleOpenMmtBlackKnowMoreBottomSheet = (param) => {
    setMmtBlackKnowMore(true);
  };
  const handleCloseMmtBlackKnowMoreBottomSheet = () => {
    setMmtBlackKnowMore(false);
  };

  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
    useNativeDriver: false,
    listener: (event) => {
      const yOffset = event.nativeEvent.contentOffset.y;

      if (yOffset > 55 && !showSticky) {
        setShowSticky(true);
        Animated.parallel([
          Animated.timing(stickyOpacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(stickyTranslateY, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      } else if (yOffset <= 55 && showSticky) {
        Animated.parallel([
          Animated.timing(stickyOpacity, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(stickyTranslateY, {
            toValue: -100,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start(() => setShowSticky(false));
      }
    },
  });

  useEffect(() => {
    let onLoginEventReceivedListener = DeviceEventEmitter.addListener(
      EVENTS.DETAIL.LOGIN_EVENT_LANDING,
      onLoginEventReceived,
    );
    visaTrackClickEvent({
      eventType: TYPE_OF_EVENTS.PAGE_LOAD,
    });

    const totalPrice = parseInt(visaCost.totalPriceV2.replace(/[^\d]/g, ""), 10);
    const serviceFee = parseInt(visaCost.serviceFeePerPaxV2.replace(/[^\d]/g, ""), 10);
    const paxPriceIncludingServiceFee = totalPrice + serviceFee;

    sendPixelTrackingEvent({
      countryName: country?.name,
      paxPrice: `₹ ${paxPriceIncludingServiceFee}`
    });
    store.dispatch(updateSearchContextAction({
      visa_type: getVisaTypeCode({ visaTypeName: visaProcess?.visaServiceName }),
      search_type: "country",
      journey_type: potId,
    }));
    updateRecentSearchHistoryForCommon({
      countryCode: country?.code,
      deeplink:`https://visa.makemytrip.com/detail/v2?searchId=${searchId}&potId=${potId}&countryCode=${country?.code}&countryName=${encodeURIComponent(country?.name)}`,

    });
    visaPdtEventsInitilizer();

    return () => {
      store.dispatch(resetSearchContextAction());
    }
  }, []);

  // Separate useEffect to handle POT bottom sheet for direct access
  useEffect(() => {
    
    // Only show POT bottom sheet if:
    // 1. User is coming directly AND
    // 2. No original potId was provided in URL AND
    // 3. Country requires POT selection
    if (isDirectAccess && !hasOriginalPotId && visaDetailData && country?.code) {
      // Check if this country requires POT selection
      const isSchengenCountry = visaDetailData.isSchengen === 1;
      const hasPOTDetails = visaDetailData.purposeOfTravelDetails && 
                           visaDetailData.purposeOfTravelDetails.description && 
                           visaDetailData.purposeOfTravelDetails.description.length > 1;
            
      if (isSchengenCountry || hasPOTDetails) {
        // Prepare destination details for POT bottom sheet using the actual data structure
        const destDetails = {
          code: country.code,
          name: country.name,
          countryCode: country.code,
          countryName: country.name,
          imgLink: country.imgLink,
          countryImgLink: country.imgLink,
          isSchengen: isSchengenCountry,
          purposeOfTravelDetails: visaDetailData.purposeOfTravelDetails
        };
        
        setSelectedDestDetails(destDetails);
        // Small delay to ensure component is fully rendered before showing bottom sheet
        setTimeout(() => {
          setOpenPOTBottomsheet(true);
        }, 300);
      }
    } else {
      console.log('POT Bottom Sheet NOT shown - conditions not met');
    }
  }, [visaDetailData, country, isDirectAccess, hasOriginalPotId]);

  const onLoginEventReceived = (resp) => {
    const { loggedIn } = resp || {};
    if (loggedIn) {
      setShowCalender(true);
    } else {
      showShortToast(ALERTS.LOGIN_MANDATORY);
    }
  };

  const handleConfirmClickTracking = () => {
    visaTrackClickEvent({
      eventName: 'getstarted_detail',
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    logPDTEvent({
      eventValue: 'click_getstarted',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
  };
  const renderBookingButton = () => {
    const handleBooking = async () => {
      handleConfirmClickTracking();
      // const isLoggedIn = await isUserLoggedIn();
      // if (!isLoggedIn) {
      //   VisaModule.loginUser({ page: 'visaLandingNew' });
      // } else {
        setShowCalender(true);
      // }
    };

    return (
      <ButtonWpr
        buttonText={'GET STARTED'}
        buttonSize={{ fontSize: 14 }}
        onButtonPress={handleBooking}
      />
    );
  };

  const handleCalendarConfirmTracking = (dateOfEntry, dateOfExit) => {
    const entryDate = fecha.format(new Date(dateOfEntry), 'YYYY-MM-DD');
    const exitDate = fecha.format(new Date(dateOfExit), 'YYYY-MM-DD');
    const bookingDate = fecha.format(new Date(), 'YYYY-MM-DD');
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.DONE_CALENDAR,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
      params: {
        dateOfEntry: entryDate,
        dateOfExit: exitDate,
      },
    });
    const timeDifference = new Date(entryDate) - new Date(bookingDate);
    const advancePurchaseDays = Math.ceil(timeDifference / (1000 * 60 * 60 * 24)); // Convert milliseconds to days
    store.dispatch(updateToandFromDateTimeAction({
      fromDateTime: new Date(entryDate)?.getTime(),
      toDateTime: new Date(exitDate)?.getTime(),
      advance_purchase: advancePurchaseDays?.toString(),
    }));
    logPDTEvent({
      eventValue: 'click_done',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
  };
  const onCalendarDone = (dateOfEntry, dateOfExit) => {
    setVisaDetail(dateOfEntry, dateOfExit, name);
    const dateEntry = fecha.format(new Date(dateOfEntry), 'DD MMM');
    const dateExit = fecha.format(new Date(dateOfExit), 'DD MMM');
    handleCalendarConfirmTracking(dateOfEntry, dateOfExit);
    setShowCalender(!showCalender);
    VisaNavigation.push(VISA_ROUTE_KEYS.ADD_TRAVELLER, {
      country: country,
      journeyDate: `${dateEntry} - ${dateExit}`,
      exception,
      searchId,
      potId: selectedPotId || potId, // Use selected POT ID from bottom sheet or effective potId from container
      visaCost,
      idNameInstruction,
      lastNameValidationDescription,
    });
  };

  const onCalendarBack = () => {
    visaTrackClickEvent({
      eventName: NAME_OF_EVENTS.CLOSE_CALENDAR,
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    setShowCalender(!showCalender);
  };

  const handleShareIcon = () => {
    visaTrackClickEvent({
      eventName: 'detail_share',
      eventType: TYPE_OF_EVENTS.BUTTON_CLICK,
    });
    const url = `https://visa.makemytrip.com/detail/v2?searchId=${searchId}&potId=${potId}&countryCode=${country?.code}&countryName=${encodeURIComponent(country?.name)}`;
    Share.share({
      message: `${STRING_MAP.DETAIL_PAGE_SHARE}${url}`,
    });
  };

  const renderDetailSections = ({ section, index }) => {
    const Component = COMPONENTS[section.name];
    if (Component === undefined) return null;
    const hideHeader = section.name === 'passportInfo';
    const hideBorder = section.name === 'tips';
    return (
      <View key={`Section-${index}`}>
        <SectionContainer
          hideBorder={hideBorder}
          hideHeader={hideHeader}
          title={section.header}
          titleStyles={{
            ...fontStyles.labelLargeBold,
          }}
          showTag={section.name === 'visaProcess'}
          tagDetails={visaTagDetails}
          tagType={visaProcess?.visaServiceName}
          index={index}
          countryName={country?.name}
          bgColors={
            section.name === 'passportInfo' || section.name === 'tips' ? lightBlueGradient : null
          }
          subTitle={section?.data?.subTitle || ''}
          component={<Component details={section} country={country} />}
        />
      </View>
    );
  };

  const handleCloseBottomSheet = () => {
    visaTrackClickEvent({
      eventName: 'close_purposeoftravel',
      eventType: TYPE_OF_EVENTS.POPUP_BUTTON_CLICK,
    });
    logPDTEvent({
      eventValue: 'close_POT',
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
    setOpenPOTBottomsheet(false);
  };

  // State to store selected POT ID
  const [selectedPotId, setSelectedPotId] = useState(potId);

  const openDestDetailPage = ({ potId: selectedPotId = '', countryCode = '' } = {}) => {
    
    // Close the POT bottom sheet - user has made their selection
    setOpenPOTBottomsheet(false);
    
    // Store the selected POT ID for use in the calendar flow
    if (selectedPotId) {
      setSelectedPotId(selectedPotId);
    }
    
    // Show calendar immediately after POT selection
    setShowCalender(true);
  };

  return (
    <View style={styles.visaDetailsContainer}>
      {showSticky && (
        <StickyHeader
          handleBackClick={handleBackClick}
          handleShareIcon={handleShareIcon}
          stickyOpacity={stickyOpacity}
          stickyTranslateY={stickyTranslateY}
          name={name}
        />
      )}
      <ScrollView
        vertical={true}
        style={styles.wrapperHeight}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        <MainHeader
          attributes={modifiedAttributes}
          handleBackClick={handleBackClick}
          handleShareIcon={handleShareIcon}
          name={name}
          processingTime={processingTime}
          imgLink={imgLink}
          backgroundLink={backgroundLink}
          mmtBlackData={mmtBlackData}
          mmtBlackDatahandleClick={handleOpenMmtBlackKnowMoreBottomSheet}
        />
        {/* {visualPersuasion && visualPersuasion?.header && <MMTAssurance visualPersuasion={visualPersuasion} handleClick={handleOpenMmtAssuranceBottomSheet} contentType={CONTENT_TYPES.DETAIL} />} */}
        <View style={styles.mmtAssuranceWrapper}>
          {visualPersuasion && visualPersuasion?.header && <MMTAssuranceNew visualPersuasion={visualPersuasion} handleClick={handleOpenMmtAssuranceBottomSheet} />}
        </View>

        {sectionsData?.map((section, index) => renderDetailSections({ section, index }))}

        <VisaInformation infoDetails={consent} />
        {/* {country?.persuasionText && <PersuationText text={country?.persuasionText} />} */}
      </ScrollView>
      {!showCalender && getIsMyraEnabled() && (
        <MyraFloatingCard footerHeight={50} pageName="Detail" chatBotUrl={chatBotUrl} region={country?.name} tripType={potId}
         />
      )}
      <View style={styles.bottomBarWrapper}>
      {!isEmpty(convenienceFeeTitle) && <PersuationText text={convenienceFeeTitle} customTextStyle={{textAlign: 'left',color: colors.gray,...fontStyles.labelSmallRegular}} customContainerStyle={{...paddingStyles.pv8, ...paddingStyles.ph12}} />}
        <BottomBarWpr
          title={totalPriceV2}
          description={totalPriceDescription}
          description2={serviceFeePerPaxV2}
          rightComponent={renderBookingButton}
        />
      </View>
      {showCalender && (
        <View style={styles.calendarContainer}>
          <Calender
            calenderDetails={calenderDetails}
            onDone={onCalendarDone}
            onCalendarBack={onCalendarBack}
          />
        </View>
      )}
      {mmtAssurance && (
        <BottomSheetWpr
          isCrossIcon
          title={'MMT Assurance'}
          visible={mmtAssurance}
          setVisible={setMmtAssurance}
          onDismiss={handleCloseMmtAssuranceBottomSheet}
          children={<AssuranceList details={visualPersuasion?.otherDetails} showTitlesOnly={false} isModel={true} />}
        />
      )}
      {mmtBlackKnowMore && (
        <BottomSheetWpr
          visible={mmtBlackKnowMore}
          setVisible={setMmtBlackKnowMore}
          onDismiss={handleCloseMmtBlackKnowMoreBottomSheet}
          containerStyles={{
            padding: 0,
            margin: 0,
          }}
          children={<MMTBlackBottomSheet mmtBlackData={mmtBlackData} togglePopup={handleCloseMmtBlackKnowMoreBottomSheet}></MMTBlackBottomSheet>}
        >
        </BottomSheetWpr>
      )}
      {openPOTBottomsheet && (
        <BottomSheetWpr
          visible={openPOTBottomsheet}
          setVisible={setOpenPOTBottomsheet}
          onDismiss={handleCloseBottomSheet}
          isCrossIcon={true}
          bottomsheetName={PDT_SUB_PAGE_NAMES.POT_POPUP}
          topIcon={{ uri: selectDestDetails?.countryImgLink || selectDestDetails?.imgLink }}
          children={
            <PurposeofTravel
              destDetails={selectDestDetails}
              openDestDetailPage={openDestDetailPage}
              schengenMultiCountryInfo={visaDetailData?.schengenMultiCountryInfo}
              onDismiss={handleCloseBottomSheet}
              searchId={searchId}
            />
          }
        ></BottomSheetWpr>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  visaDetailsContainer: {
    backgroundColor: colors.lightGray2,
    flex: 1,
  },
  wrapperHeight: {
    // marginBottom: 58,
    flex: 1,
  },
  bottomBarWrapper: {
    width: '100%',
    zIndex: 0,
    marginTop: 'auto',
  },
  calendarContainer: {
    position: 'absolute',
    top: 0,
    height: '100%',
    width: '100%',
    zIndex: 2,
    backgroundColor: colors.white,
  },
  mmtAssuranceWrapper: {
    backgroundColor: colors.white,
    ...marginStyles.mb16,
    ...paddingStyles.ph16,
  },
});

const mapDispatchToProps = {
  setVisaDetail,
};

export default connect(null, mapDispatchToProps)(VisaDetailPage);
