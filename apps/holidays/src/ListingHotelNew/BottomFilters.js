import React from 'react';
import { connect } from 'react-redux';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import rupeeIcon from './images/rupeeIcon.webp';
import starIcon from './images/starIcon.webp';
import locationIcon from './images/locationIcon.webp';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../Navigation';
import { isEmpty } from 'lodash';
import { holidayColors } from '@mmt/holidays/src/Styles/holidayColors';
class BottomFilters extends BasePage {
  componentDidMount() {
    super.componentDidMount();
  }
  openWholeFilterSection = () => {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_FILTER_LIST, {
      isOpenedDirectly: true,
      filterData: this.props.filterData,
      days: this.props.days,
    });
  };

  openPriceFilterSection = () => {
    const { priceList } = this.props.filterData;
    const fullPriceRange = {
      min: priceList[0],
      max: priceList[priceList.length - 1],
    };
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_PRICE_RANGE, {
      isOpenedDirectly: true,
      fullPriceRange,
      appliedPriceRange: this.props.appliedFilterData.appliedPriceRange,
      days: this.props.days,
    });
  };

  openPopularityFilterSection = () => {
    const { popularList } = this.props.filterData;
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_POPULAR_SECTION, {
      isOpenedDirectly: true,
      appliedPopularList: this.props.appliedFilterData.appliedPopularList,
      popularList,
    });
  };

  openPropertyFilterSection = () => {
    const { propertyTypeList } = this.props.filterData;
    const { starRatingList } = this.props.filterData;
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_PROPERTY_TYPE, {
      appliedPropertyTypeList: this.props.appliedFilterData.appliedPropertyTypeList,
      appliedStarRatingList: this.props.appliedFilterData.appliedStarRatingList,
      propertyTypeList,
      starRatingList,
    });
  };
  render() {
    const { popularList } = this.props.filterData;
    return (
      <View style={styles.bottomFiltersWrapper}>
        <TouchableOpacity
          style={[AtomicCss.flex1, AtomicCss.alignCenter, AtomicCss.justifyCenter]}
          onPress={this.openPriceFilterSection}
        >
          <Image style={styles.rupeeIcon} source={rupeeIcon} />
          <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.blackText]}>Price</Text>
        </TouchableOpacity>
        {!isEmpty(popularList) && (
          <TouchableOpacity
            style={[AtomicCss.flex1, AtomicCss.alignCenter, AtomicCss.justifyCenter]}
            onPress={this.openPopularityFilterSection}
          >
            <Image style={styles.starIcon} source={starIcon} />
            <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.blackText]}>
              Popularity
            </Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[AtomicCss.flex1, AtomicCss.alignCenter, AtomicCss.justifyCenter]}
          onPress={this.openPropertyFilterSection}
        >
          <Image style={styles.locationIcon} source={locationIcon} />
          <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.blackText]}>
            Property
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[AtomicCss.flex1, AtomicCss.alignCenter, AtomicCss.justifyCenter]}
          onPress={this.openWholeFilterSection}
        >
          <Image style={styles.rupeeIcon} source={rupeeIcon} />
          <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.blackText]}>
            Sort &amp; Filter
          </Text>
        </TouchableOpacity>
      </View>
    );
  }
}
const styles = StyleSheet.create({
  rupeeIcon: { width: 23, height: 23, marginBottom: 5 },
  starIcon: { width: 23, height: 23, marginBottom: 5 },
  locationIcon: { width: 17, height: 22, marginBottom: 5 },
  bottomFiltersWrapper: {
    position: 'absolute',
    bottom: 10,
    backgroundColor: holidayColors.white,
    paddingVertical: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 14,
    borderRadius: 8,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 2,
  },
});

const mapStateToProps = (state) => {
  const { appliedFilterData } = state.holidayHotelListingReducer;
  return { appliedFilterData };
};

export default connect(mapStateToProps)(BottomFilters);
