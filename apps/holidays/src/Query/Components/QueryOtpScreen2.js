import React, { useEffect, useState } from 'react';
import { Text, View, StyleSheet, TouchableOpacity, ScrollView, TextInput, Image, Keyboard } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconBackArrow from '@mmt/legacy-assets/src/img/iconBack.webp';
import LinearGradient from 'react-native-linear-gradient';
import iconClose from '@mmt/legacy-assets/src/ic_clear_black.webp';

const QueryOtpScreen = (props) => {

    const { onSubmitClick,
        error,
        resend,
        onOtpClose,
        onBack } = props;
    const [seconds, setSeconds] = useState(30);
    const [otp, setOTP] = useState('');
    const initiateTimer = () => {
        if (seconds > 0) {
            setTimeout(() => setSeconds(seconds - 1), 1000);
        } else {
            clearTimeout(initiateTimer);
        }


    };

    useEffect(() => {
        initiateTimer();
    });
    const onSubmit = () => {
        onSubmitClick(otp);
    };
    const onClose = () => {
        onOtpClose();
    };
    const onChangeText = (otp) => {
        setOTP(otp);
    };
    const getBorderColor = () => {
        if (error) {
            return '#eb2026';
        } else {
            return 'rgb(128, 197, 255)';
        }
    };
    const resendOtp = ()=>{
        setSeconds(30);
        resend();
    };
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <View>
                    <TouchableOpacity onPress={onBack}>
                        <Image source={iconBackArrow} style={styles.iconBackArrow} />
                    </TouchableOpacity>
                </View>
                <View style={styles.iconCloseWrap}>
                    <TouchableOpacity onPress={onClose}>
                        <Image source={iconClose} style={styles.iconClose} />
                    </TouchableOpacity>
                </View>
            </View>
            <ScrollView>
                <View style={[styles.scrollWrap]}>
                    <View><Text style={[AtomicCss.font20, AtomicCss.blackText, AtomicCss.boldFont]}>Verify your Mobile Number</Text></View>
                    <View style={AtomicCss.marginTop10}><Text style={[AtomicCss.font14, AtomicCss.defaultText, AtomicCss.regularFont, AtomicCss.lineHeight20]}>Please enter the One time password to verify your number for a quick response.</Text></View>
                    <View style={AtomicCss.marginTop15}>
                        <View style={styles.inputWrapper}>
                            <Text style={AtomicCss.boldFont}>Enter OTP<Text style={styles.mandatory}>*</Text></Text>
                            <TextInput
                                onChangeText={onChangeText}
                                style={[styles.input,{borderColor:getBorderColor()}]}
                                value={otp}
                                keyboardType={'numeric'}
                                editable={true}
                                onSubmitEditing={() => { Keyboard.dismiss(); }} />
                        </View>


                        {/* {isVerfied ? (<Text style={styles.successText}>Success! Number verified.</Text>) : null} */}
                    </View>
                     {/* <View style={AtomicCss.marginTop10}><Text style={[AtomicCss.font14, AtomicCss.defaultText]}>Auto fetching OTP sent via SMS</Text></View> */}
                    <TouchableOpacity onPress={resendOtp} disabled={seconds > 0}>
                    <View style={AtomicCss.marginTop15} ><Text style={[AtomicCss.font14, seconds > 0 ? AtomicCss.greyText : AtomicCss.blackText, {textDecorationLine: 'underline'}]}>Resend OTP {seconds > 0 && `(${seconds}s)`}</Text></View>
                    </TouchableOpacity>

                </View>
            </ScrollView>
            <View style={styles.formFooter}>
                <TouchableOpacity onPress={onSubmit}>
                    <LinearGradient colors={['#53b2fe', '#065af3']} style={styles.callbackBtn}>
                        <View><Text style={styles.callbackText}>VERIFY</Text></View>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        flex: 1,
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        zIndex: 5,
        paddingTop:15,

    },
    scrollWrap: {
        paddingVertical: 20,
        paddingHorizontal: 15,
    },
    header: {
        paddingVertical: 20,
        paddingHorizontal: 15,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    iconBackArrow: {
        width: 16,
        height: 16,
        marginRight: 15,
    },
    callbackBtn: {
        padding: 13,
        width: '100%',
        borderRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },

    callbackText: {
        fontSize: 16,
        fontFamily: 'Lato-Black',
        color: 'white',
    },
    formFooter: {
        padding: 15,
    },
    successText: {
        fontSize: 12,
        fontFamily: 'Lato-Bold',
        color: '#1a7971',
        paddingTop: 10,
    },
    input: {
        borderRadius: 4,
        flex: 1,
        width: '100%',
        paddingHorizontal: 10,
        backgroundColor: '#fff',
        color: '#4a4a4a',
        paddingVertical: 5,
        fontFamily: 'Lato-Bold',
        fontSize: 14,
        borderWidth: 1,
    },
    mandatory: {
        color: '#eb2026',
    },
     iconCloseWrap: {
        backgroundColor: 'white',
        // paddingVertical: 20,
        // paddingHorizontal: 20,
        alignItems: 'flex-end',
    },
    iconClose: {
        width: 25,
        height: 25,

    },

});
export default QueryOtpScreen;
