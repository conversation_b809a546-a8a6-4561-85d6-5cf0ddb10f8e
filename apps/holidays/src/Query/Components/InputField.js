import React from 'react';
import {Text,View,StyleSheet,TextInput} from 'react-native';

const InputField = (props) => {

    const {
        error,
        label,
        placeholder,
        onChangeText,
        value,
        icon,
        iconPosition,
        keyboardType,
        isMandatory,
        editable,
        defaultValue,
        maxLength,
    } = props;
    const getFlexDirection = () => {
        if (icon && iconPosition) {
          if (iconPosition === 'left') {
            return 'row';
          } else if (iconPosition === 'right') {
            return 'row-reverse';
          }
        }
    };
    const getBorderColor = () => {
        if (error) {
          return '#eb2026';
        } else {
          return 'rgb(128, 197, 255)';
        }
      };
    return (
        <View>
            {!!label && <Text style={styles.label}>{label} {isMandatory && <Text style={styles.mandatory}>*</Text>}</Text>}
            <View style={[styles.inputWrapper, {
                flexDirection: getFlexDirection(),
                borderColor: getBorderColor()}]}
            >
                <View >{icon && icon}</View>
                <TextInput
                    style = {{...styles.input, color: editable ? '#4a4a4a' : '#9B9B9B'}}
                    placeholder={placeholder}
                    onChangeText={onChangeText}
                    value = {value}
                    keyboardType = {keyboardType}
                    defaultValue = {defaultValue}
                    editable={editable}
                    {...props}
                    maxLength={maxLength}
                />
            </View>
            {error ? (<View style={styles.errorWrap}><Text style={styles.errorText}>{'This field is required'}</Text></View>) : []}
        </View>

    );
};

const styles = StyleSheet.create({
    inputWrapper: {
        borderWidth: 1,
        alignItems: 'right',
        justifyContent:'center',
        borderRadius: 4,
        paddingVertical: 2,
        paddingHorizontal: 15,
        height:42,
    },
    labelWrapper: {
        flexDirection: 'row',
    },
    label: {
        fontSize:14,
        fontFamily:'Lato-Bold',
        color:'#4a4a4a',
        marginBottom:6,

    },
    input:{
        borderRadius: 4,
        flex: 1,
        width: '100%',
        paddingHorizontal: 10,
        backgroundColor: '#fff',
        color: '#4a4a4a',
        paddingVertical: 5,
        fontFamily: 'Lato-Bold',
        fontSize: 14,
    },
    mandatory: {
        color: '#eb2026',

    },
    errorWrap: {
        marginTop: 5,
    },
    errorText: {
        color: '#eb2026',
        fontSize: 12,
    },
    errorTxtbox: {
        borderColor: '#eb2026',
    },

});

export default InputField;
