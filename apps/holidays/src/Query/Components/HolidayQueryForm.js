import React from 'react';
import {
  View, BackHandler, Platform,
} from 'react-native';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import QueryFormStatic from './QueryFormStatic';
import { initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { HolidayNavigation } from '../../Navigation';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import {isIosClient} from "../../utils/HolidayUtils";

export default class HolidayQueryForm extends BasePage {
  constructor(props) {
    super(props);
    this.state = {
      loaded: false,
      showDynamicForm: true,
    };
  }

  componentDidMount() {
    this.initAb();
  }

  async initAb() {
    await initAbConfig();
    this.setState({
      loaded: true,
    });
  }

  handleBackPress = () => {
    const somethingPoped = HolidayNavigation.canGoBack();
    if (somethingPoped) {
      HolidayNavigation.pop();
  } else  {
      if (isIosClient()) {
        ViewControllerModule.popViewController(getRootTag());
      } else {
        BackHandler.exitApp();
      }
    }
  };

  onDynamicFormError = () => {
    this.setState({
      loaded: true,
      showDynamicForm: false,
    });
  };

  render() {
    if (!this.state.loaded) {
      return (<View />);
    }
    let QueryFormDynamic = require('./QueryFormDynamic').default;
    return (
      this.state.showDynamicForm ?
        (<QueryFormDynamic
          {...this.props}
          onBackPressed={this.handleBackPress}
          onDynamicFormError={this.onDynamicFormError}
        />) : (<QueryFormStatic
          {...this.props}
          onBackPressed={this.handleBackPress}
        />)
    );
  }
}
