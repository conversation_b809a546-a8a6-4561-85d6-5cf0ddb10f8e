/* eslint-disable global-require */
import React from 'react';
import PropTypes from 'prop-types';
import {
  View,
  StyleSheet,
  TextInput,
  Text,
  StatusBar,
  Image,
  BackHandler,
  TouchableOpacity,
  Platform,
  NativeModules,
  Pressable,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';
import Dropdown from '@Frontend_Ui_Lib_App/Dropdown';
import { addDays, now } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import editIcon from '@mmt/legacy-assets/src/ic_edit_stroke.webp';
import crossIcon from '@mmt/legacy-assets/src/cross_tg.webp';

import {
  createLoggingMap, getApiDate, getApiDateDynamic,
  getParamFromQuery,
  getQueryFormDataV2,
  submitDynamicQuery,
  submitQueryUpdate,
  verifyOtp,
  resendOtp,
  getQueryFormDataByTicketIdV2,
  getPackageCode,
} from '../Utils/HolidayQueryUtils';
import FilterLoader from '../../SearchWidget/Components/FilterLoader';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import {
  containsStringIgnoreCase,
  createRandomString,
  getDepartureCity,
  isEmptyString,
  isNotNullAndEmptyCollection,
  isNullOrEmptyCollection,
  isRawClient,
  isGIAffiliate,
  getDimensionsForQueryFormAd, isAndroidClient, isIosClient,
  updatedValuesforvariable_m_v83,
  isHolidaysPage,
} from '../../utils/HolidayUtils';
import { getEvar108ForFunnel } from '../../utils/HolidayTrackingUtils';
import {
  DOM_BRANCH,
  PAGE_NAME_QUERY,
  PDT_BACK_CLICK,
  PDT_CHANGE_CLICK,
  PDT_CHOOSE_DATE,
  PDT_CHOOSE_DESTINATION,
  PDT_DONE_CLICK,
  PDT_OTP_SUBMIT_CLICK,
  PDT_PAGE_EXIT_EVENT,
  PDT_PAGE_LOAD,
  PDT_PAGE_VIEW,
  PDT_RAW_EVENT,
  PDT_SUBMIT_CLICK,
  SEO_MOBILE_QUERY_ID,
  USER_AGREEMENT_URL,
  PRIVACY_POLICY_URL,
  MONTH_INTEGER_MAP,
  ERROR_TEXT,
  STEP_COUNT,
  AD,
  SUB_PAGE_NAMES,
  QUERY_DETAIL_CONSTANTS,
} from '../../HolidayConstants';
import QueryDestinationSelector from './QueryDestinationSelector';
import {
  DEEP_LINK_FORM_PARAM,
  EMPTY_ERROR_OTP,
  ERROR_API,
  ERROR_OTP,
  FIELD_DATE,
  FIELD_TYPE_DATE,
  FIELD_DESTINATION,
  FIELD_EMAIL,
  FIELD_NAME,
  FIELD_FIRSTNAME,
  FIELD_EMAILID,
  FIELD_NUMBER,
  FIELD_TYPE_RADIO,
  FIELD_TYPE_SINGLE_SELECT,
  FIELD_TYPE_STAR,
  FIELD_TYPE_TEXT,
  FIELD_TYPE_COUNTRY_CODE,
  FIELD_MONTH_DATE,
  FIELD_SEARCH_DATE,
  FIELD_TYPE_INLINE_BOX,
  FIELD_TYPE_SLIDER,
  MSG_ALLOW_GI_CONTACT,
  MSG_QUERY_SUBMITTED,
  PHONE_PLACEHOLDER,
  FIELD_TYPE_SINGLE_INLINE_BOX,
  EVENT_THANKYOU_CLICK,
  EVENT_THANKYOU_LOAD,
  EVENT_QUERY_CLOSE,
  EVENT_QUERY_SUBMIT,
  EVENT_QUERY_NEXT,
  EVENT_QUERY_EDIT,
  ERROR_AUTHORIZE,
  FIELD_DEPARTURE,
} from '../QueryConstants';
import HolidayCalendar from '../../Calender/MmtHolidayCalender';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  getUserDetails,
  isUserLoggedIn,
} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import RadioOptions from './RadioOptions';
import QueryPriceFilter from './QueryPriceFilter';
import Separator from '@mmt/legacy-commons/Common/Components/Separator';
import QueryOtpScreen from './QueryOtpScreen2';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { getPokusExpVarientKey } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import MonthCalendar from './MonthCalendar';
import calendar_icon from '@mmt/legacy-assets/src/holidays/calendar.webp';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import QueryFormHeader from './QueryFormHeader';
import QueryFormHeaderV2 from './QueryFormHeaderV2';
import LinearGradient from 'react-native-linear-gradient';
import ThankYouPage from './ThankyouPage';
import InlineBoxField from './InlineBoxField';
import CountryList from './CountryList';
import { HolidayNavigation, HOLIDAY_ROUTE_KEYS } from '../../Navigation';
import isEmpty from 'lodash/isEmpty';
import { trackHolidayQueryClickEvent, trackHolidayQueryLoadEvent, trackQueryPDTClickEvents } from '../Utils/HolidayQueryTrackingUtils';
import { setFirebaseTrackingForQuery } from '../../utils/FirebaseUtils/FirebaseTracking';
import BranchIOTracker from '../../utils/HolidayBranchSDKEventTracker';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import {
  getHolQueryFormDefaultTravelDaysCount,
  getT2QFabFlags,
} from '../../utils/HolidaysPokusUtils';
import HolidayAdCard from '../../Common/Components/HolidayAdCard';
import { adQuery } from '../../LandingNew/Components/phoenixCss';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { ATTRIBUTE_IDENTIFIER, DEFAULT_QUERY_HEADER } from '../QueryConstants';

const Callback_cta_step = 0;
const CONTINUE = 'CONTINUE';
const SUBMIT = 'SUBMIT';
const DONE = 'DONE';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import Header from '@Frontend_Ui_Lib_App/Header';
import { sendMMTGtmEvent } from '../../utils/ThirdPartyUtils';
import { getFabsIconUrls } from '../../utils/CtaUtils';
import withBackHandler from '../../hooks/withBackHandler';
import { holidayNavigationPush } from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';
const getCompletionFeedBackText = (stepDetails, state) => {
  const regex = /@([a-zA-Z]+)/g;
  return stepDetails[0].completionFeedback.replace(regex, (match, key) =>
    state[key] !== undefined ? state[key] : match,
  );
};

 class QueryFormDynamic extends React.Component {
  constructor(props) {
    super(props);
    this.fromDeepLink = props.DL;
    this.destinations = [];
    this.tagDestination = props.destination;
    this.packageId = props.packageId;
    this.packageName = props.packageName;
    this.searchDate = props.searchDate;
    this.pageName = props.pageName ? props.pageName : props?.pageSection ?? 'listing';
    this.funnelStep = props.funnelStep;
    this.isPremiumPackage = props?.isPremiumPackage || false;
    this.branch = props.branch ? props.branch : DOM_BRANCH;
    this.defaultBranch = this.branch;
    this.getUserDepartureCity();
    this.branchMap = new Map();
    this.cmp = '';
    this.nodl = true;
    this.formId = props.formId;
    this.ticketId = props.ticketId;
    this.aff = props.aff;
    this.trackPDTV3Event = isRawClient() ? trackQueryPDTClickEvents : this?.props?.trackPDTV3Event;
    this.ad = [];
    if (props.cmp) {
      this.cmp = props.cmp;
    } else if (props.campaign) {
      this.cmp = props.campaign;
    }
    if (props.banner) {
      HolidayDataHolder.getInstance().setBanner(props.banner);
    }
    this.budgets = [];
    if (this.fromDeepLink) {
      this.queryParams = props.query;
      this.destinations = props.dest ? props.dest.split(',') : [];
      this.fromSeo = props.fromSeo;
      this.tagDestination = props.dest;
      this.budgets = [this.destinations.length];
      let split = [];
      for (let i = 0; i < this.destinations.length; i++) {
        split = this.destinations[i].split('_');
        this.destinations[i] = split[0];
        this.budgets[i] = split.length > 1 ? split[1] : '';
      }
      this.nodl = false;
      if (this.queryParams.includes('nodl=true')) {
        this.nodl = true;
      }
      if (this.queryParams.includes('banner')) {
        HolidayDataHolder.getInstance().setBanner(getParamFromQuery(this.queryParams, 'banner'));
      }
    }
    HolidayDataHolder.getInstance().setCmp(this.cmp);
    this.cmp = HolidayDataHolder.getInstance().getCmp();
    let travelDate = props.travelDate ? props.travelDate : this.getDefaultTravelDate();
    if (props.travelDateMillis) {
      travelDate = new Date(parseInt(props.travelDateMillis, 10));
    }
    this.state = {
      destination:
        this.fromDeepLink && this.destinations.length > 0
          ? this.destinations[0]
          : props.destination,
      budget: this.budgets.length > 0 ? this.budgets[0] : '',
      cityList: false,
      userName: '',
      firstName: '',
      email: '',
      travelDate,
      searchDate: this.searchDate,
      countryCode: '+91',
      countryCodeSelector: false,
      countryCodeOptions: [],
      countryCodeList: [],
      phoneNumber: '',
      imageUrl: '',
      showUpdateForm: false,
      isLoading: true,
      itemError: false,
      hideChange: this.fromDeepLink && this.destinations.length === 1 && !this.nodl,
      allowContact: true,
      showCalendar: false,
      showMonthCalendar: false,
      isSuccess: false,
      showOtpFlow: false,
      travelDateFixed: '',
      dependentFields: [],
      step: 0,
      showWelcomePage: false,
      otpError: false,
      thankYouPageData: {},
      hotelStarV2: [],
      budgetPrefRange: {},
      budgetRange: {},
      itemName: '',
      filledField: [],
    };
    this.mainComps = [];
    this.secComps = [];
    this.mainFields = [];
    this.sectionFields = [];
    this.sectionComps = [];
    this.totalSteps = 0;
    this.stepDetails = {};
    this.optionsMap = new Map();
    this.queryCode = '';
    this.queryId = 0;
    this.requestCode = '';
    this.formCode = '';
    this.formHeader = '';
    this.ctaText = '';
    this.authorizationText = '';
    this.footerSubText = '';
    this.footerText = '';
    this.forceShowCityInput = false;
    this.getUserData();
    this.requestId = createRandomString();
    this.pageDataMap = createLoggingMap(
      this.funnelStep,
      this.cmp,
      this.tagDestination,
      this.packageId,
      this.packageName,
      this.isPremiumPackage,
      { source: this.props.source || '' },
    );
    this.loadQueryForm();
  }

  componentDidMount() {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.QUERY_FORM)
    this.fetchCountryCodeData();
  }
  onBackClick=()=>{
    return this.handleBackPress();
HolidayDataHolder.getInstance().clearSubPageName()}

  getDefaultTravelDate = () => {
    const dates = getHolQueryFormDefaultTravelDaysCount();
    const nextDate = addDays(now(), dates);
    return nextDate;
  };

  openList = (item) => {
    item.error = false;
    this.setState({ cityList: true, singleSelectField: item });
    this.trackLocalClickEvent(PDT_CHANGE_CLICK);
  };

  openCountryList = (item, options) => {
    const optionMap = [];
    options.forEach((option) => {
      const countryObject = this.state.countryCodeList.find(
        (e) => e.phoneCode === option.value.substring(1),
      );
      optionMap.push({
        value: option.value,
        key: countryObject && countryObject.name,
        flag: countryObject && countryObject.emoji,
      });
    });
    this.setState({
      countryCodeOptions: optionMap,
      countryCodeSelector: true,
      singleSelectField: item,
    });
    this.trackLocalClickEvent(PDT_CHANGE_CLICK);
  };

  onCountryCodeClick = (code) => {
    const item = this.optionsMap.get(this.state.singleSelectField?.name);
    this.setState({
      countryCode: code,
      countryCodeSelector: false,
    });
    if (item && item.values) {
      item.values = [code];
    }
  };

  onCitySelect = (dest, branch = '') => {
    let budg = '';
    if (isNotNullAndEmptyCollection(this.destinations)) {
      for (let i = 0; i < this.destinations.length; i++) {
        if (this.destinations[i] === dest) {
          budg = this.budgets[i];
          break;
        }
      }
    }
    this.tagDestination = dest;
    if (!isEmptyString(branch)) {
      this.branch = branch;
    } else {
      this.updateBranch(dest);
    }
    const item = this.optionsMap.get(FIELD_DESTINATION);
    if (item) {
      item.values = [dest.value];
    }
    this.setState({
      destination: dest.value,
      budget: budg,
      cityList: false,
    });
    if (this.pageDataMap && this.pageDataMap.otherDetails) {
      this.pageDataMap.otherDetails.tagDestination = dest;
    }
    this.trackLocalClickEvent(PDT_CHOOSE_DESTINATION);
  };

  onSingleSelectClick = (dest) => {
    const item = this.optionsMap.get(this.state.singleSelectField.name);
    if (item) {
      item.values = [dest.value];
    }
    this.setState({
      [this.state.singleSelectField.name]: dest.value,
      cityList: false,
    });
  };

  onClickSpecifyDate = async (item) => {
    if (!(item?.editable == false)) {
      this.setState({
        showCalendar: true,
        itemName: item.name,
      });
      item.error = false;
      this.trackLocalClickEvent(PDT_CHOOSE_DATE);
    }
  };

  onCalendarBack = () => {
    this.setState({
      showCalendar: false,
    });
  };

  updateTravelDate = (selectedDate) => {
    if (selectedDate) {
      const item = this.optionsMap.get(this.state.itemName);
      if (item) {
        item.values = [[getApiDateDynamic(selectedDate)]];
      }
      this.setState({
        [this.state.itemName]: getApiDateDynamic(selectedDate),
        showCalendar: false,
      });
    }
  };

  loadQueryForm = () => {
    if (this.ticketId) {
      this.getQueryFormDataTicketId();
    } else {
      this.getQueryFormData();
    }
    this.setState({
      isLoading: true,
    });
  };

  getDestinationValue = (options) => {
    let destination = '';
    for (let i = 0; i < options?.length; i++) {
      if (options[i]?.optionsExtraInfo?.type !== 'header') {
        destination = options[i]?.value;
        break;
      }
    }
    return destination;
  };

  getQueryFormData = async () => {
    try {
      const result = await getQueryFormDataV2(
        this.pageName,
        getParamFromQuery(this.queryParams, DEEP_LINK_FORM_PARAM),
        this.cmp,
        this.formId,
      );
      const response = await result.json();

      this.mainFields = [];
      this.secFields = [];
      this.sectionFields = [];
      this.optionsMap = new Map();
      this.mainComps.splice(0, this.mainComps.length);
      this.secComps.splice(0, this.secComps.length);
      this.sectionComps.splice(0, this.sectionComps.length);
      let destination = this.state.destination;
      let hideChange = this.state.hideChange;
      let budget = this.state.budget;
      const { categoryTrackingEvent = '' } = this.props?.trackingData || {};
      if (response && response.statusCode === 1) {
        if (this.fromDeepLink) {
          trackHolidayQueryLoadEvent({
            logOmni: true,
            omniPageName: this.pageName,
            omniData: {
              [TRACKING_EVENTS.M_V57]: categoryTrackingEvent || '',
            },
            pdtData: {
              pageDataMap: this.pageDataMap,
              eventType: PDT_PAGE_VIEW,
              activity: PDT_PAGE_LOAD,
              requestId: this.requestId,
              branch: this.branch,
              omniPageName108: 'listing|standalone',
            },
          });
        }
        this.formCode = response.formCode;
        this.formHeader = response.formHeader;
        this.ctaText = response.ctaText;
        this.authorizationText = response.authorizationText;
        this.footerSubText = response.footerSubText;
        this.footerText = response.footerText;
        this.imageUrl = response.imageUrl;
        if (response.sections) {
          this.totalSteps = response.sections.length;
          response.sections.forEach((section, i) => {
            var sectionList = [];
            this.stepDetails[i] = {
              header: section.header,
              subHeader: section.subHeader,
              completionFeedback: section.completionFeedback,
            };
            if (section.queryFields) {
              section.queryFields.forEach((item) => {
                item.values = [];
                item.error = false;
                this.optionsMap.set(item.name, item);

                if (item.fieldType === FIELD_TYPE_TEXT) {
                  if (item.name && this.state[item.name]) {
                    item.values.push(this.state[item.name]);
                  }
                } else if (item.fieldType === FIELD_TYPE_SLIDER) {
                  let [min, max] =
                    item.options.length > 0 ? item.options[0].value.split('-') : [0, 0];
                  min = parseInt(min);
                  max = parseInt(max);
                  this.setState({
                    budgetPrefRange: { min, max },
                    budgetRange: { min, max },
                  });
                  item.values.push(`${item.options.length > 0 ? item.options[0].value : [0, 0]}`);
                } else if (item.fieldType === FIELD_TYPE_COUNTRY_CODE) {
                  item.values.push(`${this.state.countryCode}`);
                } else if (item.fieldType === FIELD_MONTH_DATE) {
                  const date = new Date();
                  date.setMonth(date.getMonth() + 1);
                  if (Object.keys(MONTH_INTEGER_MAP)?.length > date.getMonth()) {
                    const month = Object.keys(MONTH_INTEGER_MAP)[date.getMonth()];
                    const monthNumber = Object.values(MONTH_INTEGER_MAP)[date.getMonth()];
                    const year = date.getFullYear();
                    if (item.required) {
                      this.setState({ [item.name]: `${month} ${year}` });
                      item.values.push(`${monthNumber}/${year}`);
                    }
                  }
                } else if (item.fieldType === FIELD_SEARCH_DATE) {
                  if (this.searchDate) {
                    const searchDateObject = new Date(this.searchDate);
                    const date = getApiDateDynamic(searchDateObject);
                    item.values.push(date);
                    this.setState({
                      [item.name]: date,
                      showCalendar: false,
                    });
                  }
                } else if (item.fieldType === FIELD_TYPE_SINGLE_SELECT) {
                  if (item.options && item.options.length > 0) {
                    if (item.name === FIELD_DESTINATION) {
                      this.setDestinationsData(item.options);
                      const defaultOption = containsStringIgnoreCase(this.destinations, destination)
                        ? destination
                        : '';
                      if (!isEmpty(defaultOption)) {
                        this.setState({ [item.name]: defaultOption });
                        item.values.push(defaultOption);
                      }
                    } else if(item.name === FIELD_DEPARTURE){
                      const defaultOption = this.departureCity || this.getDestinationValue(item.options);
                      if (item.required) {
                        this.setState({ [item.name]: defaultOption });
                        item.values.push(defaultOption);
                      }
                    } else {
                      let defaultOption = this.getDestinationValue(item.options);
                      if (item.required) {
                        this.setState({ [item.name]: defaultOption });
                        item.values.push(defaultOption);
                      }
                    }
                  }
                }
                sectionList.push(item);
              });
            }
            if (section?.subSections) {
              const ad = section?.subSections.filter((el) => el.type == AD);
              this.ad.push(!isEmpty(ad?.[0]) ? ad?.[0] : { type: AD, contextId: '' });
            }
            this.sectionFields.push(sectionList);
          });
        }
        if (
          getT2QFabFlags().isSingleStepSubmission &&
          !this.validateData({
            sectionFields: this.sectionFields,
            dependentFields: this.state.dependentFields,
            step: 0,
          })
        ) {
          this.submitQueryRequest(true); // Auto-submission
        } else {
          this.setState({
            destination,
            hideChange,
            budget,
            isSuccess: true,
            isLoading: false,
          });
        }
      } else {
        if (this.ticketId) {
          this.handleBackPress();
        }
        this.onDynamicFormError();
      }
    } catch (e) {
      if (this.ticketId) {
        this.handleBackPress();
      }
      this.onDynamicFormError();
    }
  };

  getQueryFormDataTicketId = async () => {
    try {
      let result;
      if (this.ticketId) {
        result = await getQueryFormDataByTicketIdV2(
          this.pageName,
          getParamFromQuery(this.queryParams, DEEP_LINK_FORM_PARAM),
          this.cmp,
          this.ticketId,
        );
      } else {
        result = await getQueryFormDataV2(
          this.pageName,
          getParamFromQuery(this.queryParams, DEEP_LINK_FORM_PARAM),
          this.cmp,
          this.formId,
        );
      }
      const response = await result.json();
      this.mainFields = [];
      this.secFields = [];
      this.sectionFields = [];
      this.optionsMap = new Map();
      this.mainComps.splice(0, this.mainComps.length);
      this.secComps.splice(0, this.secComps.length);
      this.sectionComps.splice(0, this.sectionComps.length);
      let destination = this.state.destination;
      let hideChange = this.state.hideChange;
      let budget = this.state.budget;

      if (response && response.statusCode === 1) {
        this.formCode = response.formCode;
        this.formHeader = response.formHeader;
        this.ctaText = response.ctaText;
        this.authorizationText = response.authorizationText;
        this.footerSubText = response.footerSubText;
        this.footerText = response.footerText;
        this.imageUrl = response.imageUrl;
        this.queryCode = response?.queryCode ? response.queryCode : '';
        let valuesState = {};
        if (response.sections) {
          this.totalSteps = response.sections.length;
          response.sections.forEach((section, i) => {
            var sectionList = [];
            this.stepDetails[i] = {
              header: section.header,
              subHeader: section.subHeader,
              completionFeedback: section.completionFeedback,
            };
            if (section.queryFields) {
              section.queryFields.forEach((item) => {
                item.values = [];
                if (item?.value && !isEmptyString(item.value)) {
                  if (item.fieldType === FIELD_TYPE_TEXT) {
                    item.values.push(item.value);
                    valuesState[item.name] = item.value;
                  } else if (item.fieldType === FIELD_TYPE_SLIDER) {
                    let [min, max] = item.value.length > 0 ? item.value.split('-') : [0, 0];
                    min = parseInt(min);
                    max = parseInt(max);
                    valuesState.budgetPrefRange = { min, max };
                    valuesState.budgetRange = { min, max };
                    item.values.push(item.value);
                  } else if (item.fieldType === FIELD_TYPE_COUNTRY_CODE) {
                    valuesState[item.name] = item.value;
                    item.values.push(item.value);
                  } else if (item.fieldType === FIELD_MONTH_DATE) {
                    let split = item.value?.split('/');
                    if (split?.length > 1) {
                      let month = Object.keys(MONTH_INTEGER_MAP).find(
                        (key) => MONTH_INTEGER_MAP[key] === split[0],
                      );
                      let year = split[1];
                      valuesState[item.name] = `${month} ${year}`;
                      item.values.push(item.value);
                    }
                  } else if (item.fieldType === FIELD_TYPE_SINGLE_SELECT) {
                    if (item.options && item.options.length > 0) {
                      valuesState[item.name] = item.value;
                      item.values.push(item.value);
                    }
                  } else if (item.fieldType === FIELD_TYPE_RADIO) {
                    valuesState[item.name] = item.value;
                    item.values.push(item.value);
                    if (item.options && item.options.length > 0) {
                      item.options.forEach((option, index) => {
                        if (option.value === item.value) {
                          const dependentListFiltered =
                            valuesState.dependentFields?.length > 0
                              ? valuesState.dependentFields
                              : [];
                          valuesState.dependentFields = [
                            ...dependentListFiltered,
                            ...option.dependentFieldIds,
                          ];
                        }
                      });
                    }
                  } else {
                    valuesState[item.name] = item.value;
                    item.values.push(item.value);
                  }
                }
                item.error = false;
                this.optionsMap.set(item.name, item);
                sectionList.push(item);
              });
            }
            this.sectionFields.push(sectionList);
          });
        }
        this.setState({
          ...valuesState,
          isLoading: false,
          isSuccess: true,
        });
      } else {
        if (this.ticketId) {
          this.handleBackPress();
        }
        this.onDynamicFormError();
      }
    } catch (e) {
      if (this.ticketId) {
        this.handleBackPress();
      }
      this.onDynamicFormError();
    }
  };

  onDynamicFormError = () => {
    this.props.onDynamicFormError();
  };

  setDestinationsData = (destinations) => {
    this.destinations = [];
    this.branchMap.clear();
    destinations.forEach((element) => {
      this.destinations.push(element.value);
      if (element.optionsExtraInfo && element.optionsExtraInfo.branch) {
        this.branchMap.set(element.value, element.optionsExtraInfo.branch);
      }
    });
    this.budgets = [this.destinations.length];
    let split = [];
    for (let i = 0; i < this.destinations.length; i++) {
      split = this.destinations[i].split('_');
      this.destinations[i] = split[0];
      this.budgets[i] = split.length > 1 ? split[1] : '';
    }
  };

  updateBranchMap = (destinations) => {
    if (isNotNullAndEmptyCollection(destinations)) {
      this.branchMap.clear();
      destinations.forEach((element) => {
        if (element.optionsExtraInfo && element.optionsExtraInfo.branch) {
          this.branchMap.set(element.value, element.optionsExtraInfo.branch);
        }
      });
    }
  };

  updateBranch = (destination) => {
    const branch = this.branchMap.get(destination);
    this.branch = !isEmptyString(branch) ? branch : this.defaultBranch;
  };

  onSubmitClick = () => {
    let error = this.checkData();
    if (!error && !this.state.allowContact) {
      showShortToast(ERROR_AUTHORIZE);
      error = true;
    }
    if (error) {
      this.setState({
        itemError: true,
      });
    } else {
      if (this.ticketId?.length > 0) {
        this.submitUpdateRequest();
      } else {
        this.submitQueryRequest();
      }
      this.setState({
        isLoading: true,
      });
    }
  };

  checkData = () => {
    let error = false;
    let filteredFields = this.sectionFields[this.state.step].filter(
      (e) => (!e.dependent || this.state.dependentFields.includes(e.id)) && e.required,
    );
    filteredFields.forEach((item) => {
      if (this.checkValues(item.values, item.regex)) {
        item.error = true;
        error = true;
      }
    });
    return error;
  };

  validateData = ({ sectionFields, dependentFields, step }) => {
    let error = false;
    let filteredFields = sectionFields[step].filter(
      (e) => (!e.dependent || dependentFields.includes(e.id)) && e.required,
    );
    filteredFields.forEach((item) => {
      if (this.checkValues(item.values, item.regex)) {
        error = true;
      }
    });
    return error;
  };

  checkValues = (values, regex) => {
    if (isNullOrEmptyCollection(values)) {
      return true;
    }
    let error = false;
    let pattern;
    if (!isEmptyString(regex)) {
      pattern = new RegExp(regex);
    }
    values.forEach((value) => {
      if (isEmptyString(value) || (pattern && !pattern.test(value))) {
        error = true;
      }
    });
    return error;
  };

  submitQueryRequest = async (isAutoSubmit = false) => {
    const queryObject = {
      userCity: this.departureCity,
      duration: 0,
      pkgName: this.packageName,
      pkgCode: this.packageId,
      destinationCity: this.state.destination,
      journeyDate: getApiDate(this.state.travelDate),
      branch: this.branch,
      id: this.queryCode,
      requestId: this.requestId,
      pageSection: this.pageName,
      requestCode: this.requestCode,
      formCode: this.formCode,
      cmp: this.cmp,
      pkgType: this.props.pkgType,
      dynamicPackageId: this.props.dynamicPackageId,
    };
    if (this.packageId) {
      queryObject.pkgCode = getPackageCode(this.aff, this.branch, this.packageId);
    }

    const extraInfo = await this.getExtraInfo();
    const filteredFieldsByDependentIds = this.sectionFields[this.state.step].filter(
      (e) => !e.dependent || this.state.dependentFields.includes(e.id),
    );
    const response = await submitDynamicQuery(filteredFieldsByDependentIds, queryObject, extraInfo);
    try {
      const result = await response.json();
      if (result && result.success) {
        this.setState({
          isSuccess: true,
        });
        this.pageDataMap.querySubmitSuccess = true;
        if (result.queryCode) {
          this.queryCode = result.queryCode;
        }
        if (result.queryId) {
          this.queryId = result.queryId;
        }
        const queryDetail={
          id:`${EVENT_THANKYOU_CLICK}${this.queryId}`,
          contact_type:QUERY_DETAIL_CONSTANTS.CONTACT_TYPE.QUERY,
          intervention_type:this.props.fromIcon ? QUERY_DETAIL_CONSTANTS.INTERVENTION_TYPE.STANDALONE_ICON: QUERY_DETAIL_CONSTANTS.INTERVENTION_TYPE.FAB_ICON,
          intervention_id:`${this.props.formId}`,
          submit_type: isAutoSubmit ? 'autoscs' : 'clicksubmit'
        }
        this.trackPDTV3Event({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          value: `${PDT_SUBMIT_CLICK}_query|${
            this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : ''
          }`,
          queryDetail:{query_details:queryDetail},
          pageName: this.pageName,
        });
        sendMMTGtmEvent({
          eventName: 'callback',
          data: {
            pageName: 'queryPage',
            branch: this.branch,
          }
         })
        const { categoryTrackingEvent = '' } = this.props?.trackingData || {};
        if (isRawClient()) {
          trackHolidayQueryLoadEvent({
            logOmni: true,
            omniPageName: this.props?.omniPageName || PAGE_NAME_QUERY,
            omniData: {
              [TRACKING_EVENTS.M_V57]: categoryTrackingEvent || '',
            },
            pdtData: {
              pageDataMap: this.pageDataMap,
              eventType: PDT_PAGE_VIEW,
              activity: PDT_PAGE_LOAD,
              requestId: this.requestId,
              branch: this.branch,
            },
          });
        }
        this.trackLocalClickEvent(`${PDT_SUBMIT_CLICK}_query`, {
          prop66: this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : '',
        });
        this.pageDataMap.querySubmitSuccess = false;

        if (result.thankYouPageData) {
          this.setState({
            thankYouPageData: result.thankYouPageData,
          });
        }

        setFirebaseTrackingForQuery({
          queryId: this.queryId,
          branch: this.branch,
          fromPage: PAGE_NAME_QUERY,
        });

        if (this.state.step + STEP_COUNT === this.totalSteps) {
          this.trackPDTV3Event({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: `${EVENT_QUERY_SUBMIT}_${this.state.step + STEP_COUNT}|${
              this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : ''
            }`,
            pageName: this.pageName,
            queryDetail:{query_details:queryDetail}
          });
          setTimeout(() => {
            this.trackLocalClickEvent(`${EVENT_QUERY_SUBMIT}_${this.state.step + STEP_COUNT}`, {
              prop66: this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : '',
            });
            sendMMTGtmEvent({
              eventName: 'submit',
              data: {
                pageName: 'queryPage',
                branch: this.branch,
              }
             })
            BranchIOTracker.trackContentEvent({
              [BranchIOTracker.KEYS.EVENT_NAME]: BranchIOTracker.EVENT.QUERY,
              [BranchIOTracker.KEYS.PAGE_NAME]: BranchIOTracker.PAGE.QUERY,
              [BranchIOTracker.KEYS.DESCRIPTION]: 'Submit Query',
              [BranchIOTracker.KEYS.CUSTOM_DATA]: {
                queryId: this.queryId,
              },
            });
          }, 100);
          if (result?.otpFlow) {
            this.setState({
              isLoading: false,
              showOtpFlow: true,
            });
          } else if (this.state?.thankYouPageData) {
            this.showWelcomePage();
          }

          return;
        } else {
          setTimeout(() => {
  
          this.trackPDTV3Event({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: `${EVENT_QUERY_NEXT}_${this.state.step + STEP_COUNT}|${
              this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : ''
            }`,
            pageName: this.pageName,
          });
          this.trackLocalClickEvent(`${EVENT_QUERY_NEXT}_${this.state.step + STEP_COUNT}`, {
              prop66: this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : '',
            });
          }, 200);
        }
        this.setState({
          isLoading: false,
          step: this.state.step + 1,
        });
      } else {
        this.onError();
      }
    } catch (e) {
      this.onError();
    }
  };

  showQueryUpdateForm = () => {
    if (isNullOrEmptyCollection(this.secFields)) {
      this.showToastAndAExit();
    } else {
      this.setState({
        isLoading: false,
        showUpdateForm: true,
        showOtpFlow: false,
      });
    }
  };

  onDoneClicked = () => {
    this.setState({
      isLoading: true,
    });
    this.submitUpdateRequest();
  };

  submitUpdateRequest = async () => {
    const filteredFieldsByDependentIds = this.sectionFields[this.state.step].filter(
      (e) => !e.dependent || this.state.dependentFields.includes(e.id),
    );
    const response = await submitQueryUpdate(
      filteredFieldsByDependentIds,
      this.queryCode,
      this.formCode,
    );
    try {
      const result = await response.json();
      this.trackLocalClickEvent(PDT_DONE_CLICK);
      if (result && result.success) {
        if (isRawClient()) {
          const { categoryTrackingEvent = '' } = this.props?.trackingData || {};
          trackHolidayQueryLoadEvent({
            logOmni: true,
            omniPageName: this.props?.omniPageName || PAGE_NAME_QUERY,
            omniData: {
              [TRACKING_EVENTS.M_V57]: categoryTrackingEvent || '',
            },
            pdtData: {
              pageDataMap: this.pageDataMap,
              eventType: PDT_PAGE_VIEW,
              activity: PDT_PAGE_LOAD,
              requestId: this.requestId,
              branch: this.branch,
            },
          });
        }
        if (this.queryId !== 0) {
          this.trackPDTV3Event({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: `query_submit|${EVENT_THANKYOU_CLICK}${this.queryId}`,
            pageName: this.pageName,
          });
          sendMMTGtmEvent({
            eventName: 'submit',
            data: {
              pageName: 'queryPage',
              branch: this.branch,
            }
           })
          this.trackLocalClickEvent('query_submit', {
            prop66: `${EVENT_THANKYOU_CLICK}${this.queryId}`,
          });
        }
        this.showToastAndAExit();
      } else {
        this.onError();
      }
    } catch (e) {
      this.onError();
    }
  };

  showWelcomePage = () => {
    this.setState({
      showWelcomePage: true,
      showOtpFlow: false,
      isLoading: false,
    });
  };

  onOtpSubmitClick = (otp) => {
    this.setState({
      isLoading: true,
    });
    this.submitOtpRequest(otp);
  };

  submitOtpRequest = async (otp) => {
    if (isEmptyString(otp)) {
      this.onOtpError(EMPTY_ERROR_OTP);
      return;
    }
    const response = await verifyOtp(this.queryCode, otp);
    try {
      const result = await response.json();
      this.trackLocalClickEvent(PDT_OTP_SUBMIT_CLICK);
      if (result && result.success && result.verified) {
        this.setState({ isLoading: false });
        this.showWelcomePage();
      } else {
        this.onOtpError(ERROR_OTP);
      }
    } catch (e) {
      this.onOtpError(ERROR_OTP);
    }
  };

  showToastAndAExit = () => {
    this.setState({
      isLoading: false,
    });
    showShortToast(MSG_QUERY_SUBMITTED);
    this.trackLocalClickEvent(PDT_PAGE_EXIT_EVENT);
    this.props.onBackPressed();
  };

  getUserDepartureCity = async () => {
    this.departureCity = this.props.departureCity
      ? this.props.departureCity
      : await getDepartureCity();
  };

  onError = () => {
    showShortToast(ERROR_API);
    this.setState({
      isLoading: false,
    });
  };

  onOtpError = (msg) => {
    showShortToast(msg);
    this.setState({
      isLoading: false,
      showOtpFlow: true,
      otpError: true,
    });
  };
  getExtraInfo = async () => {
    let noteVal = this.cmp;
    const { trackingData = {}, source } = this.props || {};
    const { categoryTrackingEvent = '' } = trackingData || {};
    if (this.fromSeo) {
      noteVal = SEO_MOBILE_QUERY_ID;
    }
    // const source = HolidayDataHolder.getInstance().getSource();
    return {
      evar79: getPokusExpVarientKey(PokusLobs.HOLIDAY, 'NA'),
      evar17: noteVal ? noteVal : 'NA',
      evar81: noteVal ? noteVal : 'NA',
      evar83: await updatedValuesforvariable_m_v83({[TRACKING_EVENTS.M_V83]: HolidayDataHolder.getInstance().getBanner()}),
      evar57: categoryTrackingEvent,
      evar108: getEvar108ForFunnel({
        source,
        trackingPageName: this.props.omniPageName || this.pageName,
      }),
      Adults: 2,
      Children: 1,
      'Senior Citizen Included': false,
      'Need flight': true,
      'Transfer type': 'Private',
      'Sightseeing type': 'All Popular Activities',
      Duration: 5,
      callBackPage: this.totalSteps === this.state.step + STEP_COUNT,
      sectionOrder: this.state.step + STEP_COUNT,
      source,
    };
  };

  getUserData = async () => {
    const loggedIn = await isUserLoggedIn();
    if (loggedIn) {
      const userData = await getUserDetails();
      const firstName = userData.firstName ? userData.firstName : 'Visitor';
      this.setState({
        email: userData.email,
        phoneNumber: userData.mobile ? userData.mobile.mobileNumber : '',
        firstName: userData.firstName,
      });
    }
  };

  async fetchCountryCodeData() {
    if (isAndroidClient()) {
      const list = await GenericModule.getCountryList('countries_list.json');
      if (list) {
        this.setState({
          countryCodeList: list,
        });
      }
    } else if (isIosClient()) {
      const list = await VisaModule.getCountriesCodeList();
      if (list) {
        this.setState({
          countryCodeList: list,
        });
      }
    } else {
      // mWeb
      const { HolidayModule } = NativeModules;
      const list = await HolidayModule.getCountriesCodeList();
      if (list) {
        this.setState({
          countryCodeList: list,
        });
      }
    }
  }

  handleValueChange = (item, text, state) => {
    item.values = [text];
    item.error = false;
    this.setState({
      [state]: text,
    });
  };

  handleHotelStar = (item, values) => {
    this.setState({ hotelStarV2: values });
    item.values = values;
  };

  handleRadioChange = (item, data, dependentFieldList) => {
    if (!(item?.editable === false)) {
      item.values = [data.normalText];
      item.error = false;
      const dependentListFiltered =
        this.state.dependentFields.length > 0
          ? this.state.dependentFields.filter((e) => !dependentFieldList.includes(e))
          : [];
      this.setState({
        [item.name]: data.normalText,
        dependentFields: [...dependentListFiltered, ...data.dependentFieldIds],
      });
    }
  };

  setRadioObjects = (item, data, dependentFieldList) => {
    item.values = [data.normalText];
    item.error = false;
    const dependentListFiltered =
      this.state.dependentFields.length > 0
        ? this.state.dependentFields.filter((e) => !dependentFieldList.includes(e))
        : [];
    this.setState({
      [item.name]: data.normalText,
      dependentFields: [...dependentListFiltered, ...data.dependentFieldIds],
    });
  };

  handleBackPress = () => {
    if (this.state.cityList) {
      this.setState({
        cityList: false,
      });
    } else if (this.state.countryCodeSelector) {
      this.setState({
        countryCodeSelector: false,
      });
    } else if (this.state.showOtpFlow) {
      this.setState({
        showOtpFlow: false,
      });
    } else {
      // Check if there are any holiday pages in the navigation stack
      const hasHolidayPageInStack = this.checkForHolidayPagesInStack();

      if (!hasHolidayPageInStack) {
        // Navigate to holiday landing page if no holiday pages found
        this.navigateToHolidayLandingPage();
      } else {
        this.props.onBackPressed();
      }

      this.trackPDTV3Event({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: EVENT_QUERY_CLOSE,
        pageName: this.pageName,
      });
      this.trackLocalClickEvent(EVENT_QUERY_CLOSE);
    }
    this.trackLocalClickEvent(PDT_BACK_CLICK);
    return true;
  };

  trackLocalClickEvent = (eventName, { prop66 = '' } = {}) => {
    this.trackClickEvent({ eventNameOmni: eventName, eventNamePdt: eventName, prop66 });
  };

  checkForHolidayPagesInStack = () => {
    try {
      const navigationState = HolidayNavigation.getNavigationRef()?.dangerouslyGetState();
      if (!navigationState || !navigationState.routes) {
        return false;
      }

      // Get all page names from the navigation stack
      const pageNames = navigationState.routes.map(route => route.name);

      // Check if any page in the stack is a holiday page (excluding current query form)
      return pageNames.some(pageName =>
        isHolidaysPage(pageName) && pageName !== HOLIDAY_ROUTE_KEYS.QUERY_FORM
      );
    } catch (error) {
      console.warn('Error checking holiday pages in stack:', error);
      return false;
    }
  };

  navigateToHolidayLandingPage = () => {
    try {
      // Navigate to holiday landing page
      if (isRawClient()) {
        holidayNavigationPush({
          pageKey: HOLIDAY_ROUTE_KEYS.LANDING_NEW,
          props: JSON.stringify({
            cmp: this.cmp,
            aff: this.aff,
            pt: this.props.pt || '',
          }),
          showOverlay: this.props.showOverlay,
          hideOverlays: this.props.hideOverlays,
        });
      }
      else {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.LANDING_NEW, {
          // Pass any necessary parameters for the landing page
          holidaysLandingData: JSON.stringify({
            // Add default landing data if needed
            cmp: this.cmp,
            aff: this.aff,
            pt: this.props.pt || '',
          }),
        });
      }
    } catch (error) {
      console.warn('Error navigating to holiday landing page:', error);
      // Fallback to default behavior
      this.props.onBackPressed();
    }
  };

  trackClickEvent = ({ eventNameOmni = '', prop66 = '', eventNamePdt = '' }) => {
    const { trackingData = {} } = this.props || {};
    const { categoryTrackingEvent = '' } = trackingData || {};
    const pageName = this.props?.omniPageName || PAGE_NAME_QUERY;
    trackHolidayQueryClickEvent({
      omniPageName: pageName,
      omniEventName: eventNameOmni,
      prop66,
      omniData: {
        [TRACKING_EVENTS.M_V22]: this.props.fromErrorPage
          ? `HLD:${this.props?.errorMessage}` || ''
          : '',
        [TRACKING_EVENTS.M_C1]: this.props?.formId ? `form_no:${this.props.formId}` : '',
        [TRACKING_EVENTS.M_V57]: categoryTrackingEvent || '',
      },
      pdtData: {
        pageDataMap: this.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: eventNamePdt,
        requestId: this.requestId,
        branch: this.branch,
      },
    });
  };

  findGroups = (items, groupList) => {
    items &&
      items.length > 0 &&
      items.forEach((item) => {
        if (item.groupId) {
          groupList[item.groupId]
            ? (groupList[item.groupId] = [...groupList[item.groupId], item])
            : (groupList[item.groupId] = [item]);
        }
      });
  };

  createQueryComponents = (items, comps, addSeparator = false) => {
    let groupList = {};
    this.findGroups(items, groupList);
    items.length > 0 &&
      items
        .filter(
          (e) =>
            (!e.dependent || this.state.dependentFields.includes(e.id)) &&
            (e.groupId
              ? groupList[e.groupId] &&
                groupList[e.groupId].length > 0 &&
                groupList[e.groupId][0].id === e.id
              : true),
        )
        .forEach((item) => {
          if (item.name === FIELD_DATE || item.name === FIELD_TYPE_DATE) {
            this.getDateField(comps, item);
          } else if (item.fieldType === FIELD_TYPE_TEXT) {
            this.getTextField(comps, item);
          } else if (item.fieldType === FIELD_TYPE_RADIO) {
            this.getRadioField(comps, item);
          } else if (item.fieldType === FIELD_TYPE_SINGLE_SELECT) {
            this.getSingleSelectField(comps, item);
          } else if (item.fieldType === FIELD_MONTH_DATE) {
            this.getMonthDateField(comps, item);
          } else if (item.fieldType === FIELD_TYPE_COUNTRY_CODE) {
            this.getCountryCodeField(comps, item, groupList);
          } else if (
            item.fieldType === FIELD_TYPE_INLINE_BOX ||
            item.fieldType === FIELD_TYPE_SINGLE_INLINE_BOX
          ) {
            this.getInlineBoxField(comps, item);
          } else if (item.fieldType == FIELD_TYPE_SLIDER) {
            this.getSliderField(comps, item);
          }

          if (addSeparator) {
            comps.push(
              <View style={{ marginTop: 8 }}>
                <Separator type="noMargin" />
              </View>,
            );
          }
        });
    return comps;
  };

  getLabelTextField = (label, isRequired) => (
    <Text style={styles.labelContainer}>
      <Text style={styles.labelText}>{label}</Text>
      {isRequired && <Text style={styles.asterisk}>*</Text>}
    </Text>
  );

  budgetPrefChange = (values, item) => {
    if (values.length > 0) {
      const myPriceRange = {
        min: values[0],
        max: values[1],
      };
      this.setState({
        budgetPrefRange: myPriceRange,
      });
      item.values = [`${myPriceRange.min}-${myPriceRange.max}`];
    }
  };

  getSliderField = (comps, item) => {
    const { budgetRange } = this.state || {};
    comps.push(
      <View>
        {this.getLabelTextField(item.fieldText, item.required)}
        <QueryPriceFilter
          budgetPrefChange={(e) => this.budgetPrefChange(e, item)}
          min={budgetRange.min}
          max={budgetRange.max}
          isCurrency={item.name === 'budgetPrefRange'}
        />
      </View>,
    );
  };

  getAd = () => {
    return (
      !isEmpty(this.ad?.[this.state.step]) && (
        <HolidayAdCard
          card={this.ad?.[this.state.step]}
          data={{}}
          adStyles={{ styles: adQuery, resizeMode: 'cover' }}
          onPress={() => {}}
          adDimensions={styles.ad}
          showOnlyAd
        />
      )
    );
  };

  getDestinationField = (comps, item) => {
    comps.push(
      <View style={AtomicCss.marginBottom15}>
        {this.getLabelTextField(item.fieldText, item.required)}
        <View style={[styles.inputText, styles.inputWrapper]}>
          <View style={{ flex: 1 }}>
            <Text
              style={{
                fontSize: 14,
                fontFamily: 'Lato-Regular',
                color: '#4a4a4a',
              }}
              type="default"
            >
              {this.state.destination}
            </Text>
          </View>
          {!this.state.hideChange && (
            <TouchableOpacity
              style={{ alignSelf: 'center' }}
              activeOpacity={0.7}
              onPress={(e) => {
                e.preventDefault();
                this.openList(item);
              }}
            >
              <Text style={styles.changeText}>CHANGE</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>,
    );
  };

  getLabel = (label, isMandatory) => {
    return (
      <Text style={styles.labelTextField}>
        {label?.toUpperCase()}
        {isMandatory && <Text style={styles.mandatoryTextField}> *</Text>}
      </Text>
    );
  };

  handleLabelChange = (name, value) => {
    const { filledField } = this.state;
    // Checks if the value is not empty and if the field is not already in the array
    if (value && !filledField.includes(name)) {
      this.setState((prevState) => ({
        filledField: [...prevState.filledField, name],
      }));
    }
    // If the value is empty and the field exists in the array, removes it
    else if (!value && filledField.includes(name)) {
      this.setState((prevState) => ({
        filledField: prevState.filledField.filter((fieldName) => fieldName !== name),
      }));
    }
  };

  getTextField = (comps, item) => {
    let keyType = 'default';
    let value = '';
    let maxLength;
    let state = '';
    const errorMessage = item.error ? 'This field is required.' : '';
    switch (item.name) {
      case FIELD_NAME:
        value = this.state.userName;
        state = 'userName';
        break;
      case FIELD_FIRSTNAME:
        value = this.state.firstName;
        state = 'firstName';
        break;
      case FIELD_EMAILID:
        keyType = 'email-address';
        value = this.state.email;
        state = 'email';
        break;

      case FIELD_EMAIL:
        keyType = 'email-address';
        value = this.state.email;
        state = 'email';
        break;

      case FIELD_NUMBER:
        keyType = 'numeric';
        value = this.state.phoneNumber || this.state.mobileNumber;
        state = 'phoneNumber';
        maxLength = 10;
        break;

      default:
        break;
    }
    if (item.values && item.values.length > 0) {
      value = item.values[0];
    } else {
      item.values = [value];
    }
    comps.push(
      <View style={[AtomicCss.marginBottom15, AtomicCss.flex1]}>
        <FloatingInput
          label={item.fieldText?.toUpperCase()}
          requiredText={item.required && <Text style={styles.mandatory}> *</Text>}
          value={value}
          onChangeText={(text) => {
            this.handleValueChange(item, text, state);
            this.handleLabelChange(item?.name, text);
          }}
          isError={item.error}
          errorMessage={errorMessage}
          customStyle={{
            labelStyle: [
              styles.emptyLabel,
              this.state.filledField.includes(item?.name) || value
                ? { color: holidayColors.lightGray }
                : { color: holidayColors.gray },
            ],
            inputFieldStyle: styles.inputTextField,
            errorMessageStyle: [styles.errorWrapTextField, styles.errorMessageTextField],
          }}
          inputProps={{
            maxLength: maxLength,
            keyboardType: keyType,
          }}
        />
      </View>,
    );
  };

  getRadioField = (comps, item) => {
    const data = [];
    let dependentFieldList = [];
    item.options.forEach((element) => {
      data.push({
        boldText: '',
        normalText: element.value,
        dependentFieldIds: element.dependentFieldIds ? element.dependentFieldIds : [],
      });
      if (element.dependentFieldIds?.length > 0) {
        dependentFieldList = [...dependentFieldList, ...element.dependentFieldIds];
      }
    });
    let activeIndex = -1;
    if (isNotNullAndEmptyCollection(item.values)) {
      data.forEach((option, index) => {
        if (option.normalText === item.values[0]) {
          activeIndex = index;
        }
      });
    }
    const itemWidth = item?.widthPercentage
      ? `${item.widthPercentage}%`
      : item?.width
      ? `${item.width}%`
      : '100%';
    comps.push(
      <View>
        {this.getLabelTextField(item.fieldText, item.required)}
        <RadioOptions
          heading=""
          data={data}
          activeIndex={activeIndex}
          onSelectionChange={(active) =>
            this.handleRadioChange(item, data[active], dependentFieldList)
          }
          disabled={item?.editable === false}
          radioStyles={{
            radioBtn: {
              marginBottom: 10,
              flexDirection: 'row',
              minWidth: itemWidth,
              flexWrap: 'wrap',
            },
            radioWrapper: { marginTop: 6, flexDirection: 'row', width: '100%', flexWrap: 'wrap' },
          }}
        />
        {item.error && <Text style={styles.errorText}>{ERROR_TEXT}</Text>}
      </View>,
    );
  };

  getBorderColor = (error) => {
    if (error) {
      return '#eb2026';
    } else {
      return 'rgb(128, 197, 255)';
    }
  };

  getDateField = (comps, item) => {
    item.values = this.state[item.name] ? [this.state[item.name]] : [];
    comps.push(
      <View style={AtomicCss.marginBottom15}>
        <Dropdown
          label={item.fieldText?.toUpperCase()}
          requiredText={item.required && <Text style={styles.mandatory}> *</Text>}
          value={this.state[item.name]}
          isError={!!item.error}
          errorMessage={ERROR_TEXT}
          isFloating={true}
          endIcon={calendar_icon}
          onEndIconPress={() => this.onClickSpecifyDate(item)}
          onSelect={(selectedValue) => {
            this.onClickSpecifyDate(item);
            this.handleLabelChange(item?.name, selectedValue);
          }}
          customStyle={{
            labelStyle: [
              styles.emptyLabel,
              this.state.filledField.includes(item?.name) || this.state[item?.name]
                ? { color: holidayColors.lightGray }
                : { color: holidayColors.gray },
            ],
            dropdownWrapperStyle: styles.inputDropdownField,
            errorMessageStyle: [styles.errorWrapTextField, styles.errorMessageTextField],
            dropdownValueStyle: styles.dropdownValueStyle,
          }}
        />
      </View>,
    );
  };

  handelMonthCalendar = (item) => {
    this.setState({
      showMonthCalendar: !this.state.showMonthCalendar,
    });
    item.error = false;
  };

  handleSelectMonth = (item, month, selYear) => {
    this.setState({
      [item.name]: `${month} ${selYear}`,
      showMonthCalendar: false,
    });
    item.values = [`${MONTH_INTEGER_MAP[month]}/${selYear}`];
  };

  getMonthDateField = (comps, item) => {
    comps.push(
      <View style={AtomicCss.marginBottom15}>
        <View style={{ position: 'absolute', top: -190, zIndex: 2 }}>
          {this.state.showMonthCalendar && (
            <MonthCalendar
              handleSelectMonth={this.handleSelectMonth}
              item={item}
              selectedDate={this.state[item.name] || ''}
            />
          )}
        </View>
        <Dropdown
          label={item.fieldText?.toUpperCase()}
          requiredText={item.required && <Text style={styles.mandatory}> *</Text>}
          value={this.state[item.name]}
          isError={!!item.error}
          errorMessage={ERROR_TEXT}
          isFloating={true}
          endIcon={calendar_icon}
          onEndIconPress={(selectedValue) => {
            this.handelMonthCalendar(item);
            this.handleLabelChange(item?.name, selectedValue);
          }}
          onSelect={(selectedValue) => {
            this.handelMonthCalendar(item);
            this.handleLabelChange(item?.name, selectedValue);
          }}
          customStyle={{
            labelStyle: [
              styles.emptyLabel,
              this.state.filledField.includes(item?.name) || this.state[item?.name]
                ? { color: holidayColors.lightGray }
                : { color: holidayColors.gray },
            ],
            dropdownWrapperStyle: styles.inputDropdownField,
            errorMessageStyle: [styles.errorWrapTextField, styles.errorMessageTextField],
            dropdownValueStyle: styles.dropdownValueStyle,
          }}
        />
      </View>,
    );
  };

  getSingleSelectField = (comps, item) => {
    const onDropDownClick = () => {
      this.openList(item);
      this.handleLabelChange(item?.name, this.state[item.name]);
    }
    comps.push(
      <View style={AtomicCss.marginBottom15}>
        <Dropdown
          label={item.fieldText?.toUpperCase()}
          requiredText={item.required && <Text style={styles.mandatory}> *</Text>}
          value={this.state[item.name]}
          isError={!!item.error}
          errorMessage={ERROR_TEXT}
          isFloating={true}
          endIcon={editIcon}
          onSelect={onDropDownClick}
          onEndIconPress={onDropDownClick}
          customStyle={{
            labelStyle: [
              styles.emptyLabel,
              this.state.filledField.includes(item?.name) || this.state[item?.name]
                ? { color: holidayColors.lightGray }
                : { color: holidayColors.gray },
            ],
            dropdownWrapperStyle: styles.inputDropdownField,
            errorMessageStyle: [styles.errorWrapTextField, styles.errorMessageTextField],
            actionTextStyle: [styles.labelTextField, { color: holidayColors.primaryBlue }],
            dropdownValueStyle: styles.dropdownValueStyle,
            endIconStyle: styles.endIconStyle,
          }}
        />
      </View>,
    );
  };

  getGroupedComponents = (comps, items) => {
    items &&
      items.length > 0 &&
      items.forEach((item) => {
        if (item.name === FIELD_NUMBER) {
          let keyType = 'numeric';
          let value = this.state.phoneNumber;
          let maxLength = 10;
          let state = 'phoneNumber';
          comps.push(
            <TextInput
              style={[styles.input, { color: 'black' }]}
              underlineColorAndroid="#00000000"
              type="default"
              placeholder={PHONE_PLACEHOLDER}
              value={value}
              defaultValue={value}
              keyboardType={keyType}
              onChangeText={(text) => this.handleValueChange(item, text, state)}
              editable={!(item?.editable === false)}
            />,
          );
        }
      });
  };

  getCountryCodeField = (comps, item, groupList) => {
    const downArrow = require('@mmt/legacy-assets/src/down_arrow_blue.webp');
    const groupComp = [];
    if (item.groupId) {
      this.getGroupedComponents(
        groupComp,
        groupList[item.groupId].filter((e) => e.id !== item.id),
      );
    }
    const validateCountryCode =
      groupList[item.groupId] &&
      groupList[item.groupId].filter((e) => e.id !== item.id).find((e) => e.error);
    comps.push(
      <View style={AtomicCss.marginBottom20}>
        {this.getLabelTextField('Phone', item.required)}
        <View
          style={[
            styles.inputGroupWrapper,
            {
              flexDirection: 'row',
              borderColor: validateCountryCode ? '#eb2026' : 'rgb(128, 197, 255)',
            },
          ]}
        >
          <View style={{ flex: 0.3, width: '30%' }}>
            <TouchableOpacity
              disabled={item?.editable === false}
              onPress={(e) => {
                e.preventDefault();
                this.openCountryList(item, item.options);
              }}
            >
              <View style={styles.selectBox}>
                <View style={AtomicCss.marginRight5}>
                  <Text style={[styles.countryCodeText, { fontSize: 20 }]}>
                    {
                      this.state.countryCodeList.find(
                        (e) => e.phoneCode === this.state.countryCode.substring(1),
                      ).emoji
                    }
                  </Text>
                </View>
                <View style={AtomicCss.marginLeft5}>
                  <Image source={downArrow} style={styles.iconArrow} />
                </View>
                <View style={AtomicCss.marginLeft5}>
                  <Text style={styles.countryCodeText}>{this.state.countryCode}</Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
          {groupComp}
        </View>
        {item.error || (validateCountryCode && <Text style={styles.errorText}>{ERROR_TEXT}</Text>)}
      </View>,
    );
  };

  getInlineBoxField = (comps, item) => {
    comps.push(
      <View style={AtomicCss.marginBottom15}>
        <InlineBoxField
          hotelStarV2={this.state.hotelStarV2}
          options={item.options}
          setHotelStar={(e) => this.handleHotelStar(item, e)}
          isSingleSelect={item.name === FIELD_TYPE_SINGLE_INLINE_BOX}
          item={item}
          disabled={item?.editable === false}
        />
        {item.error && <Text style={styles.errorText}>{ERROR_TEXT}</Text>}
      </View>,
    );
  };

  openUrl = (url) => {
    const { HolidayModule } = NativeModules;
    HolidayModule.openWebView({ url });
  };

  resendOtp = () => {
    this.setState({
      isLoading: true,
    });
    const response = resendOtp(this.queryCode)
      .then((response) => response.json())
      .then((result) => {
        if (result && result.success) {
          this.setState({ isLoading: false });
        } else {
          this.onOtpError(ERROR_API);
        }
      })
      .catch(() => {
        this.onOtpError(ERROR_API);
      });
  };

  onClose = () => {
    HolidayNavigation.pop();
  };

  onPressCheckBox = () => {
    this.setState({ allowContact: !this.state.allowContact });
  };

  render() {
    const checkedIcon = require('@mmt/legacy-assets/src/ic-check-box-active.webp');
    const unCheckedIcon = require('@mmt/legacy-assets/src/ic-check-box-inactive.webp');
    const mainComps = [];
    const secComps = [];
    const sectionComps = [];
    const { trackingData } = this.props || {};
    const { categoryTrackingEvent = '' } = this.props?.trackingData || {};

    if (this.state.showUpdateForm) {
      this.createQueryComponents(this.secFields, secComps, true);
    } else {
      this.createQueryComponents(this.mainFields, mainComps);
    }
    if (this.sectionFields && this.sectionFields.length > 0) {
      this.createQueryComponents(this.sectionFields[this.state.step], sectionComps);
    }
    const header = this.stepDetails[this.state.step]
      ? this.stepDetails[this.state.step].header
      : '';
    const subHeader = this.stepDetails[this.state.step]
      ? this.stepDetails[this.state.step].subHeader
      : '';
    const rightIcon = [
      {
        icon: require('@mmt/legacy-assets/src/holidays/ic_cross.png'),
        onPress: this.handleBackPress,
        customStyles: {
          iconStyle: styles.iconClose,
        },
      },
    ];

    const QueryFromV2HeaderIcons = {
      STEP_1: getFabsIconUrls().formHeaderIconUrl,
      STEP_2: getFabsIconUrls().doneGif,
    };

    return (
      <View style={styles.queryFormWrapper}>
        <FilterLoader
          show={Boolean(this.state.isLoading && !this.state.showCalendar)}
          showCenterLoader={true}
          loadingFirstTime={false}
        />

        {this.state.showWelcomePage && (
          <ThankYouPage
            data={this.state?.thankYouPageData}
            trackQueryLoadEvent={() => {
              let querySuffix = EVENT_THANKYOU_LOAD;
              trackHolidayQueryLoadEvent({
                logOmni: true,
                omniPageName: ( this.props?.omniPageName || PAGE_NAME_QUERY ),
                prop66: this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : '',
                omniData: {
                  [TRACKING_EVENTS.M_V57]: categoryTrackingEvent || '',
                },
                pdtData: {
                  pageDataMap: this.pageDataMap,
                  eventType: PDT_PAGE_VIEW,
                  activity: PDT_PAGE_LOAD,
                  requestId: this.requestId,
                  branch: this.branch,
                },
              });
            }}
            onClose={() => {
              let querySuffix = EVENT_THANKYOU_CLICK;
              this.props.onBackPressed();
              this.trackPDTV3Event({
                actionType: PDT_EVENT_TYPES.buttonClicked,
                value: EVENT_THANKYOU_LOAD,
                pageName: this.pageName,
              });
              this.trackLocalClickEvent(EVENT_THANKYOU_LOAD, {
                prop66: this?.queryId ? `${EVENT_THANKYOU_CLICK}${this.queryId}` : '',
              });
            }}
          />
        )}
        {this.state.showOtpFlow && !this.state.showCalendar && (
          <QueryOtpScreen
            onBack={this.handleBackPress}
            onSubmitClick={this.onOtpSubmitClick}
            resend={this.resendOtp}
            onOtpClose={() => {
              this.props.onBackPressed();
              this.trackLocalClickEvent(EVENT_QUERY_CLOSE);
            }}
            error={this.state.otpError}
          />
        )}
        {this.state.isSuccess &&
          !this.state.showOtpFlow &&
          !this.state.showCalendar &&
          !this.state.countryCodeSelector &&
          !this.state.cityList &&
          !this.state.showWelcomePage && (
            <View style={{ flex: 1, backgroundColor: 'rgba(255,255,255,0.1)' }}>
              {getT2QFabFlags().isNewFabHeader ? (
                <Pressable
                  style={{
                    position: 'absolute',
                    top: 0,
                    right: 16,
                    zIndex: 1000,
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    padding: 10,
                    borderRadius: 70,
                  }}
                  onPress={this.handleBackPress}
                >
                  <Image source={crossIcon} style={{ width: 36, height: 36 }} />
                </Pressable>
              ) : (
                <View style={styles.iconCloseWrap}>
                  <Header customStyles={styles.customStyles} rightIcons={rightIcon} />
                </View>
              )}
              <KeyboardAwareScrollView>
                <View
                  style={{
                    ...styles.scrollWrapper,
                    ...(getT2QFabFlags().isNewFabHeader ? marginStyles.mt24 : {}),
                  }}
                >
                  {getT2QFabFlags().isNewFabHeader ? (
                    <>
                      <QueryFormHeaderV2
                        {...{
                          heading: this.state.step === 0 ? this.formHeader : header,
                          ctaDisplayName: this.state.step === 0 ? '' : 'Edit Details',
                          onClick: () => {
                            this.setState({ step: 0 });
                            this.trackLocalClickEvent(`${EVENT_QUERY_EDIT}_0_1`);
                          },
                          subHeading:
                            this.state.step === 0
                              ? subHeader
                              : getCompletionFeedBackText(this.stepDetails, this.state),
                          iconUrl:
                            this.state.step === 0
                              ? QueryFromV2HeaderIcons.STEP_1
                              : QueryFromV2HeaderIcons.STEP_2,
                        }}
                      />
                      {this.state.step !== 0 && subHeader?.length > 0 && (
                        <Text
                          style={{
                            ...fontStyles.labelLargeBlack,
                            ...paddingStyles.pv14,
                            lineHeight: 24,
                          }}
                        >
                          {subHeader}
                        </Text>
                      )}
                      {!this.state.isLoading && (
                        <View style={AtomicCss.marginTop10}>
                          <View style={styles.contactformWrapper}>{sectionComps}</View>
                        </View>
                      )}
                    </>
                  ) : (
                    <>
                      <QueryFormHeader
                        formHeader={this.formHeader}
                        state={this.state}
                        stepDetails={this.stepDetails}
                        setStep={(step, initialStep) => {
                          this.setState({ step: step });
                          this.trackLocalClickEvent(
                            `${EVENT_QUERY_EDIT}_${initialStep + STEP_COUNT}_${step + STEP_COUNT}`,
                          );
                        }}
                      />
                      <View style={AtomicCss.marginTop10}>
                        <View style={styles.step}>
                          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                            <Text style={styles.header}>
                              {this.state.step === 0 ? header : subHeader}{' '}
                            </Text>
                            {this.totalSteps > STEP_COUNT ? (
                              <Text style={styles.stepTxt}>
                                (Step {this.state.step + STEP_COUNT} of {this.totalSteps}){' '}
                              </Text>
                            ) : (
                              []
                            )}
                          </View>
                          <View
                            style={{
                              ...styles.contactformWrapper,
                              ...marginStyles.mt16,
                            }}
                          >
                            {sectionComps}
                          </View>
                        </View>
                      </View>
                    </>
                  )}
                  {this.state.step === Callback_cta_step ? (
                    <View>
                      {isGIAffiliate(this.props.aff) ? (
                        <View
                          style={{
                            marginTop: 0,
                            marginRight: 4,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: 'row',
                              padding: 8,
                            }}
                          >
                            <View>
                              <CheckBox
                                activeColor={holidayColors.primaryBlue}
                                isChecked={this.state.allowContact}
                                alignMiddle
                                borderColor={holidayColors.gray}
                                boxRadius={4}
                                customContainerStyle={marginStyles.mt0}
                                onPress={this.onPressCheckBox}
                                size={18}
                                tickSize={10}
                              />
                            </View>
                            <View
                              style={{
                                flex: 1,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                              }}
                            >
                              <View style={styles.checkBoxContent}>
                                <Text style={styles.checkBoxText}>{MSG_ALLOW_GI_CONTACT}</Text>
                              </View>
                            </View>
                          </View>
                        </View>
                      ) : getT2QFabFlags()?.showPrivacyPolicy ? (
                        <View
                          style={{
                            marginTop: 0,
                            marginRight: 4,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: 'row',
                              padding: 8,
                            }}
                          >
                            <View>
                              <CheckBox
                                activeColor={holidayColors.primaryBlue}
                                isChecked={this.state.allowContact}
                                alignMiddle
                                borderColor={holidayColors.gray}
                                boxRadius={4}
                                customContainerStyle={marginStyles.mt0}
                                onPress={this.onPressCheckBox}
                                size={18}
                                tickSize={10}
                              />
                            </View>
                            <View
                              style={{
                                flex: 1,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                              }}
                            >
                              <View style={styles.checkBoxContent}>
                                <Text style={styles.checkBoxText}>
                                  I have read and agree to the{' '}
                                </Text>
                                <TouchableOpacity
                                  onPress={() => this.openUrl(USER_AGREEMENT_URL, 'User Agreement')}
                                >
                                  <Text style={styles.linkTxt}>user agreement</Text>
                                </TouchableOpacity>
                                <Text style={styles.checkBoxText}> and </Text>
                                <TouchableOpacity
                                  onPress={() => this.openUrl(PRIVACY_POLICY_URL, 'Privacy Policy')}
                                >
                                  <Text style={styles.linkTxt}>privacy policy</Text>
                                </TouchableOpacity>
                                <Text style={styles.checkBoxText}>.</Text>
                              </View>
                            </View>
                          </View>
                        </View>
                      ) : null}
                    </View>
                  ) : (
                    <View />
                  )}
                  <TouchableOpacity onPress={() => this.onSubmitClick()}>
                    <LinearGradient colors={['#53b2fe', '#065af3']} style={styles.nextBtn}>
                      <Text style={styles.nextBtnTxt}>
                        {this.state.step === Callback_cta_step
                          ? SUBMIT
                          : this.state.step === this.totalSteps - STEP_COUNT
                          ? DONE
                          : CONTINUE}
                      </Text>
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
                {this.state.step !== this.totalSteps - STEP_COUNT &&
                  this.footerText?.length > 0 && (
                    <View style={{ height: 80, marginHorizontal: 10 }}>
                      <View style={styles.footerWrapper}>
                        <View style={{ flex: 1 }}>
                          <Text style={{ ...styles.footerText, flexWrap: 'wrap' }}>
                            {this.footerText}{' '}
                          </Text>
                          <Text
                            style={[
                              AtomicCss.font10,
                              AtomicCss.regularTextFont,
                              { color: 'rgba(0, 15, 33, 0.6)' },
                            ]}
                          >
                            {this.footerSubText}{' '}
                          </Text>
                        </View>
                        {this.imageUrl?.length > 0 ? (
                          <Image
                            source={{ uri: this.imageUrl }}
                            style={{ width: 81, height: 102 }}
                          />
                        ) : (
                          []
                        )}
                      </View>
                    </View>
                  )}
                {this.getAd()}
              </KeyboardAwareScrollView>
            </View>
          )}

        {this.state.cityList && !this.state.showCalendar && (
          <QueryDestinationSelector
            style={{
              position: 'absolute',
              flex: 1,
            }}
            forceShowInput={true}
            destinations={this.state.singleSelectField?.options}
            placeholder={this.state.singleSelectField?.placeholder}
            citySelect={this.onSingleSelectClick}
            onBack={this.handleBackPress}
            singleSelect={true}
            scrollable
            hideLocationIcon={true}
          />
        )}

        {this.state.countryCodeSelector && (
          <CountryList
            data={this.state.countryCodeOptions}
            onCountryClick={this.onCountryCodeClick}
            onBack={this.handleBackPress}
          />
        )}
        {this.state.showCalendar && (
          <HolidayCalendar
            selectedDate={this.state.travelDate}
            onDone={this.updateTravelDate}
            onCalendarBack={this.onCalendarBack}
          />
        )}
      </View>
    );
  }
}

QueryFormDynamic.propTypes = {
  DL: PropTypes.bool.isRequired,
  fromSeo: PropTypes.bool.isRequired,
  packageId: PropTypes.string.isRequired,
  packageName: PropTypes.string.isRequired,
  destination: PropTypes.string.isRequired,
  pageName: PropTypes.string.isRequired,
  funnelStep: PropTypes.string.isRequired,
  dest: PropTypes.string.isRequired,
  branch: PropTypes.string.isRequired,
  cmp: PropTypes.string.isRequired,
  query: PropTypes.string.isRequired,
  campaign: PropTypes.string.isRequired,
  travelDate: PropTypes.string.isRequired,
  departureCity: PropTypes.string.isRequired,
  onBackPressed: PropTypes.func.isRequired,
  onDynamicFormError: PropTypes.func.isRequired,
  pkgType: PropTypes.string.isRequired,
  dynamicPackageId: PropTypes.string.isRequired,
  aff: PropTypes.string,
};

QueryFormDynamic.defaultProps = {
  aff: null,
};
export default withBackHandler(QueryFormDynamic);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingBottom: 10,
  },
  updateContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  inputWrapper: {
    flexDirection: 'row',
  },
  ad : {
    width: getDimensionsForQueryFormAd()?.width,
    height: getDimensionsForQueryFormAd()?.height,
  },
  budgetWrapper: {
    paddingHorizontal: 9,
    paddingVertical: 11,
    backgroundColor: '#d3f1e5',
    borderRadius: 4,
    marginTop: 12,
  },
  budgetText: {
    fontSize: 14,
    fontFamily: 'Lato-Bold',
    color: '#1a7971',
  },
  formStyle: {
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 30,
  },
  changeBtn: {
    padding: 2,
    alignSelf: 'flex-end',
  },
  changeText: {
    color: '#008cff',
    fontSize: 12,
    alignSelf: 'center',
    fontFamily: 'Lato-Bold',
  },
  backWrapper: {
    padding: 16,
    position: 'absolute',
  },
  iconBack: {
    height: 16,
    width: 16,
  },
  headerWrapper: {
    height: 40,
  },
  inputText: {
    color: '#4a4a4a',
    paddingLeft: 14,
    marginTop: 6,
    paddingTop: 10,
    minHeight: 42,
    paddingRight: 14,
    paddingBottom: 10,
    fontSize: 14,
    fontFamily: 'Lato-Regular',
    backgroundColor: '#00000000',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: 'rgb(128, 197, 255)',
  },
  input: {
    borderRadius: 4,
    width: '60%',
    flex: 0.6,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    color: '#4a4a4a',
    paddingVertical: 5,
    fontFamily: 'Lato-Bold',
    fontSize: 14,
  },
  checkBoxText: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: colors.black,
  },
  dateText: {
    color: '#4a4a4a',
    fontSize: 14,
    fontFamily: 'Lato-Regular',
  },
  labelContainer: {
    marginBottom: 6,
  },
  labelText: {
    color: '#4a4a4a',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
  },
  errorText: {
    color: '#eb2026',
    marginTop: 4,
    fontSize: 12,
    fontFamily: 'Lato-Regular',
  },
  btnWrapper: {
    paddingHorizontal: 9,
  },
  asterisk: {
    color: '#ff0000',
    fontSize: 12,
    fontFamily: 'Lato-Bold',
  },
  linkTxt: {
    fontFamily: 'Lato-Bold',
    color: '#008cff',
    fontSize: 14,
    marginBottom: 3,
  },
  checkBoxContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    lineHeight: 18,
    flex: 1,
    marginLeft: 8,
  },
  queryFormWrapper: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollWrapper: {
    padding: 15,
    paddingTop: 0,
  },
  pageHeader: {
    fontSize: 24,
    color: 'black',
    fontFamily: 'Lato-Bold',
    paddingRight: 30,
  },
  iconCloseWrap: {
    backgroundColor: 'white',
    alignItems: 'flex-end',
  },
  iconClose: {
    width: 15,
    height: 15,
  },
  stepText: {
    fontSize: 12,
    color: 'rgba(0, 0, 0, 0.8)',
    fontFamily: 'Lato-Regular',
    lineHeight: 20,
  },
  userInfoText: {
    color: '#249995',
    fontSize: 13,
    fontFamily: 'Lato-Bold',
    lineHeight: 20,
  },
  selectedHdrTxt: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  changeCityWrap: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 2,
  },
  queryFormClose: {
    borderBottomWidth: 1,
    borderColor: '#e5e5e5',
    paddingBottom: 10,
    marginBottom: 10,
  },
  step1ValidatedTxt: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  stepClickableTxt: {
    textDecorationLine: 'underline',
    ...fontStyles.labelLargeBold,
    color: holidayColors.primaryBlue,
    lineHeight: 25,
  },
  header: {
    ...fontStyles.labelLargeBold,
    color: holidayColors.gray,
  },
  stepTxt: {
    ...fontStyles.labelLargeBold,
    color: holidayColors.gray,
    fontStyle: 'italic',
  },
  contactformWrapper: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    paddingTop: 15,
    paddingBottom: 5,
    paddingHorizontal: 15,
    marginBottom: 15,
  },
  nextBtn: {
    padding: 13,
    width: '100%',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  nextBtnTxt: {
    fontSize: 16,
    fontFamily: 'Lato-Black',
    color: 'white',
  },
  inputGroupWrapper: {
    borderWidth: 1,
    alignItems: 'center',
    borderRadius: 4,
    paddingVertical: 2,
    paddingHorizontal: 5,
    display: 'flex',
    flex: 1,
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconArrow: {
    width: 14,
    height: 8,
  },
  selectText: {
    fontSize: 14,
    color: 'black',
    fontFamily: fonts.bold,
  },
  selectBox: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
  },
  countryCodeText: {
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Lato-Bold',
    fontSize: 14,
  },
  footerWrapper: {
    position: 'absolute',
    bottom: 0,
    paddingLeft: 15,
    height: 100,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  footerText: {
    color: '#445978',
    fontSize: 14,
    paddingBottom: 5,
    fontFamily: 'Lato-Bold',
  },
  customStyles: {
    wrapperStyle: {
      width: '100%',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      elevation: 0,
    },
  },
  labelTextField: {
    fontWeight: '700',
  },
  labelDropdown: {
    fontWeight: '700',
  },
  inputTextField: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
    ...paddingStyles.pt20,
  },
  inputDropdownField: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
  },
  mandatoryTextField: {
    color: holidayColors.red,
  },
  errorWrapTextField: {
    marginTop: 5,
    minHeight: 22,
  },
  errorMessageTextField: {
    color: holidayColors.red,
    ...fontStyles.labelSmallRegular,
  },
  dropdownValueStyle: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
    ...Platform.select({
      ios: { ...marginStyles.mt8 },
      android: { ...marginStyles.mt0 },
    }),
  },
  endIconStyle: {
    tintColor: holidayColors.primaryBlue,
    width: 20,
    height: 20,
  },
  emptyLabel: {
    ...Platform.select({
      ios: {
        ...fontStyles.labelSmallBold,
      },
      android: {
        ...fontStyles.labelBaseBold,
      },
    }),
  },
  mandatory: {
    color: holidayColors.red,
  },
});
