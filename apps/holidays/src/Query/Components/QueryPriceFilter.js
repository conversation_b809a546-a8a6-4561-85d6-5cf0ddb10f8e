import React, { useState } from 'react';
import { StyleSheet, View, Text, Dimensions, Platform } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import MultiSlider from './sliderImport';
import {isRawClient} from "../../utils/HolidayUtils";

const QueryPriceFilter = ({ budgetPrefChange, min, max, isCurrency }) => {
  const [multiSliderValue, setMultiSliderValue] = useState([min, max]);
  const multiSliderValuesChange = (values) => {
    setMultiSliderValue(values);
    budgetPrefChange(values);
  };

  const CustomMarkerLeft = () => {
    return (
      <View style={styles.marker}>
        <View style={styles.markerDot} />
          <View>
            <Text style={[styles.markerText,styles.markerTextLeft]}>{isCurrency ? '₹' : ''}{multiSliderValue[0]}</Text>
          </View>
      </View>
    );
  };

  const CustomMarkerRight = () => {
    return (
      <View style={styles.marker}>
        <View style={styles.markerDot} />
        <View>
          <Text style={[styles.markerText,styles.markerTextRight]}>{isCurrency ? '₹' : ''}{multiSliderValue[1]}</Text>
        </View>
      </View>
    );
  };

  const getWebMultiSlider = () => {
  return (
      <MultiSlider
        multiSliderValue={multiSliderValue}
        min={min}
        max={max}
        multiSliderValuesChange={multiSliderValuesChange}
        isCurrency={isCurrency}
      />
    );
  };

  const getMobileMultiSlider = () => (
        <MultiSlider
          values={[multiSliderValue[0], multiSliderValue[1]]}
          trackStyle={{
            height: 6,
            backgroundColor: '#e6e6e6',
          }}
          selectedStyle={{
            backgroundColor: '#008cff',
          }}
          onValuesChange={multiSliderValuesChange}
          sliderLength={Dimensions.get('window').width - 100}
          min={min}
          max={max}
          step={1}
          allowOverlap={false}
          snapped
          isMarkersSeparated={true}
          customMarkerLeft={CustomMarkerLeft}
          customMarkerRight={CustomMarkerRight}
        />
  );

  return (
    <View>
      {isCurrency ?
      <View>
        <Text style={[AtomicCss.font14, AtomicCss.greyText]}>{ `Per person : ₹${multiSliderValue[0]} - ₹${multiSliderValue[1]}`}</Text>
      </View> : []
      }
      <View style={[styles.multiSliderWrap]}>
        {isRawClient() ? getWebMultiSlider() : getMobileMultiSlider()}
      </View>
    </View>
  );
};

export default QueryPriceFilter;

var styles = StyleSheet.create({
  multiSliderWrap: {
    paddingHorizontal: 15,
    paddingTop: 10,
    paddingBottom: 20,
    position: 'relative',
  },
  marker:{
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  markerDot: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e5e5e5',
  },

  markerText: {
    width: 60,
    fontSize: 14,
    color: '#4a4a4a',
    fontFamily: 'Lato-Regular',
    position: 'absolute',
    alignSelf: 'flex-start',
    textAlign: 'center',
    backgroundColor: 'white',bottom: -20,
  },
  markerTextLeft: {
    left: -30,
  },
  markerTextRight: {
    right: -35,
  },
});
