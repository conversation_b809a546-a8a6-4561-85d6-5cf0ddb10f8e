import { callPDTApiQueryNew } from '../../utils/HolidayPDTTracking';
import { populateQueryParams } from '../../utils/HolidayOmnitureUtils';
import {
  fetchAffiliateSuffix,
  getOmniPageName,
  holidayTrackingClickEvent,
  holidayTrackingLoadEvent,
  holidayTrackingParameters,
} from '../../utils/HolidayTrackingUtils';
import { LISTING_TRACKING_PAGE_NAME } from '../../Listing/ListingConstants';
import { GROUPING_TRACKING_PAGE_NAME } from '../../Grouping/HolidayGroupingConstants';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { removeSubstringAfterHyphen } from '../../utils/HolidayUtils';
import { DETAIL_TRACKING_PAGE_NAME } from '../../PhoenixDetail/DetailConstants';
import { PRESALES_DETAIL_PAGE } from '../../MimaPreSales/constants/preSalesMimaConstants';
import { REVIEW_PDT_PAGE_NAME } from '../../Review/HolidayReviewConstants';
import { logHolidaysLandingPDTEvents } from '../../LandingNew/Utils/HolidayLandingPdtTrackingUtils';
import { logHolidaysGroupingPDTEvents } from '../../PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils';
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';

/* Following are the values for PDT_DATA OBJECT
    pdtData =  {
      pageDataMap: {},
      interventionDetails: {},
      eventType: '',
      activity: '',
      requestId: '',
      branch: ''
    }
  } */
export const trackHolidayQueryLoadEvent = async ({
  logOmni = false,
  omniPageName = '',
  pdtData = {},
  omniData = {},
  prop66 = '',
}) => {
  let pageName = '';
  let params = {};

  const triggerPageName =
  omniPageName === LISTING_TRACKING_PAGE_NAME || removeSubstringAfterHyphen(omniPageName) === GROUPING_TRACKING_PAGE_NAME
        ? LISTING_TRACKING_PAGE_NAME
        : removeSubstringAfterHyphen(omniPageName);

  if (logOmni) {
    const isWG = pdtData?.pageDataMap?.requestDetails?.isWG;
    populateQueryParams(params, pdtData.pageDataMap, pdtData.branch, prop66, {
      omniData,
    });
    pageName = getOmniPageName(
      triggerPageName,
      pdtData?.branch || '',
      isWG,
      await fetchAffiliateSuffix(),
    );
  }
  holidayTrackingLoadEvent({
    logOmni,
    params,
    pdtData,
    parametersToSet: holidayTrackingParameters,
    isCallPDTFunction: true,
    pageVisitPageName: pageName,
    pageName: triggerPageName,
    trackingPageName: pdtData?.omniPageName108 || omniPageName,
    branch: pdtData?.branch,
    callPDTApiFunction: callPDTApiQueryNew,
  });
};

export const trackHolidayQueryClickEvent = ({
  omniPageName = '',
  omniEventName = '',
  prop66 = '',
  omniData = {},
  pdtData = {},
}) => {
  const params = {};
  const isWG = pdtData?.pageDataMap?.requestDetails?.isWG;
  populateQueryParams(params, pdtData.pageDataMap, pdtData.branch, prop66, {
    omniData,
  });
  const pageName = removeSubstringAfterHyphen(omniPageName); // function is used to remove any -version present in name
  holidayTrackingClickEvent({
    callPDTApiFunction: callPDTApiQueryNew,
    pdtData,
    clickEventParameters: {
      pageName:
        pageName === LISTING_TRACKING_PAGE_NAME || pageName === GROUPING_TRACKING_PAGE_NAME
          ? LISTING_TRACKING_PAGE_NAME
          : pageName,
      trackingPageName: omniPageName,
      branch: pdtData?.branch,
      eventName: omniEventName,
      params,
      isWG,
      parametersToSet: holidayTrackingParameters,
    },
  });
};

export const trackQueryPDTClickEvents = ({ value, pageName, actionType }) => {
  return null;
  switch (pageName) {
    case LANDING_PAGE_NAME:
      logHolidaysLandingPDTEvents({ value, actionType });
      break;
    case LISTING_TRACKING_PAGE_NAME || GROUPING_TRACKING_PAGE_NAME:
      logHolidaysGroupingPDTEvents({ value, actionType });
      break;
    case DETAIL_TRACKING_PAGE_NAME || PRESALES_DETAIL_PAGE:
      logPhoenixDetailPDTEvents({ value, actionType });
      break;
    case REVIEW_PDT_PAGE_NAME: 
      logHolidayReviewPDTClickEvents({actionType, value});
      break;
    default: 
      break;
  }
};