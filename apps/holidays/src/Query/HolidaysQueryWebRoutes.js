import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import HolidayQueryForm from './Components/HolidayQueryForm';
import {withRouterState} from '../../../../../web/WebRouter';
import url from 'url';
import {isEmptyString} from '../utils/HolidayUtils';
import HolidaysWebRoute from '../HolidayWebRoute';

class HolidaysQueryWeb extends HolidaysWebRoute {
  constructor(props) {
    super(props, 'holidaysQueryForm');
    const urlObj = url.parse(window.location.href, window.location.search);
    const {query} = urlObj;
    let queryParam = window.location.search;
    if (!isEmptyString(queryParam)) {
      queryParam = queryParam.replace('?', '');
    }
    if (
      query &&
      (!isEmptyString(queryParam) ||
        query.dest ||
        query.branch ||
        query.cmp ||
        query.fromSeo ||
        query.pageName ||
        query.seoQuery ||
        query.banner)
    ) {
      this.state = {
        dest: query.dest,
        branch: query.branch,
        cmp: query.cmp,
        fromSeo: query.fromSeo ? query.fromSeo : query.seoQuery,
        query: queryParam,
        pageName: query.pageName,
        DL: !query.nodl,
        baner: query.banner,
      };
    }
  }

  render() {
    if (this.state && this.state.DL) {
      return (
        <HolidayQueryForm {...this.state}/>
      );
    } else {
      return (
        <HolidayQueryForm {...this.props}/>
      );
    }
  }
}

const mapStateToProps = state => ({
    ...state,
});


export const HolidaysQueryContainer = connect(mapStateToProps, null)(HolidaysQueryWeb);

const HolidaysQueryRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/sendQuery30!packageSendQueryForm"
                   component={withRouterState(HolidaysQueryContainer)}/>
            <Route exact path="/holidays/mobileSendQuery!packageSendQueryForm"
                   component={withRouterState(HolidaysQueryContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysQueryRoutes);
