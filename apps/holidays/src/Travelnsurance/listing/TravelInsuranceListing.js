import React, { useEffect, useRef, useState } from 'react';
import {
  FlatList,
  Platform,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import InsuranceCard from '../components/insuranceCard';
import useBackHandler from '../../hooks/useBackHandler';

import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { holidayColors } from '../../Styles/holidayColors';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import {
  holidayNavigationPop,
  holidayNavigationPush,
} from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import { REVIEW_OVERLAYS } from '../../PhoenixReview/Components/ReviewOverlays';

/* Actions */
import { hideReviewOverlays, showReviewOverlay } from '../../Review/Actions/reviewOverlayActions';

/* Components */
import PageFooter from '../components/pageFooter';
import UpdateView from '../bottomsheets/UpdateView';
import PageHeader from '../../Common/Components/PageHeader';
import DeselectedBottomSheet from '../bottomsheets/DeselectedBottomSheet';
import TagComponent from '../components/TagComponent';
import { INSURANCE_ACTIONS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const TravelInsuranceListing = ({
  insuranceAddonDetail = {},
  trackReviewLocalClickEvent,
  hideReviewOverlays,
  showReviewOverlay,
  onSelect,
}) => {
  const { addonPriceMap = {}, addons = [] } = insuranceAddonDetail || {};
  const priceAddOnMap = addonPriceMap || {};

  const previousSelectedPosition = useRef(-1);
  const previousSelectedInsurance = useRef();

  const [selectPosition, setSelectedPosition] = useState(-1);

  const selectedAddOnItem = useRef();

  const [confirmationDialog, setConfirmationDialog] = useState(false);

  const [deselectedDialog, setDeselectedDialog] = useState(false);

  useEffect(() => {
    addons?.map((item, index) => {
      if (item.isSelected) {
        previousSelectedPosition.current = index;
        previousSelectedInsurance.current = item;
        setSelectedPosition(index);
        selectedAddOnItem.current = item;
      }
    });
  }, []);
  const hardwareBackPress = () => {
    goBack();
    return true;
  };

  useBackHandler(hardwareBackPress);

  const captureClickEvents = ({ eventName = '', prop1 = '' }) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: prop1,
    });
    trackReviewLocalClickEvent(eventName, '', { prop1 });
  };

  const goBack = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.CLOSE, prop1: INSURANCE_PAGES.LISTING });
    if (selectPosition == previousSelectedPosition.current) {
      holidayNavigationPop({
        navigationFunction: goBack,
        overlayKeys: [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE],
        hideOverlays: hideReviewOverlays,
      });
    } else {
      setConfirmationDialog(true);
    }
  };

  const onItemClick = (item, index) => {
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_DETAILS,
      overlayKey: REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE,
      showOverlay: showReviewOverlay,
      hideOverlays: hideReviewOverlays,
      props: {
        insuranceAddOn: item,
        onSelect,
        priceMap: priceAddOnMap[item.id],
        selectedFromPrevPage: index === selectPosition,
        trackReviewLocalClickEvent,
      },
    });
  };

  const onSelectInsurance = (insuranceAddon, position) => {
    if (position !== selectPosition) {
      selectedAddOnItem.current = insuranceAddon;
      setSelectedPosition(position);
      captureClickEvents({ eventName: INSURANCE_ACTIONS.SELECT, prop1: INSURANCE_PAGES.LISTING });
    } else {
      selectedAddOnItem.current = insuranceAddon;
      captureClickEvents({ eventName: INSURANCE_ACTIONS.UNSELECT, prop1: INSURANCE_PAGES.LISTING });
      setSelectedPosition(-1);
      if (previousSelectedPosition.current >= 0) {
        setDeselectedDialog(true);
      }
    }
  };

  const onUpdateClick = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.UPDATE, prop1: INSURANCE_PAGES.LISTING });
    if (selectedAddOnItem.current) {
      if (selectedAddOnItem.current.isSelected) {
        holidayNavigationPop({
          overlayKeys: [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE],
          hideOverlays: hideReviewOverlays,
        });
      } else {
        onSelect(selectedAddOnItem.current, true);
        holidayNavigationPop({
          overlayKeys: [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE],
          hideOverlays: hideReviewOverlays,
        });
      }
    }
  };
  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.5}
        style={styles.cardContainer}
        onPress={() => {
          onItemClick(item, index);
        }}
      >
        {!!insuranceAddonDetail.tag ? <TagComponent text={insuranceAddonDetail.tag} /> : null}
        <InsuranceCard
          insuranceAddOn={item}
          isSelected={selectPosition === index}
          priceMap={priceAddOnMap[item.id]}
          trackReviewLocalClickEvent={trackReviewLocalClickEvent}
          fromListing={true}
          onSelect={() => {
            onSelectInsurance(item, index);
          }}
        />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.viewContainer}>
      <PageHeader
        showBackBtn
        showShadow
        title={'Travel + Medical Insurance'}
        onBackPressed={goBack}
        containerStyles={paddingStyles.pa16}
      />

      {insuranceAddonDetail.addons && insuranceAddonDetail.addons.length > 0 ? (
        <FlatList
          contentContainerStyle={{ paddingBottom: 100 }}
          data={insuranceAddonDetail?.addons}
          renderItem={renderItem}
          key={(item, index) => {
            return index;
          }}
        />
      ) : null}

      {selectPosition > -1 && selectPosition !== previousSelectedPosition.current ? (
        <PageFooter
          btnTxt={'Update'}
          totalTravellers={priceAddOnMap?.[selectedAddOnItem?.current?.id]?.totalPackagePriceUnit}
          discountedPrice={priceAddOnMap?.[selectedAddOnItem?.current?.id]?.totalPackagePrice}
          onUpdateClick={() => {
            onUpdateClick();
          }}
        />
      ) : null}

      <UpdateView
        modalVisible={confirmationDialog}
        trackReviewLocalClickEvent={trackReviewLocalClickEvent}
        onNotNowClicked={() => {
          holidayNavigationPop({
            overlayKeys: [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE],
            hideOverlays: hideReviewOverlays,
          });
          setConfirmationDialog(false);
        }}
        onUpdateNowClicked={() => {
          onSelect(selectedAddOnItem.current, true);
          holidayNavigationPop({
            overlayKeys: [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE],
            hideOverlays: hideReviewOverlays,
          });
        }}
      />

      <DeselectedBottomSheet
        modalVisible={deselectedDialog}
        trackReviewLocalClickEvent={trackReviewLocalClickEvent}
        removeInsuranceClicked={() => {
          setDeselectedDialog(false);
          onSelect(previousSelectedInsurance.current, true);
          holidayNavigationPop({
            overlayKeys: [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE],
            hideOverlays: hideReviewOverlays,
          });
        }}
        onKeepSelectionClicked={() => {
          setDeselectedDialog(false);
          setSelectedPosition(previousSelectedPosition.current);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mt16,
    ...marginStyles.mh12,
    marginHorizontal: 8,
    backgroundColor: holidayColors.white,
    overflow: 'hidden',
    ...paddingStyles.pa16,
  },
  viewContainer: {
    height: '100%',
    backgroundColor: holidayColors.lightGray2,
  },
});

const mapDispatchToProps = {
  hideReviewOverlays,
  showReviewOverlay,
};
export default connect(null, mapDispatchToProps)(TravelInsuranceListing);
