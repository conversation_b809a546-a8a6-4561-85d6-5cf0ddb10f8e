import { Image, StyleSheet, Text, View } from "react-native";
import React from "react";
import { holidayColors } from "../../Styles/holidayColors";
import { fontStyles } from "../../Styles/holidayFonts";
import { paddingStyles, marginStyles } from "../../Styles/Spacing";


const ItemPolicyInfo = ({ imageUrl, title, subtitle, price }) => {
  return <View style={styles.container}>
    {imageUrl ? <View style={styles.imageContainer}>
      <Image source={{uri:imageUrl}} style={styles.image} />
    </View>: null}
    <View style={styles.textContainer}>
      {title ? <Text style={styles.title}>{title}</Text> : null}
      {subtitle ? <Text style={styles.subtitle}>{subtitle}</Text> : null}
    </View>
    {price ? <View style={styles.priceContainer}>
      <Text style={styles.price}>{price}</Text>
      {/* <HTMLView*/}
      {/*  value={price}*/}
      {/*/> */}
    </View> : null}
  </View>;
};


const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
    flexDirection: "row",
    ...marginStyles.mb12,
  },
  image: {
    ...marginStyles.ma4,
    width: 20,
    height: 20,
    resizeMode: "cover",
  },
  textContainer: {
    flex: 1,
    ...paddingStyles.ph4,
  },
  title: {
    ...fontStyles.labelBaseBlack,
    ...marginStyles.mt4,
    color: holidayColors.black,
  },
  subtitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    // lineHeight: 18,
  },

  price: {
    ...fontStyles.labelBaseBlack,
    color:holidayColors.black,
    ...marginStyles.mt4,
  },

  priceContainer: {
    height:"100%",
    width:"30%",
    ...paddingStyles.pl20,
    justifyContent:'flex-start',
    alignSelf: "flex-end",
    alignItems: "flex-end",
  },
});

export default ItemPolicyInfo;

