import { StyleSheet, Text, View } from "react-native";
import AtomicCss from "@mmt/legacy-commons/Styles/AtomicCss";
import { rupeeFormatterUtils } from "../../utils/HolidayUtils";
import PrimaryButton from "../../Common/Components/Buttons/PrimaryButton";
import React from "react";
import { colors } from "@mmt/legacy-commons/Styles/globalStyles";
import { marginStyles, paddingStyles } from "../../Styles/Spacing";
import { fontStyles } from "../../Styles/holidayFonts";
import { holidayColors } from "../../Styles/holidayColors";
import { holidayBorderRadius } from "../../Styles/holidayBorderRadius";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { isMobileClient } from "../../utils/HolidayUtils";


const PageFooter = ({ discountedPrice, totalTravellers, btnTxt, onUpdateClick , isDisabled, containerStyle = {}}) => {

  let insets = {};
  if (isMobileClient()) {
    insets = useSafeAreaInsets();
  }

  return <View style={[styles.pageFooter, { paddingBottom: insets.top + 10 }, containerStyle]}>
    <View style={styles.footerLeft}>
      <View style={styles.netPriceContainer}>
        <Text style={styles.netPrice}>
          {rupeeFormatterUtils(discountedPrice)}
        </Text>
      </View>
      <Text style={styles.grandTotal}>
        {totalTravellers}{" "}
      </Text>
    </View>
    <View style={styles.footerRight}>
      <PrimaryButton
        isDisable={isDisabled}
        buttonText={btnTxt?.toUpperCase()}
        handleClick={onUpdateClick}
        btnContainerStyles={styles.footerButton}
      />
    </View>
  </View>;


};

export default PageFooter;

const styles = StyleSheet.create({
  pageFooter: {
    backgroundColor: colors.black28,
    paddingVertical: 10,
    marginBottom: 4,
    ...paddingStyles.ph16,
    flexDirection: "row",
    alignItems: "center",

  },
  footerLeft: {},
  footerRight: {
    marginLeft: "auto",
  },
  footerButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    ...paddingStyles.ph16,
  },
  upperHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    opacity: 0.7,
  },
  netPriceContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...AtomicCss.flexWrap,
    ...paddingStyles.pt4,
    ...paddingStyles.pb2,
  },
  netPrice: {
    ...fontStyles.headingBase,
    color: holidayColors.white,
  },
  fareDetailsWrap: {
    borderWidth: 1,
    ...paddingStyles.pv2,
    ...paddingStyles.ph8,
    flexDirection: "row",
    alignItems: "center",
    borderColor: holidayColors.lightGray2,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.ml16,
  },
  fareDetailsText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallRegular,
  },
  grandTotal: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.disableGrayBg,
    opacity: 0.7,
  },
  iconArrowUp: {
    width: 8.5,
    height: 5,
    resizeMode: "cover",
  },
  grossPrice: {
    ...fontStyles.labelSmallRegular,
    color: "rgba(256,256,256,0.7)",
    textDecorationLine: "line-through",
    ...marginStyles.ml4,
  },
});
