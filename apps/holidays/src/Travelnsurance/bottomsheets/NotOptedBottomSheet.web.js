import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux' //'@mmt/react-redux';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { holidayNavigationPush } from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import { REVIEW_OVERLAYS } from '../../PhoenixReview/Components/ReviewOverlays';

/* Components */
import SecondaryButton from '../../Common/Components/Buttons/SecondaryButton';
import PrimaryButton from '../../Common/Components/Buttons/PrimaryButton';
import CardFooter from '../components/cardFooter';
import InsuranceCard from '../components/insuranceCard';
import BottomSheet from '../../PhoenixReview/Components/BottomSheet';
import BottomSheetOverlay, { BottomSheetCross } from '../../Common/Components/BottomSheetOverlay';

/* Icons */
import icCross from '@mmt/legacy-assets/src/ic_cross__gray.webp';

/* Actions */
import {
  showReviewOverlay,
  hideReviewOverlays,
} from '../../Review/Actions/reviewOverlayActions'
import { INSURANCE_ACTIONS, INSURANCE_BOTTOMSHEETS,  } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { isEmpty } from 'lodash';

const NotOptedBottomSheet = ({
  insuranceAddonDetail,
  title,
  positiveButtonText,
  negativeButtonText,
  onPositiveClick,
  onNegativeClick,
  onCancel,
  trackReviewLocalClickEvent,
  showReviewOverlay,
  hideReviewOverlays,
}) => {
  // if (!insuranceAddonDetail) {    //todo remove if not needed
  //   return null;
  // }

  // if (!insuranceAddonDetail?.addons) {
  //   return null;
  // }

  if (insuranceAddonDetail?.addons?.length <= 0) {
    return null;
  }

  const [modalVisible, setModalVisible] = useState(true);

  const captureClickEvents = ({ eventName = '', prop1 = '', actionType = {} }) => {
    logHolidayReviewPDTClickEvents({
      actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: prop1,
      shouldTrackToAdobe:isEmpty(actionType) 
    });
    trackReviewLocalClickEvent(eventName, '', { prop1 });
  };

  useEffect(() => {
    captureClickEvents({
      eventName: INSURANCE_ACTIONS.SHOWN,
      prop1: INSURANCE_BOTTOMSHEETS.PROMT_ADD,
      actionType: PDT_EVENT_TYPES.contentSeen,
    });
  }, []);
  const onPositiveButtonPress = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.SELECT, prop1: INSURANCE_BOTTOMSHEETS.PROMT_ADD })
    setModalVisible(false);
    onPositiveClick(insuranceAddonDetail.addons[0]);
  };
  const onViewMoreClick = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.MOREPLANS, prop1: INSURANCE_BOTTOMSHEETS.PROMT_ADD })
    
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_LISTING,
      overlayKey: REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE,
      showOverlay: showReviewOverlay,
      hideOverlays: hideReviewOverlays,
      props: {
        insuranceAddonDetail,
        trackReviewLocalClickEvent,
      },
    });

    // HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_LISTING, {
    //   insuranceAddonDetail,
    //   trackReviewLocalClickEvent,
    // });

    setModalVisible(false);
    onCancel();
  };
  const onNegativeButtonPress = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.NOT_SELECT, prop1: INSURANCE_BOTTOMSHEETS.PROMT_ADD })
    setModalVisible(false);
    onNegativeClick();
  };

  const handleCloseClick = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.CLOSE, prop1: INSURANCE_BOTTOMSHEETS.PROMT_ADD })
    setModalVisible(false);
    onCancel();
  };

  return (
    <BottomSheetOverlay
      toggleModal={handleCloseClick}
      visible={modalVisible}
      title={title || 'Would you like to opt for travel insurance?'}
      headingTextStyle={styles.title}
      containerStyles={styles.containerStyles}
    >
      <View style={styles.container}>
        {insuranceAddonDetail.addons?.length > 1 && <Text style={styles.recommendedPlanTitle}>Recommended Plan</Text>}
        <View style={styles.cardContainer}>
          <InsuranceCard
            insuranceAddOn={insuranceAddonDetail.addons?.[0]}
            trackReviewLocalClickEvent={trackReviewLocalClickEvent}
            priceMap={insuranceAddonDetail.addonPriceMap[insuranceAddonDetail.addons?.[0].id]}
            fromPopUp={true}
            onCancel={onCancel}
            setModalVisible={setModalVisible}
            hideDisclaimer
            hideSelect
          />

          {insuranceAddonDetail.addons.length > 1 ? <View style={styles.separator} /> : null}

          {insuranceAddonDetail.addons.length > 1 ? (
            <TouchableOpacity activeOpacity={0.5} onPress={onViewMoreClick}>
              <CardFooter count={insuranceAddonDetail.addons.length - 1} />
            </TouchableOpacity>
          ) : null}
        </View>

        <PrimaryButton
          isDisable={false}
          buttonText={positiveButtonText?.toUpperCase() || 'YES, ADD INSURANCE'}
          handleClick={onPositiveButtonPress}
          btnContainerStyles={styles.footerButton}
          activeOpacity={0.7}
        />
        <SecondaryButton
          buttonText={negativeButtonText?.toUpperCase() || 'NO, CONTINUE ANYWAY'}
          handleClick={onNegativeButtonPress}
          btnContainerStyles={styles.ctaContainer}
          activeOpacity={0.5}
        />
      </View>
    </BottomSheetOverlay>
  );
};

const mapDispatchToProps = {
  showReviewOverlay,
  hideReviewOverlays,
};
export default connect(null, mapDispatchToProps)(NotOptedBottomSheet);

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pb20,
  },
  titleContainer: {
    flexDirection: 'row',
  },
  title: {
    flex: 1,
  },

  cardContainer: {
    ...paddingStyles.pa16,
    ...paddingStyles.pb12,
    ...marginStyles.mv8,
    marginTop: 8,
    borderWidth: 1, // Width of the border
    borderColor: holidayColors.grayBorder, // Color of the border
    borderStyle: 'solid', // Style of the border: 'solid', 'dotted', 'dashed'
    ...holidayBorderRadius.borderRadius16, // Border radius to make it rounded
  },
  separator: {
    ...marginStyles.mv6,
    height: 1,
    backgroundColor: holidayColors.grayBorder,
  },
  recommendedPlanTitle: {
    ...marginStyles.mt16,
    ...fontStyles.labelMediumBold,
    lineHeight: 19.2,
    color: holidayColors.gray,
  },

  footerButton: {
    ...marginStyles.mt16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...paddingStyles.ph16,
  },
  handleCloseStyle: {
    backgroundColor: holidayColors.lightGray,
    ...holidayBorderRadius.borderRadius16,
    width: 24,
    height: 24,
    position: 'absolute',
    right: 0,
    ...paddingStyles.pa6,
    marginLeft: 'auto',
    ...marginStyles.ma18,
  },
  icCrossStyle: {
    width: 12,
    height: 12,
    tintColor: holidayColors.white,
  },
  ctaContainer: {
    ...holidayBorderRadius.borderRadius4,
    ...paddingStyles.pt16,
    ...paddingStyles.pb16,
    color: holidayColors.primaryBlue,
    textAlign: 'center',
    ...fontStyles.labelMediumBlack,
    lineHeight: 19,
    ...marginStyles.mt12,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    borderStyle: 'solid',
    ...holidayBorderRadius.borderRadius4,
  },
  containerStyles: {
    ...paddingStyles.pa16
  }
});
