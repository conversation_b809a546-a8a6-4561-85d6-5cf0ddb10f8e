import React, { useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { isEmpty } from 'lodash';
import { connect } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import { holidayColors } from '../../Styles/holidayColors';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import {
  getReviewComponentVisitResponse,
  sectionTrackingPageNames,
  setReviewComponentVisit,
} from '../../PhoenixReview/Utils/ReviewVisitTracking';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { holidayNavigationPush } from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';

/* Components */
import TagComponent from '../components/TagComponent';
import PersuationInfo from '../components/persuationInfo';
import InsuranceCard from '../components/insuranceCard';
import CardFooter from '../components/cardFooter';
import CardHeader from '../components/cardHeader';
import { INSURANCE_ACTIONS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
/* Actions */
import { showReviewOverlay, hideReviewOverlays } from '../../Review/Actions/reviewOverlayActions';
import { REVIEW_OVERLAYS } from '../../PhoenixReview/Components/ReviewOverlays';
import { SUB_PAGE_NAMES } from '../../HolidayConstants';
import { ADDON_TRACKING_VALUES } from '../../Common/Components/VisaProtectionPlan/VPPConstant';

const TravelInsuranceCard = ({
  insuranceAddonDetail,
  onSelect,
  trackReviewLocalClickEvent,
  fromListing = false,
  showReviewOverlay,
  hideReviewOverlays,
}) => {
  if (insuranceAddonDetail.addons.length <= 0) {
    return;
  }
  const captureClickEvents = ({ eventName = '', prop1 = '', actionType = {} }) => {
    logHolidayReviewPDTClickEvents({
      actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: SUB_PAGE_NAMES.INSAURANCE_DETAIL,
      shouldTrackToAdobe:isEmpty(actionType)
    });
    trackReviewLocalClickEvent(eventName, '', { prop1 });
  };
  const onViewMoreClick = () => {
    const prop1Val = fromListing ? INSURANCE_PAGES.LISTING : INSURANCE_PAGES.BASECARD;
    captureClickEvents({ eventName: INSURANCE_ACTIONS.MOREPLANS, prop1: prop1Val });
    // HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_LISTING, {
    //   insuranceAddonDetail,
    //   trackReviewLocalClickEvent,
    // });
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_LISTING,
      overlayKey: REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE,
      showOverlay: showReviewOverlay,
      hideOverlays: hideReviewOverlays,
      props: {
        insuranceAddonDetail,
        trackReviewLocalClickEvent,
        onSelect,
        fromListing,
      },
    });
  };
  useEffect(() => {
    let sectionVisitResponse = getReviewComponentVisitResponse(
      sectionTrackingPageNames.PHOENIX_REVIEW_PAGE,
    );
    if (!sectionVisitResponse?.[sectionTrackingPageNames.PHOENIX_REVIEW_PAGE]) {
      setReviewComponentVisit({
        pageName: sectionTrackingPageNames.PHOENIX_REVIEW_PAGE,
        value: sectionVisitResponse,
      });
    const selectedValue = insuranceAddonDetail?.addons?.[0]?.isSelected ? `${INSURANCE_ACTIONS.SHOWN}`+ `|${ADDON_TRACKING_VALUES.PRE_SELECT}` :INSURANCE_ACTIONS.SHOWN
      captureClickEvents ( {
        eventName : selectedValue,
        prop1:INSURANCE_PAGES.BASECARD,
        actionType : PDT_EVENT_TYPES.contentSeen
      })
    }
  }, []);
  return (
    <View style={styles.cardContainer}>
      <LinearGradient
        colors={[holidayColors.white, holidayColors.orange]}
        start={{ x: 0, y: 1 }}
        end={{ x: 1, y: 0 }}
        locations={[0.5, 1]}
      >
        <View style={styles.innerContainer}>
          {insuranceAddonDetail.addonHeader ? (
            <CardHeader
              icon={insuranceAddonDetail.addonHeader.icon}
              title={insuranceAddonDetail.addonHeader.heading}
              subtitle={insuranceAddonDetail.addonHeader.subHeading}
              inclusionMessage={insuranceAddonDetail.addonHeader?.inclusionMessage}
            />
          ) : null}

          {!isEmpty(insuranceAddonDetail.persuasionData) ? (
            <PersuationInfo
              title={insuranceAddonDetail.persuasionData.heading}
              icon={insuranceAddonDetail.persuasionData.icon}
            />
          ) : null}

          {insuranceAddonDetail.addonHeader || !isEmpty(insuranceAddonDetail.persuasionData) ? (
            <View style={styles.separator} />
          ) : null}

          {!!insuranceAddonDetail.tag ? <TagComponent text={insuranceAddonDetail.tag} /> : null}

          <InsuranceCard
            insuranceAddOn={insuranceAddonDetail.addons?.[0]}
            priceMap={insuranceAddonDetail?.addonPriceMap?.[insuranceAddonDetail.addons?.[0].id]}
            isSelected={insuranceAddonDetail.addons?.[0]?.isSelected}
            trackReviewLocalClickEvent={trackReviewLocalClickEvent}
            onSelect={() => {
              onSelect(insuranceAddonDetail.addons?.[0], fromListing);
            }}
          />

          {insuranceAddonDetail.addons.length > 1 ? <View style={styles.separator} /> : null}

          {insuranceAddonDetail.addons.length > 1 ? (
            <TouchableOpacity activeOpacity={0.5} onPress={onViewMoreClick}>
              <CardFooter count={insuranceAddonDetail.addons.length - 1} />
            </TouchableOpacity>
          ) : null}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mt16,
    elevation: 1,
    overflow: 'hidden',
    ...marginStyles.ma16,
    borderColor: holidayColors.grayBorder,
    borderWidth:1,
  },

  innerContainer: {
    ...paddingStyles.pa16,
    ...paddingStyles.pb12,
  },

  separator: {
    ...marginStyles.mv8,
    height: 1,
    backgroundColor: holidayColors.grayBorder,
  },
});

const mapDispatchToProps = {
  hideReviewOverlays,
  showReviewOverlay,
};
export default connect(null, mapDispatchToProps)(TravelInsuranceCard);
