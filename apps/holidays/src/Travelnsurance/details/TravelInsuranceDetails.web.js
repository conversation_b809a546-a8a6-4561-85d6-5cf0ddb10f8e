import React, { useCallback, useEffect, useState } from 'react';
import { BackHandler, Platform, ScrollView, StatusBar, StyleSheet, Text, View } from 'react-native';
import { connect } from 'react-redux';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { holidayNavigationPop } from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import { REVIEW_OVERLAYS } from '../../PhoenixReview/Components/ReviewOverlays';

/* Actions */
import { hideReviewOverlays } from '../../Review/Actions/reviewOverlayActions';

/* Components */
import ItemCancellationPolicy from '../components/itemCancellationPolicy';
import PageFooter from '../components/pageFooter';
import UpdateView from '../bottomsheets/UpdateView';
import DeselectedBottomSheet from '../bottomsheets/DeselectedBottomSheet';
import ItemPolicyInfo from '../components/itemPolicyInfo';
import PageHeader from '../../Common/Components/PageHeader';
import InsuranceDetailsCard from '../components/insuranceDetailsCard';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import { INSURANCE_ACTIONS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import useBackHandler from '../../hooks/useBackHandler';
import { noop } from 'lodash';
import { ADDON_TRACKING_VALUES } from '../../Common/Components/VisaProtectionPlan/VPPConstant';

const TravelInsuranceDetails = (props) => {
  const { hideReviewOverlays: hideOverlays } = props || {};
  const insuranceAddon = props.insuranceAddOn;
  const priceMap = props.priceMap;
  const selectedFromPrevPage = props.selectedFromPrevPage;
  const fromPage = props.fromPage == 'pre_sale'
  const trackEvent=props?.trackEvent

  const [isSelected, setSelected] = useState();

  const [confirmationDialog, setConfirmationDialog] = useState(false);

  const [deselectedDialog, setDeselectedDialog] = useState(false);
const hardwareBackPress = () => {

  goBack();
  return true;
};

  useBackHandler(hardwareBackPress);

  useEffect(() => {
    if (selectedFromPrevPage) {
      setSelected(true);
    } else {
      setSelected(false);
    }
  }, []);

  const captureClickEvents = ({ eventName = '', prop1 = '', }) => {
      logHolidayReviewPDTClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
        subPageName: INSURANCE_PAGES.DETAIL,
      });
    props.trackReviewLocalClickEvent(eventName, '', { prop1 });
  };
  const updateClick = () => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.UPDATE, prop1: INSURANCE_PAGES.DETAIL });
    if (insuranceAddon.isSelected) {
      hideOverlays([REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE]);
      return;
    }
    props.onSelect(insuranceAddon);
    holidayNavigationPop({
      overlayKeys: [REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE],
      hideOverlays: hideOverlays,
    })
  };

  const goBack = () => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.CLOSE, prop1: INSURANCE_PAGES.DETAIL });
    if (isSelected && !insuranceAddon.isSelected) {
      setConfirmationDialog(true);
    } else {
      // HolidayNavigation.goBack();
      holidayNavigationPop({
        overlayKeys: [REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE],
        hideOverlays: hideOverlays,
        navigationFunction: HolidayNavigation.goBack,
      });
    }
  };

  const onSelectInsurance = () => {
    const prop54Val = isSelected ? INSURANCE_ACTIONS.UNSELECT :  INSURANCE_ACTIONS.SELECT;
    if (isSelected && insuranceAddon.isSelected) {
      setDeselectedDialog(true);
    }
    setSelected(!isSelected);
    if (trackEvent) {
      trackEvent({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: ADDON_TRACKING_VALUES.INSURANCE_UNSELECT
      })
    }
    else {
    captureClickEvents({ eventName: prop54Val, prop1: INSURANCE_PAGES.DETAIL });
  }
  };

  return (
    <View style={styles.viewContainer}>
      <PageHeader
        showBackBtn
        showShadow
        title={'Travel + Medical Insurance'}
        onBackPressed={goBack}
      />
      <ScrollView contentContainerStyle={{ paddingBottom: 100 }}>
        <View style={styles.cardContainer}>
          <InsuranceDetailsCard
            insuranceCardDetails={insuranceAddon?.insuranceCardDetails}
            disclaimer={insuranceAddon?.disclaimer}
            trackReviewLocalClickEvent={props.trackReviewLocalClickEvent}
            priceMap={priceMap}
            onSelect={() => {
              onSelectInsurance();
            }}
            selectedText={insuranceAddon.selectedText}
            isSelected={isSelected}
          />
        </View>

        {insuranceAddon?.policyBenefits?.length > 0 ? (
          <View style={styles.cardContainer}>
            <Text style={styles.benefitTitle}>Policy Benefits</Text>

            <View style={styles.separator} />
            <View style={styles.bottomContainer}>
              {insuranceAddon?.policyBenefits?.map((value, index, array) => {
                return (
                  <ItemPolicyInfo
                    title={value.heading}
                    subtitle={value.subHeading}
                    imageUrl={value.icon}
                    price={value.price}
                  />
                );
              })}
            </View>
          </View>
        ) : null}

        {insuranceAddon?.cancellationPolicy?.length > 0 ? (
          <View style={styles.cardContainer}>
            <Text style={styles.benefitTitle}>Cancellation Policy</Text>

            <View style={styles.separator} />
            <View style={styles.bottomContainer}>
              {insuranceAddon?.cancellationPolicy.map?.((value, index, array) => {
                return (
                  <ItemCancellationPolicy
                    title={value.heading}
                    subtitle={value.subHeading}
                    icon={value.icon}
                  />
                );
              })}
            </View>
          </View>
        ) : null}
      </ScrollView>

      {isSelected && !insuranceAddon?.isSelected ? (
        <PageFooter
          btnTxt={'Update'}
          totalTravellers={priceMap?.totalPackagePriceUnit}
          discountedPrice={priceMap?.totalPackagePrice}
          onUpdateClick={() => {
            updateClick();
          }}
        />
      ) : null}

      <DeselectedBottomSheet
        modalVisible={deselectedDialog}
        trackReviewLocalClickEvent={props.trackReviewLocalClickEvent}
        removeInsuranceClicked={() => {
          setDeselectedDialog(false);
          props.onSelect(insuranceAddon);
          holidayNavigationPop({
            overlayKeys: [REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE],
            hideOverlays: hideOverlays,
          })
        }}
        onKeepSelectionClicked={() => {
          setDeselectedDialog(false);
          setSelected(true);
        }}
      />

      <UpdateView
        modalVisible={confirmationDialog}
        trackReviewLocalClickEvent={props.trackReviewLocalClickEvent}
        onNotNowClicked={() => {
          setConfirmationDialog(false);
          holidayNavigationPop({
            overlayKeys: [ REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE ],
            hideOverlays,
          });
        }}
        onUpdateNowClicked={() => {
          setConfirmationDialog(false);
          props.onSelect(insuranceAddon);
          holidayNavigationPop({
            overlayKeys: [ REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE ],
            hideOverlays,
          })
        }}
      />
    </View>
);
};

const styles = StyleSheet.create({
  cardContainer: {
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mt16,
    ...marginStyles.mh12,
    backgroundColor: holidayColors.white,
    overflow: 'hidden',
    ...paddingStyles.pv12,
  },
  viewContainer: {
    height: '100%',
    paddingBottom: 20,
    backgroundColor: holidayColors.lightGray2,
  },

  separator: {
    ...marginStyles.mv8,
    height: 1,
    backgroundColor: holidayColors.grayBorder,
  },
  bottomContainer:{
    ...paddingStyles.ph16,
  },
  benefitTitle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...paddingStyles.ph16,
  },
});

const mapDispatchToProps = {
  hideReviewOverlays,
};
export default connect(null, mapDispatchToProps)(TravelInsuranceDetails);
