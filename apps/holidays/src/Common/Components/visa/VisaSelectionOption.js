import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import SelectableVisaOption from '../../../PhoenixDetail/Components/FDFeatureEditOverlayV2/SelectableVisaOption';
import { trackPhoenixDetailLocalClickEvent } from '../../../PhoenixDetail/Utils/PhoenixDetailTracking';
import { packageVisaPromise } from '../../../utils/HolidayNetworkUtils';
import FeatureHeading from '../../../PhoenixDetail/Components/FDFeatureEditOverlayV2/FeatureHeading';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const VisaSelectOption = ({
  dynamicPackageId,
  updateVisa,
  visaPackageFeature,
  fromPresalesBase = false,
}) => {
  const [visaDetails, setVisaDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const fetchVisaDetails = async (dynamicPackageId) => {
    try {
      const packageVisaResponse = await packageVisaPromise(dynamicPackageId);
      packageVisaResponse.json().then((resp) => {
        setVisaDetails(resp);
        setLoading(false);
      });
    } catch (e) {
      setVisaDetails([]);
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchVisaDetails(dynamicPackageId);
  }, [dynamicPackageId]);

  const captureClickEvents = (eventName = '', suffix = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
    });
  };
  const handleUpdateVisa = (sellableId, visaIncluded) => {
    captureClickEvents({
      eventName: 'feature_select_Visa_',
      suffix: `${sellableId}`,
    });
    updateVisa(visaIncluded);
  };

  const renderProgressView = () => (
    <View style={styles.progressView}>
            <Spinner
              size={36}
              strokeWidth={4}
              progressPercent={85}
              speed={1.5}
              color={holidayColors.primaryBlue}
            />
    </View>
  );

  const renderVisaOptions = () => {
    const { visaListingData = {} } = visaDetails || {};
    const { availableVisas = [], discountedFactor, packagePriceMap = [] } = visaListingData || {};
    const { title = '', description = '' } = visaPackageFeature || {};

    return (
      <View style={styles.visaSelectContainer}>
        <View style={styles.headingContainer}>
          <FeatureHeading heading={title} description={description} />
        </View>
        {/* Hide Selectable Visa Options for PSM */}
        {!fromPresalesBase && availableVisas?.length > 0 && (
          <SelectableVisaOption
            options={availableVisas}
            discountedFactor={discountedFactor}
            packagePriceMap={packagePriceMap}
            updateVisa={handleUpdateVisa}
            containerStyles={{ paddingHorizontal: 20 }}
          />
        )}
      </View>
    );

    return [];
  };

  return loading ? renderProgressView() : renderVisaOptions();
};

const styles = StyleSheet.create({
  progressView: {
    width: '100%',
    height: 200,
    backgroundColor: holidayColors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },

  visaSelectContainer: {
    paddingBottom: 10,
  },
  headingContainer: {
    width: '100%',
    flex: 1,
    paddingTop: 10,
    paddingBottom: 5,
  },
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  descriptionContainer: {
    marginTop: 6,
    marginLeft: 3,
  },
  description: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});
export default VisaSelectOption;
