import {setDataInStorage, getDataFromStorage} from '@mmt/legacy-commons/AppState/LocalStorage';
import {fetchCOVIDSafeData} from '../../../utils/HolidayNetworkUtils';
import { getHeading } from '../../../utils/HolidayUtils';
import { getHolidayCovidConent } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';

const KEY_COVID_CONTENT = 'hol_covid_content';

export default class HolidaySafeDataHolder {

    static instance = null;

    constructor() {
        this.contentData = null;
        this.refreshRequired = true;
    }

    static getInstance() {
        if (HolidaySafeDataHolder.instance == null) {
            HolidaySafeDataHolder.instance = new HolidaySafeDataHolder();
        }
        return HolidaySafeDataHolder.instance;
    }

    async setData() {
        const contentData = await fetchCOVIDSafeData();
        this.contentData = contentData;
        await setDataInStorage(KEY_COVID_CONTENT, contentData);
        this.refreshRequired = false;
    }

    async getDataFromStorage() {
        let isSuccess = '';
        const data = await getDataFromStorage(KEY_COVID_CONTENT);
        if (data != null) {
            isSuccess = data.success;
            this.contentData = data;
        }
        return isSuccess;
    }

    getInMemoryContent() {
        return this.contentData;
    }

    async refreshDataIfRequired(fromLanding = false) {
        if (getHolidayCovidConent()) {
            if ( (!this.contentData || fromLanding) && this.refreshRequired) {
                this.getDataFromStorage();
                await this.setData();
            }
        }
    }
}

export const getBannerDetails = (branch) => {
    const safeData = getPackageBannerSafeData(branch, component.PACKAGE);
    if (safeData && safeData.listingHeaderBanner) {
        return safeData.listingHeaderBanner;
    }
    return null;
};

export const getPackageBannerSafeData = (branch, component) => {
    const safetyContent = getBranchContent(branch);
    if (safetyContent &&
      safetyContent.components &&
      safetyContent.components[component] &&
      safetyContent.components[component].toolTipLevel) {
        let key;
        for (key in safetyContent.components[component].toolTipLevel) {
            return safetyContent.components[component].toolTipLevel[key];
        }
    }
    return null;
};

export const getCovidLandingBannerData = () => {
    const covidData = HolidaySafeDataHolder.getInstance().getInMemoryContent();
    if (covidData &&
        covidData.safetyContent &&
        covidData.safetyContent.length > 0 &&
        covidData.safetyContent[0].landingSection) {
        return covidData.safetyContent[0].landingSection;
    }
    return null;
};

export const getDetailsPageBannerDetails = (branch) => {
    const safeData = getPackageBannerSafeData(branch, component.PACKAGE);
    if (safeData && safeData.detailBanner) {
        return safeData.detailBanner;
    }
    return null;
};

export const getThankuBannerDesciption = (branch) => {
    const safeData = getPackageBannerSafeData(branch, component.PACKAGE);
    if (safeData &&
        safeData.thankYouBanner &&
        safeData.thankYouBanner.description) {
        return safeData.thankYouBanner.description;
    }
    return null;
};

export const mySafetyStripDataForComponent = (rating, component, branch) => {
    const safetyContent = getBranchContent(branch);
    if (safetyContent && rating &&
      safetyContent.components &&
      safetyContent.components[component] &&
      safetyContent.components[component].toolTipLevel &&
      safetyContent.components[component].toolTipLevel[rating]) {
        return safetyContent.components[component].toolTipLevel[rating];
    }
    return null;
};

export const getHolidayDetailSafeExternalLink = (branch) => {
    const safeData = getPackageBannerSafeData(branch, component.PACKAGE);
    if (safeData &&
        safeData.detailBanner &&
        safeData.detailBanner.regionLinkURL &&
        safeData.detailBanner.regionLinkText) {
        return {
            'text': safeData.detailBanner.regionLinkText,
            'url': safeData.detailBanner.regionLinkURL,
        };
    }
    return null;
};

export const getHeadingTextForFilters = (rating, component, branch) => {
    const safetyContent = getBranchContent(branch);
    if (safetyContent &&
        safetyContent.components &&
        safetyContent.components[component] &&
        safetyContent.components[component].toolTipLevel &&
        safetyContent.components[component].toolTipLevel[rating]) {
        let heading = getHeading(safetyContent.components[component].toolTipLevel[rating].heading);
        return heading.join(' ');
    }
    return '';
};

export const getBranchContent = (branch) => {
    const covidData = HolidaySafeDataHolder.getInstance().getInMemoryContent();
    if (covidData && covidData.safetyContent) {
        const contentData = covidData.safetyContent.filter((row) => (row.metaData && row.metaData.branch && row.metaData.branch === branch));
        if (contentData && contentData.length > 0) {
            return contentData[0];
        }
    }
    return {};
};

export const component = {
    PACKAGE: 'PACKAGE',
    HOTEL: 'HOTEL',
    ACTIVITY: 'ACTIVITY',
    TRANSFER: 'TRANSFER',
    COMMUTE: 'COMMUTE',
  };
