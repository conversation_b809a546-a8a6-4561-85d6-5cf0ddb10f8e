import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React from 'react';
import { Image, Platform, StyleSheet, Text, View } from 'react-native';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import LinearGradient from 'react-native-linear-gradient';
import {getImageUrl, IMAGE_ICON_KEYS} from '../HolidayImageUrls';

const SelectedTag = () => {
  return (
      <LinearGradient
          end={{ x: 1.0, y: 1.0 }}
          style={styles.selected}
          start={{ x: 0.0, y: 0.0 }}
          colors={['#02B9E1', '#3A7BD5']}
      >
          <View style={styles.tickIconContainer}>
      <Image source={{uri: getImageUrl(IMAGE_ICON_KEYS.WHITE_TICK)}} style={styles.selectedIcon} />
          </View>
      <Text style={styles.selectedText}>SELECTED</Text>
      </LinearGradient>
  );
};

const styles = StyleSheet.create({
  selected: {
    ...holidayBorderRadius.borderRadius16,
    flexDirection: 'row',
    paddingVertical: 2,
    paddingHorizontal: 4,
    alignItems: 'center',
  },
  selectedIcon: {
    height: '100%',
    width: '60%',
      resizeMode:'contain',
  },
  selectedText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallBold,
    letterSpacing: 0,
      ...marginStyles.mr8,
  },
    tickIconContainer: {
        width: 16,
        height: 16,
        backgroundColor: '#85D7EF',
        borderRadius: 50,
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        ...marginStyles.mr4,
        ...marginStyles.ml8,
    },
});
export default SelectedTag;
