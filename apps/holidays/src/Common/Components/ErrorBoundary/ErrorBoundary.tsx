import type { ErrorInfo, ReactNode } from 'react';
import React, { Component } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import type noop from 'lodash/noop';

type ErrorBoundaryProps = {
    children: ReactNode;
    fallback?:
        | ReactNode
        | ((error: Error, resetError: typeof noop) => ReactNode);
};

type ErrorBoundaryState = {
    hasError: boolean;
    error: Error | null;
};

class ErrorBoundaryClass extends Component<
    ErrorBoundaryProps,
    ErrorBoundaryState
> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('Uncaught error:', error, errorInfo);
        // You can log the error to an error reporting service here
    }

    resetErrorBoundary = () => {
        this.setState({ hasError: false, error: null });
    };

    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                if (typeof this.props.fallback === 'function') {
                    return this.props.fallback(
                        this.state.error || new Error('Unknown error'),
                        this.resetErrorBoundary
                    );
                }
                return this.props.fallback;
            }
            return (
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>Something went wrong.</Text>
                    <Text style={styles.errorMessage}>
                        {this.state.error?.message}
                    </Text>
                    <TouchableOpacity
                        style={styles.button}
                        onPress={this.resetErrorBoundary}
                    >
                        <Text style={styles.buttonText}>Try again</Text>
                    </TouchableOpacity>
                </View>
            );
        }

        return this.props.children;
    }
}

// Functional component wrapper
const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({
    children,
    fallback,
}) => {
    return (
        <ErrorBoundaryClass fallback={fallback}>{children}</ErrorBoundaryClass>
    );
};

const styles = StyleSheet.create({
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    errorMessage: {
        fontSize: 14,
        color: 'red',
        marginBottom: 20,
        textAlign: 'center',
    },
    button: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 5,
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
    },
});

export default ErrorBoundary;
