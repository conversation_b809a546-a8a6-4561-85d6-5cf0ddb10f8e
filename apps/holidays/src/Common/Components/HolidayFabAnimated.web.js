import React from 'react';
import PropTypes from 'prop-types';
import {
  TouchableOpacity,
  Image,
  Animated,
  View,
  Text,
  StyleSheet,
  Platform,
  UIManager,
  LayoutAnimation,
  Dime
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {DEVICE_WINDOW, isAndroidClient} from '../../utils/HolidayUtils';
import FabPulseView from './FabPulseView';
import * as FabPulseViewHandler from './FabPulseViewHandler';
import {
  getCallIconUrl,
  getCallText,
  getChatIconUrl,
  getChatText,
  getCtaExtendedText,
  getCtaImageUrl, getMyraChatIconUrl,
  getWebQueryIconUrl, getWebQueryText,
} from '../../utils/CtaUtils';
import {isEmpty} from 'lodash';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { getNewFabAnimationData } from '../../utils/HolidaysPokusUtils';


const askIcon = require('@mmt/legacy-assets/src/icons_fab_white_ask.webp');
const userQuery = require('@mmt/legacy-assets/src/icons_fab_query.webp');
const userQueryWhite = require('@mmt/legacy-assets/src/icons_fab_query_white.webp');
const userCall = require('@mmt/legacy-assets/src/icons_fab_call.webp');
const userCallWhite = require('@mmt/legacy-assets/src/icons_fab_call_white.webp');
const userChat = require('@mmt/legacy-assets/src/icons_fab_chat_with_us.webp');
const userChatWhite = require('@mmt/legacy-assets/src/icons_fab_white_chat_with_us.webp');
const userClose = require('@mmt/legacy-assets/src/ic_close_fab.webp');
const userLocate = require('@mmt/legacy-assets/src/icons_fab_branch_locator.webp');
const userLocateWhite = require('@mmt/legacy-assets/src/icons_fab_white_branch_locator.webp');
const myraChatWhite = require('@mmt/legacy-assets/src/holidays/myra_chat_icon.webp');

const GET_A_CALLBACK = 'Get a callback';
const LOCATE_US = 'Locate Us';
const CALL_NOW = 'Call Now';
const LIVE_CHAT = 'Live Chat';
const FALLBACK_TEXT = 'Need Help?';

if (isAndroidClient()) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const screenHeight = DEVICE_WINDOW.height;
const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);



const renderFabRotatingIcons = (fabCta, rotationAnimationValue) => {
  const {showChat, showCall, showQuery} = fabCta;
  const images = [
    askIconFilledUrl,
    ...(showChat ? [getChatIconUrl()] : ''),
    ...(showQuery ? [getWebQueryIconUrl()] : ''),
    ...(showCall ? [getCallIconUrl()] : ''),
  ];

  return (
    <CircularImageCarousel
      images={images}
      GRADIENT_COLORS={NEW_CTA_GRADIENT_COLORS}
      containerStyle={styles.linearGradient}
      iconStyle={styles.openStyle}
      iconSize={20}
      animationCount={rotationAnimationValue}
    />
  );
}

class HolidayFabAnimated extends React.Component {
  constructor(props){
    super(props);
    this.state = {
      animation: new Animated.Value(0),
      fabAnimation: false,
      expanded: !this.props.textShrinked,
      crossContainer: false,
      showPulseAnimation: false,
    };
    this.uniqueValue = 0; // this value will be used to avoid re-render of animation
  }
  pulseAnimationCallback = {
    startPulseAnimation: config => {
      // check if screen is sending prop textShrinked=true and current state is not expanded
      if (this.props.textShrinked && !this.state.expanded) {
        this.uniqueValue += 1; // change flag when need animation
        this.showPulseAnimation(true);
      }
    },
    stopPulseAnimation: config => {
      this.showPulseAnimation(false);
    },
  }
  componentDidMount(){
    FabPulseViewHandler.register(this.pulseAnimationCallback);
  }
  componentWillUnmount(){
    FabPulseViewHandler.remove();
  }
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.textShrinked !== this.props.textShrinked) {
      this.changeAnimationLayout(false, false);
    }
  }
  changeAnimationLayout = (toBeExpanded, shrinked) => {
    if (!shrinked) {
      LayoutAnimation.configureNext({
        duration: 300,
        create:
            {
              type: LayoutAnimation.Types.easeInEaseOut,
              property: LayoutAnimation.Properties.opacity,
            },
        update:
            {
              type: LayoutAnimation.Types.easeInEaseOut,
            },
      });
      this.setExpanded(toBeExpanded);
    }
  }
  setExpanded = expanded => this.setState({ expanded });
  setCrossContainer = crossContainer => this.setState({ crossContainer });
  showPulseAnimation = show => this.setState({ showPulseAnimation: show });
  handleOpen = () => {
    if (this.props.fabCta?.showMyraChat) {
      if(this.props.startChatGpt) {
        return this.props.startChatGpt(true);
      }
    } else if (this.props.fabCta && this.props.fabCta.showQuery && !this.props.fabCta.showCall && !this.props.fabCta.showChat && !this.props.fabCta.branchLocator) {
      return this.props.startQuery(true);
    } else if (this.props.fabCta && !this.props.fabCta.showQuery && this.props.fabCta.showCall && !this.props.fabCta.showChat && !this.props.fabCta.branchLocator) {
      return this.props.startCall(true);
    } else if (this.props.fabCta && !this.props.fabCta.showQuery && !this.props.fabCta.showCall && this.props.fabCta.showChat && !this.props.fabCta.branchLocator) {
      return this.props.startChat(true);
    } else if (this.props.fabCta && !this.props.fabCta.showQuery && !this.props.fabCta.showCall && !this.props.fabCta.showChat && this.props.fabCta.branchLocator) {
      return this.props.showLocator(true);
    }
    if (this.props.onFabToggle) {
      this.props.onFabToggle(true);
    }
    FabPulseViewHandler.stop(); // stop all animation and never show in current session
    this.changeAnimationLayout(false, this.props.textShrinked);
    this.setState({ fabAnimation: true, crossContainer: true });
    if (this.props.handleDefaultFabClick) {
      this.props.handleDefaultFabClick(true);
    }
    Animated.timing(this.state.animation, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    })
      .start();
  };

  handleClose = () => {
    if (this.props.onFabToggle) {
      this.props.onFabToggle(false);
    }
    if (this.props.handleDefaultFabClick) {
      this.props.handleDefaultFabClick(false);
    }
    if (!this.props.textShrinked) {this.setCrossContainer(false);}
    this.changeAnimationLayout(true, this.props.textShrinked);

    Animated.timing(this.state.animation, {
      toValue: 0,
      duration: 600,
      useNativeDriver: true,
    })
      .start(() => {
        this.setState({ fabAnimation: false, crossContainer: this.props.textShrinked ? false : this.state.crossContainer });
      });
  };
  render() {
    const {pageName} = this.props || {};
    const { fabCta } = this.props;
    const { showCall, showQuery, showChat, branchLocator, showMyraChat = false } = fabCta;
    const { isNewFabCta = false } = getNewFabAnimationData();
    const noOfIcons = [showCall, showQuery, showChat].filter(Boolean).length;
    const fabIcon = this.getFabIcon(fabCta, pageName);
    const chatIconUrl = getChatIconUrl(pageName);
    const callIconUrl = getCallIconUrl(pageName);
    const callBackIcon = getWebQueryIconUrl(pageName);

    const fabIconExtendedText = this.getFabExtendedText(this.props.fabCta, pageName);
    const overlayAnimate = {
      opacity: this.state.animation.interpolate({
        inputRange: [0.01, 0.3],
        outputRange: [0, 1],
        extrapolate: 'clamp',
      }),
    };

    const triggerAnimate = {
      transform: [
        {
          translateY: this.state.animation.interpolate({
            inputRange: [0, 0.1],
            outputRange: [screenHeight, 0],
            extrapolate: 'clamp',
          }),
        },
      ],
      opacity: this.state.animation.interpolate({
        inputRange: [0.04, 0.4],
        outputRange: [0.2, 1],
        extrapolate: 'clamp',
      }),
    };


    const slideAnimate = {
      transform: [
        {
          translateY: this.state.animation.interpolate({
            inputRange: [0, 0.3],
            outputRange: [0, -1 * screenHeight],
            extrapolate: 'clamp',
          }),
        },
      ],
      opacity: this.state.animation.interpolate({
        inputRange: [0.04, 0.4],
        outputRange: [0.2, 1],
        extrapolate: 'clamp',
      }),
    };

    const getPopUpOuterBottom = (isListing) => {
      switch (Platform.OS) {
        case 'ios':
        case 'android':
          return isListing ? 75 : 50;
        case 'web':
          return isListing ? 140 : 90;
      }
    };
    const popUpOuterBottomValue = getPopUpOuterBottom(this.props.isListing);
    const popUpOuterBottom = { bottom: popUpOuterBottomValue + (this.props.containerBottomValue || 0) };
    return (
      <React.Fragment>

        {this.state.fabAnimation &&
          <AnimatedTouchable
            style={[styles.cover, overlayAnimate]}
            onPress={this.handleClose}
            activeOpacity={1}
          />
        }
        <View
            style={[
              styles.btnOuter,
              showMyraChat ? styles.btnOuterMyra : {},
              this.props.containerStyle ||
              (this.props.isListing ? styles.btnBottomListing : styles.btnBottom),
            ]}
        >
          <TouchableOpacity onPress={this.handleOpen} activeOpacity={1}>

            {showMyraChat
                ? this.renderMyraFabIcon(pageName)
                : this.state.showPulseAnimation && !this.state.crossContainer
                    ? this.renderFabPulseView(fabIcon)
                    : this.renderFabIcon(fabIcon, fabIconExtendedText)}
          </TouchableOpacity>
          {this.state.crossContainer &&
            <AnimatedTouchable
              style={[styles.closeTrigger, triggerAnimate]}
              onPress={this.handleClose}
              activeOpacity={1}
            >
              <LinearGradient
                start={{
                  x: 0,
                  y: 0,
                }}
                end={{
                  x: 0,
                  y: 1,
                }}
                colors={[holidayColors.orangeGradient, holidayColors.orangeGradientDark]}
                style={[styles.linearGradient]}
              >
                <Image style={styles.openStyle} source={userClose} />
              </LinearGradient>
            </AnimatedTouchable>
          }
        </View>
        {this.state.fabAnimation ? (
          <View
            style={[styles.popUpOuter, popUpOuterBottom]}
          >
            <Animated.View style={[styles.sheet, slideAnimate]}>
              {!!showChat &&
                <TouchableOpacity
                  style={[styles.queryBtnOuter]}
                  onPress={() => this.props.startChat(false)}
                  activeOpacity={0.7}
                >
                  <View style={styles.queryBtnText}>
                    <Text style={styles.queryBtnText}>{getChatText(pageName)}</Text>
                  </View>
                  <View style={styles.fabIcon}>
                    <Image style={styles.chatStyle} source={isEmpty(chatIconUrl) ? userChat : {uri: chatIconUrl}}/>
                  </View>
                </TouchableOpacity>
              }

              {!!showCall &&
                <TouchableOpacity
                  style={[styles.queryBtnOuter]}
                  onPress={() => this.props.startCall(false)}
                  activeOpacity={0.7}
                >
                  <View style={styles.queryBtnText}>
                    <Text style={styles.queryBtnText}>{getCallText(pageName)}</Text>
                  </View>
                  <View style={styles.fabIcon}>
                    <Image style={styles.contactUsStyle} source={isEmpty(callIconUrl) ? userCall : {uri: callIconUrl}} />
                  </View>
                </TouchableOpacity>
              }

              {!!branchLocator &&
                <TouchableOpacity
                  style={[styles.queryBtnOuter]}
                  onPress={() => this.props.showLocator(false)}
                  activeOpacity={0.7}
                >
                  <View style={styles.queryBtnText}>
                    <Text style={styles.queryBtnText}>
                      {LOCATE_US}
                    </Text>
                  </View>
                  <View style={styles.fabIcon}>
                    <Image style={styles.locationStyle} source={userLocate} />
                  </View>
                </TouchableOpacity>
              }

              {!!showQuery &&
                <TouchableOpacity
                  style={[styles.queryBtnOuter]}
                  onPress={() => this.props.startQuery(false)}
                  activeOpacity={0.7}
                >
                  <View style={styles.queryBtnText}>
                    <Text style={styles.queryBtnText}>{getWebQueryText(pageName)}
                    </Text>
                  </View>
                  <View style={styles.fabIcon}>
                    <Image style={styles.queryStyle} source={isEmpty(callBackIcon) ? userQuery : {uri: callBackIcon}}/>
                  </View>
                </TouchableOpacity>
              }
            </Animated.View>
          </View>
        ) : null}
      </React.Fragment>
    );
  }
  renderFabPulseView = (fabIcon) => {
    return (
      <FabPulseView
        icon={fabIcon}
        gradientStyle={styles.linearGradient}
        iconStyle={styles.openStyle}
        shouldAnimate={this.uniqueValue} />
    );
  }
  renderFabIcon = (fabIcon, fabText) => {
    return (
      <LinearGradient
        start={{
          x: 0,
          y: 0,
        }}
        end={{
          x: 0,
          y: 1,
        }}
        colors={['#ff3e5e', '#ff7f3f']}
        style={styles.linearGradient}
      >
        <Image style={styles.openStyle} source={typeof fabIcon === 'string' ? {uri: fabIcon} : fabIcon}/>
        {this.state.expanded ? <Text style={styles.openTextStyle}>{fabText}</Text> : null}
      </LinearGradient>
    );
  }
  getFabIcon = (fabCta, pageName) => {
    const { isNewFabCta = false } = getNewFabAnimationData();
    if(isNewFabCta) return getFabIconV2Url(fabCta);

    if (fabCta && fabCta.showQuery && !fabCta.showCall && !fabCta.showChat && !fabCta.branchLocator) {
      return isEmpty(getWebQueryIconUrl(pageName)) ? userQueryWhite : getWebQueryIconUrl(pageName);
    } else if (fabCta && !fabCta.showQuery && fabCta.showCall && !fabCta.showChat && !fabCta.branchLocator) {
      return isEmpty(getCallIconUrl(pageName)) ? userCallWhite : getCallIconUrl(pageName);
    } else if (fabCta && !fabCta.showQuery && !fabCta.showCall && fabCta.showChat && !fabCta.branchLocator) {
      return isEmpty(getChatIconUrl(pageName)) ? userChatWhite : getChatIconUrl(pageName);
    } else if (fabCta && !fabCta.showQuery && !fabCta.showCall && !fabCta.showChat && fabCta.branchLocator) {
      return userLocateWhite;
    }
    return isEmpty(getCtaImageUrl(pageName)) ? askIcon : getCtaImageUrl(pageName);
  };
  getFabExtendedText = (fabCta, pageName) => {
    if (fabCta && fabCta.showQuery && !fabCta.showCall && !fabCta.showChat && !fabCta.branchLocator) {
      return isEmpty(getWebQueryText(pageName)) ? GET_A_CALLBACK : getWebQueryText(pageName);
    } else if (fabCta && !fabCta.showQuery && fabCta.showCall && !fabCta.showChat && !fabCta.branchLocator) {
      return isEmpty(getCallText(pageName)) ? CALL_NOW : getCallText(pageName);
    } else if (fabCta && !fabCta.showQuery && !fabCta.showCall && fabCta.showChat && !fabCta.branchLocator) {
      return  isEmpty(getChatText(pageName)) ? LIVE_CHAT : getChatText(pageName);
    } else if (fabCta && !fabCta.showQuery && !fabCta.showCall && !fabCta.showChat && fabCta.branchLocator) {
      return LOCATE_US;
    }
    return isEmpty(getCtaExtendedText(pageName)) ? FALLBACK_TEXT : getCtaExtendedText(pageName);
  };


renderMyraFabIcon = (pageName) => {
    const myraIcon = isEmpty(getMyraChatIconUrl(pageName)) ? myraChatWhite : getMyraChatIconUrl(pageName);
    return (
        <LinearGradient
            start={{
              x: 0,
              y: 0,
            }}
            end={{
              x: 0,
              y: 1,
            }}
            colors={['#3023AE', '#C86DD7']}
            style={styles.linearGradientMyra}
        >
          <Image
              style={styles.openMyraStyle}
              source={typeof myraIcon === 'string' ? { uri: myraChatWhite } : myraChatWhite}
          />
          {this.state.expanded && (
              <View style={styles.openTextContainerStyle}>
                <Text style={styles.openTextHeadingStyle}>I’m Myra, Your travel assistant</Text>
                <Text style={styles.openTextStyle}>Smart chat bot powered by ChatGPT</Text>
              </View>
          )}
        </LinearGradient>
    );
  };

}



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fabOuter: {
    zIndex: 9,
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  linearGradient: {
    height: 55,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    // added
    flexDirection: 'row'
  },
  linearGradientMyra: {
    height: 55,
    borderTopLeftRadius: 50,
    borderBottomLeftRadius: 50,
    // added
    flexDirection: 'row',
    overflow: 'hidden',
  },
  cover: {
    backgroundColor: 'rgba(0,0,0,.8)',
    position: 'absolute',
    width: '100%',
    height: '100%',
    left: 0,
    bottom: 0,
    zIndex: 3,
    elevation: 5,
  },
  sheet: {
    position: 'absolute',
    top: DEVICE_WINDOW.height,
    right: -3,
    bottom: 0,
    height: '100%',
    justifyContent: 'flex-end',
    zIndex: 5,
    elevation: 5,
  },
  btnOuter: {
    height: 55,
    borderRadius: 50,
    position: 'absolute',
    right: 16,
    elevation: 5,
    zIndex: 5
  },
  btnOuterMyra: {
    height: 55,
    position: 'absolute',
    right: 0,
    elevation: 5,
    zIndex: 5
  },
  btnBottom: {
    bottom: 70
  },
  btnBottomListing: {
    bottom: 20
  },
  closeTrigger: {
    position: 'absolute'
  },
  fabIcon: {
    backgroundColor: '#fff',
    marginHorizontal: 10,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    elevation: 5

  },
  queryBtnOuter: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 180,
    justifyContent: 'space-between',
    height: 40,
    marginTop: 15,
  },
  closeStyle: {
    width: 17,
    height: 17,
    marginRight: 25,
    marginLeft: 21,
  },
  openStyle: {
    width: 23,
    height: 20,
    marginHorizontal: 16,
  },
  openTextStyle: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    marginRight: 15,
    marginLeft: -10,
  },
  locationStyle: {
    width: 16,
    height: 24,
  },
  queryStyle: {
    width: 21,
    height: 20,
  },
  contactUsStyle: {
    width: 23,
    height: 20,
  },
  chatStyle: {
    width: 22,
    height: 22,
  },
  queryBtnText: {
    color: holidayColors.white,
    width: 110,
    textAlign: 'right',
    ...fontStyles.labelSmallRegular,
  },
  popUpOuter: {
    height: 230,
    position: 'absolute',
    overflow: 'hidden',
    width: 180,
    right: 20,
    zIndex: 3,
    paddingBottom: 25,
    elevation: 26,
  },
  popUpOuterBottom: {
    ...Platform.select({
      ios: {
        bottom: 50,
      },
      android: {
        bottom: 50,
      },
      web: {
        bottom: 140,
      },
    }),
  },
  popUpOuterBottomListing: {
    ...Platform.select({
      ios: {
        bottom: 75,
      },
      android: {
        bottom: 75,
      },
      web: {
        bottom: 90,
      },
    }),
  },
  popUpOuterDissabled: {
    right: 20,
    position: 'absolute',
    paddingBottom: 25,
  },
  openMyraStyle: {
    width: 40,
    height: 50,
    marginVertical:8,
    marginHorizontal: 20,
  },
  openTextContainerStyle: {
    justifyContent: 'center',
    marginTop: 6,
  },
  openTextHeadingStyle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.white,
    marginRight: 15,
    marginLeft: -10,
  },
});

HolidayFabAnimated.propTypes = {
  startCall: PropTypes.func.isRequired,
  startQuery: PropTypes.func.isRequired,
  startChat: PropTypes.func.isRequired,
  showLocator: PropTypes.func.isRequired,
  handleDefaultFabClick: PropTypes.func.isRequired,
  fabCta: PropTypes.object.isRequired,
  isListing: PropTypes.bool,
  textShrinked: PropTypes.bool,
  pageName:PropTypes.string,
};

HolidayFabAnimated.defaultProps = {
  isListing: false,
};

export default HolidayFabAnimated;
