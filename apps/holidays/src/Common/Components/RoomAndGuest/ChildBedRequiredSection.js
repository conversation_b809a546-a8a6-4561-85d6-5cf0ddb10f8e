import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import Radio from '@mmt/ui/components/radio';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

export default class ChildBedRequiredSection extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentChildIndex: this.props.index,
      bedRequired: this.props.bedRequired,
      //activeSorter is the current selected age of child
      activeSorter: this.props.activeSorter,
    };
  }

  handleChange = (bedRequired) => {
    const { index: currentChildIndex, activeSorter } = this.props || {};
    this.setState(() => this.props.handleChange(currentChildIndex, activeSorter, bedRequired));
  };

  handleButtonClick = () => {
    this.setState({ bedRequired: !this.state.bedRequired });
    this.handleChange(!this.state.bedRequired);
  };
  render() {
    return (
      <View style={styles.container}>
        <View style={[AtomicCss.flexRow]}>
          {/*Yes Button*/}
          <TouchableOpacity onPress={this.handleButtonClick}>
            <View style={styles.radioContainer}>
              <Radio style={styles.radio} isSelected={this.state.bedRequired} />
              <Text style={styles.radioText}>Yes</Text>
            </View>
          </TouchableOpacity>

          {/*No button*/}
          <TouchableOpacity onPress={this.handleButtonClick}>
            <View style={styles.radioContainer}>
              <Radio style={styles.radio} isSelected={!this.state.bedRequired} />
              <Text style={styles.radioText}>No</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginStyles.mv16,
    marginLeft: 65,
  },
  radio: {
    ...marginStyles.mr6,
    width: 20,
    height: 20,
  },
  radioContainer: {
    flexDirection: 'row',
    ...marginStyles.mr16,
    alignItems: 'center',
  },
  radioText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  disabledText: {
    color: holidayColors.red,
  },
});
