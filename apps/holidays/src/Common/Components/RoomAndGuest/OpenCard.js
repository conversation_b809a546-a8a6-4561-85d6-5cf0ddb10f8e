import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { connect } from 'react-redux';
import { isEmpty } from 'lodash';
import PropTypes from 'prop-types';
import {
  MIN_CHILD_AGE,
  MAX_CHILD_AGE,
  MAX_COMPULSORY_BED_CHILD_AGE,
} from 'mobile-holidays-react-native/src/PhoenixDetail/DetailConstants';
import { fetchPaxGuidelines } from 'mobile-holidays-react-native/src/PhoenixDetail/Actions/HolidayDetailActions';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { actionStyle } from 'mobile-holidays-react-native/src/PhoenixDetail/Components/DayPlan/dayPlanStyles';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';

/* Components */
import HoldiaysMessageStrip from '../HolidaysMessageStrip';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import Counter from './Counter';
import ChildAgeSection from './ChildAgeSection';
import ChildBedRequiredSection from './ChildBedRequiredSection';
import ChildPolicy from './ChildPolicy';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';

const activeBtnOpacity = 0.7;

const getEldestChild = (childAgeArray = []) => {
  const max = childAgeArray.reduce(
    (prev, current) => (prev.age > current.age ? prev : current),
    {},
  );
  return max;
};

const OpenCard = (props) => {
  const {
    adultCount,
    childCount,
    childAgeArray,
    paxDetail = {},
    fetchPaxGuidelines = () => {},
    changeCallbacks = () => {},
    trackClickEvent = () => {},
    trackPDTV3Event = () => {},
  } = props || {};
  const [adultCountState, setAdultCountState] = useState(adultCount);
  const [childCountState, setChildCountState] = useState(childCount);
  const [childAgeArrayState, setChildAgeArrayState] = useState(childAgeArray);
  const [policyModal, setPolicyModal] = useState(false);
  const [bedState, setBedState] = useState('');
  const [bedRequired, setBedRequired] = useState('');

  useEffect(() => {
    if (isEmpty(paxDetail)) {
      fetchPaxGuidelines();
    }
  }, []);

  useEffect(() => {
    setAdultCountState(adultCount);
    setChildCountState(childCount);
    setChildAgeArrayState(childAgeArray);
  }, [adultCount, childCount, childAgeArray]);

  const pad = (n) => (n < 10 ? `0${n}` : n);

  const ageSection = (packagePaxDetail) => {
    const {
      childWithBed,
      minChildAge,
      maxChildAge,
      minChildAgeForBedCompulsory = 5,
      extraBedAllowedMessage,
    } = packagePaxDetail || {};
    const ageSectionDom = [];
    // const { childAgeArray } = this.state || {};
    const shouldSetChildAge = childAgeArrayState.length > 0; //this.state.childAgeArray.length > 0;

    // MIN_CHILD_AGE is the default child age.
    // minChildAge is the age we received through backend.
    const minimumChildAge = childWithBed ? minChildAge : MIN_CHILD_AGE;
    const maximumChildAge = childWithBed
      ? minChildAgeForBedCompulsory
      : MAX_COMPULSORY_BED_CHILD_AGE;

    for (let i = 0; i < childAgeArrayState.length; i++) {
      let age;
      let bedRequired;
      if (shouldSetChildAge === true) {
        age = childAgeArrayState.length > i ? childAgeArrayState[i].age : 0;
        bedRequired = childAgeArrayState.length > i ? childAgeArrayState[i].bedRequired : true;
      } else {
        age = -1;
        bedRequired = true;
      }
      ageSectionDom.push(
        <View key={i}>
          <View style={styles.childAgeContainer}>
            <Text style={styles.heading}>
              Age of Child {i + 1} <Text style={styles.required}>*</Text>
            </Text>
            <ChildAgeSection
              handleChange={changeCallbacks.setAge}
              index={i}
              activeSorter={age}
              bedRequired={bedRequired}
              packagePaxDetail={packagePaxDetail}
              maximumChildAge={maximumChildAge}
            />
          </View>
        </View>,
      );
    }

    const childForBed =
      childAgeArrayState.length === 1
        ? childAgeArrayState?.[0]
        : getEldestChild(childAgeArrayState);
    const { age: childBedAge = 0, bedRequired: childBedRequired = false } = childForBed || {};
    const childForBedIndex = childAgeArrayState.findIndex((item) => item.age === childForBed?.age);
    // Ask bed in range of >=min age and <maxAge
    const showChildWithBed =
      childWithBed &&
      childAgeArrayState.length === 1 &&
      childBedAge >= minimumChildAge &&
      childBedAge < maximumChildAge;
    return (
      <View>
        {ageSectionDom}
        {showChildWithBed && (
          <View style={styles.childWithBedContainer}>
            <View style={styles.textContainer}>
              <Text style={[styles.heading]}>
                Bed Required <Text style={styles.required}>*</Text>
              </Text>
              <Text style={styles.subHeading}>
                Child
                {childBedAge > 0 ? `,  ${childBedAge} years` : ''}
              </Text>
            </View>
            <ChildBedRequiredSection
              handleChange={changeCallbacks.setAge}
              index={childForBedIndex}
              activeSorter={childBedAge} // activeSorter is the selected age of eldest child
              bedRequired={childBedRequired}
              packagePaxDetail={packagePaxDetail}
            />
          </View>
        )}
        <HoldiaysMessageStrip
          message={extraBedAllowedMessage}
          shouldShow={
            childWithBed &&
            (childAgeArrayState.length > 1 || childBedAge >= maximumChildAge) &&
            !!extraBedAllowedMessage
          }
        />
      </View>
    );
  };
  const trackClickEvents = () => {
    const eventName = 'clicked_child_policies_know_more';
    trackPDTV3Event({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: !policyModal ? eventName : `${eventName}_closed`,
    });
    trackClickEvent(eventName);
  };
  const openPoliciesModal = () => {
    // this.setState({
    //   policyModal: !this.state.policyModal,
    // });
    setPolicyModal(!policyModal);
    trackClickEvents();
  };
  const getPolicyModal = () => {
    return paxDetail ? (
      <BottomSheetOverlay 
        toggleModal={openPoliciesModal}
        visible={policyModal}
        containerStyles={styles.bottomContainerStyles}
      >
        <ChildPolicy paxDetail={paxDetail} />
      </BottomSheetOverlay>
    ) : (
      showShortToast('Something went wrong!!')
    );
  };
  const addButton = ({ isDisabled = false, callBack = () => {} } = {}) => {
    const handleAdd = () => {
      trackClickEvent('add_pax');
      callBack();
    }
    return (
      <TouchableOpacity onPress={handleAdd} style={[styles.addBtnContainer]}>
        <Text
          style={[
            styles.addBtnText,
            isDisabled ? styles.addBtnTextDisabled : styles.addBtnTextActive,
          ]}
        >
          Add
        </Text>
      </TouchableOpacity>
    );
  };
  // render() {
  const { index, packagePaxDetail, removeRoom, roomCount } = props || {};
  const { maxPaxAllowed, maxAdultAllowed } = packagePaxDetail || {};
  return (
    <View style={styles.openCard}>
      {maxPaxAllowed && maxPaxAllowed > 0 && (
        <View style={styles.guestCondition}>
          <Text style={styles.guestConditionText}>
            Total
            <Text style={styles.guestConditionTextBold}>
              {' '}
              {maxPaxAllowed} guests (Max {maxAdultAllowed} adults)
            </Text>{' '}
            allowed in a room
          </Text>
        </View>
      )}

      <View style={styles.innerView}>
        <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={styles.roomHeading}>ROOM {index + 1}</Text>
          {roomCount > 1 && (
            <TouchableOpacity onPress={() => removeRoom(index)} activeOpacity={activeBtnOpacity}>
              <Text style={[styles.roomHeading, { color: holidayColors.primaryBlue }]}>REMOVE</Text>
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.container}>
          <View style={styles.textContainer}>
            <Text style={styles.heading}>Adults</Text>
            <Text style={styles.subHeading}>Above 12 Years</Text>
          </View>
          {adultCountState === 0 ? (
            addButton({ callBack: changeCallbacks.increaseAdultCount })
          ) : (
            <Counter
              value={adultCountState}
              onIncrease={changeCallbacks.increaseAdultCount}
              onDecrease={changeCallbacks.decreaseAdultCount}
            />
          )}
        </View>
        <View style={styles.container}>
          <View style={styles.textContainer}>
            <Text style={styles.heading}>Children</Text>
            <Text style={styles.subHeading}>Below 12 Years</Text>
          </View>
          {childCountState === 0 ? (
            addButton({
              isDisabled: adultCountState < 1,
              callBack: changeCallbacks.increaseChildCount,
            })
          ) : (
            <Counter
              value={childCountState}
              onIncrease={changeCallbacks.increaseChildCount}
              onDecrease={changeCallbacks.decreaseChildCount}
            />
          )}
        </View>
        {!!childCountState && (
          <View style={styles.viewMoreWraper}>
            <Text style={styles.selectAgeMessage}>
              Select child’s age as on the{' '}
              <Text style={styles.selectAgeMessageBold}>last day of travel</Text>
            </Text>
            <TouchableOpacity onPress={openPoliciesModal}>
              <Text style={actionStyle}>More</Text>
            </TouchableOpacity>
          </View>
        )}
        {ageSection(packagePaxDetail)}
        {policyModal && getPolicyModal()}
      </View>
    </View>
  );
  // }
};

OpenCard.propTypes = {
  childAgeArray: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  childCount: PropTypes.number.isRequired,
  adultCount: PropTypes.number.isRequired,
  changeCallbacks: PropTypes.object.isRequired,
  removeRoom: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 15,
  },
  viewMoreWraper: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
    alignItems: 'center',
  },
  cross: {
    height: 30,
    marginRight: 10,
  },
  headerStyle: {
    justifyContent: 'flex-end',
    height: 25,
    marginBottom: 0,
  },
  heading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    ...marginStyles.mb4,
  },
  subHeading: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
  },
  openCard: {
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,

    borderRadius: 4,
    elevation: 2,
    shadowColor: '#330000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: {
      width: 1,
      height: 1,
    },
    marginBottom: 20,
  },
  addBtnContainer: {
    ...paddingStyles.ph20,
    ...paddingStyles.pv6,
    ...holidayBorderRadius.borderRadius8,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  addBtnText: {
    ...fontStyles.labelMediumBlack,
  },
  addBtnTextActive: {
    color: holidayColors.primaryBlue,
  },
  addBtnTextDisabled: {
    color: holidayColors.lightGray,
  },
  guestCondition: {
    backgroundColor: holidayColors.fadedYellow,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  guestConditionText: {
    color: holidayColors.yellow,
    ...fontStyles.labelSmallRegular,
  },
  guestConditionTextBold: {
    color: holidayColors.yellow,
    ...fontStyles.labelSmallBold,
  },
  innerView: {
    margin: 1,
    paddingHorizontal: 22,
    paddingVertical: 14,
  },
  roomHeading: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
    marginBottom: 10,
  },
  ageHeading: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.gray,
    marginBottom: 12,
    marginTop: 2,
  },
  childAgeContainer: {
    ...marginStyles.mt20,
  },
  childWithBedContainer: {
    ...marginStyles.mt20,
    flexDirection: 'row',
  },
  selectAgeMessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  selectAgeMessageBold: {
    ...fontStyles.labelSmallBold,
  },
  required: {
    color: holidayColors.red,
  },
  bottomContainerStyles: {
    ...paddingStyles.pa16
  }
});

const mapStateToProps = (state) => ({
  paxDetail: state.holidaysDetail?.paxDetail,
});

const mapDispatchToProps = (dispatch) => ({
  fetchPaxGuidelines: () => dispatch(fetchPaxGuidelines()),
});
export default connect(mapStateToProps, mapDispatchToProps)(OpenCard);
