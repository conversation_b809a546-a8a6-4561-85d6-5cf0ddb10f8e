import React from 'react';
import { toast } from 'react-toastify';
import './Toast.css';

const styles = {
    toast: {
        zIndex: 10000,
        width:'fit-content',
        maxWidth:'80%',
        left:0,
        right:0,
        marginLeft:'auto',
        marginRight:'auto',
        height:30,
        bottom:15,
    },
};
toast.configure({ style: styles.toast,limit:1,newestOnTop:true});


export const notify = (msg) => {
    toast(
    <div style={{zIndex:10000}}>{msg}</div>,
    {position:toast.POSITION.BOTTOM_CENTER,autoClose:3000,hideProgressBar:true}
    );
};

