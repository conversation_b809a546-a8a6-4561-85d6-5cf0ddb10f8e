import React from 'react';
import PropTypes from 'prop-types';
import { ImageBackground, StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

const startPosition = { x: 0, y: 0 };
const endPosition = { x: 0, y: 1 };
const PlaceholderImageWithText = ({ description, url, style, fontStyle, imageStyle, wrapperStyle }) => {
  return (
    <View style={wrapperStyle}>
      <ImageBackground source={{ uri: url }} style={style} imageStyle={imageStyle}>
        {!!description && (
          <LinearGradient
            style={[styles.textWrapper, imageStyle]}
            start={startPosition}
            end={endPosition}
            colors={['#00000000', '#000000']}
          >
            <Text style={[fontStyle, styles.text]}>{description}</Text>
          </LinearGradient>
        )}
      </ImageBackground>
    </View>
  );
};

PlaceholderImageWithText.propTypes = {
  description: PropTypes.string,
  url: PropTypes.string.isRequired,
  style: PropTypes.object,
  fontStyle: PropTypes.object,
};
const styles = StyleSheet.create({
  wrapper: { width: 90 },
  textWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: 50,
    width: '100%',
    justifyContent: 'flex-start',

  },
  text: {
    left: 10,
    paddingTop: 10,
    width: '90%',
    paddingBottom: 15,
  },
});

export default PlaceholderImageWithText;
