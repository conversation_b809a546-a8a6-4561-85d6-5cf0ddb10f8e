import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { Pagination } from 'react-native-snap-carousel';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from 'apps/holidays/src/Styles/Spacing';

/* Components */
import Carousel from '../../../Common/Components/Carousel';
import HolidayImageHolder from 'apps/holidays/src/Common/Components/HolidayImageHolder';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';

const SLIDER_SPACING = 30;
const PackageImageCarousal = ({ cardImages, trackClickEvent, packageId, handlePackageClick }) => {
  const [activeSlide, setActiveSlide] = useState(0);

  const onSnapToIndexCallback = (index) => {
    trackClickEvent({ eventName: `Package_image_Package_${packageId}_${index}` });
    setActiveSlide(index);
  };

  const renderItem = (item) => {
    return (
      <View style={styles.slide}>
        <TouchableOpacity onPress={handlePackageClick} activeOpacity={1}>
          <HolidayImageHolder imageUrl={item?.fullPath} style={styles.image} />
        </TouchableOpacity>
      </View>
    );
  };

  const sliderWidth = Dimensions.get('window').width - SLIDER_SPACING;
  const itemWidth = sliderWidth;

  return (
    <View style={styles.container}>
      <Carousel
        data={cardImages}
        showBullets={cardImages?.length > 1}
        renderItem={renderItem}
        sliderWidth={sliderWidth}
        itemWidth={itemWidth}
        layout="default"
        loop={true}
        autoplay={false}
        useScrollView={true}
        autoplayInterval={5000}
        onSnapToItem={onSnapToIndexCallback}
        inactiveSlideScale={1}
        enableMomentum={true}
      />
      {/* // TODO to check in app and multiple images in web */}
      {/* <View style={styles.dotsContainer}>
        <Pagination
          dotsLength={cardImages.length}
          activeDotIndex={activeSlide}
          dotStyle={styles.dot}
          inactiveDotOpacity={1}
          inactiveDotScale={1}
          inactiveDotStyle={styles.inactiveDot}
          containerStyle={styles.paginationContainer}
          dotContainerStyle={styles.dotContainerStyle}
        />
      </View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // position:'relative',
  },
  slide: {
    width: '100%',
    height: 152,
    overflow: 'hidden',
    // ...marginStyles.mb4,
  },
  image: {
    width: '100%',
    height: 152,
  },
  dotsContainer: {
    position: 'absolute',
    bottom: -9,
    alignSelf: 'center',
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pv4,
    flexDirection: 'row',
  },
  dot: {
    width: 4,
    height: 4,
    ...holidayBorderRadius.borderRadius4,
    backgroundColor: holidayColors.primaryBlue,
    alignSelf: 'center',
  },
  inactiveDot: {
    width: 4,
    height: 4,
    ...holidayBorderRadius.borderRadius4,
    backgroundColor: holidayColors.black,
    alignSelf: 'center',
  },
  paginationContainer: {
    ...paddingStyles.pv4,
    marginHorizontal: -12,
  },
  dotContainerStyle: {
    ...marginStyles.mh2,
  },
});

export default PackageImageCarousal;
