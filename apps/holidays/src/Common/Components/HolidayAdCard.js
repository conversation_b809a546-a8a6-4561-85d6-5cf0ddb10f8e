import React, { useEffect, useState } from 'react';
import { Platform, View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import RNAdWrapper from 'react-native-ad-wrapper';
import AdComponent from 'ad-react-wrapper';
import ErrorBoundary from '@mmt/legacy-commons/adsConfig/errorBoundary';
import HolidayImageHolder from './HolidayImageHolder';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_package_default.webp';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';
import {isRawClient} from "../../utils/HolidayUtils";
import adTechConfigSetter from '@mmt/legacy-commons/adsConfig/configMobile'; // Adtech config

export const FallBackWrapper = ({
  card,
  data,
  styles = {},
  resizeMode = '',
  onPress,
  fullWidth,
}) => {
  return (
    <TouchableOpacity
      onPress={() => onPress(card, data)}
      activeOpacity={0.8}
      style={fullWidth ? fullWidth : []}
    >
      <HolidayImageHolder
        style={styles.adImage}
        imageUrl={card.image}
        defaultImage={genericCardDefaultImage}
        {...(resizeMode ? { resizeMode } : {})}
      />
    </TouchableOpacity>
  );
};
FallBackWrapper.propTypes = {
  card: PropTypes.object,
  data: PropTypes.object,
  styles: PropTypes.object,
  resizeMode: PropTypes.string,
  onPress: PropTypes.func,
};

FallBackWrapper.defaultProps = {
  card: {},
  data: {},
  styles: {},
  resizeMode: '',
  onPress: null,
};

const AdCardWrapper = ({ uuid, onError, styles, adDimensions = {} }) => {
  if (isRawClient()) {
    return (
      <ErrorBoundary>
        <View style={styles.adImage}>
          <AdComponent uuid={uuid} />
        </View>
      </ErrorBoundary>
    );
  }
  return (
    <ErrorBoundary>
      <View style={styles.adImage}>
        <RNAdWrapper
          setter={adTechConfigSetter}
          uuid={uuid}
          topWindowThreshold={60}
          onError={onError}
          horizontal_margin={0}
          {...(!isEmpty(adDimensions) && { style: adDimensions })}
        />
      </View>
    </ErrorBoundary>
  );
};
AdCardWrapper.propTypes = {
  uuid: PropTypes.string.isRequired,
  onError: PropTypes.func,
  styles: PropTypes.object,
  adDimensions: PropTypes.object,
};

AdCardWrapper.defaultProps = {
  onError: () => {},
  styles: {},
};

const MemoizedAdCardWrapper = React.memo(AdCardWrapper);

const HolidayAdCard = ({ card, data, adStyles, onPress, adDimensions = {}, showOnlyAd }) => {
  const { contextId: uuid = '' } = card;
  const [hideview, setHideView] = useState(false);
  const [showFallBack, setShowFallBack] = useState(false);
  if (!uuid) {
    return null;
  }

  const onError = () => {
    if (!showOnlyAd) setShowFallBack(true);
    else {
      setHideView(true);
    }
  };
  return !hideview ? (
    <View style={[adStyles.styles.adCardContainer, { overflow: 'hidden' }]}>
      {showFallBack ? (
        <FallBackWrapper
          resizeMode={adStyles.resizeMode}
          styles={adStyles.styles}
          card={card}
          data={data}
          onPress={onPress}
        />
      ) : (
        <MemoizedAdCardWrapper
          uuid={uuid}
          onError={onError}
          styles={adStyles.styles}
          adDimensions={adDimensions}
        />
      )}
    </View>
  ) : null;
};

HolidayAdCard.propTypes = {
  card: PropTypes.object,
  data: PropTypes.object,
  adStyles: PropTypes.object,
};

HolidayAdCard.defaultProps = {
  card: {},
  data: {},
  adStyles: {},
};
export default React.memo(HolidayAdCard);
