import React, { useEffect } from 'react';
import {
  BackHandler,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  Text,
  SafeAreaView,
} from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import { HARDWARE_BACK_PRESS } from '../../../SearchWidget/SearchWidgetConstants';
import { borderRadiusValues } from '../../../Styles/holidayBorderRadius';
import useBackHandler from '../../../hooks/useBackHandler';

/**
 * Usage
 *  <BottomModal children={[<SiddhiBanner/> <SomeOtherBanner/>]} toggleBannerState={toggleBannerState}/>}
 *
 *  Supports multiple children JSX
 */

const BottomSheet = ({childStyle = {}, isCloseBtnVisible, children, title, onBackPressed, isOpen,containerStyle = [] ,closeButtonStyle = [], animationType = 'fade', marginBottom = 15, marginTop = 0}) => {
  useBackHandler(() => {
    if (isOpen) {
      handleBackPress();
      return true;  // Prevent default back behavior when modal is open
    }
    return false;  // Allow system to handle back press when modal is closed
  });

  const handleBackPress = () => {
    onBackPressed?.();
  };

  if (!children) {
    log.error('BottomModal requires at least one child to render.');
    return null;
  }

  return (
    <Modal
      visible={isOpen}
      animationType={animationType}
      transparent={true}
      onRequestClose={handleBackPress}
    >
      <View style={styles.bottomSheetContainer}>
        <TouchableOpacity
          activeOpacity={1}
          style={styles.overlay}
          onPress={handleBackPress}
         />
        <View style={[styles.container,containerStyle ]}>
            {isCloseBtnVisible || title ? (
              <View style={[styles.header,closeButtonStyle ]}>
                {isCloseBtnVisible ? (
                  <TouchableOpacity onPress={handleBackPress}>
                    <Image source={CrossIcon} style={styles.crossIcon} />
                  </TouchableOpacity>
                ) : (
                  []
                )}

                {title ? <Text style={styles.title}>{title}</Text> : null}
              </View>
            ) : null}
            <View style={[{marginBottom, marginTop}, childStyle]}>{children}</View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  bottomSheetContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    flex: 1,
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    top: 0,
    left: 0,
    right: 0,
  },
  container: {
    backgroundColor: '#fff',
    borderTopRightRadius: borderRadiusValues.br16,
    borderTopLeftRadius: borderRadiusValues.br16,
    padding: 13,
    maxHeight: '75%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    marginBottom: 15,
    minHeight: 40,
  },
  crossBtn: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  crossIcon: {
    height: 12,
    width: 12,
    marginVertical: 4,
  },
  title: {
    fontSize: 18,
    lineHeight: 18,
    fontFamily: 'lato',
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
    paddingTop: 8,
  },
});
export default BottomSheet;
