import React from 'react';
import { Range } from 'rc-slider';
import 'rc-slider/assets/index.css';


const MultiSlider = (props) => {
    return <Range
        key={Date.now()}
        defaultValue={props.values}
        min={props.min}
        max={props.max}
        onChange={props.onChange}
        onAfterChange={props.onValuesChange}
        step={props.step}
        trackStyle={props.trackStyle}
        style={props.style}
        allowCross={props.allowOverlap}
    />;
};
export default MultiSlider;
