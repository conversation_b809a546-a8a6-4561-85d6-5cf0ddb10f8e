import React from 'react';
import { Image, Text, View, ScrollView, StyleSheet, TouchableOpacity, Platform, StatusBar } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import useBackHandler from '../../../hooks/useBackHandler';
const errorIcon = require('@mmt/legacy-assets/src/error.webp');

/* Components */
import PageHeader from '../PageHeader';


const GenericErrorPage = ({ onBackPress = () => true, title = '', description = '', pageHeader = '', children }) => {
  
  useBackHandler(onBackPress);
  let messageTitle = 'No packages found';
  let messageDesc = 'Try typing something else to find the holiday\n' + 'package of your choice.';

  return (
    <View style={styles.pageWrapper}>
      <PageHeader
        showBackBtn
        showShadow
        title={pageHeader || 'PACKAGES SUITABLE FOR YOU'}
        onBackPressed={onBackPress}
        containerStyles={paddingStyles.pa16}
      />
      <ScrollView contentContainerStyle={styles.pageContent}>
        <View style={styles.messageContent}>
          <Image style={styles.messageImg} source={errorIcon} />
          <Text style={styles.messageTitle}>{title || messageTitle}</Text>
          <Text style={styles.messageDesc}>{description || messageDesc}</Text>
        </View>
        {children}
      </ScrollView>
    </View>
  );
};

const styles = {
  pageWrapper: {
    flex: 1,
  },
  pageContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    height: '100%',
  },
  messageContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageImg: {
    height: 200,
    width: 200,
    resizeMode: 'contain',
    marginBottom: 40,
  },
  messageTitle: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    ...marginStyles.mb10,
    textAlign: 'center',
  },
  messageDesc: {
    ...fontStyles.labelBaseRegular,
    textAlign: 'center',
    lineHeight: 21,
    color: holidayColors.lightGray,
    opacity: 0.7,
    marginBottom: 40,
  },
};

export default GenericErrorPage;
