import React, { useState } from 'react';
import entities from 'entities';
import { View, Text, TouchableOpacity } from 'react-native';
import { actionStyle } from '../../PhoenixDetail/Components/DayPlan/dayPlanStyles';
import { isEmpty } from 'lodash';
import { marginStyles } from '../../Styles/Spacing';
import { isMobileClient, isRawClient } from '../../utils/HolidayUtils';

const HTMLView = isMobileClient()
? require('react-native-htmlview').default
: require('../../Common/Components/HTML').default;

const ReadMoreReadLessHTMLText = ({
  textValue = '',
  limit = 2,
  textStyle = {},
  anchorStyle = null,
  clickEventParams = {},
  containerStyle = {},
  handleOnClick = null,
  hideReadMoreText = false,
  captureClickEvents= () => {}
}) => {
  if (isEmpty(textValue)) {
    return [];
  }
  const [isWrapped, setIsWrapped] = useState(false);
  const textSplit = textValue.replace(/([.?!])\s*(?=[A-Z,a-z])/g, '$1|').split('|'); //TODO this is only used for HTML TEXT

  const readMore = React.useMemo(() => {
    if (textSplit.length <= limit) {
      return false;
    }
    return true;
  }, [textSplit]);

  React.useEffect(() => {
    if (textSplit.length > limit) {
      setIsWrapped(true);
    }
  }, []);
  const visibleText = isWrapped ? textSplit.slice(0, limit).join(' ') : textValue;

  
  const handleCollapse = () => {
    if (!isEmpty(clickEventParams)) {
      const readText = isWrapped ? 'Readmore' : 'Readless';
      const {component = '', sectionName = '', prop1 =''   } = clickEventParams;
      captureClickEvents({
        eventName :  `${readText}_${component}_${sectionName}`,
        prop1,
      })
    }
    setIsWrapped(!isWrapped);
  };

  const handleOnPress = () => {
    if (handleOnClick) {
      handleOnClick();
    } else {
      handleCollapse();
    }
  };

  const renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.type === 'tag') {
      if (node.name === 'p') {
        return (
          <Text ellipsizeMode={'clip'} style={textStyle.paragraph}>
            {defaultRenderer(node.children, parent)}
          </Text>
        );
      } else if (node.name === 'b') {
        return <Text style={textStyle.bold}>{defaultRenderer(node.children, parent)}</Text>;
      }
    } else if (node.type === 'text') {
      return <Text style={textStyle.paragraph}>{entities.decodeHTML(node.data)}</Text>;
    }
    return undefined;
  };

  return (
    <View style={[{ flex: 1 }, containerStyle]}>
      <HTMLView
        value={`<p>${visibleText}</p>`}
        stylesheet={isRawClient()  ? textStyle.default : { ...textStyle, flex: 1 }}
        renderNode={renderNode}
      />
      {readMore && !hideReadMoreText && (
        <TouchableOpacity onPress={handleOnPress}>
          <Text style={[anchorStyle || actionStyle, marginStyles.mv2]}>{isWrapped ? 'Read More' : 'Read Less'}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ReadMoreReadLessHTMLText;
