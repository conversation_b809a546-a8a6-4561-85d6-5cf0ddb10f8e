import React from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import { getFontFamily } from '../../theme';
import { FONT_KEYS } from '../../theme/font';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles } from '../../Styles/Spacing';

const BRANDING_TEXT = 'Powered By';
const MMT_LOGO = require('@mmt/legacy-assets/src/holiday-iconMmt.webp');
const MMT_LOGO_WHITE =require ('@mmt/legacy-assets/src/MMTwhite.webp')

const PoweredByMMT = ({isLanding}) => (
  <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
    <Text style={[isLanding? styles.fontColor:AtomicCss.defaultText, AtomicCss.marginRight5, AtomicCss.font10,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>{BRANDING_TEXT}</Text>
    <Image style={styles.brandImg} source={isLanding ? MMT_LOGO_WHITE :MMT_LOGO} />
  </View>
);

const styles = StyleSheet.create({
  brandImg: {
    width: 56,
    height: 18,
    ...marginStyles.mt2,
  },
  fontColor:{
    color:holidayColors.white,
  }
});

export default PoweredByMMT;