import React, {useState} from 'react';
import PropTypes from 'prop-types';
import {Image, View} from 'react-native';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';
import {isEmpty} from 'lodash';
import {
    getOptimisedUrlForFastImage,
    isAndroidClient,
    isAppPlatform,
    isMobileClient,
    isRawClient,
} from '../../utils/HolidayUtils';
 
const HolidayImageHolder = ({style, imageUrl, defaultImage, resizeMode, containerStyles = {}}) => {
    const [showDefaultImage, setShowDefaultImage] = useState(false);
    const FastImage = isRawClient() ? Image : require('react-native-fast-image');
    const [dimensions ,setDimensions] = useState({});
    const onLoadError = () => {
        setShowDefaultImage(true);
    };
 
    const extraProps = {
        ...(style?.tintColor && {tintColor: style.tintColor}),
    };
 
    let IMAGE_URL = imageUrl?.trim() 
    const normalisedSource = !isEmpty(IMAGE_URL) && (IMAGE_URL.split('https://')[1] || IMAGE_URL.split('http://')[1]) ? IMAGE_URL : null;
    const normalisedSourceString = !isEmpty(IMAGE_URL) && (IMAGE_URL.split('https://')[1] || IMAGE_URL.split('http://')[1]) ? IMAGE_URL : '';
    const showNoImage = isEmpty(normalisedSourceString)  ||  showDefaultImage;
    const platformAndroid = isAndroidClient();
   
    const getImageSource = () => {
        if (showNoImage) {
          return defaultImage;
        }
        const retObj = { uri: normalisedSource };
        if (isAppPlatform()) {
          retObj.priority = platformAndroid ? FastImage.priority.high : FastImage.priority.normal;
        }
        return retObj;
    };
 
    const ImageComponent = (
        <FastImage
            style={style}
            onError={onLoadError}
            source={getImageSource()}
            resizeMode={resizeMode}
            {...extraProps}
        />
    );
 
    return isMobileClient()
        ? <View style={containerStyles}>{ImageComponent}</View>
        : ImageComponent;
};
 
HolidayImageHolder.defaultProps = {
    style: null,
    imageUrl: null,
    defaultImage: genericCardDefaultImage,
    resizeMode: 'cover',
};
 
HolidayImageHolder.propTypes = {
    style: PropTypes.object,
    imageUrl: PropTypes.string,
    // defaultImage: PropTypes.number,
    resizeMode: PropTypes.string,
};
export default HolidayImageHolder;