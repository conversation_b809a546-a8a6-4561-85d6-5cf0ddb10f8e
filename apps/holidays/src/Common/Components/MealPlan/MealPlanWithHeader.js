import React, { memo } from 'react';
import { StyleSheet, Text, View, FlatList } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const MealPlanWithHeader = memo(({ dayMeals = [], containerStyle = null, mealItemContainerStyles = {} }) => {
  // If there are no meals for the day, return an empty view
  if (!dayMeals?.length) {
    return <View />;
  }

  // Render each day's meal plan
  const renderDayMeal = ({ item: dayMenu }) => {
    const { heading: dayHeading, menu = [] } = dayMenu || {};

    return (
      <View style={[styles.mealItemContainer, mealItemContainerStyles]}>
        {/* Render the day's heading */}
        <Text style={styles.heading}>{dayHeading}</Text>

        {/* Render the meal options for the day */}
        {renderMealOptions(menu)}
      </View>
    );
  };

  // Render each meal option
  const renderMealOptions = (mealOptions = []) => {
    return mealOptions.map((option, index) => {
      const { heading: optionHeading, description } = option || {};

      // If the option has no heading or description, skip rendering it
      if (!optionHeading || !description) {
        return null;
      }

      return (
        <View key={index} style={styles.mealOption}>
          {/* Render the option's heading */}
          <Text style={styles.optionText}>{optionHeading}</Text>

          {/* Render the option's description */}
          <Text style={styles.subHeading}>{description}</Text>
        </View>
      );
    });
  };

  return (
    <View style={containerStyle ? containerStyle : { flex: 1 }}>
      <FlatList
        data={dayMeals}
        renderItem={renderDayMeal}
        keyExtractor={(_, index) => index.toString()}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  mealItemContainer: {
    paddingHorizontal: 27,
  },
  mealOption: {
    marginLeft: 5,
  },
  heading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
    ...marginStyles.mt20,
  },
  subHeading: {
    marginTop: 4,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  optionText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    paddingTop: 8,
  },
});

export default MealPlanWithHeader;
