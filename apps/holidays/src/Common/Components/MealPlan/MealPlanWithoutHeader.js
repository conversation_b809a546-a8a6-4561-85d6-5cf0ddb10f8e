import React, { memo } from 'react';
import { StyleSheet, Text, View, FlatList } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

// Memoize the component to avoid unnecessary re-renders
const MealPlanWithoutHeader = memo(({ dayWiseMeals = [] }) => {

    // If dayWiseMeals is null, undefined, or empty, return an empty view
    if (!dayWiseMeals?.length) {
        return <View />;
    }

    // Render each meal in the FlatList
    const renderMeal = ({ item: meal }) => {
        return (
            <View style={styles.mealContainer}>
                <Text style={styles.mealText}>{meal}</Text>
            </View>
        );
    };

    // Use the FlatList component to render the list of meals
    return (
        <View style={{flex: 1}}>
        <FlatList
            data={dayWiseMeals}
            renderItem={renderMeal}
            keyExtractor={(_, index) => index.toString()}
        />
        </View>
    );
});

// Define the styles for the component
const styles = StyleSheet.create({
    mealContainer: {
        marginBottom: 10,
        marginStart:33,
        marginTop:4,
    },
    mealText: {
        color: holidayColors.black,
        ...fontStyles.labelBaseRegular,
    },
});

// Export the component as the default export
export default MealPlanWithoutHeader;
