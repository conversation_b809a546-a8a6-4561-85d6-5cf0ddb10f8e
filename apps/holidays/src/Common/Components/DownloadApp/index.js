import React from 'react';
import PwaHeaderStrip from 'MMT-UI/cards/pwaHeaderStrip';

import { StyleSheet, View } from 'react-native';
import { isNonMMTAffiliate } from '@mmt/holidays/src/utils/HolidayUtils';
import { getAffiliate } from '@mmt/holidays/src/theme';

const DownloadApp = () => {
  //@todo Ashish text needs to be confirmed by <PERSON><PERSON><PERSON><PERSON><PERSON>
  if(isNonMMTAffiliate(getAffiliate())){
    return null;
  }
  return (
    <View style={styles.container}>
      <PwaHeaderStrip
        heading={'<font face="Lato"><span style="font-weight: 900;">Upto 25% OFF*</span> on your first holiday booking</font>'}
        text={'<font face="Lato" color="#4A4A4a">Use code <b>WELCOMEMMT</b></font>'}
        style={{ width:'auto' }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // width: '87%',
  },
});
export default DownloadApp;
