import React from 'react';
import PropTypes from 'prop-types';
import {
  StyleSheet,
  View,
  Image,
  TouchableWithoutFeedback,
  StatusBar,
  TouchableOpacity,
  Dimensions,
  Platform,
  PanResponder,
} from 'react-native';
import InstaStoryTab from './InstaStoryTab';
import InstaStoryInfoSection, {DEFAULT_UP_BOTTOM} from './InstaStoryInfoSection';
import {
  isMobileClient,
  isNotNullAndEmptyCollection,
  isNullOrEmptyCollection, isRawClient,
} from '../../utils/HolidayUtils';
import {MAX_STORY_COUNT} from '../../HolidayConstants';
import Loader from '../../SearchWidget/Components/Loader';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';

const ImageLoadingState = {
  LOADING: 'loading',
  COMPLETED: 'completed',
};

export default class HolidayInstaStory extends React.Component {
  constructor(props) {
    super(props);
    const screenDimension = Dimensions.get('screen');
    this.screen = {
      width: screenDimension.width,
      height: screenDimension.height,
    };
    this.imgRef = null;
    this.inTime = 0;
    this.outTime = 0;
    const count = props.count ? Math.min(MAX_STORY_COUNT, props.count) : MAX_STORY_COUNT;
    const images = this.removeDuplicatesFromImages(props.images);
    this.state = {
      images: isNotNullAndEmptyCollection(this.removeDuplicatesFromImages(images)) ?
        images.slice(0, count) : [],
      active: 0,
      isAnimationStopped: true,
      isImgLoading: true,
      upContainerBottom: DEFAULT_UP_BOTTOM,
      loading: ImageLoadingState.LOADING,
    };

    this.next = this.next.bind(this);
    this.handlePressIn = this.handlePressIn.bind(this);
    this.handlePressOut = this.handlePressOut.bind(this);
    this.continueAnimation = this.continueAnimation.bind(this);
    this.setImageOpacity = this.setImageOpacity.bind(this);
    this.previousDy = 0;
    this.moveYDirection = null;
    this.createPanResponder();
  }

  createPanResponder() {
    this.panResponder = PanResponder.create({
      onMoveShouldSetPanResponder: (event, gestureState) => true,
      onPanResponderMove: this.onPanResponderMove.bind(this),
      onPanResponderRelease: this.onPanResponderRelease.bind(this),
    });
  }

  onPanResponderMove(event, gestureState) {
    // stop timer
    this.handlePressIn();
    this.moveYDirection = gestureState.dy - this.previousDy <= 0 ? 'up' : 'down';
    this.previousDy = gestureState.dy;
    this.setState({
      upContainerBottom: DEFAULT_UP_BOTTOM + Math.abs(gestureState.dy),
    });
    const imageOpacity = 1 - (Math.abs(gestureState.dy) / Dimensions.get('screen').height);
    this.setImageOpacity(imageOpacity);
  }

  onPanResponderRelease(event, gestureState) {
    if (this.moveYDirection === 'down') {
      this.setState({
        upContainerBottom: DEFAULT_UP_BOTTOM,
      });
      this.setImageOpacity(1);
      this.continueAnimation();
    } else {
      // exit
      this.props.onInstaStoryDone();
    }
  }

  removeDuplicatesFromImages = (images) => {
    const retImages = [];
    if (isNotNullAndEmptyCollection(images)) {
      const imageUrlsSet = new Set();
      for (let i = 0; i < images.length; i += 1) {
        if (!imageUrlsSet.has(images[i].path)) {
          retImages.push(images[i]);
          imageUrlsSet.add(images[i].path);
        }
      }
    }
    return retImages;
  }

  static navigationOptions = {
    header: null,
  };

  componentDidMount() {
    this.prefetchImage();
    StatusBar.setHidden(true);
  }

  componentWillUnmount() {
    StatusBar.setHidden(false);
  }

  continueAnimation() {
    this.setState({
      isAnimationStopped: false,
    });
  }

  async prefetchImage() {
    const urls = this.state.images;
    if (isNullOrEmptyCollection(urls)) {
      return;
    }
    for (let index = 0; index <= urls.length; index += 1) {
      const url = urls[index];
      if (url.path) {
        this.preFetchImageForUrl(url.path);
      }
    }
  }

  async preFetchImageForUrl(url) {
    if (!url || url.length === 0) {
      this.setState({loadingState: ImageLoadingState.LOADING});
      return;
    }
    if (isMobileClient()) {
      const cacheData = await Image.queryCache([url]);
      if (cacheData[url]) {
        this.setState({loadingState: ImageLoadingState.COMPLETED});
        return;
      }
    }
    await Image.prefetch(url)
      .then(() => {
        this.setState({loadingState: ImageLoadingState.COMPLETED});
      })
      .catch(() => {
        this.setState({loadingState: ImageLoadingState.LOADING});
      });
  }

  handlePressIn() {
    this.inTime = Date.now();
    this.setState({
      isAnimationStopped: true,
    });
  }

  handlePressOut(direction) {
    this.outTime = Date.now();
    const diff = this.outTime - this.inTime;

    if (diff > 150) {
      this.setState({
        isAnimationStopped: false,
      });
    } else if (direction === 'back' && this.state.active !== 0) {
      this.setState({
        active: this.state.active - 1,
        isAnimationStopped: true,
      });
    } else {
      this.next();
    }
  }

  loadingDone = () => {
    this.setState({isAnimationStopped: false, isImgLoading: false});
  };

  loadingStart = () => {
    this.setState({isImgLoading: true});
  };

  next() {
    const {active, images} = this.state;
    if (active + 1 === images.length) {
      this.props.onInstaStoryDone();
    } else {
      this.setState({
        active: active + 1,
        isAnimationStopped: true,
      });
    }
  }

  setImageOpacity(opacity) {
    this.imgRef.setNativeProps({
      opacity: opacity || 1,
    });
  }

  render() {
    const {
      images,
      active,
      isAnimationStopped,
      isImgLoading,
    } = this.state;
    const tabContainerStyle = [styles.tabContainer];
    // if (isAnimationStopped) {
    //   tabContainerStyle.push({opacity: 0});
    // }
    const activeImage = images[active];
    return (
      <View
        {...this.panResponder.panHandlers}
        style={styles.container}>
        {(isImgLoading) &&
        <Loader
          position="center"
          loadingColor="#008cff"
          width={0.4}
        />
        }
        <TouchableWithoutFeedback
          onPressIn={(event) => {
            if (event && isRawClient()) {
              event.preventDefault();
            }
            this.handlePressIn();
          }}
          onPressOut={(event) => {
            if (event && isRawClient()) {
              event.preventDefault();
            }
            this.handlePressOut();
          }}
        >
          <Image
            ref={component => this.imgRef = component}
            fadeDuration={0}
            source={{uri: activeImage.path}}
            resizeMode="cover"
            style={[styles.image, {opacity: isImgLoading ? 0.2 : 1}]}
            onLoadEnd={this.loadingDone}
            onLoadStart={this.loadingStart}
          />
        </TouchableWithoutFeedback>

        {Platform.OS !== 'web' &&
        <TouchableOpacity
          activeOpacity={0.9}
          style={styles.back}
          onPressIn={this.handlePressIn}
          onPressOut={() => this.handlePressOut('back')}
        />}


        <View style={tabContainerStyle} isAnimationStopped={isAnimationStopped}>
          {images.map((image, index) => (
            <InstaStoryTab
              key={index}
              isActive={index === active}
              activeIndex={active}
              index={index}
              next={this.next}
              isAnimationStopped={isAnimationStopped}
            />
            ))}
        </View>
        <InstaStoryInfoSection
          isAnimationStopped={isAnimationStopped}
          onInstaStoryDone={this.props.onInstaStoryDone}
          title={activeImage.title}
          stopAnimation={this.handlePressIn}
          continueAnimation={this.continueAnimation}
          swipeUpText={this.props.swipeUpText || 'Swipe up'}
          setImgOpacity={this.setImageOpacity}
          upContainerBottom={this.state.upContainerBottom}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    ...Platform.select({
      ios: {
        zIndex: 26,
        marginTop: -statusBarHeightForIphone,
      },
      web: {
        height: Dimensions.get('window').height,
      },

    }),
  },
  imgContainer: {
    ...Platform.select({
      web: {
        height: '100vh',
      },
    }),
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    position: 'absolute',
    top: 0,
    width: '100%',
    zIndex: 1,
    paddingTop: 10,
    paddingBottom: 5,
    backgroundColor: 'rgba(0,0,0,0.1)',
    ...Platform.select({
      ios: {
        paddingTop: statusBarHeightForIphone + 10,
      },
      android: {
        paddingTop: 10,
      },
    }),
  },
  image: {
    position: 'absolute',
    height: '100%',
    width: '100%',
  },
  text: {
    color: '#fff',
  },
  back: {
    height: '100%',
    width: '30%',
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,

  },
});

HolidayInstaStory.propTypes = {
  count: PropTypes.number.isRequired,
  images: PropTypes.arrayOf(PropTypes.object).isRequired,
  onInstaStoryDone: PropTypes.func.isRequired,
  swipeUpText: PropTypes.string,
};
