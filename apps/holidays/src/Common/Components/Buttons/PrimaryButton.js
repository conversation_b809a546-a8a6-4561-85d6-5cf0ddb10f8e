import React from 'react';
import { StyleSheet } from 'react-native';
import { getPrimaryButtonColor, getSecondaryButtonColor, holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import Button from '@Frontend_Ui_Lib_App/Button';
import { getButtonStyle } from 'apps/holidays/src/theme';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const PrimaryButton = ({
      buttonText,
      handleClick,
      btnContainerStyles = {},
      isDisable: isDisabled,
      children = null,
      startIcon = '',
   }) => {
    const buttonBgColors = [getPrimaryButtonColor(), getSecondaryButtonColor()];
  return (
        <Button
            buttonText={buttonText}
            onButtonPress={handleClick}
            buttonSize="xl"
            buttonType="fill"
            buttonWidth="full"
            buttonBgColors={buttonBgColors}
            customStyle={{
              buttonWrapperStyle: [styles.btnWrapperStyle],
              buttonTextStyle: [styles.textCta],
              buttonStyle: [btnContainerStyles,getButtonStyle(), styles.ctaContainer]
            }}
            btnActiveOpacity={0.4}
            disabled={isDisabled}
            children={children}
            startIcon={startIcon}
          />
  );
};

const styles = StyleSheet.create({
  btnWrapperStyle: {
    width: 'auto'
  },
  textCta: {
    color: holidayColors.white,
    // lineHeight is intentionally omitted here to avoid breaking text alignment.
    textAlign: 'center',
    fontFamily :fonts.black,
    fontSize:16,
    fontWeight:'900',
  },
});
export default PrimaryButton;
