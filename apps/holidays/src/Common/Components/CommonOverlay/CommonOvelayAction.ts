export const overlayActionTypes = {
    SHOW_OVERLAYS: 'show_overlays',
    HIDE_OVERLAYS: 'hide_overlays',
    CLEAR_OVERLAYS:'clear_overlays',
  };
  
  
  export const showCommonOverlay = (key, data = {}) => dispatch => {
    dispatch({
      type: overlayActionTypes.SHOW_OVERLAYS,
      key: key,
      data: data,
    });
  };
  
  export const hideCommonOverlay = (keys = []) => dispatch => {
    dispatch({
      type: overlayActionTypes.HIDE_OVERLAYS,
      overlays: keys,
    });
  };
  export const clearCommonOverlays = () => dispatch => {
    dispatch({
      type: overlayActionTypes.CLEAR_OVERLAYS,
    });
  };
  