import React from 'react';
import { Modal, View } from 'react-native';
import { connect } from 'react-redux';

import VppDetailPage from '../../../Common/Components/VisaProtectionPlan/VPPDetailPage'


export const COMMON_OVERLAYS = {
    VPP_OVERLAY: 'vpp_overlay',
  }
const ComponentMap = {
    [COMMON_OVERLAYS.VPP_OVERLAY]: VppDetailPage,
  };

const CommonOverlays = (props) => {
    const { overlayDataMap = {}, overlayMap = {}} = props || {};
    const renderComponent = (key, data) => {
      const Component = ComponentMap[key];
      return (
        <Modal visible={overlayMap[key]}>
          <Component {...data} />
        </Modal>
      );
    };
  
    const renderModal = (key, show) => {
      if (show) {
        const data = overlayDataMap[key];
        return renderComponent(key, data);
      }
      return [];
    };
  
    const overlaysArray = overlayMap ? Object.entries(overlayMap) : [];
    return <View>{overlaysArray.map(([key, value]) => renderModal(key, value))}</View>;
  };
  
  const mapStateToProps = (state) => ({
    ...state.holidaysCommonOverlays,
  });
  
  export default connect(mapStateToProps, null)(CommonOverlays);
  