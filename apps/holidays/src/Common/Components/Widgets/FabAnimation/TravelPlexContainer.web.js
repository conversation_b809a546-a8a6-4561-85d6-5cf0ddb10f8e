import React, { useCallback, useEffect, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { debounce, isEmpty } from 'lodash';
import HolidayDataHolder from '@mmt/holidays/src/utils/HolidayDataHolder';
import { holidayColors } from '@mmt/holidays/src/Styles/holidayColors';
import { fontStyles } from 'mobile-react-native-styles';
import { createQueryDto } from '@mmt/holidays/src/Common/Components/Widgets/FabAnimation/TravelPlex/TravelPlexUtils';
import { createChatID, doQuery } from '@mmt/holidays/src/utils/HolidayUtils';
import { TRACKING_EVENTS } from '@mmt/holidays/src/HolidayTrackingConstants';
import { showTravelPlex } from '@mmt/holidays/src/utils/HolidaysPokusUtils';
import { EVENT_NAMES, EVENT_TYPES } from '@mmt/holidays/src/utils/HolidayPDTConstants';
import { createChatConfigFromUrl } from '@mmt/holidays/src/Common/Components/Widgets/FabAnimation/RoadBlockUtils';
import TravelplexIcon from '@travelplex/floating-icon-web-pwa';

export const TRAVELPLEX_CONSTANTS = {
  CHAT_BOT__BRIDGE__GET_META_DATA: 'CHAT_BOT__BRIDGE__GET_META_DATA',
  CHAT_BOT__BRIDGE__TRIGGER_ANALYTICS: 'CHAT_BOT__BRIDGE__TRIGGER_ANALYTICS',
  CHAT_BOT__BRIDGE__OPTIONS_CLICKED: 'CHAT_BOT__BRIDGE__OPTIONS_CLICKED',
  CHAT_BOT__GET_META_DATA: 'CHAT_BOT__GET_META_DATA',
};

// Global registry to track active TravelPlex bot instances.
// This patch was added due to https://jira.mmt.live/browse/HLD-20705,
// where TravelPlex library issues caused the bot ref to be lost on re-init.
// Storing refs in a local registry helps retain them reliably.

const globalBotRegistry = {
  activeBots: new Map(),

  register(id, botRef) {
    this.activeBots.set(id, botRef);
  },

  unregister(id) {
    this.activeBots.delete(id);
  },

  getActiveBot() {
    // Find the first bot that has a valid ref and is likely active
    for (const [id, botRef] of this.activeBots.entries()) {
      if (botRef.current && typeof botRef.current.collapse === 'function') {
        return botRef.current;
      }
    }
    return null;
  },

  collapseActiveBot() {
    const activeBot = this.getActiveBot();
    if (activeBot) {
      activeBot.collapse();
      return true;
    }
    return false;
  }
};

// Type definitions
/**
 * @typedef {Object} ChatContext
 * @property {Object} contextMetadata
 * @property {Object} contextMetadata.pageContext
 * @property {Object} contextMetadata.searchContext
 * @property {Object} expertMetadata
 * @property {Object} botMetadata
 * @property {boolean} [forceFetch]
 * @property {string|null} [debugger_uuid]
 */

/**
 * TravelPlexContainer - Floating Action Button with TravelPlex Chat Integration
 *
 * Features:
 * 1. Normal FAB behavior: User clicks FAB → Chat opens
 * 2. Auto-open from deeplink: Supports notification-driven conversation continuity
 *
 * Deeplink Auto-Open Feature:
 * - When app is opened via deeplink with openTravelPlex=true, chat auto-opens
 * - activeConversationId from deeplink is passed to TravelPlex for conversation context
 * - Enables seamless user experience from notification → app → continued conversation
 *
 * Parameters from fabData:
 * - openTravelPlex: Boolean flag to trigger auto-opening
 * - activeConversationId: Conversation ID for context continuity
 */
const TravelPlexContainer = React.memo((props) => {
  const componentId = useMemo(() => `TP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, []);
  const {
    travelPlexConfigData = {}, fabData = {},
    containerStyle = {}, onChatClose, multipleCTA = true, trackPDTV3Event, trackLocalClickEvent,
    invalidateChatViewData = false,
    onTravelPlexDataUsed = () => {}, // Callback to a reset forceHitTravelplex flag
    roadBlockUrl = null,
  } = props;
  const { page_context = {},
    search_context = {},
    dynamicPackageId = '',
    product = [],
    to_date_time = '',
    attr1,
    attr2,
    attr4,
    hideInput = false,
  } = travelPlexConfigData;
  let getAQuoteClicked = false;

  const uniqChatId = createChatID();

  // Extract key data properties at the top
  const {
    lob = '',
    page_name = '',
    lob_category = '',
    prev_page_name = '',
    sub_page_name = '',
    funnel_step = '',
    navigation = '',
  } = page_context;

  const {
    from_date_time = null,
    pax = {},
    from = {},
    to = {},
    search_type = '',
    advance_purchase = '',
  } = search_context;

  const {
    details: paxDetails = {},
    count: paxCount = 0,
    rooms: paxRooms = 0,
  } = pax;

  const {
    adult: adultDetails = {},
    infants: infantDetails = {},
    child: childDetails = {},
  } = paxDetails;

  const {
    locus: fromLocus = {},
    location: fromLocation = {},
  } = from;

  const {
    locus: toLocus = {},
    location: toLocation = {},
  } = to;



  // Debounced function to handle chat close with duplicate call prevention
  const debouncedHandleCollapsed = useMemo(
    () => debounce(
      () => {
        if (onChatClose && !getAQuoteClicked) {
          trackTravelPlexEntryExit(EVENT_NAMES.PAGE_EXIT, 'close');
          onChatClose();
          getAQuoteClicked = false;
        }
      },
      500, // ms window to catch duplicates
      {
        leading: true,   // Execute immediately on first call
        trailing: false  // Don't execute after the delay
      }
    ),
    [onChatClose]
  );

  // Memoize the search context creation
  const searchContext = useMemo(() => {
    if (isEmpty(search_context)) {return {};}

    const searchContextObj = {
      'fromDateTime': {
        'timestamp': from_date_time,
      },
      'toDateTime': {
        'timestamp': to_date_time,
      },
      'lob': lob.toUpperCase(),
      'pax': [{
        'details': {
          'adult': {
            'count': adultDetails.count,
          },
          'infant': {
            'count': infantDetails.count,
          },
          'child': {
            'count': childDetails.count,
          },
        },
        'count': paxCount,
      }],
      'funnelSource': lob.toUpperCase(),
      'lobCategory': lob_category,
      'from': {
        'locus': {
          'id': fromLocus.locus_id,
          'country': fromLocation.country_code,
          'name': fromLocation.name || 'New Delhi',
          'cityName': fromLocation.name || 'New Delhi',
          'type': fromLocus.locus_type || 'CITY',
          'area': [fromLocation.name || 'New Delhi'],
        },
        'cityName': fromLocation.name || 'New Delhi',
      },
      'to': {
        'locus': {
          'id': toLocus.locus_id,
          'country': toLocation.country_code,
          'name': toLocation.name,
          'cityName': toLocation.name,
          'type': toLocus.locus_type,
          'area': [toLocation.name],
        },
        'cityName': toLocation.name,
      },
      'rooms': paxRooms,
      'travelPurposeOpted': true,
      'searchText': 'NA',
      'searchType': search_type,
      'advancePurchase': advance_purchase,
      'currency': '',
    };

 // Add product only when it's not empty
   if (!isEmpty(product)) {
      searchContextObj.product = product;
    }

    // Add toDateTime only when it's not empty
    if (to_date_time) {
      searchContextObj.toDateTime = {
        'timestamp': to_date_time,
      };
    }

    return searchContextObj;
  }, [
    from_date_time,to_date_time, lob, lob_category, adultDetails.count,
    infantDetails.count, childDetails.count, paxCount, fromLocus.locus_id,
    fromLocation.country_code, fromLocation.name, fromLocus.locus_type,
    toLocus.locus_id, toLocation.country_code, toLocation.name,
    toLocus.locus_type, paxRooms, search_type, advance_purchase, product,
  ]);
  const current_page_name = HolidayDataHolder.getInstance().getCurrentPage();
  const getPageName = ()=>{
   if (current_page_name === 'collections'){
    return 'listing';
   }
   return current_page_name;
  };
  const getprevPageName = ()=>{
   const prev_page_name = HolidayDataHolder.getInstance().getPrevPageName(current_page_name);
    if (prev_page_name === 'collections'){
     return 'listing';
    }
    return prev_page_name;
   };
  const pageContext = {
    'lob': lob.toUpperCase(),
    'lobCategory': lob_category,
    'pageName': getPageName() ,
    'prevPageName': getprevPageName() || '',
    'lobFunnel': '',
    'subpageName': sub_page_name || '',
    'funnelStep': funnel_step,
    'pgTimeInterval': '',
    'navigation': navigation || '',
    'subLob': 'MMT',
    'pageUrl': '',
  };

  // Memoize the chat configuration
  const chatConfig = useMemo(() => ({
    'context': {
      'lob': lob.toUpperCase(),
      'view': page_name,
      'lobCategory': lob_category,
      'platform': 'pwa-ashish-remove-this-before-sending-live', //todo remove this before sending live
    },
    'expertMetadata': {
      'lob': lob.toUpperCase(),
      'funnelType': '',
      'page': page_name,
      'crid': '',
      'itid': '',
      'redirectType': '',
      'summarize': '',
      'lobCode': 'B2C',
      'src': 'MMT',
      'currency': 'inr',
      'userCurrency': 'INR',
      'botType': 'TRAVELPLEX',
      'entityType': 'Funnel_Holiday',
      attr1,
      attr2,
      'attr3':'Chat',
      attr4,
      dynamicPackageId: dynamicPackageId || '',
      // Add activeConversationId from deeplink for conversation continuity
      // This allows TravelPlex to continue specific conversations when user opens app via notification
      ...(fabData.activeConversationId && { activeConversationId: fabData.activeConversationId }),
      hideInput,
      multipleCTA,
      uniqChatId,
      extraData: {...fabData, uniqChatId},
    },
    'contextMetadata': {
      'searchContext': searchContext,
      'pageContext': pageContext,
    },
    // Add activeConversationId from deeplink for conversation continuity
    // This allows TravelPlex to continue specific conversations when user opens app via notification
    ...(fabData.activeConversationId && {
      'botMetadata': {
        activeConversationId: fabData.activeConversationId,
      },
    }),
  }), [
    lob, page_name, lob_category, dynamicPackageId, searchContext,
    prev_page_name, sub_page_name, funnel_step, navigation, fabData.activeConversationId,
  ]);


  // Memoized callback for opening chat
  const openChat = useCallback(() => {
    // If This page has been opened from a road block
    if (!isEmpty(roadBlockUrl)){
      const config = createChatConfigFromUrl(roadBlockUrl, uniqChatId, fabData?.cmp);
      openTravelplexBot(config);
      return;
    }

    openTravelplexBot(chatConfig, invalidateChatViewData);

    // Call the callback to notify parent that TravelPlex data has been consumed
    // Only call when invalidateChatViewData is true, indicating new data was processed
    if (invalidateChatViewData) {
      onTravelPlexDataUsed();
    }
  }, [chatConfig, invalidateChatViewData, onTravelPlexDataUsed, roadBlockUrl, uniqChatId, fabData]);

  // Memoized callback for closing chat
  const closeChat = useCallback(() => {
    getAQuoteClicked = true;
    const success = globalBotRegistry.collapseActiveBot();
    if (!success) {
      console.error('[TravelPlexContainer] ERROR: No active bot found in global registry');
    }
  }, []);

  const trackTravelPlexEntryExit = (eventName, value = '') => {
    const { page_context = {} } = travelPlexConfigData || {};
    const { funnel_step = '', lob = 'holidays' } = page_context || {};
    // Track PDT Data
    trackPDTV3Event({
      actionType: {
        event_name: eventName,
        event_type: EVENT_TYPES.USER_ACTION,
      },
      value: 'new_myra_' + value,
      shouldTrackToAdobe:false
    });

    // Track Omni Data
    trackLocalClickEvent({
      omniData: {
        [TRACKING_EVENTS.M_V15]: eventName,
        [TRACKING_EVENTS.M_C54]: funnel_step ? lob + '_' + funnel_step : lob,
        [TRACKING_EVENTS.M_C14]: 'travelplex',
      },
    });
  };

  // Memoized callback for view state change
  const handleViewStateChange = useCallback((viewState) => {
    // Call onChatClose when chat is collapsed/closed
    if (viewState === 'collapsed') {
      debouncedHandleCollapsed();
    }
    if (viewState === 'expanded') {
      const eventValue = roadBlockUrl ? 'open_roadblock' : 'open';
      trackTravelPlexEntryExit(EVENT_NAMES.PAGE_ENTRY, eventValue);
    }
  }, [debouncedHandleCollapsed, roadBlockUrl, trackTravelPlexEntryExit]);

  const handleAction = useCallback((params) => {
    if (params.actionType === 'Analytics') {
      const tracking = params?.actionPayload?.tracking;
      const actionLob = tracking?.find(item => item?.actionLobHldTravelPlex);
      const omniData = tracking?.find(item => item?.omnitureEventDetail);
      const pdtData = tracking?.find(item => item?.pdtEventDetails);
      const { pdtTrackingId } = tracking?.find(item => item?.pdtTrackingId);

      if (pdtTrackingId === 'travelplex_help_cta_clicked') {
        // Handle headphone clicked on travelplex
        trackPDTV3Event({
          actionType: {
            event_name: 'button-clicked',
            event_type: 'action',
          },
          value: 'myra_query | headphone',
        });
        return;
      }

        if (actionLob) {
          trackPDTV3Event({
            actionType: {
              event_name: pdtData?.pdtEventDetails?.event_name,
              event_type: pdtData?.pdtEventDetails?.eventType,
            },
            value:pdtData?.pdtEventDetails?.event_value,
            shouldTrackToAdobe:false
          });
          trackLocalClickEvent({
            omniData: {
              [TRACKING_EVENTS.M_V15]: omniData?.omnitureEventDetail?.mv15,
              [TRACKING_EVENTS.M_C54]: omniData?.omnitureEventDetail?.m_c54,
              [TRACKING_EVENTS.M_C14]: omniData?.omnitureEventDetail?.prop14,
            },
          });
        }
    }
    if (params?.actionPayload?.option === 'schedule-callback-lob-flow') {
      closeChat();

      const queryDto = {
        ...createQueryDto(search_context, page_context, attr2),
        trackPDTV3Event,
      };
      //Enable this when we want to open query from not from deeplink.
      doQuery(queryDto);
    }
  }, [search_context, page_context, trackPDTV3Event, componentId, closeChat]);

  /**
   * Track TravelPlex CTA load event when component renders
   *
   * This tracks when the TravelplexFloatingCta component is loaded and visible to the user.
   */
  useEffect(() => {
    if (showTravelPlex()) {
      trackTravelPlexEntryExit(EVENT_NAMES.PAGE_RENEDERED, 'travel_plex_cta_load');
    }
  }, []); // Empty dependency array means this runs once when component mounts

  /**
   * Auto-open TravelPlex chat from deeplink notification flow
   *
   * Purpose: This feature enables users to continue specific conversations when they click
   * notifications that open the app via deeplink.
   *
   * Flow:
   * 1. User receives notification with deeplink containing:
   *    - openTravelPlex=true (flag to auto-open chat)
   *    - activeConversationId=<conversation_id> (specific conversation context)
   *
   * 2. User clicks notification → App opens via deeplink
   *
   * 3. HolidayDeeplinkParser parses deeplink parameters and adds them to holidayLandingGroupDto
   *
   * 4. createGroupingFabData extracts these parameters and includes them in fabData
   *
   * 5. TravelPlexContainer receives fabData with these parameters:
   *    - openTravelPlex triggers auto-opening of chat (this useEffect)
   *    - activeConversationId gets passed to TravelPlex bot via expertMetadata
   *    - isFromDeeplink indicates if this page was opened directly from deeplink
   *
   * 6. TravelPlex bot receives activeConversationId and can continue the specific conversation
   *
   * 7. Query planner within TravelPlex uses activeConversationId for conversation context
   *
   * Implementation Details:
   * - openTravelPlex: Boolean flag that triggers auto-opening of chat
   * - activeConversationId: String passed to expertMetadata for conversation continuity
   * - isFromDeeplink: Boolean flag indicating if page was opened directly from deeplink
   * - Only auto-opens when both openTravelPlex=true AND isFromDeeplink=true
   */
  useEffect(() => {
    if ((fabData.openTravelPlex && fabData.isFromDeeplink) || fabData.openTravelPlexForOldMyra) {
      openChat();
    }
  }, [fabData.openTravelPlex, fabData.isFromDeeplink, fabData.openTravelPlexForOldMyra]);

  const persuasions = [
    {
      html: "<span style='line-height:14px; font-family: Lato;'><span style='color:#000; font-size:16px;font-weight:400;'>Hey, </span><span style='background-image: linear-gradient(to  left, #824BC4, #5735B8); background-clip: text; -webkit-text-fill-color: transparent;font-size:18px;font-weight:900;line-height:20px;'><b>I am Myra</b></span></span>",
    },
  ];

  const TRAVELPLEX_BOT_URL = 'https://www.makemytrip.com/myra-v1?platform=pwa';

  const openTravelplexBot = (chatConfig, invalidateChatViewData) => {
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
    overlay.style.zIndex = '12';
    document.body.appendChild(overlay);

    const iframe = document.createElement('iframe');
    iframe.src = TRAVELPLEX_BOT_URL;
    iframe.style.width = '100vw';
    iframe.style.height = '100vh';
    iframe.style.position = 'fixed';
    iframe.style.bottom = '0%';
    iframe.style.zIndex = '13';
    iframe.style.border = 'none';
    document.body.appendChild(iframe);

    const closeIframe = () => {
      document.body.removeChild(iframe);
      document.body.removeChild(overlay);
    };

    overlay.addEventListener('click', closeIframe);

    window.addEventListener('message', event => {
      if (event.data.type === TRAVELPLEX_CONSTANTS.CHAT_BOT__BRIDGE__GET_META_DATA) {
        iframe.contentWindow?.postMessage(
          {
            type: TRAVELPLEX_CONSTANTS.CHAT_BOT__GET_META_DATA,
            payload: JSON.stringify({
              chatConfig, invalidateChatViewData
            }),
          },
          '*'
        );
      }
      if(event?.data?.type === TRAVELPLEX_CONSTANTS.CHAT_BOT__BRIDGE__TRIGGER_ANALYTICS) {
        console.log('>>>>>>> Trigree analytics');
        const payload = JSON.parse(event?.data?.payload || '{}');
        const eventDetail = payload?.payload?.tracking?.find(item => item.pdtEventDetails)?.pdtEventDetails;
        if(eventDetail) {
          // Handle PDT here @todo Ashish
          faOnTravelplex(eventDetail);
        }
      }
    });
  };

  const handleGetMetaDataMessage = () => {
    const iframe = document.getElementById('myra-assistive-app');
    console.log('>>>> From ICON click', iframe, iframe.contentWindow);
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage(
        {
          type: 'CHAT_BOT__GET_META_DATA',
          payload: JSON.stringify({
            chatConfig,
          }),
        },
        '*'
      );
    } else {
      console.warn('[TravelPlexContainer] myra-assistive-app iframe not found - TravelPlex may not be properly initialized for web');
    }
  };


  const handleMyraPDTTracking = (event) => {
    console.log('>>>>>> ', event);
  };

  return (
    <View style={{...styles.container ,...containerStyle }}>
      { showTravelPlex()  && <TravelplexIcon
          lob="Holidays"
          platform="desktop"
          frameStyles={{}}
          botStyles={{}}
          persuasions={persuasions}
          onBotGetMetaDataEvent={handleGetMetaDataMessage}
          onBotEvent={handleMyraPDTTracking}
        />
      }
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    zIndex: 14,
  },
  buttonText: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBold,
  },
});

TravelPlexContainer.displayName = 'TravelPlexContainer';

export default TravelPlexContainer;
