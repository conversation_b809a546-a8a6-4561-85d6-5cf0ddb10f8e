import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { TRIP_IDEAS_GALLERY_KEY } from 'mobile-holidays-react-native/src/HolidayConstants';
import { sortByKey } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { isEmpty } from 'lodash';

let isTIDeeplinkOpened = false;

export const setTIDeeplinkOpened = (value) => {
  isTIDeeplinkOpened = value;
};
export const getTIDeeplinkOpened = () => {
  return isTIDeeplinkOpened;
};
export const openTIDeepLink = ({ url = '' }) => {
  if (isEmpty(url)) {
    return;
  }

  setTIDeeplinkOpened(true);

  GenericModule.openDeepLink(url);
};

export const getEntrySectionCards = ({ cards = [], showGallery = false, galleryCards = [] }) => {
  let entrySectionCards = sortByKey({ obj: cards, key: 'order' });

  // Filter out gallery card if showGallery is false or gallery is not present in the data
  if (!showGallery || galleryCards.length <= 0) {
    return entrySectionCards?.filter((card) => card?.deeplinkKey !== TRIP_IDEAS_GALLERY_KEY);
  }
  return entrySectionCards.slice(0, 2) || [];
};
