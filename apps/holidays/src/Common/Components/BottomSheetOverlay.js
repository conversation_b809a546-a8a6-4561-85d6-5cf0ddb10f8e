import React from 'react';
import { Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import HolidayImageHolder from './HolidayImageHolder';
import BottomSheet from '@Frontend_Ui_Lib_App/BottomSheet';
import { isEmpty } from 'lodash';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles } from '../../Styles/Spacing/index.js';
import { RESIZE_MODE_IMAGE } from '../../HolidayConstants';

export const BottomSheetCross = ({ toggleModal, closeIconWrapper }) => {
  return (
    <TouchableOpacity onPress={toggleModal} style={[styles.crossIconContainer, closeIconWrapper]} activeOpacity={1}>
      <HolidayImageHolder defaultImage={CrossIcon} style={styles.crossIcon} />
    </TouchableOpacity>
  );
};

export const BottomSheetHeading = ({ title = '' }) => {
  return <Text style={styles.overlayHeading}>{title}</Text>;
};

const BottomSheetOverlay = ({
  title,
  children,
  toggleModal,
  containerStyles,
  childStyle = {},
  showCross = true,
  headingContainerStyles = {},
  headingTextStyle = {},
  bottomSheetStyle = {},
  headingIconUrl = '',
  visible,
  closeIconWrapper = {},
  onDismiss
}) => {
  return (
    <BottomSheet
      visible={visible}
      setVisible={toggleModal}
      customStyles={{
        containerStyle: [styles.wrapperStyle, containerStyles],
        bottomSheetStyle,
      }}
      onDismiss={onDismiss}
    >
      <View style={[styles.overlayHeadingContainer, headingContainerStyles]}>
        <View style={[styles.overlayHeadingContent, headingTextStyle]}>
          {headingIconUrl && (
            <HolidayImageHolder
              imageUrl={headingIconUrl}
              style={styles.headingIconStyles}
              resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
            />
          )}
          {!!title && <BottomSheetHeading title={title} />}
        </View>
        {showCross && <BottomSheetCross closeIconWrapper={closeIconWrapper} toggleModal={toggleModal} />}
      </View>
      <View style={!isEmpty(childStyle) ? childStyle : []}>{children}</View>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  // OVERLAY STYLES
  wrapperStyle: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  overlayHeadingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    elevation: 0,
    zIndex: 1,
  },
  overlayHeadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  overlayHeading: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    flex: 1,
    marginRight: 35
  },
  headingIconStyles: {
    width: 24,
    height: 24,
    ...marginStyles.mr10,
  },
  crossIconContainer: {
    backgroundColor: holidayColors.lightGray,
    borderRadius: 15,
    padding: 6,
    position: 'absolute',
    right: 0,
    top: 0,
    elevation: 1,
    zIndex: 1
  },
  crossIcon: {
    width: 12,
    height: 12,
    tintColor: holidayColors.white,
  },
  padding0: {
    padding: 0,
  },
});

export default BottomSheetOverlay;
