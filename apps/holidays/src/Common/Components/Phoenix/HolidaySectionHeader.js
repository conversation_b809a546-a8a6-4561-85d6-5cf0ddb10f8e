import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { isLuxeFunnel } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import MMTLUXEHeader from '../MMTLUXEHeader';

const MMTSectionHeader = ({ heading, subHeading, children, styles }) => (
  <View style={styles.headerContainer}>
    {!!heading && (
      <Text numberOfLines={1} style={headerStyles.heading}>
        {heading}
      </Text>
    )}
    {!!subHeading && (
      <Text numberOfLines={3} style={headerStyles.subHeading}>
        {subHeading}
      </Text>
    )}
  </View>
);

const HolidaySectionHeader = ({ heading = null, subHeading = null, children, styles }) => {
  return (
    <View style={styles.sectionHeaderContainer}>
      {isLuxeFunnel() ? (
        <MMTLUXEHeader heading={heading} subHeading={subHeading} />
      ) : (
        <MMTSectionHeader heading={heading} subHeading={subHeading} styles={styles} />
      )}
      {children}
    </View>
  );
};

const headerStyles = StyleSheet.create({
  heading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 26,
  },
  subHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },
});

HolidaySectionHeader.propTypes = {
  heading: PropTypes.string,
  subHeading: PropTypes.string,
  styles: PropTypes.object.isRequired,
};

export default HolidaySectionHeader;
