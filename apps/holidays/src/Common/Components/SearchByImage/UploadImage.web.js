import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Image,
  Button,
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  BackHandler,
  ScrollView,
} from 'react-native';
import { isEmpty } from 'lodash';
import { getCitiesFromImage } from '../../../utils/NetworkUtils/commonApis';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { getScreenWidth, isEmptyString, isRawClient } from '../../../utils/HolidayUtils';
import { DEST_SEARCH_TYPES, RESIZE_MODE_IMAGE } from '../../../HolidayConstants';
// import { Asset, ImageLibraryOptions, launchImageLibrary } from 'react-native-image-picker';
import { IMAGE_SEARCH_STATES, defaultErrorMessage, defaultServerErrorMessage } from './constants';
import { HARDWARE_BACK_PRESS } from 'apps/holidays/src/SearchWidget/SearchWidgetConstants';
import { enableSearchByImage, getSearchByImageWidth } from '../../../utils/HolidaysPokusUtils';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../HolidayImageUrls';
import { appendImgSearch, getBtnText, getResultList } from './utils';
import { onCardClicked } from 'apps/holidays/src/LandingNew/Utils/HolidayLandingUtilsV2';
import { setIsImageSearchFromBanner } from 'apps/holidays/src/utils/SectionVisitTracking';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';

/* Components */
import PageHeader from '../PageHeader';
import HolidayImageHolder from '../HolidayImageHolder';
import PrimaryButton from '../Buttons/PrimaryButton';
import ImageSearchContainer from './Components/SearchContainer';
import { TRACKING_EVENTS } from 'apps/holidays/src/HolidayTrackingConstants';
import useBackHandler from '../../../hooks/useBackHandler';

export const IMAGE_SEARCH_SUFFIX = 'IMG_SEARCH';
export const IMAGE_SEARCH_EVENT_PRE_FIX = 'imagesearch_';
const UploadImage = ({
  onDone = () => {},
  onBackPressed = () => {},
  trackClickEvent = () => {},
  isFromImageSearchBanner = false,
  onCitySelect = () => {},
}) => {
  const [processingImage, setProcessingImage] = useState(false);
  const [imageDetails, setImageDetails] = useState({});
  const [showImage, setShowImage] = useState(false);
  const [imageSearchState, setImageSearchState] = useState(IMAGE_SEARCH_STATES.UPLOAD);
  const [imageRespDetails, setImageRespDetails] = useState({});
  const [activeCityIndex, setActiveCityIndex] = useState(0);
  const { searchDestinationList = [], ctaConfig = {} } = imageRespDetails || {};
  const btnCtaText = getBtnText({ imageSearchState, showImage, imageRespDetails });
  const cityList = getResultList({ searchDestinationList });
  const { secondaryCtaUrl, name } = cityList?.[activeCityIndex] || {};
  const pageHeader =
    imageSearchState === IMAGE_SEARCH_STATES.UPLOAD ? 'Upload Image' : 'Search Results';
  const { displayName = '' } = ctaConfig?.['SECONDARY'] || {};

  useEffect(() => {
    return () => {
      setIsImageSearchFromBanner(false);
    };
  }, []);

  const handleBackPress = useCallback(() => {
    const prop1 =
      imageSearchState === IMAGE_SEARCH_STATES.UPLOAD
        ? showImage
          ? 'search'
          : 'upload'
        : 'result';
    trackClickEvent('imagesearch_back', '', prop1);
    onBackPressed();
    return true;
  }, [imageSearchState, showImage, trackClickEvent, onBackPressed]);

  useBackHandler(handleBackPress);

  const onImageSearchDone = ({ data = {} }) => {
    if (data?.success) {
      if (data?.searchDestinationList?.length === 0) {
        onDone({
          error: defaultErrorMessage,
        });
      } else {
        onDone({ citiesList: appendImgSearch(data?.searchDestinationList) });
      }
    }
    if (!data?.success) {
      if (!isEmptyString(data?.error?.message)) {
        onDone({
          error: data?.error || defaultErrorMessage,
        });
      } else {
        onDone({
          error: defaultServerErrorMessage,
        });
      }
    }
  };
  const onImageSearchDoneNew = ({ data = {} }) => {
    const {
      success = {},
      searchDestinationList = [],
      error = defaultErrorMessage,
      confidenceScore = '',
    } = data || {};
    if (success && !isEmpty(searchDestinationList)) {
      trackClickEvent('Search Result', '', data?.responseCategory)
      setImageSearchState(data?.responseCategory);
      setImageRespDetails(data);
    } else {
      const errorMessage = !isEmptyString(error?.message) ? error : defaultServerErrorMessage;
      if (!isEmpty(error?.errorType)) {
        trackClickEvent('Search Result', '', IMAGE_SEARCH_STATES.IMAGE_ERROR)
        setImageSearchState(IMAGE_SEARCH_STATES.IMAGE_ERROR);
        setImageRespDetails({
          title: error?.title,
          subTitle: error?.message,
          errorMessage: error?.error_message
        });
      } else {
        onDone({ error: errorMessage });
      }
    }
  };

  const getResults = async () => {
    const { base64, type = '' } = imageDetails || {};
    const data = await getCitiesFromImage({
      imageBase64String: base64 || '',
      extension: type?.split('/').pop(),
      source: isFromImageSearchBanner ? 'BANNER' : 'CTA',
    });
    setProcessingImage(false);
    if (enableSearchByImage()?.showNewSearchByImage) {
      onImageSearchDoneNew({ data });
      return;
    } else {
      onImageSearchDone({ data });
      return;
    }
  };

  const handleImageSearch = () => {
    trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, 'search');
    setProcessingImage(true);
    getResults();
  };

  const handleImageUpload = () => {
    const imageGalleryParams = {
      mediaType: 'photo',
      selectionLimit: 1,
      includeBase64: true,
      ...getSearchByImageWidth(),
    };
    trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, 'upload');
    if(isRawClient())
    {
      return ;
    }
    // launchImageLibrary(imageGalleryParams, async (res) => {
    //   if (res?.didCancel) {
    //     return;
    //   } else if (res.error) {
    //     trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, 'errorupload');
    //     return;
    //   }
    //   setImageDetails(res?.assets?.[0] || {});
    //   setShowImage(true);
    //   // if there is response then show image
    // });
  };

  const handleCitySelect = () => {
    const {
      displayName,
      name,
      branch,
      locusId = '',
      type = '',
      confidenceScore = {},
    } = cityList?.[activeCityIndex] || {};
    trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, btnCtaText, `${imageSearchState}|${name}`);
    onCitySelect(name, branch, locusId, {});
  };

  const handleTryAnotherImage = () => {
    trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, btnCtaText, `${imageSearchState}`, '', {
      omniData: { [TRACKING_EVENTS.M_V22]: imageRespDetails?.error_message },
    });
    setImageSearchState(IMAGE_SEARCH_STATES.UPLOAD);
    setShowImage(false);
  };

  const handleCitySearch = () => {
    const {
      displayName,
      name,
      branch,
      locusId = '',
      type = '',
      confidenceScore = {},
    } = cityList?.[activeCityIndex] || {};
    trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, btnCtaText, `${imageSearchState}|${name}`);
    onCardClicked({
      card: {
        destination: name,
        redirectionPage: 'GROUP',
      },
    });
  };

  const handleBtnClick = () => {
    switch (imageSearchState) {
      case IMAGE_SEARCH_STATES.UPLOAD:
        return showImage ? handleImageSearch : handleImageUpload;

      case IMAGE_SEARCH_STATES.PARTIAL_MATCH:
      case IMAGE_SEARCH_STATES.EXACT_MATCH:
      case IMAGE_SEARCH_STATES.EXPLORE:
        return isFromImageSearchBanner ? handleCitySearch : handleCitySelect;

      case IMAGE_SEARCH_STATES.IMAGE_ERROR:
        return handleTryAnotherImage;
      default:
        return () => {};
    }
  };

  const onSecondaryCtaClick = () => {
    const { secondaryCtaUrl, name } = cityList?.[activeCityIndex] || {};
    if (secondaryCtaUrl) {
      trackClickEvent('know_more', '', `${imageSearchState}|${name}`);
      GenericModule.openDeepLink(secondaryCtaUrl);
    }
  };

  return (
    <View style={styles.container}>
      <PageHeader
        title={pageHeader}
        onBackPressed={handleBackPress}
        showBackBtn
        containerStyles={styles.headerContainer}
      />
      <ScrollView
        contentContainerStyle={{
          justifyContent: 'space-between',
          flexGrow: 1,
        }}
      >
        <ImageSearchContainer
          imageDetails={imageDetails}
          showImage={showImage}
          processingImage={processingImage}
          imageSearchState={imageSearchState}
          imageRespDetails={imageRespDetails}
          activeCityIndex={activeCityIndex}
          setActiveCityIndex={setActiveCityIndex}
          trackClickEvent={trackClickEvent}
        />
        <View>
          <View style={styles.secondaryCtaContainer}>
            {!!displayName && !!secondaryCtaUrl && (
              <TouchableOpacity onPress={onSecondaryCtaClick}>
                <Text style={styles.secondaryCtaText}>{displayName}</Text>
              </TouchableOpacity>
            )}
          </View>
          <PrimaryButton
            isDisable={IMAGE_SEARCH_STATES.UPLOAD === imageSearchState && processingImage} // when image is being processed
            buttonText={btnCtaText?.toUpperCase()}
            handleClick={handleBtnClick()}
            btnContainerStyles={styles.btnChildContainer}
            startIcon={
              imageSearchState === IMAGE_SEARCH_STATES.UPLOAD && (
                <HolidayImageHolder
                  imageUrl={getOpitimsedImageUrl(
                    IMAGE_ICON_KEYS[showImage ? 'SEARCH_DEST_ICON' : 'UPLOAD_FILE_ICON'],
                  )}
                  style={styles.imageHolder}
                />
              )
            }
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    backgroundColor: holidayColors.white,
  },
  btnContainer: {
    marginTop: 'auto',
    ...paddingStyles.pb20,
  },
  btnChildContainer: {
    ...marginStyles.mb20,
    ...marginStyles.mh16,
    height: 44,
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerContainer: {
    zIndex: 2,
    elevation: 2,
  },
  imageHolder: {
    width: 30,
    height: 30,
    ...marginStyles.mr14,
  },
  secondaryCtaContainer: {
    alignItems: 'center',
    marginTop: 'auto',
    ...marginStyles.mb10,
  },
  secondaryCtaText: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.primaryBlue,
  },
});

export default UploadImage;
