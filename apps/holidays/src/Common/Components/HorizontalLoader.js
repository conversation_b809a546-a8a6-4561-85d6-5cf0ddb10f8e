import React, {Component} from 'react';
import {ProgressBarAndroid, View, ProgressViewIOS, Platform} from 'react-native';
import PropTypes from 'prop-types';
import {isAndroidClient, isIosClient} from "../../utils/HolidayUtils";

export default class HorizontalLoader extends Component {
  constructor(props) {
    super(props);
    this.startProgress = this.startProgress.bind(this);
    this.stopProgress = this.stopProgress.bind(this);
    this.clearProgress = this.clearProgress.bind(this);
    this.state = {
      Progress_Value: 0.00,
    };
  }

  componentDidMount() {
    if (isIosClient()) {
      this.startProgress();
    }
  }
  componentDidUpdate() {
    if (this.state.Progress_Value >= 1) {
      this.clearProgress();
    }
  }
  componentWillUnmount() {
    this.stopProgress();
  }
  startProgress() {
    this.value = setInterval(() => {
      if (this.state.Progress_Value <= 1) {
        this.setState({Progress_Value: this.state.Progress_Value + 0.1});
      }
    }, 10);
  }

  stopProgress() {
    clearInterval(this.value);
  }

  clearProgress() {
    this.setState({
      Progress_Value: 0.0,
    });
  }


  render() {
    return (
      <View style={[this.props.style.container, this.props.dimensions]}>
        <View style={this.props.style.loaderWidth} >
          {isAndroidClient() &&
            <ProgressBarAndroid
              styleAttr="Horizontal"
              color={this.props.style.loadingColor}
            />}
          {isIosClient() &&
            <ProgressViewIOS
              style={{width: 100}}
              progress={this.state.Progress_Value}
              progressTintColor={this.props.style.loadingColor}
            />}
        </View>

      </View>
    );
  }
}

HorizontalLoader.defaultProps = {
  style: {
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f2f2f2',
    },
    loadingColor: '#008cff',
    loaderWidth: '10%',
  },
};

HorizontalLoader.propTypes = {
  dimensions: PropTypes.object.isRequired,
  style: PropTypes.object,
};
