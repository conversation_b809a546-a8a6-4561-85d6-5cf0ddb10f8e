import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import React, { FC, useMemo } from 'react';
import { View, StyleSheet, Text, ImageStyle, TextStyle } from 'react-native';
import HolidayImageHolder from '../HolidayImageHolder'
import { IconPointsProps } from './VPPTypes';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const IconPoints: FC<IconPointsProps> = (props: IconPointsProps) => {
  const { text, icon, textStyle } = props;
  const iconTextStyle = useMemo(() => ({textStyle, ...styles.pointStyle}), [textStyle]);  return (
    <View style={styles.wrapper}>
      {icon ? (
        <HolidayImageHolder imageUrl={icon} style={styles.icon} defaultImage={null} resizeMode="cover" />
      ) : (
        <Text style={iconTextStyle}>{`\u2022`}</Text>
      )}
      {text && <Text style={[styles.text, textStyle]}>{text}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    ...paddingStyles.pb12,
    flexWrap: 'wrap',
  },
  icon: {
    height: 16,
    ...marginStyles.mt2,
    width: 16,
  } as ImageStyle,
  pointStyle: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mh2,
  } as TextStyle,
  text: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.ml8,
    flex: 1,
  } as TextStyle,
});

export default IconPoints;
