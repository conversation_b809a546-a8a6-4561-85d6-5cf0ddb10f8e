import { fonts , fontSizes, colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {INDIGO} from './themeConstants';

export const KEY = {
  BACKGROUND_COLOR: 'background-color',
  TEXT_COLOR: 'text-color',
};

// will works as mmt affiliate
const buttonStyle = {
  [KEY.BACKGROUND_COLOR]: colors.black,
  [KEY.TEXT_COLOR]: colors.gray1,
};

const indigo = {
  [KEY.BACKGROUND_COLOR]: colors.azureBlue,
  [KEY.TEXT_COLOR]: colors.lightBlue,
};

export default (aff, key) => {
  let buttonStyle = buttonStyle[key];
  if (aff === INDIGO) {
    buttonStyle = indigo[key] || buttonStyle;
  }
  return buttonStyle;
};
