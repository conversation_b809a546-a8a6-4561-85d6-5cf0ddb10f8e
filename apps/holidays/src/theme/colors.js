
import { GI, INDIGO, AI<PERSON> } from './themeConstants';
import { colors } from "@mmt/legacy-commons/Styles/globalStyles";
import isEmpty from 'lodash/isEmpty';

export const isAppPlatform = deviceType =>(
  deviceType !== 'web' && !isEmpty(deviceType)
);

export const COLOR_KEYS = {
  TITLE_COLOR: 'title',
  SUB_TITLE_COLOR: 'subTitle',
  CTA_COLORS:'cta_colors',
  CTA_COLOR:'cta_color',
  REV_CTA_COLORS:'reverse_cta_color',
  SORTER_COLOR:'day_plan_sorter',
  THANK_YOU_GRAD:'thank_you_page_grad_color',
  HEADER_BACKGROUND_COLOR: 'header_background_color',
  HEADER_TITLE_FONT_COLOR: 'header_title_font_color',
  HEADER_SUBTITLE_FONT_COLOR: 'header_subtitle_font_color',
  HEADER_CITY_FONT_COLOR: 'header_city_font_color',
};
export const themeColor = {
  airAsia : '#FF6423',
}
// will works as mmt affiliate
const defaultColors = {
  [COLOR_KEYS.TITLE_COLOR]: colors.black,
  [COLOR_KEYS.SUB_TITLE_COLOR]: colors.textgrey,
  [COLOR_KEYS.CTA_COLORS]: [colors.darkBlue,colors.lightBlue],
  [COLOR_KEYS.CTA_COLOR]: colors.azure,
  [COLOR_KEYS.SORTER_COLOR]: colors.azure,
  [COLOR_KEYS.REV_CTA_COLORS]: [colors.lightBlue,colors.darkBlue],
  [COLOR_KEYS.THANK_YOU_GRAD]: [colors.skyBlue1,colors.skyBlue2],
  [COLOR_KEYS.HEADER_BACKGROUND_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_TITLE_FONT_COLOR]: colors.lightTextColor,
  [COLOR_KEYS.HEADER_SUBTITLE_FONT_COLOR]: colors.defaultTextColor,
  [COLOR_KEYS.HEADER_CITY_FONT_COLOR]: colors.azure,
};

const gi = {
  [COLOR_KEYS.TITLE_COLOR]: colors.white,
  [COLOR_KEYS.SUB_TITLE_COLOR]: colors.white,
  [COLOR_KEYS.CTA_COLORS]: [colors.orange3,colors.orange3],
  [COLOR_KEYS.CTA_COLOR]: colors.orange3,
  [COLOR_KEYS.SORTER_COLOR]: colors.darkBlue8,
  [COLOR_KEYS.REV_CTA_COLORS]: [colors.orange3,colors.orange3],
  [COLOR_KEYS.THANK_YOU_GRAD]: [colors.skyBlue6,colors.skyBlue5],
  [COLOR_KEYS.HEADER_BACKGROUND_COLOR]: colors.orange3,
  [COLOR_KEYS.HEADER_TITLE_FONT_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_SUBTITLE_FONT_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_SUBTITLE_FONT_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_CITY_FONT_COLOR]: colors.white,
};
const indigo = {
  [COLOR_KEYS.TITLE_COLOR]: colors.white,
  [COLOR_KEYS.SUB_TITLE_COLOR]: colors.white,
  [COLOR_KEYS.CTA_COLORS]: [colors.indigoBlue,colors.indigoBlue],
  [COLOR_KEYS.CTA_COLOR]: colors.indigoBlue,
  [COLOR_KEYS.SORTER_COLOR]: colors.indigoBlue,
  [COLOR_KEYS.REV_CTA_COLORS]: [colors.indigoBlue,colors.indigoBlue],
  [COLOR_KEYS.THANK_YOU_GRAD]: [colors.indigoBlue,colors.indigoBlue],
  [COLOR_KEYS.HEADER_BACKGROUND_COLOR]: colors.indigoBlue,
  [COLOR_KEYS.HEADER_TITLE_FONT_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_SUBTITLE_FONT_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_CITY_FONT_COLOR]: colors.white,
};

const aix = {
  [COLOR_KEYS.TITLE_COLOR]: colors.black,
  [COLOR_KEYS.SUB_TITLE_COLOR]: colors.textgrey,
  [COLOR_KEYS.CTA_COLORS]: [colors.red,colors.orange3],
  [COLOR_KEYS.CTA_COLOR]: themeColor.airAsia,
  [COLOR_KEYS.SORTER_COLOR]: colors.azure,
  [COLOR_KEYS.REV_CTA_COLORS]: [colors.lightBlue,colors.darkBlue],
  [COLOR_KEYS.THANK_YOU_GRAD]: [themeColor.airAsia,themeColor.airAsia],
  [COLOR_KEYS.HEADER_BACKGROUND_COLOR]: colors.white,
  [COLOR_KEYS.HEADER_TITLE_FONT_COLOR]: colors.lightTextColor,
  [COLOR_KEYS.HEADER_SUBTITLE_FONT_COLOR]: colors.defaultTextColor,
  [COLOR_KEYS.HEADER_CITY_FONT_COLOR]: colors.azure,
}

export default (aff, key, appColor) => {
  if (isAppPlatform()){
    return appColor;
  }
  let color = defaultColors[key];
  if (aff === GI) {
    color = gi[key] || color;
  }
  if (aff === INDIGO){
    color = indigo[key] || color;
  }
  if (aff === AIX){
    color = aix[key] || color;
  }

  return color;
};
