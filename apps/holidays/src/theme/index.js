import colors, {COLOR_KEYS} from './colors';
import {fontSize, fontFamily, FONT_KEYS as FONT_KEYS} from './font';
import { isEqual } from 'lodash';

import { GI } from './themeConstants';
import textStyle from './text';
/**
 * Set Affiliate
 *
 */
let affiliate;
export const setAffiliate = (aff) => affiliate = aff;
export const getAffiliate = () => affiliate;
export const isGiAffiliate = () => affiliate !== undefined && isEqual(affiliate.toLowerCase(), 'gi');
export const is6eAffiliate = () => affiliate !== undefined && isEqual(affiliate.toLowerCase(), '6e');
export const getColor = (key, defaultColor) => colors(affiliate, key, defaultColor);
export const getFontFamily = (key) => fontFamily(affiliate, key);
export const getFontSize = (key) => fontSize(affiliate, key);
export const getString = (key) => strings(affiliate, key);
export const getTextStyle = () => textStyle(affiliate);

export const getButtonStyle = () => {
    if(affiliate === GI){
        return {borderRadius : 100};
    }
    return {};
};
export default {
    setAffiliate,
    getAffiliate,
    getColor,
    getFontFamily,
    getFontSize,
    getString,
    getTextStyle,
    COLOR: COLOR_KEYS,
    FONT: FONT_KEYS,
    STRING: '',
};
