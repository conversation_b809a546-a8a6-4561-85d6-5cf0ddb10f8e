import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { GI, INDIGO } from './themeConstants';

export const FONT_KEYS = {
  TITLE_FONT: 'title',
  SUB_TITLE_FONT: 'subTitle',
  TEXT_DEFAULT: 'text_default',
  TEXT_BOLD: 'text_bold',
  HEADING_FONT:'heading'
};
// Will works as mmt affiliate
const defaultFonts = {
  [FONT_KEYS.TITLE_FONT]: fonts.bold,
  [FONT_KEYS.SUB_TITLE_FONT]: fonts.regular,
  [FONT_KEYS.TEXT_DEFAULT]: fonts.regular,
  [FONT_KEYS.TEXT_BOLD]: fonts.bold,
  [FONT_KEYS.HEADING_FONT]: fonts.bold,
};

const gi = {
  [FONT_KEYS.TITLE_FONT]: fonts.quicksand_bold,
  [FONT_KEYS.SUB_TITLE_FONT]: fonts.quicksand_regular,
  [FONT_KEYS.TEXT_DEFAULT]: fonts.helvetica,
  [FONT_KEYS.TEXT_BOLD]: fonts.helvetica_bold,
  [FONT_KEYS.HEADING_FONT]: fonts.quicksand_bold,

};
const indigo = {
  [FONT_KEYS.TITLE_FONT]: fonts.bold,
  [FONT_KEYS.SUB_TITLE_FONT]: fonts.regular,
  [FONT_KEYS.TEXT_DEFAULT]: fonts.regular,
  [FONT_KEYS.TEXT_BOLD]: fonts.bold,
  [FONT_KEYS.HEADING_FONT]: fonts.bold,

};

export const fontFamily = (aff, key) => {
  let fontFamily = defaultFonts[key];
  if (aff === INDIGO) {
    fontFamily = indigo[key] || fontFamily;
  }
  if (aff === GI) {
    fontFamily = gi[key] || fontFamily;
  }
  return fontFamily;
};
