/**
 * This file contains custom themes for a text for an affiliate.
 * @type {{toggleBorder: string, gradient: string, text: string, body: string}}
 */

import {INDIGO} from './themeConstants';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';

// indigo text Theme
const indigoText = {
  body: colors.gray1,
  text: colors.black,
  toggleBorder: colors.white,
  gradient: 'linear-gradient(#39598A, #79D7ED)',
};

//Normal Text Theme.
const normalText = {
  body: colors.gray2,
  text: colors.black,
  toggleBorder: colors.white,
};

export default (aff) => {
  let textStyle = normalText;
  if (aff === INDIGO) {
    textStyle = indigoText || textStyle;
  }
  return textStyle;
};
