import { fonts , fontSizes, colors} from '@mmt/legacy-commons/Styles/globalStyles';
import {INDIGO} from './themeConstants';

export const KEY = {
  SELECTED_COLOR: 'selected-color',
  DEFAULT_COLOR: 'default-color',
};

// will works as mmt affiliate
const radioButtonStyle = {
  [KEY.SELECTED_COLOR]: colors.black,
  [KEY.DEFAULT_COLOR]: colors.gray1,
};

const indigo = {
  [KEY.SELECTED_COLOR]: colors.azureBlue,
  [KEY.DEFAULT_COLOR]: colors.lightBlue,
};

export default (aff, key) => {
  let radioButtonStyle = radioButtonStyle[key];
  if (aff === INDIGO) {
    radioButtonStyle = indigo[key] || radioButtonStyle;
  }
  return radioButtonStyle;
};
