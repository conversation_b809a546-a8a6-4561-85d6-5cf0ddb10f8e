import React from 'react';
import {
  <PERSON>,
  Text,
  BackHandler,
  Platform, DeviceEventEmitter,
  ScrollView,
  Switch,
} from 'react-native';
import fecha from 'fecha';
import {cloneDeep, isEmpty} from 'lodash';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import styles from './SearchWidgetCss';
import FilterHeader from './FilterHeader';
import FilterHeaderIOS from './FilterHeaderIOS';
import FilterSection from './FilterSection';
import AvailableInclusionFilter from './AvailableInclusionFilter';
import DateFilter from './DateFilter';
import GenericFilter from './GenericFilter';
import FilterAction from './FilterAction';
import AutoComplete from './AutoComplete';
import PlacesFilter from './PlacesFilter';
import Separator from '@mmt/legacy-commons/Common/Components/Separator';
import HolidayCalendar from '../../Calender/MmtHolidayCalender';
import {getPopularPlaces} from '../utils/cityRepository';
import {
  REQUEST_LOB,
  REQUEST_WEBSITE,
  PDT_PAGE_EXIT_EVENT,
  DOM_BRANCH, WEEKEND_GETAWAY_PAGE_TYPE, HLD_PAGE_NAME, PDT_RAW_EVENT,
} from '../../HolidayConstants';
import {
  getPlatformIdentifier,
  getDepartureCity,
  createRandomString,
  isRawClient,
  createWGFilter,
  isNullOrEmptyCollection,
  isEmptyString,
  isNotNullAndEmptyCollection, isAndroidClient,
} from '../../utils/HolidayUtils';
import HolidayGroupingNoFilter from '../../Grouping/Filters/HolidayGroupingNoFilter';
import FilterLoader from './FilterLoader';
import {
  SEARCH_WIDGET_SUB_HEADER,
  TO,
  FROM,
  DATE_FORMAT,
  EVENT_SEARCH_WIDGET_REFRESH,
  ON_DONE_SW_EVENT,
  LISTING_PAGE,
  DEFAULT_PAGE_NAME,
  GROUP_PAGE_NAME,
  GROUPING_PAGE_NAME,
  LISTING_PAGE_NAME,
  HARDWARE_BACK_PRESS,
  RESET_TEXT, ERROR_MSG,
  PLATFORM_ANDROID, PLATFORM_IOS, DATE_WITH_DAY_FORMAT, TODAY, TOMORROW,
  CALENDAR_EVENT_RECEIVED, pdtConstants, PAGE_NAME_SEARCH_WIDGET, DESTINATION_APPEND,
  CALENDAR_SELECTED_DATE_DIFF,
  FILTER_HEADER_TITLE, WG_FILTER_HEADER_TITLE, RESET_ALL,
  DESTINATION_CITY_FILTER_ID, FILTER_URL_NAME_CONSTANTS, LANDING_PAGE_NAME, DATE_FORMAT_OMNI,
} from '../SearchWidgetConstants';
import {addDays, isToday, isTomorrow, today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {
  updateSelectedMonthsList,
  getDateValues,
  createLoggingMap, copyFilter,
  getRecentSearchLabels,
} from '../utils/SearchWidgetUtil';
import {
  KEY_USER_DEP_CITY,
  setDataInStorage,
  getDataFromStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { trackSearchWidgetClickEventNew } from '../../utils/HolidayTrackingUtils';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {trackPageVisits} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import BudgetSlider from './BudgetSlider';
import DurationSlider from './DurationSlider';
import FilterDestinationSelector from './FilterDestinationSelector';
import SelectedCities from './SelectedCities';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import Title from '@mmt/legacy-commons/Common/Components/Title';
import TouchableOpacity from '@mmt/legacy-commons/Common/Components/TouchableOpacity';
import PremiumFilter from './PremiumFilter';
import SeparatorSection from './SeperatorSection';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { LANDING_TRACKING_PAGE_NAME } from '../../LandingNew/LandingConstants';
import BranchIOTracker from '../../utils/HolidayBranchSDKEventTracker';
import { getShowBudgetGraphHol } from '../../utils/HolidaysPokusUtils';
import withBackHandler from '../../hooks/withBackHandler';

let searchWidgetRefreshListener;
let calendEventRecievedListener;

 class HolidaySearchWidgetPage extends BasePage {
  constructor(props) {
    super(props, 'search_widget');
    this.state = {
      showCalendar: false,
    };
    const criterias = cloneDeep(this.props.criterias);
    const destinationCity = cloneDeep(this.props.destinationCity);
    const dateObj = cloneDeep(this.props.dateObj);
    const packageIds = cloneDeep(this.props.packageIds);
    this.showDepDestPopUp = cloneDeep(this.props.showGoingTo);
    this.openForDestination = cloneDeep(this.props.showGoingTo);
    this.hideLocationFilter = this.props.hideLocationFilter;
    this.holidayLandingGroupDto = cloneDeep(props.holidayLandingGroupDto);
    this.isWG = !!props.isWG;
    this.groupedData = {};
    this.request = {
      lob: REQUEST_LOB,
      destinationCity,
      channel: getPlatformIdentifier(),
      criterias,
      website: REQUEST_WEBSITE,
      filterSorterParam: true,
      filteredFiltersRequired: !this.props.criterias,
      departureCity: this.props.userDepCity,
    };

    // taken care of variables need to pass in metadata (request) and pass without any change in group call (groupdata).
    if (this.props.campaign){
      this.request.campaign = cloneDeep(props.campaign);
      this.groupedData.campaign = cloneDeep(props.campaign);
    }

    if (this.holidayLandingGroupDto?.packageIds) {
      this.groupedData.packageIds = this.holidayLandingGroupDto.packageIds;
      this.request.packageIds = this.holidayLandingGroupDto.packageIds;
    } else if (packageIds) {
      this.groupedData.packageIds = packageIds;
      this.request.packageIds = packageIds;
    }

    if (this.holidayLandingGroupDto?.rooms) {
      this.groupedData.rooms = this.holidayLandingGroupDto.rooms;
    }

    if (dateObj) {
      const {fromDate, toDate, packageDate} = dateObj;
      this.request.fromDate = fromDate;
      this.request.toDate = toDate;
      this.request.packageDate = packageDate;
    }
    if (packageIds) {
      this.request.packageIds = packageIds.split(',');
    }

    this.masterMap = new Map();
    this.pageNameForFilters = this.props.pageName ? GROUP_PAGE_NAME : DEFAULT_PAGE_NAME;
    this.searchHistoryResults = getRecentSearchLabels(this.props.searchHistoryResults);
    this.travellingMonthsOptions = [];
    this.travellingMonthsOptionsMaster = [];
    this.packageCount = 0;
    this.destinationCount = 0;
    this.destinationCity = destinationCity || '';
    this.toDestination = '';
    this.popularlyPairedWithOptions = [];
    this.destinations = this.populateDestinations(destinationCity, criterias);
    this.popularPlaces = [];
    if (this.showDepDestPopUp) {
      this.populatePopularPlaces(TO);
      this.lastPage = LANDING_PAGE_NAME;
    }
    this.labelFromOrTo = this.showDepDestPopUp ? TO : FROM;
    this.availableHubs = [];
    this.dateValues = getDateValues(this.props.dateObj);
    this.lastFilter = {};
    this.requestInProgress = false;
    this.updatePopularlyPairedWithOptions = true;
    this.cmpChannel = this.props.cmpChannel;
    this.loadingFirstTime = true;
    this.budgetFilterStats = [];
    this.showBudgetGraph = getShowBudgetGraphHol();
    const pageDateMap = createLoggingMap(null, this.props.pageName, this.props.cmpChannel, this.isWG);
    const requestId = createRandomString();
  }

  componentWillMount() {
    calendEventRecievedListener = DeviceEventEmitter && DeviceEventEmitter.addListener(CALENDAR_EVENT_RECEIVED, this.calendarDateReceived);
  }
  onBackClick = ()=> {
    return this.handleBackPress();
}

  componentWillReceiveProps(nextProps) {
    if (nextProps.searchHistoryResults) {
      this.searchHistoryResults = getRecentSearchLabels(nextProps.searchHistoryResults);
    }
  }

  async componentDidMount() {
    const cityName = await getDepartureCity();
    this.props.updateDepCity(cityName);
    if (!this.props.disallowRefresh || (!this.props.isLoading && !this.props.isSuccess)) {
      this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
    }
    this.loadingFirstTime = false;
    this.props.getSearchResults();
    console.log("in componentDidMount")
    searchWidgetRefreshListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(EVENT_SEARCH_WIDGET_REFRESH, this.searchWidgetDataRefreshed);
    trackPageVisits(LANDING_PAGE_NAME, {});
  }

  componentWillUnmount() {
  console.log("in componentWillUnmount")
  if(searchWidgetRefreshListener?.remove) {
    searchWidgetRefreshListener.remove();
    // DeviceEventEmitter && DeviceEventEmitter.removeListener(EVENT_SEARCH_WIDGET_REFRESH, this.searchWidgetDataRefreshed);
  }
  if(calendEventRecievedListener?.remove) {
    calendEventRecievedListener.remove();
    // DeviceEventEmitter && DeviceEventEmitter.removeListener(CALENDAR_EVENT_RECEIVED, this.calendarDateReceived);
  
  }
  }


  searchWidgetDataRefreshed = () => {
    this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
  };


  render() {
    return (
      <View style={{flex: 1}}>
        {this.props.isError && this.renderError()}
        {(this.props.isSuccess || this.props.isLoading) && this.renderContent()}
      </View>
    );
  }

  renderError = () => {
    showShortToast(ERROR_MSG);
//    this.handleBackPress();
    return null;
  };


  getSuccessData = (filters , aff) => {
    const {masterData, availableHubs, filterIdMap, isWG} = this.props;
    this.showDepDestPopUp = this.props.showLocationPopUp ?
      this.props.showLocationPopUp : this.showDepDestPopUp;
    this.branch = masterData.branch;
    this.packageCount = masterData.packageCount;
    this.masterMap = masterData.masterMap || new Map();
    this.travellingMonthsOptions = masterData.travellingMonthsOptions;
    this.toDestination = masterData.toDestination;
    if (masterData.travellingMonthsOptionsMaster) {
      this.travellingMonthsOptionsMaster = masterData.travellingMonthsOptionsMaster;
    }
    this.popularlyPairedWithOptions = masterData.popularlyPairedWithOptions;
    this.updatePopularlyPairedWithOptions = masterData.updatePopularlyPairedWithOptions ?
      masterData.updatePopularlyPairedWithOptions : this.updatePopularlyPairedWithOptions;
    this.availableHubs = availableHubs;
    this.filterIdMap = filterIdMap;
    this.destinationCount = this.destinations.length;
    this.budgetFilterStats = this.showBudgetGraph ? masterData.budgetFilterStats : [];


    let renderedBudget = false;
    if (!this.hideLocationFilter) {
      filters.push(this.getLocationFilter(isWG));
    }
    if (isWG) {
      const durationObj = this.masterMap.get(FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME);
      if (durationObj) {
        filters.push(
          <FilterSection key={`MDF_${durationObj.id}`}>
            {this.getDateFilter(durationObj)}
          </FilterSection>
        );
      }
    }
    this.masterMap.forEach((value, key) => {
      if ((key === FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME
        || key === FILTER_URL_NAME_CONSTANTS.HOTEL_CHOICE_FILTER_URL_NAME
        || key === FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME
        || key === FILTER_URL_NAME_CONSTANTS.PREMIUM_FILTER_URL_NAME)) {
        if (!renderedBudget) {
          filters.push(this.getBudgetInclusionFilter(this.masterMap, isWG,aff));
          renderedBudget = true;
        }
      } else if (key === FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME) {
        filters.push(this.getDurationFilter(this.masterMap, isWG));
      } else if (key === FILTER_URL_NAME_CONSTANTS.CORONA_FILTER_URL_NAME) {
        filters.push(this.covidSafeFilter(value));
      } else if (key !== FILTER_URL_NAME_CONSTANTS.FLIGHT_FILTER_URL_NAME) {
        filters.push(<FilterSection
          key={value.id}
        >
          <GenericFilter
            filterObj={value}
            handleSelectionChange={this.onSelectionChange}
            handleClear={this.onFilterClear}
          />
        </FilterSection>);
      }
    });
  };

  populateDestinations = (destinationCity, criterias) => {
    let dests = [];
    if (destinationCity) {
      dests = destinationCity.split(',');
    }

    return dests;
  };


  trackEvent = (eventName) => {
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: eventName,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: pdtConstants.PDT_CLOSE_SECTION_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
  }


  renderContent = () => {
    const isWG = this.props.isWG;
    const aff = this.props.aff || '';
    const filters = [];
    if (this.props.isSuccess) {
      if (this.props.masterData && this.props.masterData.applyFiltersDirectly) {
        this.props.onSWClose(true);
        this.onDone();
        return;
      }
      this.getSuccessData(filters,aff);
    }

    /*
    TO BE ADDED LATER
    const travellerCountFilterObj = {
      noOfAdults: this.state.noOfAdults,
      noOfChildren: this.state.noOfChildren
    };

    filters.push(this.getTravellerCountFilter(travellerCountFilterObj));
    */


    const autoCompleteObj = {
      label: this.labelFromOrTo,
      availableHubs: this.availableHubs,
      optionsPopular: this.popularlyPairedWithOptions,
      popularPlaces: this.popularPlaces,
      destinations: this.destinations,
    };

    return (
      <View key="MASTER_SEARCH_WIDGET_VIEW" style={[this.props.isBottomSheet ? styles.containerForBottomSheet : styles.container  ]}>
        {this.props.isLoading && <FilterLoader
          showCenterLoader={true}
          loadingFirstTime={this.loadingFirstTime}
          isBottomsheet={this.props.isBottomSheet}
        />}

        {this.showDepDestPopUp && this.props.isSuccess &&
        <FilterDestinationSelector
          style={{
            position: 'absolute',
            flex: 1,
          }}
          autoCompleteObj={autoCompleteObj}
          forceShowInput
          destinations={this.labelFromOrTo === FROM ? this.availableHubs.map(city => city.name) : []}
          showAdavanceSearch={this.openForDestination}
          onAdvanceSearchClicked={this.onAdvanceSearchClicked}
          citySelect={this.onCitySelect}
          onBack={this.onBackAutoComplete}
          searchHistoryResults={this.labelFromOrTo === TO ? this.searchHistoryResults : []}
          scrollable
          showGetCurrLocation={this.labelFromOrTo === FROM}
          availableHubs={this.availableHubs}
          trackClickEvent={this.props?.trackClickEvent}
        />}
        {!this.showDepDestPopUp && !this.state.showCalendar && this.props.isSuccess &&
        <View style={[AtomicCss.flex1]}>
          {(isAndroidClient() || isRawClient()) &&

          <FilterHeader
            titleText={isWG ? WG_FILTER_HEADER_TITLE : FILTER_HEADER_TITLE}
            resetText={RESET_ALL}
            handleReset={this.onReset}
            handleClose={this.onClose}
          />}


          {Platform.OS === PLATFORM_IOS &&
          <View style={this.props.isBottomSheet ? [styles.headerContainer] : [styles.headerContainerStatusBarDimensions,styles.headerContainerForModal]}>
            <FilterHeaderIOS
              titleText={isWG ? WG_FILTER_HEADER_TITLE : FILTER_HEADER_TITLE}
              resetText={RESET_ALL}
              handleReset={this.onReset}
              handleClose={this.onClose}
            />
          </View>}
          <ScrollView>
            {!isWG && <Text style={styles.description}>
              {SEARCH_WIDGET_SUB_HEADER}
            </Text>}
            <View>
              {filters}
            </View>
          </ScrollView>
          <FilterAction
            packageCount={this.packageCount}
            destinationCount={isWG ? 0 : this.destinationCount}
            handleDone={this.onDoneClicked}
            isWG={isWG}
          />
        </View>}
        {this.state.showCalendar &&
        <HolidayCalendar selectedDate={this.state.departureDate}
                         onDone={this.updateDateFilterData}
                         onCalendarBack={this.onCalendarBack}/>
        }
        {(!this.packageCount || this.packageCount === 0) && this.props.isSuccess &&
        <HolidayGroupingNoFilter
            trackEvent={this.trackEvent}
            trackErrorClickEvent={this.props?.trackErrorClickEvent || null}
            removeLastFilter={this.removeLastFilter}
            remark={'REMOVE THE LAST FILTER'}
            filterErrorHeading={'No Packages found'}
        />}
      </View>

    );
  }
  ;


  getBudgetInclusionFilter = (masterMap, isWG,aff) => {
    const subFilters = [];
    const budgetObj = masterMap.get(FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME);
    const hotelChoiceObj = masterMap.get(FILTER_URL_NAME_CONSTANTS.HOTEL_CHOICE_FILTER_URL_NAME);
    const inclusionObj = masterMap.get(FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME);
    const premiumObj = masterMap.get(FILTER_URL_NAME_CONSTANTS.PREMIUM_FILTER_URL_NAME);
    let uniqueKey = '';
    if (budgetObj && budgetObj.masterOptionsList && budgetObj.masterOptionsList.length > 0) {
      const budgetOptions = budgetObj.masterOptionsList[0].filterText.split('_');
      const selectedOptions = budgetObj.optionsList[0].isActive ? budgetObj.optionsList[0].filterText.split('_') : budgetOptions;
      uniqueKey += budgetObj.id;

      let minVal = parseInt(budgetOptions[0]);
      let maxVal = parseInt(budgetOptions[1]);

      // Handle case when two selected values are same.
      // Crash fix.
      if (minVal === maxVal) {
        minVal = minVal - 1;
        maxVal = maxVal + 1;
      }

      subFilters.push(<BudgetSlider
          key={uniqueKey}
          min={minVal}
          max={maxVal}
          step={500}
          startValue={parseInt(selectedOptions[0])}
          endValue={parseInt(selectedOptions[1])}
          onChange={this.onBudgetSliderChange}
          filterStats={this.budgetFilterStats}
          onClear={() => this.clearSliderFilter(FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME)}
      />);

    }
    if (masterMap.has(FILTER_URL_NAME_CONSTANTS.PREMIUM_FILTER_URL_NAME)) {
      subFilters.push(<View key={premiumObj.id}><Separator/>
        <PremiumFilter
          key={premiumObj.name + premiumObj.id}
          premiumObj={premiumObj}
          handleSelectionChange={this.onPremiumFilterClick}
        />
      </View>);
    }

    if (masterMap.has(FILTER_URL_NAME_CONSTANTS.HOTEL_CHOICE_FILTER_URL_NAME) &&
      masterMap.has(FILTER_URL_NAME_CONSTANTS.PACKAGE_INCLUSION_FILTER_URL_NAME)) {
      subFilters.push(<View key={hotelChoiceObj.id}><SeparatorSection />
        <AvailableInclusionFilter
          key={hotelChoiceObj.id + inclusionObj.id}
          filterObjHotel={hotelChoiceObj}
          filterObjInclusions={inclusionObj}
          handleSelectionChange={this.onFlightInclusionFilterClick}
          handleAvailableInclusionClear={this.onAvailableInclusionFilterClear}
          isWG={isWG}
          aff={aff}
        />
      </View>);
      uniqueKey += hotelChoiceObj.id + inclusionObj.id;
    }

    return (
      <FilterSection key={uniqueKey}>
        {subFilters}
      </FilterSection>
    );
  };

  getDateFilter = (durationObj) => {
    const {
      dateSelected, day, date, monthYear,
    } = this.dateValues;
    const dataFilterPropsObj = {
      key: `DF${durationObj.id}`,
      onClickSpecifyDate: this.onClickSpecifyDate,
      dateSelected,
      day,
      date,
      monthYear,
      TravellingMonthsOptions: this.travellingMonthsOptions,
      TravellingMonthsOptionsMaster: this.travellingMonthsOptionsMaster,
      handleSelectionChange: this.onTravellingMonthOptionsChange,
      handleTravellingMonthClear: this.onhandleTravellingMonthClear,
    };
    return <DateFilter {...dataFilterPropsObj} />;
  }

  covidSafeFilter = (item) => {
    var safeItem = item.optionsList.filter((row) => row.uniqueId === '1')[0];

    return (
      <FilterSection>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text style={{
            fontFamily: 'Lato-Bold',
            fontSize: 12,
            flex: 1,
            color: '#4a4a4a',
          }}
          >{item.name}
          </Text>
          <Switch
            tintColor="#cccccc"
            thumbColor={[(safeItem.isActive ? '#008cff' : '#f5f5f5')]}
            onTintColor="#d7edff"
            onValueChange={(state) => this.onCovidFilterToggle(item, state)}
            value={safeItem.isActive}
          />
        </View>
      </FilterSection>
    );
  }

  getDurationFilter = (masterMap, isWG) => {
    const durationObj = this.masterMap.get(FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME);
    const subFilters = [];
    if (!isWG) {
      subFilters.push(this.getDateFilter(durationObj));
    }
    if (masterMap.has(FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME)) {
      const durationOptions = durationObj.masterOptionsList[0].filterText.split('_');
      const selectedOptions = durationObj.optionsList[0].isActive ? durationObj.optionsList[0].filterText.split('_') : durationOptions;
      if (selectedOptions.length === 1) {
        selectedOptions.push(selectedOptions[0]);
      }
      subFilters.push(<View key={`DGF${durationObj.id}`}>
        {!isWG &&
        <View style={{marginBottom: 12}}>
          <Separator type="noMargin" />
        </View>
        }
        <DurationSlider
          min={parseInt(durationOptions[0], 10)}
          max={parseInt(durationOptions[1], 10)}
          step={1}
          startValue={parseInt(selectedOptions[0], 10)}
          endValue={parseInt(selectedOptions[1], 10)}
          onChange={this.onDurationSliderChange}
          onClear={() => this.clearSliderFilter(FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME)}
        />
      </View>);
    }
    return (
      <FilterSection key={durationObj.id}>
        {subFilters}
      </FilterSection>
    );
  };

  getLocationFilter = (isWG) => {
    const {departureCity} = this.request;
    return (<FilterSection key={departureCity + this.toDestination}>
      {!isWG &&
      <View>
        <View style={AtomicCss.flexRow}>
          <Title heading="DESTINATIONS / CITIES" />
          <View style={AtomicCss.pushRight}>
            <AnchorBtn
              label="Clear"
              disable={isNullOrEmptyCollection(this.destinations)}
              handleClick={() => this.onHandleRemoveDestination('', true)}
            />
          </View>
        </View>
        <TouchableOpacity style={{width: '100%', paddingVertical: 12}} onPress={() => this.onLocationChange(TO)}>
          <Text style={{
            color: '#9b9b9b',
            fontSize: 16,
          }}
          >Enter destination
          </Text>
          <View style={{
            marginTop: 8,
            backgroundColor: '#aaaaaa',
            width: '100%',
            height: 1,
          }}
          />
        </TouchableOpacity>

        {this.destinations.length > 0 &&
        <View>
          <SelectedCities
            options={this.destinations}
            removeDestination={this.onHandleRemoveDestination}
          />
        </View>
        }
      </View>}
      <View>
        <Text style={{
          fontFamily: 'Lato',
          fontSize: 12,
          letterSpacing: 0.3,
          marginBottom: 8,
          marginTop: 6,
          color: '#9b9b9b',
        }}
        >STARTING FROM
        </Text>
        <View style={AtomicCss.flexRow}>
          <Text style={{
            fontFamily: 'Lato-Bold',
            fontSize: 14,
            flex: 1,
            color: '#4a4a4a',
          }}
          >{isEmpty(departureCity) ? 'Enter City' : departureCity}
          </Text>
          {!isWG &&
          <AnchorBtn
            label="Change"
            handleClick={() => this.onLocationChange(FROM)}
          />
          }
        </View>
      </View>
    </FilterSection>);
  };

  /* FILTER TO BE ADDED LATER
   getTravellerCountFilter = (travellerCountFilterObj) => {
     const {noOfAdults, noOfChildren} = this.state;
     const uniqueKey = noOfAdults + noOfChildren;
     const TravellerCountFilterMainObj = {
       travellerCountFilterObj,
       handleAdultNumChange: this.onAdultCountChange,
       handleChildNumChange: this.onChildCountChange,
       handleTravellerCountClear: this.onHandleTravellerCountClear
     };
     return (<FilterSection key={uniqueKey}>
       <TravellersCounterFilter
         TravellerCountFilterMainObj={TravellerCountFilterMainObj}
         travellerCountFilterObj={travellerCountFilterObj}
         handleAdultNumChange={this.onAdultCountChange}
         handleChildNumChange={this.onChildCountChange}
         handleTravellerCountClear={this.onHandleTravellerCountClear}>
       </TravellersCounterFilter>
     </FilterSection>);
   };
 */


  getPopularPlaces = async () => {
    const destinations = await getPopularPlaces();
    return destinations;
  };


  handleBackPress = () => {
    if (this.showDepDestPopUp && !this.openForDestination) {
      this.showDepDestPopUp = !this.showDepDestPopUp;
      this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
      const eventName = pdtConstants.PDT_CLOSE_SECTION_EVENT + DESTINATION_APPEND;
      if (this.props.masterData && this.props.masterData.searchWidgetDataPDT) {
        trackSearchWidgetClickEventNew({
          omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
          omniEventName: eventName,
          pdtData: {
            pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
            eventType: PDT_RAW_EVENT,
            activity: pdtConstants.PDT_CLOSE_SECTION_EVENT,
            requestId: this.props.masterData.requestId,
            branch: this.branch,
          },
        });
      }
    } else if (this.state.showCalendar) {
      this.setState({
        showCalendar: false,
      });
    } else {
      this.props.onSWClose();
    }
    return true;
  };


  onReset = () => {
    if (this.props.userDepCity) {
      setDataInStorage(KEY_USER_DEP_CITY, this.props.userDepCity);
    }
    this.request = this.getResetRequest();
    for (const [key] of this.masterMap) {
      this.deActivateOptionsMap(key);
    }

    for (let monthIndex = 0; monthIndex < this.travellingMonthsOptions.length; monthIndex++) {
      this.travellingMonthsOptions[monthIndex].isActive = false;
    }
    if (!this.isWG) {
      this.destinations = [];
      this.destinationCity = '';
    }
    this.onClearDate();
    this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
  };


  onClose = () => {
    if (this.props.userDepCity) {
      setDataInStorage(KEY_USER_DEP_CITY, this.props.userDepCity);
    }
    this.props.onSWClose();
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: pdtConstants.PDT_CROSS_EVENT,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: pdtConstants.PDT_CROSS_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: PDT_PAGE_EXIT_EVENT,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: PDT_PAGE_EXIT_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
  };


  onLocationChange = async (label) => {
    this.labelFromOrTo = label;
    await this.populatePopularPlaces(label);
    this.showDepDestPopUp = !this.showDepDestPopUp;
    this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
    if (label === TO) {
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: pdtConstants.PDT_MORE_DEST_EVENT,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: pdtConstants.PDT_MORE_DEST_EVENT,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    } else if (label === FROM) {
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: pdtConstants.PDT_CHANGE_HUB_EVENT,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: pdtConstants.PDT_CHANGE_HUB_EVENT,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }
  };

  populatePopularPlaces = async (label) => {
    if (label === TO && this.destinations.length === 0) {
      this.popularPlaces = [];
      const data = await this.getPopularPlaces();

      for (let placeIndex = 0; placeIndex < data.length; placeIndex++) {
        const option = {
          name: data[placeIndex],
          hide: false,
        };
        this.popularPlaces.push(option);
      }
    }
  };


  onClearAutoComplete = async () => {
    this.destinations = [];
    this.destinationCity = '';
    this.request.destinationCity = '';
    if (this.popularPlaces && this.popularPlaces.length === 0) {
      await this.populatePopularPlaces(TO);
    } else {
      for (let index = 0; index < this.popularPlaces.length; index++) {
        this.popularPlaces[index].hide = false;
        this.popularPlaces[index].isActive = false;
      }
    }
    this.request.criterias = [];
    this.onhandleTravellingMonthClear();
    this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
  };


  onSelectionChange = (filterId, selectedOption, uniqueNameFilter) => {
    const mapObj = this.masterMap.get(uniqueNameFilter);
    const optionsList = this.masterMap.get(uniqueNameFilter).optionsList;
    let activatedOption = {};
    for (let optionIndex = 0; optionIndex < optionsList.length; optionIndex++) {
      if (optionsList[optionIndex].uniqueId == selectedOption) {
        optionsList[optionIndex].isActive = !optionsList[optionIndex].isActive;
        activatedOption = optionsList[optionIndex];
      }
      if (optionsList[optionIndex].isActive) {
        mapObj.updateFlag = false;
      }
    }
    mapObj.optionsList = optionsList;
    this.masterMap.set(uniqueNameFilter, mapObj);
    this.addToRequest(filterId, selectedOption);
    return activatedOption;
  };

  onFlightInclusionFilterClick = (filterId, selectedOption, uniqueNameFilter) => {
    const activatedOption = this.onSelectionChange(filterId, selectedOption, uniqueNameFilter);
    if (activatedOption) {
      const eventName = activatedOption.isActive ? pdtConstants.FLIGHT_ADD_EVENT : pdtConstants.FLIGHT_REMOVE_EVENT;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: eventName,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }
  };

  onPremiumFilterClick = (filterId, selectedOption, uniqueNameFilter) => {
    const activatedOption = this.onPremiumSelectionChange(filterId, selectedOption, uniqueNameFilter);
    if (activatedOption) {
      const eventName = activatedOption.isActive ? pdtConstants.PREMIUM_ADD_EVENT : pdtConstants.PREMIUM_REMOVE_EVENT;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: eventName,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }
  };

  onPremiumSelectionChange = (filterId, selectedOption, uniqueNameFilter) => {
    const mapObj = this.masterMap.get(uniqueNameFilter);
    const optionsList = this.masterMap.get(uniqueNameFilter).optionsList;
    let activatedOption = {};
    for (let optionIndex = 0; optionIndex < optionsList.length; optionIndex++) {
      if (optionsList[optionIndex].uniqueId === selectedOption) {
        optionsList[optionIndex].isActive = !optionsList[optionIndex].isActive;
        activatedOption = optionsList[optionIndex];
      }
      if (optionsList[optionIndex].isActive) {
        mapObj.updateFlag = false;
      }
      break;
    }
    mapObj.optionsList = optionsList;
    this.masterMap.set(uniqueNameFilter, mapObj);
    this.addToRequest(filterId, selectedOption);
    return activatedOption;
  };

  onTravellingMonthOptionsChange = (selectedTravellingMonth) => {
    const selectedMonthsWithDate = updateSelectedMonthsList(
      selectedTravellingMonth,
      this.travellingMonthsOptions
    );
    this.lastFilter = null;
    this.request.fromDate = selectedMonthsWithDate.fromDate;
    this.request.toDate = selectedMonthsWithDate.toDate;
    this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
    this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
  };

  onBudgetSliderChange = (startValue, endValue) => {
    const obj = this.masterMap.get(FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME);
    const filterValue = `${startValue}.0_${endValue}.0`;
    if (obj.optionsList.length > 0 && obj.optionsList[0].uniqueId === filterValue) {
      return;
    }
    obj.optionsList[0] = {
      isActive: true,
      uniqueId: filterValue,
      filterText: filterValue,
    };
    this.addToRequest(obj.id, filterValue, true);
  };

  onDurationSliderChange = (startValue, endValue) => {
    const obj = this.masterMap.get(FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME);
    const filterValue = `${startValue}_${endValue}`;
    if (obj.optionsList.length > 0 && obj.optionsList[0].uniqueId === filterValue) {
      return;
    }
    obj.optionsList[0] = {
      isActive: true,
      uniqueId: filterValue,
      filterText: filterValue,
    };
    this.addToRequest(obj.id, filterValue, true);
  };

  clearSliderFilter = (filterName) => {
    const obj = this.masterMap.get(filterName);
    obj.optionsList = obj.masterOptionsList.slice(0, obj.masterOptionsList.length);
    this.removeFromRequest(obj.id);
  };

  onCovidFilterToggle(_item, state) {

    const item = _item;
    const optionsList = item.optionsList;

    item.optionsList.forEach((row) => {
      row.isActive = !state;
      if (row.uniqueId === '1') {
        row.isActive = state;
      }
    });

    const isActive = item.optionsList.filter((row) => row.uniqueId === '1')[0].isActive;

    item.updateFlag = true;
    item.optionsList = optionsList;

    this.masterMap.set(FILTER_URL_NAME_CONSTANTS.CORONA_FILTER_URL_NAME, item);
    this.addToRequest(this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.CORONA_FILTER_URL_NAME), isActive ? '1' : '0', true);

    const eventName = state ? pdtConstants.COVID_ADD_EVENT : pdtConstants.COVID_REMOVE_EVENT;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: eventName,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
  }

  /* TRAVELLER COUNT FILTER - WILL BE UNCOMMENTED WHILE ENABLING */
  /*   onAdultCountChange = (operation) => {
     if (operation == 'INC') {
       this.setState({
         ...this.state,
         noOfAdults: this.state.noOfAdults + 1
       });
     } else {
       this.setState({
         ...this.state,
         noOfAdults: this.state.noOfAdults - 1
       });
     }
   };

   onChildCountChange = (operation) => {
     if (operation == 'INC') {
       this.setState({
         ...this.state,
         noOfChildren: this.state.noOfChildren + 1
       });
     } else {
       this.setState({
         ...this.state,
         noOfChildren: this.state.noOfChildren - 1
       });
     }
   };

   onHandleTravellerCountClear = () => {
     this.setState({
       ...this.state,
       noOfAdults: 0,
       noOfChildren: 0
     });
   };
   */

  getActivefilteredFiltersRequired = () => true;

  onClickSpecifyDate = async () => {
    let departureDate;
    if (this.dateValues && this.dateValues.selectedDate) {
      departureDate = this.dateValues.selectedDate;
    } else {
      departureDate = addDays(today(), CALENDAR_SELECTED_DATE_DIFF);
    }
    this.setState({
      showCalendar: true,
      departureDate,
    });
  };

  updateDateFilterData = (selectedDate) => {
    delete this.request.fromDate;
    delete this.request.toDate;
    const formattedDate = fecha.format(selectedDate, DATE_WITH_DAY_FORMAT);
    const [date, month, year, weekDay] = formattedDate.split('-');
    const monthYear = `${month} ${year}`;
    const day = isToday(selectedDate) ?
      TODAY : (isTomorrow(selectedDate) ? TOMORROW : weekDay);
    this.dateValues = {
      date: parseInt(date, 10),
      monthYear,
      year,
      day,
      selectedDate,
      dateSelected: true,
    };
    this.setState({
      showCalendar: false,
    });
    this.request.packageDate = fecha.format(selectedDate, DATE_FORMAT);
    this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
    this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: `date_${fecha.format(selectedDate, DATE_FORMAT_OMNI)}`,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: 'date',
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
  };

  onCalendarBack = () => {
    this.setState({
      showCalendar: false,
    });
  };

  calendarDateReceived = (dateObj) => {
    delete this.request.fromDate;
    delete this.request.toDate;
    let selectedDate;
    const fromDateArr = dateObj.departureDate.split('-');
    const userDate = new Date();
    userDate.setMonth(fromDateArr[1] - 1);
    userDate.setYear(fromDateArr[2]);
    userDate.setDate(fromDateArr[0]);
    const todayDate = today();
    selectedDate = (todayDate > userDate) ? todayDate : userDate;
    const formattedDate = fecha.format(selectedDate, DATE_WITH_DAY_FORMAT);
    const [date, month, year, weekDay] = formattedDate.split('-');
    const monthYear = `${month} ${year}`;
    const day = isToday(selectedDate) ?
      TODAY : (isTomorrow(selectedDate) ? TOMORROW : weekDay.toUpperCase());
    this.dateValues = {
      date: parseInt(date, 10),
      monthYear,
      year,
      day,
      selectedDate,
      dateSelected: true,
    };
    this.request.packageDate = fecha.format(selectedDate, DATE_FORMAT);
    this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
    this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
  };


  showToast = () => {
    if (this.request.criterias) {
      let noOfFilters = 0;
      for (let criteria = 0; criteria < this.request.criterias.length; criteria += 1) {
        if (this.request.criterias[criteria].id !== this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME)) {
          noOfFilters += 1;
        }
      }
      if (noOfFilters > 0) {
        showShortToast(RESET_TEXT);
      }
    }
  };


  removeCriteriasExceptPlace = () => {
    if (this.request.criterias) {
      let noOfCriterias = this.request.criterias.length;
      while (noOfCriterias--) {
        if ((this.request.criterias[noOfCriterias].id !== this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME))) {
          this.request.criterias.splice(noOfCriterias, 1);
        }
      }
    }
  };


  onPlaceSelect = (destinationText) => {
    this.updatePopularlyPairedWithOptions = true;
    const destinationCities = this.destinationCity.split(',');
    this.destinationCity = '';
    let first = true;
    let inDestinationCity = false;
    for (let cityIndex = 0; cityIndex < destinationCities.length; cityIndex += 1) {
      if (destinationCities[cityIndex] !== destinationText) {
        if (first) {
          this.destinationCity = destinationCities[cityIndex];
          first = false;
        } else {
          this.destinationCity += `,${destinationCities[cityIndex]}`;
        }
      } else {
        inDestinationCity = true;
      }
    }

    if (!inDestinationCity) {
      for (let optionIndex = 0; optionIndex < this.popularlyPairedWithOptions.length; optionIndex += 1) {
        if (this.popularlyPairedWithOptions[optionIndex].uniqueId === destinationText) {
          this.popularlyPairedWithOptions[optionIndex].isActive = !this.popularlyPairedWithOptions[optionIndex].isActive;
          this.popularlyPairedWithOptions[optionIndex].hide = !this.popularlyPairedWithOptions[optionIndex].hide;
        }
        if (this.popularlyPairedWithOptions[optionIndex].isActive) {
          this.updatePopularlyPairedWithOptions = false;
        }
      }
      if (this.destinations.indexOf(destinationText) !== -1) {
        for (let destinationIndex = 0; destinationIndex < this.destinations.length; destinationIndex += 1) {
          if (this.destinations[destinationIndex] === destinationText) {
            this.destinations.splice(destinationIndex, 1);
          }
        }
      } else {
        this.destinations.push(destinationText);
      }

      this.addToRequest(this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME), destinationText);
    } else {
      for (let destinationIndex = 0; destinationIndex < this.destinations.length; destinationIndex += 1) {
        if (this.destinations[destinationIndex] === destinationText) {
          this.destinations.splice(destinationIndex, 1);
        }
      }

      for (let optionIndex = 0; optionIndex < this.popularPlaces.length; optionIndex += 1) {
        if (this.popularPlaces[optionIndex].name === destinationText) {
          this.popularPlaces[optionIndex].hide = false;
          this.popularPlaces[optionIndex].isActive = false;
        }
      }
      this.request.destinationCity = this.destinationCity;
      this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
      this.removeCriteriasExceptPlace();
      this.onClearDate();
      this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
      const eventName = `${pdtConstants.PDT_REMOVE_DEST_EVENT}_${destinationText}`;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: pdtConstants.PDT_REMOVE_DEST_EVENT,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }
  };

  onCitySelect = (city) => {
    if (this.labelFromOrTo === TO) {
      this.onDestSelect(city);
    } else {
      this.onHandleFromChangeSelection(city);
    }
  };

  onDestSelect = (city) => {
    if (isNotNullAndEmptyCollection(this.destinations) && this.destinations.includes(city)) {
      showShortToast('Destination already added');
      return;
    }
    this.lastPage = PAGE_NAME_SEARCH_WIDGET;
    this.lastFilter = {
      filterId: DESTINATION_CITY_FILTER_ID,
      uniquefilterVal: city,
    };
    this.showToast();
    if (this.destinationCity === '') {
      this.destinationCity = city;
    } else {
      this.destinationCity = `${this.destinationCity},${city}`;
    }
    this.destinations.push(city);

    this.request = this.getResetRequest();
    this.request.destinationCity = this.destinationCity;
    this.popularlyPairedWithOptions.indexOf(city) !== -1 ? this.popularlyPairedWithOptions.splice(this.popularlyPairedWithOptions.indexOf(city), 1) : this.popularlyPairedWithOptions;
    this.removeCriteriasExceptPlace();
    this.clearTravellingMonthsFilter();
    this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj(this.openForDestination));
    if (!this.openForDestination) {
      this.showDepDestPopUp = !this.showDepDestPopUp;
      this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
      this.callCloseSectionEvent();
      const eventName = `${pdtConstants.PDT_ADD_DEST_EVENT}_${city}`;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: pdtConstants.PDT_ADD_DEST_EVENT,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }
  };

  getResetRequest = () => {
    let criterias = [];
    let destinationCity = '';
    if (this.isWG) {
      destinationCity = this.request.destinationCity;
      const wgFilter = createWGFilter(this.props.searchWidgetData.listingFilters);
      if (wgFilter !== null) {
        criterias = criterias.concat([wgFilter]);
      }
      copyFilter(this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME), this.request.criterias, criterias);
    }
    return ({
      lob: REQUEST_LOB,
      destinationCity,
      channel: getPlatformIdentifier(),
      website: REQUEST_WEBSITE,
      filteredFiltersRequired: this.getActivefilteredFiltersRequired(),
      filterSorterParam: true,
      criterias,
    });
  };

  onHandleFromChangeSelection = async (departureCity) => {
    this.request.departureCity = departureCity;
    for (let fromPlaceIndex = 0; fromPlaceIndex < this.availableHubs.length; fromPlaceIndex += 1) {
      if (this.availableHubs[fromPlaceIndex].name === departureCity) {
        this.availableHubs[fromPlaceIndex].isActive = true;
      } else {
        this.availableHubs[fromPlaceIndex].isActive = false;
      }
    }
    this.showDepDestPopUp = !this.showDepDestPopUp;
    this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
    this.removeCriteriasExceptPlace();
    this.clearTravellingMonthsFilter();
    setDataInStorage(KEY_USER_DEP_CITY, departureCity);
    this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());

    const eventNameNewDepartureCity = `${pdtConstants.PDT_SELECT_HUB_EVENT}_${departureCity}`;
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: eventNameNewDepartureCity,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: pdtConstants.PDT_SELECT_HUB_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
  };

  onHandleRemoveDestination = (destinationText, removeAll = false) => {
    if (this.showDepDestPopUp) {
      this.showDepDestPopUp = false;
      this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
    }
    if (removeAll) {
      this.destinations = [];
      this.destinationCity = '';
    } else {
      for (let destinationIndex = 0; destinationIndex < this.destinations.length; destinationIndex += 1) {
        if (this.destinations[destinationIndex] === destinationText) {
          this.destinations.splice(destinationIndex, 1);
        }
      }
    }

    const destinationCities = this.destinationCity.split(',');
    this.destinationCity = '';
    let first = true;
    let inDestinationCity = false;
    for (let cityIndex = 0; cityIndex < destinationCities.length; cityIndex += 1) {
      if (destinationCities[cityIndex] !== destinationText) {
        if (first) {
          this.destinationCity = destinationCities[cityIndex];
          first = false;
        } else {
          this.destinationCity += `,${destinationCities[cityIndex]}`;
        }
      } else {
        inDestinationCity = true;
      }
    }

    if (!inDestinationCity) {
      for (let optionIndex = 0; optionIndex < this.popularlyPairedWithOptions.length; optionIndex += 1) {
        if (this.popularlyPairedWithOptions[optionIndex].uniqueId === destinationText) {
          this.popularlyPairedWithOptions[optionIndex].hide = false;
          this.popularlyPairedWithOptions[optionIndex].isActive = false;
        }
      }
      this.addToRequest(this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME), destinationText);
    } else {
      this.removeDestinationCity(destinationText);
      const eventName = `${pdtConstants.PDT_REMOVE_DEST_EVENT}_${destinationText}`;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: pdtConstants.PDT_SELECT_HUB_EVENT,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }

    this.callCloseSectionEvent();
  };

  removeDestinationCity = (destinationText) => {
    for (let optionIndex = 0; optionIndex < this.popularPlaces.length; optionIndex += 1) {
      if (this.popularPlaces[optionIndex].name === destinationText) {
        this.popularPlaces[optionIndex].hide = false;
        this.popularPlaces[optionIndex].isActive = false;
      }
    }
    this.request.destinationCity = this.destinationCity;
    this.showToast();
    this.removeCriteriasExceptPlace();
    this.clearTravellingMonthsFilter();
    this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
  };

  onHandleTogglePopularOptions = (popularoption) => {
    for (let optionIndex = 0; optionIndex < this.popularlyPairedWithOptions.length; optionIndex += 1) {
      if (this.popularlyPairedWithOptions[optionIndex].uniqueId === popularoption) {
        this.popularlyPairedWithOptions[optionIndex].hide = !this.popularlyPairedWithOptions[optionIndex].hide;
        this.popularlyPairedWithOptions[optionIndex].isActive = !this.popularlyPairedWithOptions[optionIndex].isActive;
      }
    }
    for (let optionIndex = 0; optionIndex < this.popularPlaces.length; optionIndex += 1) {
      if (this.popularPlaces[optionIndex].name === popularoption) {
        this.popularPlaces[optionIndex].hide = !this.popularPlaces[optionIndex].hide;
        this.popularPlaces[optionIndex].isActive = !this.popularPlaces[optionIndex].isActive;
      }
    }

    this.destinations.push(popularoption);
    this.showDepDestPopUp = !this.showDepDestPopUp;
    this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
    this.addToRequest(this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME), popularoption);
    this.callCloseSectionEvent();
  };

  onHandleTogglePopularPlacesOptions = (popularPlaceOption) => {
    for (let optionIndex = 0; optionIndex < this.popularPlaces.length; optionIndex += 1) {
      if (this.popularPlaces[optionIndex].name === popularPlaceOption) {
        this.popularPlaces[optionIndex].hide = !this.popularPlaces[optionIndex].hide;
        this.popularPlaces[optionIndex].isActive = !this.popularPlaces[optionIndex].isActive;
      }
    }
    this.onDestSelect(popularPlaceOption);
  };


  onBackAutoComplete = () => {
    this.closeAutoComplete();
    this.callCloseSectionEvent();
  };

  closeAutoComplete = () => {
    this.showDepDestPopUp = !this.showDepDestPopUp;
    this.props.toggleLocationAutoComplete(this.showDepDestPopUp);
    if (this.openForDestination) {
      this.props.onSWClose();
    }
  };

  onAvailableInclusionFilterClear = (filterIdHotel, uniqueNameFilterHotel, filterIdInclusion, uniqueNameFilterInclusion) => {
    this.onFilterClear(filterIdHotel, uniqueNameFilterHotel);
    this.onFilterClear(filterIdInclusion, uniqueNameFilterInclusion);
  };

  callCloseSectionEvent = () => {
    const eventName = pdtConstants.PDT_CLOSE_SECTION_EVENT + DESTINATION_APPEND;
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: eventName,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: pdtConstants.PDT_CLOSE_SECTION_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
  };


  onFilterClear = (filterId, uniqueNameFilter) => {
    this.deActivateOptionsMap(uniqueNameFilter);
    this.removeFromRequest(filterId);
    const eventName = `${pdtConstants.PDT_CLEAR_EVENT}_${uniqueNameFilter}`;
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: eventName,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: pdtConstants.PDT_CLEAR_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
  };

  deActivateOptionsMap = (uniqueNameFilter) => {
    const mapObj = this.masterMap.get(uniqueNameFilter);
    const {optionsList} = this.masterMap.get(uniqueNameFilter);
    for (let optionIndex = 0; optionIndex < optionsList.length; optionIndex += 1) {
      optionsList[optionIndex].isActive = false;
    }
    mapObj.optionsList = optionsList;
    mapObj.updateFlag = true;
    this.masterMap.set(uniqueNameFilter, mapObj);
  };

  onhandleTravellingMonthClear = () => {
    this.clearTravellingMonthsFilter();
    this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
  };

  clearTravellingMonthsFilter = () => {
    this.deActivateOptions(this.travellingMonthsOptions);
    delete this.request.fromDate;
    delete this.request.toDate;
    delete this.request.packageDate;
    this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
    this.onClearDate();
  };

  onClearDate = async () => {
    this.dateValues = {
      dateSelected: false,
    };
  };

  onAdvanceSearchClicked = () => {
    this.openForDestination = false;
    const eventName = pdtConstants.ADVANCE_SEARCH_CLICK;
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: eventName,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: eventName,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
    this.closeAutoComplete();
  };

  onDoneClicked = () => {
    let eventName;
    const doneStrAppend = this.getEventStrToAppend();
    if (doneStrAppend !== '') {
      eventName = `${pdtConstants.PDT_DONE_EVENT}_${doneStrAppend}`;
    } else {
      eventName = pdtConstants.PDT_DONE_EVENT;
    }
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: eventName,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: pdtConstants.PDT_DONE_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
    trackSearchWidgetClickEventNew({
      omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
      omniEventName: PDT_PAGE_EXIT_EVENT,
      pdtData: {
        pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
        eventType: PDT_RAW_EVENT,
        activity: PDT_PAGE_EXIT_EVENT,
        requestId: this.props.masterData.requestId,
        branch: this.branch,
      },
    });
    this.onDone();
  };

  onDone = async () => {
    const isWG = this.props.isWG;
    const aff = this.props.aff;
    const cmp = this.props.cmp;
    if (typeof this.props.updateDepCity === 'function') {
      this.props.updateDepCity(this.request.departureCity);
    }
    if (!this.requestInProgress) {
      this.requestInProgress = true;
      this.props.trackClickEvent(`select_destination_${this.request.destinationCity}`);
      if (!this.props.pageName || this.props.pageName === LANDING_TRACKING_PAGE_NAME) {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, this.getGroupingPageDataUpdated(aff, cmp));
        this.props.onSWClose(false,true);
        BranchIOTracker.trackContentEvent({
          [BranchIOTracker.KEYS.EVENT_NAME]: BranchIOTracker.EVENT.SEARCH,
          [BranchIOTracker.KEYS.PAGE_NAME]: BranchIOTracker.PAGE.LANDING,
          [BranchIOTracker.KEYS.DESCRIPTION]: 'Search updated from Landing page',
          [BranchIOTracker.KEYS.SEARCH_QUERY]: this.request.destinationCity,
          [BranchIOTracker.KEYS.CUSTOM_DATA]: {...this.request},
        });
      } else if (this.props.pageName === GROUPING_PAGE_NAME) {
        this.props.onSWDone({
          holidayGroupingNewDto: this.getGroupingPageData(aff),
          backFromSW: true,
        });
      } else if (this.props.pageName === LISTING_PAGE_NAME) {
        this.props.onSWDone({
          holidaysListingData: this.getListingPageDate(isWG, aff),
          backFromSW: true,
        });
      } else if (this.props.pageName === HLD_PAGE_NAME.MAP) {
        this.props.onSWDone({
          holidaysMapData: this.getMapPageData(isWG, aff),
          backFromSW: true,
        });
      }
    }
    this.requestInProgress = false;

  };

  getEventStrToAppend = () => {
    let str = '';
    if (this.request.criterias) {
      for (let index = 0; index < this.request.criterias.length; index += 1) {
        if (str !== '') {
          str += ' | ';
        }
        const criteria = this.request.criterias[index];
        const filterName = this.getFilterNameFromMap(criteria.id);
        str += filterName;
        for (let valIndex = 0; valIndex < criteria.values.length; valIndex += 1) {
          str = `${str}_${criteria.values[valIndex]}`;
        }
      }
    }
    return str;
  };

  getGroupingPageDataUpdated(aff, cmp ) {
    return ({
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
      selectedDate: this.dateValues.selectedDate,
      destinationCity: this.destinationCity,
      filters: this.request.criterias,
      ...this.groupedData,
      aff: aff,
      cmp:cmp,
    });
  }

  getListingPageDateUpdated(isWG, aff, cmp) {
    return ({
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
      selectedDate: this.dateValues.selectedDate,
      dest: this.destinationCity,
      filters: this.request.criterias,
      redirectionPage: LISTING_PAGE,
      event: ON_DONE_SW_EVENT,
      pt: isWG ? WEEKEND_GETAWAY_PAGE_TYPE : '',
      aff: aff,
      cmp : cmp,
      ...this.groupedData,
    });
  }

  getGroupingPageData(aff ) {
    let destinationCityData = {};
    if ( this.groupedData.campaign) {
      destinationCityData.campaign = this.groupedData.campaign;
    }
    if (this.destinationCity) {
      destinationCityData.destinationCity = this.destinationCity;
    }
    return ({
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
      selectedDate: this.dateValues.selectedDate,
      destinationCity: this.destinationCity,
      filters: this.request.criterias,
      ...this.groupedData,
      destinationCityData,
      aff: aff,
    });
  }

  getListingPageDate(isWG, aff) {
    let destinationCityData = {};
    if ( this.groupedData.campaign) {
      destinationCityData.campaign = this.groupedData.campaign;
    }

    if (this.destinationCity) {
      destinationCityData.destinationCity = this.destinationCity;
    }

    return ({
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
      selectedDate: this.dateValues.selectedDate,
      ...this.groupedData,
      destinationCityData,
      dest: this.destinationCity,
      filters: this.request.criterias,
      redirectionPage: LISTING_PAGE,
      event: ON_DONE_SW_EVENT,
      pt: isWG ? WEEKEND_GETAWAY_PAGE_TYPE : '',
      aff: aff,
    });
  }

  getMapPageData(isWG, aff) {
    return ({
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
      selectedDate: this.dateValues.selectedDate,
      ...this.groupedData,
      criterias: this.request.criterias,
      event: ON_DONE_SW_EVENT,
      pt: isWG ? WEEKEND_GETAWAY_PAGE_TYPE : '',
      aff: aff,
    });
  }

  activateOption(options, selectedOption) {
    for (let optionIndex = 0; optionIndex < options.length; optionIndex += 1) {
      if (options[optionIndex].uniqueId === selectedOption) {
        options[optionIndex].isActive = !options[optionIndex].isActive;
      }
    }
  }


  deActivateOptions(options) {
    for (let optionIndex = 0; optionIndex < options.length; optionIndex += 1) {
      options[optionIndex].isActive = false;
    }
  }

  getSearchWidgetDataObj() {
    return ({
      popularlyPairedWithOptions: this.popularlyPairedWithOptions,
      updatePopularlyPairedWithOptions: this.updatePopularlyPairedWithOptions,
      travellingMonthsOptions: this.travellingMonthsOptions,
      availableHubs: this.availableHubs,
      masterMap: this.masterMap,
      pageNamePDT: this.props.pageName,
      destinationCity: this.destinationCity,
      destinations: this.destinations,
      request: this.request,
      holidaySearchWidgetDataOld: this.props.searchWidgetData,
      filterIdMap: this.filterIdMap,
      cmpChannel: this.cmpChannel,
      budgetFilterStats: this.budgetFilterStats,
      isWG: this.isWG,
      isLanding: this.props.isLanding,
    });
  }

  getSearchWidgetDataMasterObj(applyFiltersDirectly = false) {
    const dateObj = {
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
    };
    return ({
      availableHubs: this.props.availableHubs,
      request: this.request,
      pageName: this.pageNameForFilters,
      pageNamePDT: this.props.pageName,
      destinations: this.destinations,
      criterias: this.request.criterias,
      destinationCity: this.request.destinationCity,
      dateObj,
      showGoingTo: this.showDepDestPopUp,
      cmpChannel: this.cmpChannel,
      applyFiltersDirectly,
      isWG: this.isWG,
    });
  }

  removeFromRequest(id) {
    let found = false;
    if (this.request.criterias) {
      for (let criteriaIndex = 0; criteriaIndex < this.request.criterias.length; criteriaIndex += 1) {
        if (this.request.criterias[criteriaIndex].id === id) {
          found = true;
          this.request.criterias.splice(criteriaIndex, 1);
          this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
          this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
        }
      }
    }
    if (!found) {
      this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
      this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
    }
  }

  addToRequest(id, value, replace = false) {
    if (!this.request.criterias) {
      const criterias = [];
      this.lastFilter = this.getLastFilter(id, value);
      criterias.push({
        id,
        values: [value],
      });
      this.request.criterias = criterias;
    } else {
      let found = false;
      for (let criteriaIndex = 0; criteriaIndex < this.request.criterias.length; criteriaIndex += 1) {
        if (this.request.criterias[criteriaIndex].id === id) {
          found = true;
          if (replace) {
            this.lastFilter = this.getLastFilter(id, value);
            this.request.criterias[criteriaIndex].values = [value];
          } else if (this.request.criterias[criteriaIndex].values.indexOf(value) === -1) {
            this.lastFilter = this.getLastFilter(id, value);
            this.request.criterias[criteriaIndex].values.push(value);
          } else {
            this.request.criterias[criteriaIndex].values.splice(this.request.criterias[criteriaIndex].values.indexOf(value), 1);
            if (this.request.criterias[criteriaIndex].values.length === 0) {
              this.updateMasterMap(id);
              this.request.criterias.splice(criteriaIndex, 1);
            }
          }
        }
      }
      if (!found) {
        this.lastFilter = this.getLastFilter(id, value);
        this.request.criterias.push({
          id,
          values: [value],
        });
      }
    }
    this.request.filteredFiltersRequired = this.getActivefilteredFiltersRequired();
    const filterName = this.getFilterNameFromMap(id);
    if (filterName) {
      const eventName = `${filterName}_${value}`;
      trackSearchWidgetClickEventNew({
        omniPageName: this.props?.pageName || PAGE_NAME_SEARCH_WIDGET,
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.masterData.searchWidgetDataPDT.pageDataMap,
          eventType: PDT_RAW_EVENT,
          activity: filterName,
          requestId: this.props.masterData.requestId,
          branch: this.branch,
        },
      });
    }
    this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
  }

  getFilterNameFromMap = (id) => {
    for (const [key, value] of this.filterIdMap) {
      if (value === id) {
        return key;
      }
    }
    return null;
  };

  updateMasterMap = (id) => {
    for (const [key] of this.masterMap) {
      const mapObj = this.masterMap.get(key);
      if (mapObj.id === id) {
        mapObj.updateFlag = true;
      }
    }
  };

  getLastFilter = (id, value) => ({
    filterId: id,
    uniquefilterVal: value,
  });

  removeLastFilter = () => {
    if (this.lastFilter && this.lastFilter.filterId === DESTINATION_CITY_FILTER_ID) {
      this.destinations.splice(-1, 1);
      const destinationCities = this.destinationCity.split(',');
      this.destinationCity = '';
      let first = true;
      for (let cityIndex = 0; cityIndex < destinationCities.length; cityIndex += 1) {
        if (destinationCities[cityIndex] !== this.lastFilter.uniquefilterVal) {
          if (first) {
            this.destinationCity = destinationCities[cityIndex];
            first = false;
          } else {
            this.destinationCity += `,${destinationCities[cityIndex]}`;
          }
        }
      }
      this.removeDestinationCity(this.lastFilter.uniquefilterVal);
    } else if (this.lastFilter &&
      this.lastFilter.filterId !== this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME)) {
      let uniqueNameFilter;
      for (const [key, value] of this.masterMap) {
        if (value.id === this.lastFilter.filterId) {
          uniqueNameFilter = key;
        }
      }
      if (uniqueNameFilter === FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME
        || uniqueNameFilter === FILTER_URL_NAME_CONSTANTS.BUDGET_FILTER_URL_NAME) {
        this.clearSliderFilter(uniqueNameFilter);
        return;
      }
      const mapObj = this.masterMap.get(uniqueNameFilter);
      if (mapObj && mapObj.optionsList) {
        const {optionsList} = mapObj;

        for (let optionIndex = 0; optionIndex < optionsList.length; optionIndex += 1) {
          if (optionsList[optionIndex].uniqueId === this.lastFilter.uniquefilterVal) {
            optionsList[optionIndex].isActive = !optionsList[optionIndex].isActive;
          }
          if (optionsList[optionIndex].isActive) {
            mapObj.updateFlag = false;
          }
        }
        mapObj.optionsList = optionsList;
        this.masterMap.set(uniqueNameFilter, mapObj);

        const {criterias} = this.request;
        for (let criteriaIndex = 0; criteriaIndex < criterias.length; criteriaIndex += 1) {
          if (criterias[criteriaIndex].id === this.lastFilter.filterId) {
            this.addToRequest(this.lastFilter.filterId, this.lastFilter.uniquefilterVal);
          }
        }
      }
    } else if (this.lastFilter && this.lastFilter.filterId === this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME)) {
      this.destinations.splice(-1, 1);
      this.addToRequest(this.lastFilter.filterId, this.lastFilter.uniquefilterVal);
    } else {
      delete this.request.fromDate;
      delete this.request.toDate;
      delete this.request.packageDate;
      this.dateValues = {
        dateSelected: false,
      };
      this.props.fetchSearchWidgetDataWithOldData(this.getSearchWidgetDataObj());
    }
  };
}
export default withBackHandler(HolidaySearchWidgetPage)

