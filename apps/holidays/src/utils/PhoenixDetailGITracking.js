import {formatClickEventName, formatPageLoadEventName, GI_PAGE_NAMES, sendGIEvents} from './ThirdPartyUtils';

const pageName = GI_PAGE_NAMES.DETAIL;
export const trackDetailGILoadEvent = ({ isError = false,giData={} } = {}) => {
    sendGIEvents({
        eventName: formatPageLoadEventName({pageName}),
        data: {
            pageName: `${pageName}${isError ? '_error' : ' '}`,
            googleTagParams : {...giData, "Page_Name": pageName},
        },
    });
};
export const trackDetailGIClickEvent = ({ eventName, ...rest }) => {
    sendGIEvents({
        eventName: formatClickEventName({ string: eventName, pageName }),
        data: {
            pageName,
            ...rest,
        },
    });
};
