import {EXPIRY_DAYS, GCLID, SEND_TO_ID} from './googleAdConstants';
import {isMobileClient} from './HolidayUtils';

const getParam = p => {
    let match = RegExp('[?&]' + p + '=([^&]*)').exec(window.location.search);
    return match && decodeURIComponent(match[1].replace(/\+/g, ' '));
};

const getExpiryRecord = value => {
    let expiryPeriod = EXPIRY_DAYS * 24 * 60 * 60 * 1000;
    let expiryDate = new Date().getTime() + expiryPeriod;
    return {
        value: value,
        expiryDate: expiryDate,
    };
};

const setGclId = gclIdParam => {
    if (isMobileClient()) {
        return;
    }
    let gclIdRecord = null;
    if (gclIdParam) {
        gclIdRecord = getExpiryRecord(gclIdParam);
        localStorage.setItem('gclId', JSON.stringify(gclIdRecord));
    }
};
export const getGclId = () => {
    if (isMobileClient()) {
        return;
    }
    return JSON.parse(localStorage.getItem('gclId'));
};
export const addGclId = () => {
    if (isMobileClient()) {
        return;
    }
    let gclIdParam = getParam(GCLID);
    let gclId = getGclId();
    if (gclIdParam || gclId) {
        if (!gclId) {
            setGclId(gclIdParam);
        } else {
            let gclIdValue = gclId;
            let isGclIdValid = gclIdValue && new Date().getTime() < gclIdValue.expiryDate;
            if (!isGclIdValid) {
                localStorage.removeItem('gclId');
                setGclId(gclIdParam);
            } else if (gclIdValue.value !== gclIdParam && gclIdParam !== null) {
                localStorage.removeItem('gclId');
                setGclId(gclIdParam);
            }
        }
    }
};
export const triggerEvent = (data)=>{
    if (isMobileClient()) {
        return;
    }
    gtag('event', 'conversion', {
        'send_to': SEND_TO_ID,
        'value': data.TTV,
        'currency': 'INR',
        'transaction_id': data.NLID,
    });
};
