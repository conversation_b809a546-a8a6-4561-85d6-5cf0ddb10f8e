import { Platform, NativeModules } from 'react-native';
import {isAndroidClient, isIosClient} from "./HolidayUtils";
const KEYS = {
  EXTRA_DATA: 'extraData',
  PAGE_NAME: 'pageName',
  EVENT_NAME: 'eventName',
  CUSTOMER_EVENT_ALIAS: 'customerEventAlias',
  CUSTOM_DATA: 'customData',
  DESCRIPTION: 'description',
  SEARCH_QUERY: 'searchQuery',
  AFFILIATE: 'affiliate',
  COUPON: 'coupon',
  CURRENCY: 'currency',
  SHIPPING: 'shipping',
  TAX: 'tax',
  REVENUE: 'revenue',
  CANONICAL_IDENTIFIER: 'canonicalIdentifier',
  CANONICAL_URL: 'canonicalUrl',
  TITLE: 'title',
  KEYWORDS: 'keywords',
  CUSTOM_META_DATA: 'metaData',
  IMAGE_CAPTIONS: 'imageCaptions',
  PRICE: 'price',
  ADDRESS: 'address',
  RATING: 'rating',
  LOCATION: 'location',
  PRODUCT_BRAND: 'productBrand',
  PRODUCT_CATEGORY: 'productCategory',
  PRODUCT_NAME: 'productName',
  PRODUCT_VARIANT: 'productVariant',
  SKU: 'sku',
  PRODUCT_QUANTITY: 'quantity',
};
const PAGE = {
  LANDING: 'HolidayLanding',
  GROUPING: 'HolidayCollection',
  LISTING: 'HolidayListing',
  DETAIL: 'HolidayDetail',
  REVIEW: 'HolidayReview',
  THANKYOU: 'HolidayThankyou',
  QUERY: 'HolidayQuery',
};
const EVENT = {
  PAGE_VIEW: 'PageView',
  SEARCH: 'search',
  VIEW: 'view',
  ADD_TO_CART: 'addToCart',
  PROCEED_TO_PAYMENT: 'payment',
  PURCHASE: 'purchage',
  QUERY: 'query',
};
const ANDROID_EVENT_MAP = {
  [EVENT.SEARCH]: 'SEARCH',
  [EVENT.VIEW]: 'VIEW_ITEM',
  [EVENT.ADD_TO_CART]: 'ADD_TO_CART',
  [EVENT.PROCEED_TO_PAYMENT]: 'INITIATE_PURCHASE',
  [EVENT.PURCHASE]: 'PURCHASE',
  [EVENT.QUERY]: 'SUBMIT_QUERY',
};
const IOS_EVENT_MAP = {
  [EVENT.SEARCH]: 'SEARCH',
  [EVENT.VIEW]: 'VIEW_ITEM',
  [EVENT.ADD_TO_CART]: 'ADD_TO_CART',
  [EVENT.PROCEED_TO_PAYMENT]: 'INITIATE_PURCHASE',
  [EVENT.PURCHASE]: 'PURCHASE',
  [EVENT.QUERY]: 'SUBMIT_QUERY',
};

const getEventName = (eventName) => {
  if (isAndroidClient()) {
    return ANDROID_EVENT_MAP[eventName];
  } else if (isIosClient()) {
    return IOS_EVENT_MAP[eventName];
  }
  return 'unknown';
};
const trackPageView = (params) => {
  const { BranchIOTrackerModule } = NativeModules;
  const { pageName, extraData } = params;
  const data = {
    [KEYS.PAGE_NAME]: pageName,
    [KEYS.CUSTOM_DATA]: extraData,
  };
  BranchIOTrackerModule?.pageView(data);
};
const trackCustomEvent = (params) => {
  const { BranchIOTrackerModule } = NativeModules;
  const { eventName, pageName } = params;
  const data = {
    ...params,
    [KEYS.EVENT_NAME]: getEventName(eventName),
    [KEYS.PAGE_NAME]: pageName,
  };
  BranchIOTrackerModule?.trackCustomEvent(data);
};
const trackContentEvent = (params) => {
  const { BranchIOTrackerModule } = NativeModules;
  const { eventName = '', pageName = '' } = params;
  const data = {
    ...params,
    [KEYS.EVENT_NAME]: getEventName(eventName),
    [KEYS.PAGE_NAME]: pageName,
  };
  BranchIOTrackerModule?.trackContentEvent(data);
};
const trackCommerceEvent = (params) => {
  const { BranchIOTrackerModule } = NativeModules;
  const { eventName, pageName } = params;
  const data = {
    ...params,
    [KEYS.EVENT_NAME]: getEventName(eventName),
    [KEYS.PAGE_NAME]: pageName,
  };
  BranchIOTrackerModule?.trackCommerceEvent(data);
};
export default {
  EVENT,
  KEYS,
  PAGE,
  trackPageView,
  trackCustomEvent,
  trackContentEvent,
  trackCommerceEvent,
};
