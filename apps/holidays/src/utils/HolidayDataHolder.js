import { LISTING_TRACKING_PAGE_NAME } from '../Listing/ListingConstants';
import { GROUPING_TRACKING_PAGE_NAME, SME_LISTING } from '../Grouping/HolidayGroupingConstants';
import { REVIEW_PDT_PAGE_NAME } from '../Review/HolidayReviewConstants';
import { PAGE_NAME_SEARCH_WIDGET } from '../SearchWidget/SearchWidgetConstants';
import { DETAIL_TRACKING_PAGE_NAME } from '../PhoenixDetail/DetailConstants';
import { PAGE_NAME_QUERY } from '../HolidayConstants';
import { MAP_PAGE_TRACKING_NAME } from '../Map/MapConstants';
import {
  HOLIDAYS_FLIGHT_DETAILS,
  HOLIDAYS_FLIGHT_OVERLAY,
  PHOENIX_ACTIVITY_DETAIL,
  PHOENIX_ACTIVITY_LISTING,
  PHOENIX_DETAIL_OVERLAY,
} from '../PhoenixDetail/Utils/PheonixDetailPageConstants';
import { getDataFromStorage, setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import { Session } from '../Common/Components/Interventions/InterventionConstants';
import { isEmpty } from 'lodash';
import { LANDING_TRACKING_PAGE_NAME } from '../LandingNew/LandingConstants';
import { _getUserStore } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

const SESSION_EXPIRE_TIME = 30;
let session = {};
export default class HolidayDataHolder {
  static sInstance = null;

  constructor() {
    this.cmp = '';
    this.currentPage = '';
    this.currentPagev2 = '';
    this.pageMapOmni = new Map();
    this.prevPageMap = new Map();
    this.prevPageMapV2 = new Map();
    this.campaign = '';
    this.source = '';
    this.departureCity = '';
    this.isUserOnPreSales = false;
    this.subFunnel = '';
    this.subpage_name = ''
    this.isPSMFlow = ''
    this.psmQueryDetail = {}
  }

  static getInstance() {
    if (HolidayDataHolder.sInstance == null) {
      HolidayDataHolder.sInstance = new HolidayDataHolder();
    }
    return this.sInstance;
  }

  static clearData() {
    this.sInstance = new HolidayDataHolder();
  }

  setCurrentPage(pageKey) {
    const pageName = this.getOmniPageNameForKey(pageKey);
    if (pageName === this.currentPage || pageKey === 'holidaysQueryForm') {
      return;
    }
    this.prevPageMap.set(pageName, this.currentPage);
    this.currentPage = pageName;
    if (!this.pageMapOmni.get(pageName)) {
      this.pageMapOmni.set(pageName, pageName);
    }
  }
  getCurrentPageNameV2(){
    return this.currentPagev2
  }
  setCurrentPageNameV2(pageKey){
    const pageName = this.getPageName(pageKey);
    this.prevPageMapV2.set(pageName, this.currentPagev2);
    this.currentPagev2 = pageName;
  }
  getPrevPageNameV2(pageName){
    return this.prevPageMapV2.get(pageName)
  }

  getCurrentPage() {
    return this.currentPage;
  }
  setFunnelEntry(pageName){
    this.isPSMFlow = pageName
  }
  getFunnelEntry() {
    return this.isPSMFlow;
  }

  setOmniPageName(pageName, omniPageName) {
    this.pageMapOmni.set(pageName, omniPageName);
  }

  getPrevPageOmni(pageName) {
    return this.pageMapOmni.get(this.prevPageMap.get(pageName));
  }
  getPrevPageName(pageName) {
    return this.prevPageMap.get(pageName);
  }
  setCmp(cmp) {
    if (cmp) {
      this.cmp = cmp;
    }

    // if (Platform.OS === RAW_PLATFORM_NAME && !this.cmp) {
    //   this.cmp = window.document.referrer;
    // }
  }

  setSource(source) {
    if (source) {
      this.source = source;
    }
  }
  getSource() {
    if(this.source) {
      return this.source;
    }
    return 'NA'
  }

  getCmp() {
    return this.cmp;
  }

  setCampaign(campaign) {
    if (campaign) {
      this.campaign = campaign;
    }
  }
  setSubPageName(subpagename){
    this.subpage_name = subpagename
  }
  getSubPageName(){
   return  this.subpage_name
  }
  clearSubPageName(){
    this.subpage_name = ""
  }

  setIsUserOnPreSales(onPreSales = false) {
    this.isUserOnPreSales = onPreSales;
  }

  getIsUserOnPreSales() {
    return this.isUserOnPreSales;
  }

  getCampaign() {
    return this.campaign;
  }

  setDepartureCity(depCity) {
    if (depCity) {
      this.departureCity = depCity;
    }
  }

  getDepartureCity() {
    return this.departureCity;
  }
  getPageName(pageKey) {
    switch (pageKey) {
      case 'holidaysLanding':
        return LANDING_TRACKING_PAGE_NAME;
      case 'holidaysLandingNew':
        return LANDING_TRACKING_PAGE_NAME;
      case 'holidaysListing':
        return LISTING_TRACKING_PAGE_NAME;
      case 'holidaysGrouping':
        return LISTING_TRACKING_PAGE_NAME;
      case 'holidaysDetail':
        return 'detail';
      case 'holidaysReview':
        return 'review'
      case 'holidaysThankYou':
        return 'thankyou'
      default : return this.currentPagev2
    }
  }
  setQueryDetailForPSM(data){
    this.psmQueryDetail = {ticket_details:data}
  }
  getQueryDetailForPSM(){
    return this.psmQueryDetail
  }
  clearQueryDetailForPSM(){
    this.psmQueryDetail = {}
  }
  getOmniPageNameForKey = (pageKey) => {
    switch (pageKey) {
      case 'holidaysLanding':
        return LANDING_TRACKING_PAGE_NAME;

      case 'holidaysListing':
        return LISTING_TRACKING_PAGE_NAME;

      case 'holidaysGrouping':
        return GROUPING_TRACKING_PAGE_NAME;

      case 'holidaysDetailReplace':
      case 'holidaysDetail':
        return DETAIL_TRACKING_PAGE_NAME;
      case 'PhoneixDetailOverlay':
        return PHOENIX_DETAIL_OVERLAY;
      case 'holidaysFlightOverlay':
        return HOLIDAYS_FLIGHT_OVERLAY;
      case 'flightDetail':
        return HOLIDAYS_FLIGHT_DETAILS;
      case 'phoenixActivityListing':
        return PHOENIX_ACTIVITY_LISTING;
      case 'phoenixActivityDetail':
        return PHOENIX_ACTIVITY_DETAIL;
      case SME_LISTING:
        return SME_LISTING;
      case 'holidaysReview':
        return REVIEW_PDT_PAGE_NAME;

      case 'holidaysSearchWidget':
        return PAGE_NAME_SEARCH_WIDGET;

      case 'holidaysQueryForm':
        return PAGE_NAME_QUERY;

      case MAP_PAGE_TRACKING_NAME:
        return MAP_PAGE_TRACKING_NAME;
      default:
        return pageKey;
    }
  };
  checkIfInterventionExits = async (intervention, storedSessionObject, pageName) => {
    return new Promise(async (resolve) => {
      try {
        if (intervention) {
          // commented code updates the count of displayed interventions in the session object if it comes again which is not required now.

          // const { displayedInterventions } = storedSessionObject || {};
          // let index = -1; //displayedInterventions.findIndex((e) => e.interventionId == intervention?.id);
          // if (index >= 0) {
          //   const lastSessionTime = Date.now();
          //   storedSessionObject.displayedInterventions[index].count++;
          //   storedSessionObject.displayedInterventions[index].displayTimeMillis = Date.now();
          //   storedSessionObject.displayedInterventions[index].sessionNo =
          //     storedSessionObject.sessionCount;
          //   setDataInStorage(Session.SESSION, storedSessionObject);
          //   resolve({ ...storedSessionObject, lastSessionTime });
          // } else {
          const displayTime = Date.now();
          const { displayedInterventions = [] } = storedSessionObject || {};
          const interventionObject = {
            interventionId: intervention.id,
            displayTimeMillis: displayTime,
            sessionNo: storedSessionObject.sessionCount,
            page: pageName,
            count: 1,
            type: intervention.type,
          };
          displayedInterventions.push(interventionObject);
          const obj = {
            ...storedSessionObject,
            displayedInterventions: displayedInterventions,
            lastSessionTime: displayTime,
          };
          setDataInStorage(Session.SESSION, obj);
          resolve(obj);
          // }
        }
      } catch (e) {
        console.log(e);
      }
    });
  };
  setInterventionList = async (intervention, pageName) => {
    let storedSessionObject = await getDataFromStorage(Session.SESSION);
    const updatedData = await this.checkIfInterventionExits(
      intervention,
      storedSessionObject,
      pageName,
    );
    if (updatedData) {
      session = { ...updatedData, lastSessionTime: updatedData.lastSessionTime };
      return session;
    }
  };
  getInterventionFilteredData = async (savedInterventions) => {
    const networkUtils = require('./HolidayNetworkUtils');
    const data = await networkUtils.getActiveIntervention();
    const { activeInterventionIds = [] } = data || {};
    const newInterventions = savedInterventions.filter((intervention) => {
      return activeInterventionIds?.includes(intervention?.interventionId);
    });
    return newInterventions;
  };

  setSessionInStorage = () => {
    return new Promise(async (resolve) => {
      const storedSessionObject = await getDataFromStorage(Session.SESSION);
      if (isEmpty(session)) {
        const newSessionObject = {
          sessionCount: isEmpty(storedSessionObject) ? 1 : storedSessionObject.sessionCount + 1,
          displayedInterventions: isEmpty(storedSessionObject)
            ? []
            : storedSessionObject.displayedInterventions,
        };
        session = { ...newSessionObject, lastSessionTime: Date.now() };
        setDataInStorage(Session.SESSION, newSessionObject);
        resolve(session);
      } else {
        const { lastSessionTime } = session || {};
        const diff = Math.round((Date.now() - lastSessionTime) / (1000 * 60)); //in minutes
        if (diff > 2) {
          const displayedInterventionData = await this.getInterventionFilteredData(
            storedSessionObject?.displayedInterventions,
          );
          const newSessionObject = {
            sessionCount: storedSessionObject.sessionCount + 1,
            displayedInterventions: displayedInterventionData,
          };
          const UpdatedlastSessionTime = Date.now();
          session = { ...newSessionObject, lastSessionTime: UpdatedlastSessionTime };
          setDataInStorage(Session.SESSION, newSessionObject);
          resolve(session);
        } else {
          resolve(session);
        }
      }
    });
  };

  setBanner = (bannerValue) => {
    this.bannerValue = bannerValue;
  };
  getBanner = () => {
    return this.bannerValue;
  };

  setSubFunnel = async () => {
    const { userCurrentStore } = await _getUserStore();
    this.subFunnel = userCurrentStore;
  };
  getSubFunnel = () => {
    return this.subFunnel;
  };
}
