import {NativeModules} from 'react-native';
import {AFFILIATES, FUNNELS, GI_AFFILIATE_SUFFIX, PDT_PUSH_URL, TP_AFFILIATE_SUFFIX} from '../HolidayConstants';
import {pdtConstants} from '../SearchWidget/SearchWidgetConstants';
import {
  createRandomString,
  getDepartureCity,
  isEmptyString,
  isNotNullAndEmptyCollection,
  isRawClient,
} from './HolidayUtils';
import {getOmniPageName, HOLIDAYS_BRANCH_NONE} from './HolidayTrackingUtils';
import {getDataFromStorage, KEY_AFF_REF_ID, KEY_HOL_META} from '@mmt/legacy-commons/AppState/LocalStorage';
import HolidayDataHolder from './HolidayDataHolder';
import { getPokusConfigExpDetailsList, getPokusExpVarientKey } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {PokusLobs} from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {isArray, isEmpty} from 'lodash';

export const send_gzip_post = (apiData) => {
  const apiArr = [];
  apiArr[0] = apiData;
  const apiDataArr = JSON.stringify(apiArr);
  const xmlhttp = new XMLHttpRequest();
  xmlhttp.open('POST', PDT_PUSH_URL, true);
  xmlhttp.setRequestHeader('Content-type', 'application/json');
  xmlhttp.setRequestHeader('Request-Data-Encoding', 'json');
  if (isRawClient()) {
    const pako = require('pako');
    const output = pako.gzip(apiDataArr);
    xmlhttp.send(output);
  } else {
    xmlhttp.setRequestHeader('Accept-Encoding', 'gzip');
    xmlhttp.send(apiDataArr);
  }
};

const populatePDTTableDetails = (pdtPushObject) => {
  pdtPushObject.tpl1 = '71453' //'25821'; updated template id for holiday
  pdtPushObject.m2 = '317';
  pdtPushObject.m126 = true;
  pdtPushObject.m123 = `${new Date().getTime()}`;
};

const populateFunnelDetails = async (pdtPushObject) => {
  const holMetaObj = await getDataFromStorage(KEY_HOL_META);
  pdtPushObject.meta_affiliate =
    (holMetaObj && holMetaObj.affiliate) ? holMetaObj.affiliate : AFFILIATES.MMT;
  pdtPushObject.meta_is_weekend_gateway =
    holMetaObj && holMetaObj.funnel && holMetaObj.funnel === FUNNELS.WEEKEND_GETAWAY;
  const affRefId = await getDataFromStorage(KEY_AFF_REF_ID);
  if (affRefId != null) {
    pdtPushObject.meta_affiliate_ref_id = affRefId;
  }
};

export const populateAppAndDeviceDetails = (pdtPushObject, deviceDetailsMap) => {
  if (deviceDetailsMap) {
    if (deviceDetailsMap.device_type != null) {
      pdtPushObject.dvc_type = deviceDetailsMap.device_type;
    }
    if (deviceDetailsMap.device_os != null) {
      pdtPushObject.dvc_os = deviceDetailsMap.device_os;
    }
    if (deviceDetailsMap.device_affluence != null) {
      pdtPushObject.cmp_key = deviceDetailsMap.device_affluence;
    }
    if (deviceDetailsMap.device_name != null) {
      pdtPushObject.dvc_mdl = deviceDetailsMap.device_name;
    }
    if (deviceDetailsMap.lat != null) {
      pdtPushObject.dvc_lat = deviceDetailsMap.lat;
    }
    if (deviceDetailsMap.long != null) {
      pdtPushObject.dvc_long = deviceDetailsMap.long;
    }
    if (deviceDetailsMap.browser != null) {
      pdtPushObject.dvc_brwsr = deviceDetailsMap.browser;
    }
    if (deviceDetailsMap.internet_connection != null) {
      pdtPushObject.dvc_conn_type = deviceDetailsMap.internet_connection;
    }
    if (deviceDetailsMap.mobile_os != null) {
      pdtPushObject.dvc_mob_os = deviceDetailsMap.mobile_os;
    }
    if (deviceDetailsMap.os_version != null) {
      pdtPushObject.dvc_os_ver = deviceDetailsMap.os_version;
    }
    if (deviceDetailsMap.app_version != null) {
      pdtPushObject.dvc_app_ver = deviceDetailsMap.app_version;
    }
    if (deviceDetailsMap.meta_env != null) {
      pdtPushObject.meta_env = deviceDetailsMap.meta_env;
    }
    if (deviceDetailsMap.dvc_did != null) {
      pdtPushObject.dvc_did = deviceDetailsMap.dvc_did;
    }
  }
};

export const populateUserDetails = (pdtPushObject, userDetailsMap) => {
  if (userDetailsMap) {
    if (userDetailsMap.user_city != null) {
      pdtPushObject.usr_city = userDetailsMap.user_city;
    }
    if (userDetailsMap.country_code != null) {
      pdtPushObject.usr_m_cty_cd = userDetailsMap.country_code;
    }
    if (userDetailsMap.uuid != null) {
      pdtPushObject.usr_uuid = userDetailsMap.uuid;
    }
    if (userDetailsMap.login_channel != null) {
      pdtPushObject.usr_login_chnl = userDetailsMap.login_channel;
    }
    if (userDetailsMap.profile_type != null) {
      pdtPushObject.usr_prof_typ = userDetailsMap.profile_type;
    }
    if (userDetailsMap.marketing_cloud_id != null) {
      pdtPushObject.usr_mcid = userDetailsMap.marketing_cloud_id;
    }
    if (userDetailsMap.email_comm_id != null) {
      pdtPushObject.usr_e_com_id = userDetailsMap.email_comm_id;
    }
    if (userDetailsMap.mobile_comm_id != null) {
      pdtPushObject.usr_m_com_id = userDetailsMap.mobile_comm_id;
    }
    if (userDetailsMap.is_logged_in != null) {
      pdtPushObject.usr_logged_in_stat = userDetailsMap.is_logged_in;
    }
    if (userDetailsMap.visit_number != null) {
      pdtPushObject.sess_vst_no = userDetailsMap.visit_number;
    }
    if (userDetailsMap.is_corp_user != null) {
      pdtPushObject.usr_corp_stat = userDetailsMap.is_corp_user;
    }
    if (userDetailsMap.wallet_balance != null) {
      pdtPushObject.usr_wal_balnc = parseFloat(userDetailsMap.wallet_balance);
    }
    if (userDetailsMap.hydra_segments != null) {
      if (isArray(userDetailsMap.hydra_segments)) {
        pdtPushObject.usr_hyd_seg_ls = userDetailsMap.hydra_segments;
      } else if (!isEmpty(userDetailsMap.hydra_segments)) {
        pdtPushObject.usr_hyd_seg_ls = userDetailsMap.hydra_segments.split(',');
      }
    }
    if (userDetailsMap.omni_visitor_id != null) {
      pdtPushObject.usr_omni_vid = userDetailsMap.omni_visitor_id;
    }
    if (userDetailsMap.black_status != null) {
      pdtPushObject.usr_lylty_stat = userDetailsMap.black_status;
    }
    if (userDetailsMap.hld_double_black_status != null) {
      pdtPushObject.usr_prime_stat = userDetailsMap.hld_double_black_status;
    } else if (userDetailsMap.double_black_status != null) {
      pdtPushObject.usr_prime_stat = userDetailsMap.double_black_status;
    }
    if (userDetailsMap.session_id != null) {
      pdtPushObject.sess_mmt_id = userDetailsMap.session_id;
    }
    if (userDetailsMap.user_agent != null) {
      pdtPushObject.meta_usr_agnt = userDetailsMap.user_agent;
    }
    if (userDetailsMap.client_ip != null) {
      pdtPushObject.meta_clnt_ip = userDetailsMap.client_ip;
    }
  }
};

const populateRequestDetails = (pdtPushObject, requestDetails, record_type, activity, requestId, branch) => {
  if (record_type != null) {
    pdtPushObject.meta_rw_typ = record_type;
  }
  if (activity != null) {
    pdtPushObject.meta_act_nm = activity;
  }
  if (requestId != null) {
    pdtPushObject.meta_rq_id = requestId;
  }
  pdtPushObject.meta_rw_id = createRandomString();
  if (requestDetails) {
    if (requestDetails.lob != null) {
      pdtPushObject.meta_lob_nm = (branch === 'DOM' ? 'D' : 'I') + requestDetails.lob;
    }
    if (requestDetails.page != null) {
      let affiliateSuffix = '';
      if (pdtPushObject.meta_affiliate === AFFILIATES.GI) {
        affiliateSuffix = GI_AFFILIATE_SUFFIX;
      } else if (pdtPushObject.meta_affiliate === AFFILIATES.TP) {
          affiliateSuffix = TP_AFFILIATE_SUFFIX;
      }
      pdtPushObject.meta_pg_nm = getOmniPageName(requestDetails.page, branch, requestDetails.isWG, affiliateSuffix);
      pdtPushObject.meta_pg_nm_full = requestDetails.page;
    }
    if (requestDetails.funnel_step != null) {
      pdtPushObject.meta_fnnl_step = requestDetails.funnel_step;
    }
    if (requestDetails.cmp_channel != null) {
      pdtPushObject.cmp_chnl = requestDetails.cmp_channel;
    } else if (HolidayDataHolder.getInstance().getCmp()) {
      pdtPushObject.cmp_chnl = HolidayDataHolder.getInstance().getCmp();
    }
    if (requestDetails.cmp_source != null) {
      pdtPushObject.cmp_src = requestDetails.cmp_source;
    }
    if (requestDetails.cmp_name != null) {
      pdtPushObject.cmp_name = requestDetails.cmp_name;
    }
  }
};

const populateFilterDetails = (pdtPushObject, filterDetails) => {
  if (filterDetails) {
    if (filterDetails.budget != null) {
      pdtPushObject.srch_bgt_list = filterDetails.budget;
    }
    pdtPushObject.srch_thm_list = filterDetails.themes ? filterDetails.themes : [];
    if (filterDetails.suitableFor != null) {
      pdtPushObject.srch_suitblty_list = filterDetails.suitableFor;
    }
    if (filterDetails.holidayType != null) {
      pdtPushObject.srch_hol_typ_list = filterDetails.holidayType;
    }
    if (filterDetails.generalTags != null) {
      pdtPushObject.srch_gen_tag_list = filterDetails.generalTags;
    }
    if (filterDetails.hotelChoice != null) {
      pdtPushObject.srch_hot_ratng_list = filterDetails.hotelChoice;
    }
    if (filterDetails.duration != null) {
      pdtPushObject.srch_durn_list = filterDetails.duration;
    }
    if (filterDetails.dest_list != null) {
      pdtPushObject.srch_select_dest_list = filterDetails.dest_list;
    }
    if (filterDetails.pax_count != null) {
      pdtPushObject.srch_px_cnt = filterDetails.pax_count;
    }
  }
};


const populateFilterDetailsSearchWidget = (pdtPushObject, filterDetails) => {
  if (filterDetails) {
    if (filterDetails.has(pdtConstants.DESTINATION_CITY)) {
      pdtPushObject.srch_pkg_dest = filterDetails.get(pdtConstants.DESTINATION_CITY);
    }
    if (filterDetails.has('srch_fr_city')) {
      pdtPushObject.srch_fr_city = filterDetails.get(pdtConstants.DEPARTURE_CITY);
    }
    if (filterDetails.has('srch_bgt_op_shw_stat')) {
      pdtPushObject.srch_bgt_op_shw_stat = filterDetails.get(pdtConstants.BUDGET_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_thm_op_shw_stat')) {
      pdtPushObject.srch_thm_op_shw_stat = filterDetails.get(pdtConstants.THEME_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_suitblty_op_shw_stat')) {
      pdtPushObject.srch_suitblty_op_shw_stat = filterDetails.get(pdtConstants.SUITABLE_FOR_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_hol_typ_op_shw_stat')) {
      pdtPushObject.srch_hol_typ_op_shw_stat = filterDetails.get(pdtConstants.HOLIDAY_TYPE_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_gen_tag_op_shw_stat')) {
      pdtPushObject.srch_gen_tag_op_shw_stat = filterDetails.get(pdtConstants.GENERAL_TAG_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_inclusn_op_shw_stat')) {
      pdtPushObject.srch_inclusn_op_shw_stat = filterDetails.get(pdtConstants.INCLUSION_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_hot_ratng_op_shw_stat')) {
      pdtPushObject.srch_hot_ratng_op_shw_stat = filterDetails.get(pdtConstants.HOTEL_RATING_FILTER_SHOWN);
    }
    if (filterDetails.has('srch_night_dur_op_shw_stat')) {
      pdtPushObject.srch_night_dur_op_shw_stat = filterDetails.get(pdtConstants.DURATION_FILTER_SHOWN);
    }
  }
};

const populateSorterDetails = (pdtPushObject, sorterDetails) => {
  if (sorterDetails) {
    if (sorterDetails.sort_order != null) {
      pdtPushObject.srt_ordr = sorterDetails.sort_order;
    }
    if (sorterDetails.sort_by != null) {
      pdtPushObject.srt_by = sorterDetails.sort_by;
    }
  }
};

const populateOtherDetails = (pdtPushObject, otherDetails, departureCity) => {
  if (otherDetails) {
    if (otherDetails.number_of_packages_shown != null) {
      pdtPushObject.pkg_listing_cnt = otherDetails.number_of_packages_shown;
    }
    if (otherDetails.travel_start_date != null) {
      pdtPushObject.srch_trvl_start_dt = otherDetails.travel_start_date;
    }
    if (otherDetails.dest_list != null) {
      pdtPushObject.srch_select_dest_list = otherDetails.dest_list;
    }
    if (otherDetails.last_page_name != null) {
      pdtPushObject.meta_last_pg = otherDetails.last_page_name;
    }
    if (otherDetails.quote_id != null) {
      pdtPushObject.bkg_pkg_quot_id = otherDetails.quote_id;
    }
    if (departureCity != null) {
      pdtPushObject.srch_frm_city = departureCity;
    }
  }
};

const populateTravellerDetails = (pdtPushObject, travellerDetails) => {
  if (travellerDetails) {
    const trvlr_details = {};
    if (travellerDetails.primary_trv_first_name != null) {
      trvlr_details.trvlr_fname = travellerDetails.primary_trv_first_name;
    }
    if (travellerDetails.primary_trv_last_name != null) {
      trvlr_details.trvlr_lname = travellerDetails.primary_trv_last_name;
    }
    if (travellerDetails.primary_trv_gender != null) {
      trvlr_details.trvlr_gendr = travellerDetails.primary_trv_gender;
    }
    if (travellerDetails.primary_trv_e_com_id != null) {
      trvlr_details.trvlr_t_e_com_id = travellerDetails.primary_trv_e_com_id;
    }
    if (travellerDetails.primary_trv_m_com_id != null) {
      trvlr_details.trvlr_t_m_com_id = travellerDetails.primary_trv_m_com_id;
    }
    if (travellerDetails.primary_trv_gst_city != null) {
      trvlr_details.trvlr_gst_cty = travellerDetails.primary_trv_gst_city;
    }
    if (travellerDetails.primary_trv_gst_state != null) {
      trvlr_details.trvlr_gst_st = travellerDetails.primary_trv_gst_state;
    }
    if (travellerDetails.primary_trv_gst_address != null) {
      trvlr_details.trvlr_gst_add = travellerDetails.primary_trv_gst_address;
    }
    if (trvlr_details.trvlr_fname != null) {
      pdtPushObject.trvlr_details = trvlr_details;
    }
  }
};

const populateBookingDetails = (pdtPushObject, bookingDetails) => {
  if (bookingDetails) {
    if (bookingDetails.booking_id != null) {
      pdtPushObject.bkg_txn_id = bookingDetails.booking_id;
    }
    pdtPushObject.bkg_confrm_stat = bookingDetails.is_booking_confirmed;
    pdtPushObject.bkg_partial_paid_stat = bookingDetails.is_booking_partially_paid;
    pdtPushObject.bkg_creatd_ts = bookingDetails.booking_timestamp;
  }
};

const populateInterventionDetails = (pdtPushObject, interventionDetails) => {
  if (interventionDetails) {
    if (interventionDetails.cta_options_shown != null) {
      pdtPushObject.intrv_cta_ops = interventionDetails.cta_options_shown;
    }
    if (interventionDetails.is_dot_shown != null) {
      pdtPushObject.intrv_dot_shw_stat = interventionDetails.is_dot_shown;
    }
    if (interventionDetails.intervention_rule_number != null) {
      pdtPushObject.intrv_rul_no = interventionDetails.intervention_rule_number;
    }
    if (interventionDetails.intervention_rule_name != null) {
      pdtPushObject.intrv_rul_nam = interventionDetails.intervention_rule_name;
    }
  }
};

const populatePersuasionDetails = (pdtPushObject, persuasionDetails) => {
  if (persuasionDetails) {
    if (persuasionDetails.banners != null) {
      pdtPushObject.prs_bnr_list = persuasionDetails.banners.split('|');
    }
    if (persuasionDetails.timer_type != null) {
      pdtPushObject.prs_timr_typ = persuasionDetails.timer_type;
    }
    if (persuasionDetails.timer_time_left != null) {
      pdtPushObject.prs_timr_tim_lft = persuasionDetails.timer_time_left;
    }
    if (persuasionDetails.timer_end_time != null) {
      pdtPushObject.prs_end_tim = new Date(persuasionDetails.timer_end_time).getTime();
    }
    if (persuasionDetails.timer_shown != null) {
      pdtPushObject.prs_timr_shw_stat = persuasionDetails.timer_shown;
    }
  }
};

const populateExperimentDetails = (pdtPushObject, experimentDetails) => {
  if (experimentDetails) {
    if (experimentDetails.experiment_id != null) {
      pdtPushObject.exp_id = experimentDetails.experiment_id;
    }
  }
};

const populatePokusExperimentDetails = (pdtPushObject) => {
  const expList = getPokusConfigExpDetailsList(PokusLobs.HOLIDAY);
  const expVariant = getPokusExpVarientKey(PokusLobs.HOLIDAY);
  if (isNotNullAndEmptyCollection(expList)) {
    pdtPushObject.exp_experiment_details_list = expList;
  }
  if (expVariant) {
    pdtPushObject.pokus_variant = expVariant;
    pdtPushObject.exp_id = expVariant;
  }
};

const populateErrorDetails = (pdtPushObject, errorDetailsMap) => {
  if (errorDetailsMap) {
    if (errorDetailsMap.error_code != null) {
      pdtPushObject.err_cod = errorDetailsMap.error_code;
    }
    if (errorDetailsMap.error_message != null) {
      pdtPushObject.err_msg = errorDetailsMap.error_message;
    }
    if (errorDetailsMap.error_severity != null) {
      pdtPushObject.err_svr = errorDetailsMap.error_severity;
    }
    if (errorDetailsMap.error_source != null) {
      pdtPushObject.err_sourc = errorDetailsMap.error_source;
    }
  }
};


const populatePackageDetails = (pdtPushObject, packageDetailsMap) => {
  if (packageDetailsMap) {
    if (packageDetailsMap.pkg_nm != null) {
      pdtPushObject.pkg_nm = packageDetailsMap.pkg_nm;
    }
    if (packageDetailsMap.pkg_hld_id != null) {
      pdtPushObject.pkg_hld_id = packageDetailsMap.pkg_hld_id;
    }
    if (packageDetailsMap.pkg_tag_dest != null) {
      pdtPushObject.pkg_tag_dest = packageDetailsMap.pkg_tag_dest;
    }
    if (packageDetailsMap.pd_px_ad != null) {
      pdtPushObject.pd_px_ad = packageDetailsMap.pd_px_ad;
    }
    if (packageDetailsMap.pd_px_ch != null) {
      pdtPushObject.pd_px_ch = packageDetailsMap.pd_px_ch;
    }
    if (packageDetailsMap.pd_px_inf != null) {
      pdtPushObject.pd_px_inf = packageDetailsMap.pd_px_inf;
    }
    if (packageDetailsMap.pkg_cities != null) {
      pdtPushObject.pkg_cities = packageDetailsMap.pkg_cities;
    }
    if (packageDetailsMap.pkg_countries != null) {
      pdtPushObject.pkg_countries = packageDetailsMap.pkg_countries;
    }
    if (packageDetailsMap.pkg_hld_durn != null) {
      pdtPushObject.pkg_hld_durn = packageDetailsMap.pkg_hld_durn;
    }
    if (packageDetailsMap.pkg_hld_type != null) {
      pdtPushObject.pkg_hld_type = packageDetailsMap.pkg_hld_type;
    }
    if (packageDetailsMap.pkg_hol_typ != null) {
      pdtPushObject.pkg_hol_typ = packageDetailsMap.pkg_hol_typ;
    }
    if (packageDetailsMap.pkg_htl_chkin_day_list != null) {
      pdtPushObject.pkg_htl_chkin_day_list = packageDetailsMap.pkg_htl_chkin_day_list;
    }
    if (packageDetailsMap.pkg_htl_chkout_day_list != null) {
      pdtPushObject.pkg_htl_chkout_day_list = packageDetailsMap.pkg_htl_chkout_day_list;
    }
    if (packageDetailsMap.pkg_flt_days != null) {
      pdtPushObject.pkg_flt_days = packageDetailsMap.pkg_flt_days;
    }
    if (packageDetailsMap.pkg_visa_list != null) {
      pdtPushObject.pkg_visa_list = packageDetailsMap.pkg_visa_list;
    }
    if (packageDetailsMap.pkg_img_cnt != null) {
      pdtPushObject.pkg_img_cnt = packageDetailsMap.pkg_img_cnt;
    }
    if (packageDetailsMap.pkg_flt_chng_stat != null) {
      pdtPushObject.pkg_flt_chng_stat = packageDetailsMap.pkg_flt_chng_stat;
    }
    if (packageDetailsMap.pkg_htl_chng_stat != null) {
      pdtPushObject.pkg_htl_chng_stat = packageDetailsMap.pkg_htl_chng_stat;
    }
    if (packageDetailsMap.pkg_catgry != null) {
      pdtPushObject.pkg_catgry = packageDetailsMap.pkg_catgry;
    }
    if (packageDetailsMap.pkg_custm_stat != null) {
      pdtPushObject.pkg_custm_stat = packageDetailsMap.pkg_custm_stat;
    }
    if (packageDetailsMap.pkg_itinry != null) {
      pdtPushObject.pkg_itinry = packageDetailsMap.pkg_itinry;
    }
  }
};

const populatePricingDetails = (pdtPushObject, pricingDetailsMap) => {
  if (pricingDetailsMap) {
    if (pricingDetailsMap.prc_pre_disc != null) {
      pdtPushObject.prc_pre_disc = pricingDetailsMap.prc_pre_disc;
    }
    if (pricingDetailsMap.prc_post_disc != null) {
      pdtPushObject.prc_post_disc = pricingDetailsMap.prc_post_disc;
    }
    if (pricingDetailsMap.prc_gst_amt != null) {
      pdtPushObject.prc_gst_amt = pricingDetailsMap.prc_gst_amt;
    }
    if (pricingDetailsMap.prc_cdf_disc != null) {
      pdtPushObject.prc_cdf_disc = pricingDetailsMap.prc_cdf_disc;
    }
    if (pricingDetailsMap.prc_wal_disc != null) {
      pdtPushObject.prc_wal_disc = pricingDetailsMap.prc_wal_disc;
    }
    if (pricingDetailsMap.prc_tot_payable_ad != null) {
      pdtPushObject.prc_tot_payable_ad = pricingDetailsMap.prc_tot_payable_ad;
    }
    if (pricingDetailsMap.prc_tot_bkg_amt != null) {
      pdtPushObject.prc_tot_bkg_amt = pricingDetailsMap.prc_tot_bkg_amt;
    }
    if (pricingDetailsMap.prc_part_pay_rule != null) {
      pdtPushObject.prc_part_pay_rule = pricingDetailsMap.prc_part_pay_rule;
    }
    if (pricingDetailsMap.prc_emi_amt != null) {
      pdtPushObject.prc_emi_amt = pricingDetailsMap.prc_emi_amt;
    }
  }
};

const populateDiscountDetails = (pdtPushObject, discountDetailsMap) => {
  if (discountDetailsMap) {
    if (discountDetailsMap.cpn_code != null) {
      pdtPushObject.cpn_code = discountDetailsMap.cpn_code;
    }
    if (discountDetailsMap.cpn_cd_type != null) {
      pdtPushObject.cpn_cd_type = discountDetailsMap.cpn_cd_type;
    }
    if (discountDetailsMap.cpn_status != null) {
      pdtPushObject.cpn_status = discountDetailsMap.cpn_status;
    }
    if (discountDetailsMap.cpn_inst_disc_stat != null) {
      pdtPushObject.cpn_inst_disc_stat = discountDetailsMap.cpn_inst_disc_stat;
    }
    if (discountDetailsMap.cpn_cashbk_disc_stat != null) {
      pdtPushObject.cpn_cashbk_disc_stat = discountDetailsMap.cpn_cashbk_disc_stat;
    }
  }
};

const populateLocusDetails = (pdtPushObject, locusDetails) => {
  if (locusDetails) {
    if (locusDetails.packages_cities != null) {
      pdtPushObject.packages_cities = locusDetails.packages_cities;
    }
    if (locusDetails.package_tag_destination != null) {
      pdtPushObject.package_tag_destination = locusDetails.package_tag_destination;
    }
    if (locusDetails.search_package_dest != null) {
      pdtPushObject.search_package_dest = locusDetails.search_package_dest;
    }
    if (locusDetails.search_from_city != null) {
      pdtPushObject.search_from_city = locusDetails.search_from_city;
    }
    if (locusDetails.search_select_destination_list != null) {
      pdtPushObject.search_select_destination_list = locusDetails.search_select_destination_list;
    }
  }
};

export const callPDTApiListing = async (pageDataMap = {}, interventionDetails, eventType, activity, requestId, branch) => {
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  const departureCity = await getDepartureCity();

  const {
    persuasionDetails = {},
    requestDetails = {},
    filterDetails = {},
    sorterDetails = {},
    otherDetails = {},
    errorDetails = {},
    locusDetails = {},
  } = pageDataMap || {};

  populateInterventionDetails(pdtPushObject, interventionDetails);
  populatePersuasionDetails(pdtPushObject, persuasionDetails);
  populateRequestDetails(pdtPushObject, requestDetails, eventType, activity, requestId, branch ? branch.toUpperCase() : '');
  populateFilterDetails(pdtPushObject, filterDetails);
  populateSorterDetails(pdtPushObject, sorterDetails);
  populateOtherDetails(pdtPushObject, otherDetails, departureCity);
  populateErrorDetails(pdtPushObject, errorDetails);
  populateLocusDetails(pdtPushObject, locusDetails);
  send_gzip_post(pdtPushObject);
};



export const callPDTApiLanding = async (pageDataMap = {}, interventionDetails, eventType, activity, requestId, branch) => {
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  const departureCity = await getDepartureCity();
  populateInterventionDetails(pdtPushObject, interventionDetails);
  populatePersuasionDetails(pdtPushObject, pageDataMap?.persuasionDetails);
  populateRequestDetails(pdtPushObject, pageDataMap.requestDetails, eventType, activity, requestId, branch ? branch.toUpperCase() : HOLIDAYS_BRANCH_NONE);
  populateFilterDetails(pdtPushObject, pageDataMap.filterDetails);
  populateSorterDetails(pdtPushObject, pageDataMap.sorterDetails);
  populateOtherDetails(pdtPushObject, pageDataMap.otherDetails, departureCity);
  populateErrorDetails(pdtPushObject, pageDataMap.errorDetails);
  send_gzip_post(pdtPushObject);
};

export const callPDTApiSearchWidget = async (pageDataMap, eventType, activity, requestId, branch) => {
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  populateRequestDetails(pdtPushObject, pageDataMap.requestDetails, eventType, activity, requestId, branch ? branch.toUpperCase() : HOLIDAYS_BRANCH_NONE);
  populateFilterDetailsSearchWidget(pdtPushObject, pageDataMap.filterDetails);
  populateOtherDetails(pdtPushObject, pageDataMap.otherDetails, null);
  populateLocusDetails(pdtPushObject, pageDataMap.locusDetails);
  send_gzip_post(pdtPushObject);
};

export const callPDTApiQuery = async (pageDataMap, eventType, activity, requestId, branch) => {
  if (isEmptyString(activity)) {
    return;
  }
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  populateRequestDetails(pdtPushObject, pageDataMap.requestDetails, eventType, activity, requestId, branch ? branch.toUpperCase() : '');
  populateOtherDetails(pdtPushObject, pageDataMap.otherDetails, null);
  send_gzip_post(pdtPushObject);
};

export const callPDTApiDetail = async (pageDataMap = {}, interventionDetails, eventType, activity, requestId, branch) => {
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  const departureCity = await getDepartureCity();
  populateInterventionDetails(pdtPushObject, interventionDetails);
  populatePersuasionDetails(pdtPushObject, pageDataMap.persuasionDetails ? pageDataMap.persuasionDetails : {});
  populateRequestDetails(pdtPushObject, pageDataMap.requestDetails ? pageDataMap.requestDetails : {}, eventType, activity, requestId, branch ? branch.toUpperCase() : branch);
  populatePackageDetails(pdtPushObject, pageDataMap.packageDetails ? pageDataMap.packageDetails : {});
  populatePricingDetails(pdtPushObject, pageDataMap.pricingDetails ? pageDataMap.pricingDetails : {});
  populateDiscountDetails(pdtPushObject, pageDataMap.discountDetails ? pageDataMap.discountDetails : {});
  populateFilterDetails(pdtPushObject, pageDataMap.searchCriteriaAppliedMap ? pageDataMap.searchCriteriaAppliedMap : {});
  populateOtherDetails(pdtPushObject, pageDataMap.otherDetails ? pageDataMap.otherDetails : {}, departureCity);
  populateErrorDetails(pdtPushObject, pageDataMap.errorDetails);
  populateLocusDetails(pdtPushObject, pageDataMap.locusDetails);
  send_gzip_post(pdtPushObject);
};

export const callPDTApiReview = async (pageDataMap, interventionDetails, eventType, activity, requestId, branch) => {
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  const departureCity = await getDepartureCity();
  populateInterventionDetails(pdtPushObject, interventionDetails);
  const {
    persuasionDetails,
    requestDetails,
    packageDetails,
    pricingDetails,
    discountDetails,
    searchCriteriaAppliedMap,
    otherDetails,
    travellerDetails,
    errorDetails,
  } = pageDataMap || {};
  populatePersuasionDetails(pdtPushObject, persuasionDetails ? persuasionDetails : {});
  populateRequestDetails(pdtPushObject, requestDetails ? requestDetails : {}, eventType, activity, requestId, branch ? branch.toUpperCase() : branch);
  populatePackageDetails(pdtPushObject, packageDetails ? packageDetails : {});
  populatePricingDetails(pdtPushObject, pricingDetails ? pricingDetails : {});
  populateDiscountDetails(pdtPushObject, discountDetails ? discountDetails : {});
  populateFilterDetails(pdtPushObject, searchCriteriaAppliedMap ? searchCriteriaAppliedMap : {});
  populateOtherDetails(pdtPushObject, otherDetails ? otherDetails : {}, departureCity);
  populateTravellerDetails(pdtPushObject, travellerDetails ? travellerDetails : {});
  populateErrorDetails(pdtPushObject, errorDetails);
  send_gzip_post(pdtPushObject);
};

export const callPDTApiThankyou = async (pageDataMap = {}, interventionDetails, eventType, activity, requestId, branch) => {
  const pdtPushObject = {};
  await populateCommonDetails(pdtPushObject);
  const departureCity = await getDepartureCity();
  populateInterventionDetails(pdtPushObject, interventionDetails);
  populatePersuasionDetails(pdtPushObject, pageDataMap.persuasionDetails ? pageDataMap.persuasionDetails : {});
  populateRequestDetails(pdtPushObject, pageDataMap.requestDetails ? pageDataMap.requestDetails : {}, eventType, activity, requestId, branch ? branch.toUpperCase() : branch);
  populatePackageDetails(pdtPushObject, pageDataMap.packageDetails ? pageDataMap.packageDetails : {});
  populatePricingDetails(pdtPushObject, pageDataMap.pricingDetails ? pageDataMap.pricingDetails : {});
  populateDiscountDetails(pdtPushObject, pageDataMap.discountDetails ? pageDataMap.discountDetails : {});
  populateFilterDetails(pdtPushObject, pageDataMap.searchCriteriaAppliedMap ? pageDataMap.searchCriteriaAppliedMap : {});
  populateOtherDetails(pdtPushObject, pageDataMap.otherDetails ? pageDataMap.otherDetails : {}, departureCity);
  populateTravellerDetails(pdtPushObject, pageDataMap.travellerDetails ? pageDataMap.travellerDetails : {});
  populateBookingDetails(pdtPushObject, pageDataMap.bookingDetails ? pageDataMap.bookingDetails : {});
  send_gzip_post(pdtPushObject);
};


export const fetchKafkaGenericDataMap = async () => {
  const {HolidayModule} = NativeModules;
  const kafkaDataMap = await HolidayModule.createKafkaGenericDataMap();
  return kafkaDataMap;
};

const populateCommonDetails = async (pdtPushObject) => {
  const kafkaGenericDataMap = await fetchKafkaGenericDataMap();
  const { HolidayModule } = NativeModules;
  const headers = await HolidayModule.getRequestHeader();
  populatePDTTableDetails(pdtPushObject);
  await populateFunnelDetails(pdtPushObject);
  if (kafkaGenericDataMap) {
    populateAppAndDeviceDetails(pdtPushObject, kafkaGenericDataMap.appAndDeviceDetails);
    populateUserDetails(pdtPushObject, kafkaGenericDataMap.userDetails);
    populateExperimentDetails(pdtPushObject, kafkaGenericDataMap.experimentDetails);
  }
  pdtPushObject.sess_mmt_id_new = headers['session_mmt_id']
  populatePokusExperimentDetails(pdtPushObject);
};


/* ***********  NEW PDT TRACKING FUNCTIONS *********** */

export const INTERVENTION_DETAILS = 'interventionDetails';
export const PERSUASION_DETAILS = 'persuasionsDetails';
export const REQUEST_DETAILS = 'requestDetails';
export const FILTER_DETAILS = 'filterDetails';
export const SORTER_DETAILS = 'sorterDetails';
export const OTHER_DETAILS = 'otherDetails';
export const ERROR_DETAILS = 'errorDetails';
export const LOCUS_DETAILS = 'locusDetails';
export const PACKAGE_DETAILS = 'packageDetails';
export const PRICING_DETAILS = 'pricingDetails';
export const DISCOUNT_DETAILS = 'discountDetails';

const populateDetailMap = {
  [INTERVENTION_DETAILS]: populateInterventionDetails,
  [PERSUASION_DETAILS]: populatePersuasionDetails,
  [REQUEST_DETAILS]: populateRequestDetails,
  [FILTER_DETAILS]: populateFilterDetails,
  [SORTER_DETAILS]: populateSorterDetails,
  [OTHER_DETAILS]: populateOtherDetails,
  [ERROR_DETAILS]: populateErrorDetails,
  [LOCUS_DETAILS]: populateLocusDetails,
  [PACKAGE_DETAILS] : populatePackageDetails,
  [PRICING_DETAILS]: populatePricingDetails,
  [DISCOUNT_DETAILS]: populateDiscountDetails,
};

export const populatePDTObject = async ({ populateFunctionsMap, pdtPushObject }) => {
  await populateCommonDetails(pdtPushObject);

  populateFunctionsMap.map((populateFunction) => {
    if (populateDetailMap[populateFunction.name]) {
      populateDetailMap[populateFunction.name](pdtPushObject, ...populateFunction.values);
    }
  });
  send_gzip_post(pdtPushObject);
};
export const callPDTApiLandingNew = async ({ pdtData }) => {
  const {
    pageDataMap = {},
    interventionDetails = {},
    eventType = '',
    activity = '',
    requestId = '',
    branch = '',
    extraData = {},
  } = pdtData || {};
  const {
    persuasionDetails = {},
    requestDetails = {},
    filterDetails = {},
    sorterDetails = {},
    otherDetails = {},
    errorDetails = {},
  } = pageDataMap || {};
  const pdtPushObject = {};
  const departureCity = await getDepartureCity();

  await populateCommonDetails(pdtPushObject);

  const landingPopulateFunctionsMap = [
    { name: INTERVENTION_DETAILS, values: [interventionDetails] },
    { name: PERSUASION_DETAILS, values: [persuasionDetails] },
    {
      name: REQUEST_DETAILS,
      values: [
        requestDetails,
        eventType,
        activity,
        requestId,
        branch ? branch.toUpperCase() : HOLIDAYS_BRANCH_NONE,
      ],
    },
    { name: FILTER_DETAILS, values: [filterDetails] },
    { name: SORTER_DETAILS, values: [sorterDetails] },
    { name: OTHER_DETAILS, values: [otherDetails, departureCity] },
    { name: ERROR_DETAILS, values: [errorDetails] },
  ];

  landingPopulateFunctionsMap.map((populateFunction) => {
    if (populateDetailMap[populateFunction.name]) {
      populateDetailMap[populateFunction.name](pdtPushObject, ...populateFunction.values);
    }
  });

  // Add all extra data to pdtPushObject.
  Object.keys(extraData).forEach(key => {
    if (!isEmpty(extraData[key])) {
      pdtPushObject[key] = extraData[key];
    }
  });

  send_gzip_post(pdtPushObject);
};


export const callPDTApiListingNew = async ({ pdtData }) => {
  const {
    pageDataMap = {},
    interventionDetails = {},
    eventType = '',
    activity = '',
    requestId = '',
    branch = '',
    extraData = {},
  } = pdtData || {};

  const {
    persuasionDetails = {},
    requestDetails = {},
    filterDetails = {},
    sorterDetails = {},
    otherDetails = {},
    errorDetails = {},
    locusDetails = {},
  } = pageDataMap || {};

  const pdtPushObject = {};
  const departureCity = await getDepartureCity();

  // store all the values needed to be populated
  const listingPopulateFunctionsMap = [
    { name: INTERVENTION_DETAILS, values: [interventionDetails] },
    { name: PERSUASION_DETAILS, values: [persuasionDetails] },
    {
      name: REQUEST_DETAILS,
      values: [
        requestDetails,
        eventType,
        activity,
        requestId,
        branch ? branch.toUpperCase() : '',
      ],
    },
    { name: FILTER_DETAILS, values: [filterDetails] },
    { name: SORTER_DETAILS, values: [sorterDetails] },
    { name: OTHER_DETAILS, values: [otherDetails, departureCity] },
    { name: ERROR_DETAILS, values: [errorDetails] },
    {name: LOCUS_DETAILS, values: [locusDetails]},
  ];

  // Add all extra data to pdtPushObject.
  Object.keys(extraData).forEach(key => {
    if (!isEmpty(extraData[key])) {
      pdtPushObject[key] = extraData[key];
    }
  });

  populatePDTObject({populateFunctionsMap: listingPopulateFunctionsMap, pdtPushObject});
};

export const callPDTApiQueryNew = async ({ pdtData }) => {
  const { pageDataMap, activity, branch, requestId, eventType } = pdtData || {};
  const { requestDetails, otherDetails } = pageDataMap || {};

  if (isEmptyString(activity)) {
    return;
  }
  const pdtPushObject = {};
  const queryPopulateFunctionMap = [
    {
      name: REQUEST_DETAILS,
      values : [
        requestDetails,
        eventType,
        activity,
        requestId,
        branch ? branch.toUpperCase() : '',
      ],
    },
    {
      name: OTHER_DETAILS, values: [otherDetails, null],
    },
  ];
  await populatePDTObject({populateFunctionsMap: queryPopulateFunctionMap, pdtPushObject});
};

export const callPDTApiDetailNew = async({ pdtData }) => {
    const pdtPushObject = {};
    const {
      pageDataMap = null,
      interventionDetails = {},
      eventType = '',
      activity = '',
      requestId = '',
      branch = '',
      extraData = {},
    } = pdtData || {};

  let pageDataMapModified = pageDataMap || {};

    const {
      persuasionDetails = {},
      requestDetails = {},
      searchCriteriaAppliedMap = {},
      otherDetails = {},
      errorDetails = {},
      packageDetails = {},
      pricingDetails = {},
      discountDetails = {},
      locusDetails = {},
    } = pageDataMapModified || {};

    const departureCity = await getDepartureCity();
    const detailPopulateFunctionsMap = [
      { name: INTERVENTION_DETAILS, values: [interventionDetails] },
      { name: PERSUASION_DETAILS, values: [persuasionDetails] },
      {
        name: REQUEST_DETAILS,
        values: [
          requestDetails,
          eventType,
          activity,
          requestId,
          branch ? branch.toUpperCase() : branch,
        ],
      },
      { name: PACKAGE_DETAILS, values: [packageDetails] },
      { name: PRICING_DETAILS, values: [pricingDetails] },
      { name: DISCOUNT_DETAILS, values: [discountDetails] },
      { name: FILTER_DETAILS, values: [searchCriteriaAppliedMap] },
      { name: OTHER_DETAILS, values: [otherDetails, departureCity] },
      { name: ERROR_DETAILS, values: [errorDetails] },
      { name: LOCUS_DETAILS, values:[locusDetails] },
    ];

  // Add all extra data to pdtPushObject.
  Object.keys(extraData).forEach(key => {
    if (!isEmpty(extraData[key])) {
      pdtPushObject[key] = extraData[key];
    }
  });

    await populatePDTObject({populateFunctionsMap: detailPopulateFunctionsMap, pdtPushObject});
};
