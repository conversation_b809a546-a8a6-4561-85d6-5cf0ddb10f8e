import { AFFILIATES } from '../HolidayConstants';
import { getAffiliate } from '../theme';
import { getDepartureCity,getExperimentValue,isRawClient } from './HolidayUtils';
import HolidayDeeplinkParser from './HolidayDeeplinkParser';
import {getdeviceType} from './HolidayDevicesTypes';
import {getFormattedDateFromDate} from '@mmt/legacy-commons/Helpers/dateHelpers';
import { addDays } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { AbConfigKeyMappings } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';

export const GI_PAGE_NAMES = {
    LANDING: 'holidayHomePage',
    LISTING: 'holidayListing',
    GROUPING: 'holidayGroupedListing',
    DETAIL: 'holidayDetail',
    REVIEW: 'holidayReview',
    THANKYOU: 'holidaytyPage',
    FPHREVIEW: 'holidayCustomReview',
    SEARCH_WIDGET: 'holidaySearch',
};
const DEVICE_TYPE = {
    WEB: 'web',
    ANDROID: 'android',
    IOS: 'ios',
};
export const cleanString = (string) => {
    return string?.replace(/[&\/\\#, +()$~%.'":*?<>{}|!]/g, '_');
};
export function formatClickEventName({ string, pageName }) {
    const eventName = cleanString(string);
    return `action_${eventName}_${pageName}`;
}
export const formatPageLoadEventName = ({ pageName }) => {
    return `pageLoad_${pageName}`;
};
export const getFormatterData = ({ pageName, ...rest }) => {
    return {
        pageName,
        deviceType: getdeviceType(),
        ...rest,
    };
};
export const sendGIEvents = ({ eventName = '', data = {}, iswebengage = true }) => {
    if (!isRawClient()) {
        return null;
    }
    if (getAffiliate() === AFFILIATES.GI) {
        const deviceType = getdeviceType();
        if (deviceType === DEVICE_TYPE.ANDROID && window?.JSMobileCrm?.sendEvent) {
            window.JSMobileCrm.sendEvent(
                JSON.stringify({
                    event: eventName,
                    iswebengage,
                    ...getFormatterData(data),
                }),
            );
        } else if (deviceType === DEVICE_TYPE.IOS && window?.webkit?.messageHandlers?.sendEvent) {
            window.webkit.messageHandlers
                .sendEvent.postMessage({
                event: eventName,
                iswebengage,
                ...getFormatterData(data),
            });
        } else {
            window.dataLayer.push({
                event: eventName,
                iswebengage,
                ...getFormatterData(data),
            });
        }
    }
};

export const sendConversionEvent = ({
    totalAmount = '',
    paymentReferenceId = '',
    branch = '',
    currencyCode = 'INR',
  }) => {
    if (!isRawClient()) {
      return null;
    }
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'conversion',
      send_to: 'AW-1044434772/YBe8CMmgiZcCENSeg_ID',
      value: totalAmount,
      currency: currencyCode,
      transaction_id: paymentReferenceId,
      branch: branch,
    });
  };

export const sendMMTGtmEvent = ({ eventName = '', data = {}, iswebengage = true }) => {
  if (!isRawClient()) {
    return null;
  }
  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    event: getEventName(eventName),
    iswebengage,
    ...getFormatterData(data),
    deviceType: isRawClient() ? 'web' : getdeviceType(),
  });
};

export const getEventName = (eventName = '') => {
    switch (eventName) {
        case 'contact_icon_query':
        case 'fab_query':
            return 'SEND_QUERY_CTA';
        case 'contact_icon_chat':
            return 'MYRA_CHAT_CTA';
        default:
            return eventName;
    }
};
export const sendRecentPackagesData = async ({
        usrDestCity = '',
        usrDepCity = '',
        travelDate = '',
        subTitle = '',
          }) => {
    if (!usrDestCity) {
        return null;
    }

    const usrDepartureCity = usrDepCity ? usrDepCity : await getDepartureCity();
    const expTime = getExperimentValue(AbConfigKeyMappings.giRecentSearchesExpDays, 15);
    const packageUrl = HolidayDeeplinkParser.createGroupingDeepLink({
        queryParams: { dest: usrDestCity, depCity: usrDepartureCity },
        query: window.location.href,
    });
    const jsonData = {
        n: usrDestCity,
        st: subTitle,
        dt: getFormattedDateFromDate(
            travelDate ? travelDate : addDays(new Date(), 7),
            'DD/MM/YYYY hh:mm:ss',
        ).toString(),
        gd: {
            url: encodeURI(packageUrl),
            title:"Holidays"
        },
        tg: '717',
        src: usrDepartureCity,
        dest: usrDestCity,
        v: 'holiday',
        id: usrDestCity,
        exp: getFormattedDateFromDate(addDays(new Date(), expTime),
        'DD/MM/YYYY hh:mm:ss',
        ).toString(),
    };
    if (getAffiliate() === AFFILIATES.GI) {
        const deviceType = getdeviceType();
        if (deviceType === DEVICE_TYPE.ANDROID && window?.JSMobileCrm?.addRecentSearch) {
            window.JSMobileCrm.addRecentSearch(JSON.stringify(jsonData));
        }
        if (deviceType === DEVICE_TYPE.IOS && window?.webkit?.messageHandlers?.addRecentSearch) {
            window.webkit.messageHandlers.addRecentSearch.postMessage(jsonData);
        }
    }
};
export const deleteRecentSearchPackage = ({ uniqueCityId = ''}) => {
    if (!uniqueCityId) {
        return null;
    }
    if (getAffiliate() === AFFILIATES.GI) {
        const deviceType = getdeviceType();
        if (deviceType === DEVICE_TYPE.ANDROID && window?.JSMobileCrm?.deleteRecentSearch) {
            window.JSMobileCrm.deleteRecentSearch(uniqueCityId);
        }
        if (deviceType === DEVICE_TYPE.IOS && window?.webkit?.messageHandlers?.deleteRecentSearch) {
            window.webkit.messageHandlers.deleteRecentSearch.postMessage(uniqueCityId);
        }
    }
};
