import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {
  getPokusConfigExpDetailsListV2,
  getPokusExpVarientKey,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { NativeModules } from 'react-native';
import { getPdtId, pdtIdNames, updatePDTJourneyId, updatePDTRequestId } from './PdtDataHolder';
import {
  FUNNEL_SOURCE,
  HOLIDAYS,
  HOL_PDT_SCHEMA_CONSTANTS,
  LOB_CATEGORIES,
  SEARCH_TYPE,
  TRAVEL_CLASS,
} from './HolidayPDTConstants';
import HolidayDataHolder from '../utils/HolidayDataHolder';
import { AFFILIATES, CAMPAIGN, DEFAULT_COUNTRY_CODE, DOM_BRANCH, PAGE_API_VERSIONSV2 } from '../HolidayConstants';
import {
  DEFAULT_ADULT_COUNT,
  DEFAULT_CHILD_COUNT,
  DEFAULT_INFANTS_COUNT,
  DEFAULT_ROOM_COUNT,
  DEFAULT_TRAVELLER_COUNT,
} from './RoomPaxUtils';
import { isEmpty } from 'lodash';
import { findDaysBetweenDates } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { generateUUID } from '@mmt/navigation/src/util';
import { getSubFunnelName } from './HolidayUtils';
import HolidayPDTMetaIDHolder from './HolidayPDTMetaIDHolder';
import { getTrackPDTCTAHandler } from './HolidaysPokusUtils';

const LOCUS_TYPE = 'city';

export const initializePageContext = () => {
  return {
    lob: HOLIDAYS,
    lob_category: LOB_CATEGORIES.DOM_HOLIDAYS,
    page_name: '',
    prev_page_name: '',
    sub_page_name: '',
    funnel_step: '',
    navigation: '',
    travel_store: {
      store_id: '',
    },
    page_type:''
  };
};

export const initializeExperimentalDetails = () => {
  const experimentalDetails = {
    valid_exp_list: [],
    variant_keys: '',
    honoured_exp_list: [],
  };
  return experimentalDetails;
};

export const initializeEventTrackingContext = () => {
  const eventTrackingContext = {
    env: __DEV__ ? 'dev' : 'prod',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    request_id: getPdtId({ pdtId: pdtIdNames.REQUESTID }),
    journey_id: getPdtId({ pdtId: pdtIdNames.JOURNEYID }),
    template_id: HOL_PDT_SCHEMA_CONSTANTS.TEMPLATE_ID,
    topic_name: HOL_PDT_SCHEMA_CONSTANTS.TOPIC_NAME,
    bu: HOLIDAYS,
    organisation: AFFILIATES.MMT,
    funnel_source: FUNNEL_SOURCE,
    traffic_source: HolidayDataHolder.getInstance().getCmp(),
    meta_req_id_ls: HolidayPDTMetaIDHolder.getInstance().getPdtId(),
    funnel_entry: HolidayDataHolder.getInstance().getFunnelEntry(),
  };
  return eventTrackingContext;
};
export const initializeCampaignDetails = () => {
  const campaignDetails = {
    campaign_name: CAMPAIGN,
    campaign_key: HolidayDataHolder.getInstance().getCmp(),
  };
  return campaignDetails;
};

export const initializeSearchContext = () => {
  const locus = {
    locus_id: 'null',
    locus_type: 'null',
    country: 'null',
    lat: null,
    long: null,
  };
  const location = {
    code: 'null',
    name: 'null',
    type: 'null',
    country_code: 'null',
    country: 'null',
  };
  const details = {
    adult: {
      count: DEFAULT_ADULT_COUNT,
    },
  };
  const pax = {
    count: DEFAULT_TRAVELLER_COUNT,
    rooms: DEFAULT_ROOM_COUNT,
    details: details,
  };
  const locationDetails = {
    locus: locus,
    locus_v2: locus,
    location: location,
  };
  const searchContext = {
    travel_purpose_opted: null,
    checkin_time: null,
    to: locationDetails,
    from: locationDetails,
    to_date_time: null,
    pax: pax,
    from_date_time: null,
    product_id: '',
    search_text: '',
    search_type: SEARCH_TYPE,
    trip_type: '',
    journey_type: '',
    special_fares: '',
    travel_class: TRAVEL_CLASS,
    tags: [],
    advance_purchase: '',
  };
  return searchContext;
};

export const initializeEventDetails = () => {
  const bookingInfo = {
    checkout_id: '',
    booking_id: '',
    booking_lob: '',
    booking_transaction_id: '',
    from_date_time: null,
    to_date_time: null,
    return_date_time: null,
    travel_class: '',
    trip_type: '',
    fare_type: '',
    total_stops: null,
    multi_city_count: null,
    booking_request_type: '',
    price: null,
    currency: '',
    number_of_rooms: null,
    tax: null,
    applied_coupon: '',
    origin: '',
    destination: '',
    booking_parent_id: '',
    booking_date: '',
    is_self_booking: true,
  };
  const product = {
    id: '',
    name: '',
    rank: '',
    position: {
      v: '',
      h: '',
    },
    category: '',
  };
  const content_detail = {
    id: '',
    type: '',
    name: '',
    category_id: '',
    category: '',
    position: {
      v: '',
      h: '',
    },
  };
  const components = {
    id: '',
    time_spent: null,
    product_list: [],
    content_details: [],
  };
  const eventDetails = {
    event_name: '',
    event_type: '',
    event_value: '',
    bookingInfo: bookingInfo,
    components: components,
  };
  return eventDetails;
};

export const populateExperimentalDetails = () => {
  let experimentalDetails = initializeExperimentalDetails();
  const expList = getPokusConfigExpDetailsListV2(PokusLobs.HOLIDAY);
  const expVariant = getPokusExpVarientKey(PokusLobs.HOLIDAY);
  experimentalDetails = {
    valid_exp_list: expList,
    variant_keys: expVariant,
    honoured_exp_list: [],
  };
  return experimentalDetails;
};

export const getEventDetailComponents = ({ packageDetail }) => {
  const { metadataDetail = {} } = packageDetail || {};
  const { premium = false} = metadataDetail || {};
  const category = premium ? 'Premium' : '';
  return {
    components: getComponentsForPDT({ category }),
  }
}


export const getComponentsForPDT = ({ category = ''}) => {
  const components =isEmpty(category) ? {} : {
    id: '',
    product_list: [
      {
        id: 'null',
        category: [category],
       }
    ],
  };
  return components;
}
export const logHolidaysEventToPDT = ({value, actionType, pdtObj, subPageName = '', bookingInfo = {} , compData = {} ,addonsDetails={} ,queryDetail={} , shouldTrackToAdobe=true}) => {
  try {

     // Extract any existing event details to preserve them when updating the event tracking object
     const { event_detail: existingEventDetail = {} } = pdtObj || {};

     // Ensure we preserve the components.product_list with price data
     const existingComponents = existingEventDetail.components || {};
     const existingProductList = existingComponents.product_list || [];

     // Build a component object only if there's data
     const componentsData = {
       ...(isEmpty(existingComponents) ? {} : existingComponents),
       ...(isEmpty(actionType.components) ? {} : actionType.components),
     };

     // Add product_list only if it has items
     if (!isEmpty(existingProductList)) {
       componentsData.product_list = existingProductList;
     }
     const pageName=HolidayDataHolder.getInstance().getCurrentPageNameV2()
     const psmQueryDetail=HolidayDataHolder.getInstance().getQueryDetailForPSM() || {}
      // Create the event detail, making sure to preserve the product_list
    const eventDetail = {
      ...existingEventDetail,
      ...actionType,
      // Only include components in eventDetail if it has any data
      ...(isEmpty(componentsData) ? {} : { components: componentsData }),
      event_value: value,
      event_timestamp: Date.now(),
      event_id: generateUUID(),
      ...(isEmpty(bookingInfo) ? {} : { booking_info: bookingInfo }),
      ...compData,
      ...queryDetail,
      ...psmQueryDetail,
      page_version:PAGE_API_VERSIONSV2[pageName]
    };
    updatePDTRequestId();
    const loggerObj = {
      ...pdtObj,
      page_context: populatePageContextExtraData({subPageName, pdtObj}),
      event_detail: eventDetail,
      event_tracking_context: initializeEventTrackingContext(),
      ...addonsDetails,
    }
    logEventToPDT(loggerObj,shouldTrackToAdobe);
  } catch (e) {
    console.log('Error in logHolidaysEventToPDT:', e);
  }
};

export const populatePageContextExtraData = ({subPageName = '' , pdtObj = {}}) => {
  const {page_context = {}} = pdtObj;
  const pageContext = {
   subpage_name:subPageName ||  HolidayDataHolder.getInstance().getSubPageName() || "",
   ...page_context,
  }
  return pageContext;
}

const logEventToPDT = async (loggerObj,shouldTrackToAdobe) => {
  const { PdtV2Module } = NativeModules;
  if (!PdtV2Module?.trackEvent || !PdtV2Module?.trackOmnitureNPdtEvent) {
    return;
  }
  try {
    const holidayslogobj = removeEmpty(loggerObj);

    // Ensure lob field is always present in page_context to prevent "No value for lob" error
    if (holidayslogobj.page_context && !holidayslogobj.page_context.lob) {
      holidayslogobj.page_context.lob = HOLIDAYS;
    }

    const isTrackPDTAndOmni = getTrackPDTCTAHandler();
    const payload = JSON.stringify(holidayslogobj);
    const trackEventMethod = isTrackPDTAndOmni
      ?  shouldTrackToAdobe ? PdtV2Module.trackOmnitureNPdtEvent : PdtV2Module.trackEvent
      : PdtV2Module.trackEvent;

    trackEventMethod({ payload });
  } catch (e) {
    console.log('Error in logEventToPDT:', e);
  }
};
const removeEmpty = (obj) => {
  if (Array.isArray(obj)) {
    return obj.map((item) => removeEmpty(item)).filter((item) => item !== undefined);
  } else if (obj !== null && typeof obj === 'object') {
    let cleanedObj = {};
    Object.keys(obj).forEach((key) => {
      let value = removeEmpty(obj[key]);
      if (value !== null && value !== ""  && value !== undefined) {
        cleanedObj[key] = value;
      }
    });
    return cleanedObj;
  }
  return obj;
};

export const populatePageContext = ({ funnelStep, pageName, lobCategory }) => {
  const pageNameV2=HolidayDataHolder.getInstance().getCurrentPageNameV2();
  const pageContext = initializePageContext();
  pageContext.funnel_step = funnelStep;
  pageContext.lob_category = lobCategory || LOB_CATEGORIES.DOM_HOLIDAYS;
  pageContext.page_name = pageNameV2;
  pageContext.travel_store.store_id = getSubFunnelName();
  pageContext.prev_page_name =
  HolidayDataHolder.getInstance().getPrevPageNameV2(pageNameV2) || 'landing';
  pageContext.page_type = pageNameV2 === "landing" ? "landing" : "funnel"
  return pageContext;
};

export const getPaxPdtData = ({ paxDetails }) => {
  console.log('paxDetails *******',paxDetails)
  if (isEmpty(paxDetails)) {
    return {};
  }
  const { adult, child, infantCount, roomData = [] } = paxDetails || {};
  const allAges = roomData?.map((item) => item.childAgeArray).flat();
  const childAges = allAges?.map((item) => item.age);
  const details = {
    ...(child > 0 ? {
      child: {
        count: child,
        ages: childAges,
      },
    } : {}),
      adult: {
        count: adult,
      },
    ...(infantCount > 0 ? {
      infants: {
        count: infantCount,
      }
    } : {})
  };
  const pax = {
    count: adult + child + infantCount,
    rooms: roomData?.length || DEFAULT_ROOM_COUNT,
    details: details,
  };

  return pax;
};

export const getLocationDetail = ({ cityData }) => {
  if (isEmpty(cityData)) {
    return {};
  }
  const { id = '', countryName = '', locusId = '', name = '', type = '',LocationDetails={} ,locus_v2 ={} } = cityData || {};
  const locationData = {
    location: {
      code:LocationDetails?.code || "null",
      name:LocationDetails?.name || "null",
      country_code: LocationDetails?.country_code || "null",
      country: LocationDetails?.country || "null",
      type:LocationDetails?.type
    },
    locus: {
      country: countryName || "null",
      locus_id: locusId || "null",
      locus_type: type || LOCUS_TYPE,
      locus_v2_id:locusId,
      locus_v2_type: type || LOCUS_TYPE
    },
    // adding to support new template id
    locus_v2: {
      country:locus_v2?.countryName || "null",
      locus_id:locus_v2?.locusCode || "null",
      locus_type:locus_v2?.locusType ||  "null",
    },
  };
  return locationData;
};

export const populateSearchContext = ({
  paxDetails = {},
  destCity = {},
  deptCity = {},
  packageDate = '',
  prevSearchContext ={},
  tagDetails = {},
  toDataTime=null
})=> {
  const {
    pax = {},
    to = {},
    from = {},
    from_date_time = null,
    advance_purchase = '',
  } = prevSearchContext;
  let fromDatetime;
  const adavancePurchase =
    findDaysBetweenDates(packageDate, new Date().toJSON().slice(0, 10)) || advance_purchase || -1;
  if (packageDate) {
    fromDatetime = new Date(packageDate).getTime();
  }
  const searchContext = {
    ...prevSearchContext,
    pax: {
      ...pax,
      // ...getPaxPdtData({ paxDetails }),
    },
    to: {
      ...to,
      ...getLocationDetail({ cityData: destCity }),
    },
    from: {
      ...from,
      ...getLocationDetail({ cityData: deptCity }),
    },
    search_type: SEARCH_TYPE,
    tags: [tagDetails],
    from_date_time: fromDatetime || from_date_time,
    advance_purchase: adavancePurchase,
    to_date_time:toDataTime
  };
  return searchContext;
}
export const getSearchCriteria = ({
  depCity = '',
  destCity = '',
  paxDetails = {},
  startDate = '',
}) => {
  const {
    adult = DEFAULT_ADULT_COUNT,
    child: noOfChildren = DEFAULT_CHILD_COUNT,
    infantCount : noOfInfants = DEFAULT_INFANTS_COUNT,
    noOfRooms = DEFAULT_ROOM_COUNT,
  } = paxDetails || {};
  const paxDetail = `${adult}|${noOfChildren}|${noOfInfants}|${
    adult + noOfChildren + noOfInfants
  }_${noOfRooms}`;
  return `${depCity}_${destCity}_${startDate}_${paxDetail}`;
};

export const updatePDTJourneyIdOnSearchChange = ({
  depCity = '',
  destCity = '',
  paxDetails = '',
  startDate = '',
}) => {
  const searchCriteria = getSearchCriteria({ depCity, destCity, paxDetails, startDate });
  updatePDTJourneyId({ searchCriteria });
};


export const getLobCategory = ({ branch }) => {
  return branch === DOM_BRANCH ? LOB_CATEGORIES.DOM_HOLIDAYS : LOB_CATEGORIES.INT_HOLIDAYS;
};

export const modifyPDTObj = (data,destCity) => {
  const pdtObj = {
    content_details: [],
    product_list: [],
  };

  data.forEach((item) => {
    if (item.packageDetails) {
      const { id, name, packageType, branch, listingPackageVariantsDetails, listingPricingDetails, tags } = item.packageDetails;
      const {priceKey,pricePersuasion ,otherPromo=[]}=listingPricingDetails || {};
      const {info=[]}=otherPromo || {};
      const packageVariantDetails = listingPackageVariantsDetails?.packageVariants?.map(({ uniqueId, isParent }) => ({
        unique_id: uniqueId,
        is_parent: isParent,
      }));

      const tagDetails = tags?.map(({ type }) => ({
        type,
        values: type,
      }));

      pdtObj.product_list.push({
        id:id || 'null',
        name,
        type: packageType,
        branch,
        ...(packageVariantDetails?.length > 0 && {
          package_variant_details: {
            package_variant_details_list: packageVariantDetails,
          },
        }),
        price: {
          price_key: priceKey,
          price_persuasion: pricePersuasion
            ?.map(({ text }) => text)
            .join(''),
          urgency_persuasion: "",
          other_promo: info?.map(({ text }) => text).join('') || null,
        },
        tagged_destinations:[{name:destCity || ''}],
        ...(tagDetails?.length > 0 && { tags: tagDetails }),
      });
    } else {
      const { sectionCode, header, order, id } = item;

      pdtObj.content_details.push({
        id: sectionCode === "PDO" ? sectionCode : id,
        type: sectionCode,
        name: sectionCode === "PDO" ? header : "",
        section_code: sectionCode,
        position: {v:order},
      });
    }
  });

  return {
   components: pdtObj
  };
};
