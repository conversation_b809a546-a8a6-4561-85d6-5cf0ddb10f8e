import GenericTrackerModule from '@mmt/legacy-commons/Native/GenericTrackerModule';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { FirebaseEvent, constants } from './FirebaseConstants';
import { fetchKafkaGenericDataMap } from '../HolidayPDTTracking';
import { REVIEW_PDT_PAGE_NAME } from '../../Review/HolidayReviewConstants';
import { THANKYOU_PDT_PAGE_NAME as THANKYOU_PAGE_NAME } from '../../PostPayment/Components/HolidayThankyouUtils';
import { LISTING_TRACKING_PAGE_NAME } from '../../Listing/ListingConstants';
import { PAGE_NAME_QUERY } from '../../HolidayConstants';
import {isRawClient} from '../HolidayUtils';

/* Connector Functions Start */
const sendFirebaseEvent = (eventName, data) => {
  if (isRawClient()) {
    return;
  }
  if (GenericTrackerModule.trackFirebase) {
    GenericTrackerModule.trackFirebase(eventName, data);
  }
};

const sendFaceBookEvent = (eventName, data) => {
  if (isRawClient()){
    return;
  }
  if (GenericTrackerModule.trackFaceBook) {
    GenericTrackerModule.trackFaceBook(eventName, data);
  }
};

const getDaySpentValue = async () => {
  if (isRawClient()) {
    return '';
  }
  if (GenericModule.getDaySpentValue) {
    return await GenericModule.getDaySpentValue();
  }
  return '';
};
/* Connector Functions End */

const getEventNameByPage = async (pageName, branch) => {
  const daySpentValue = await getDaySpentValue();
  let eventName = '';
  switch (pageName) {
    case LISTING_TRACKING_PAGE_NAME:
      eventName = FirebaseEvent.HOLIDAY_LISTING;
      break;
    case REVIEW_PDT_PAGE_NAME:
      eventName = FirebaseEvent.HOLIDAY_REVIEW;
      break;
    case THANKYOU_PAGE_NAME:
      eventName = FirebaseEvent.HOLIDAY_TRANSACTION;
      break;
    case PAGE_NAME_QUERY:
      eventName = FirebaseEvent.HOLIDAY_QUERY;
      break;
    default:
      eventName = '';
  }
  return `${branch ? branch : ''}${eventName}${daySpentValue ? `_${daySpentValue}` : ''}`;
};

const getAttributeValueByPageName = ({
  fromPage,
  pax,
  totalAmount,
  walletBalance,
  paymentReferenceId,
  id,
}) => {
  switch (fromPage) {
    case LISTING_TRACKING_PAGE_NAME:
      return `${pax} | ${walletBalance} `;
    case THANKYOU_PAGE_NAME:
      return `${paymentReferenceId} | ${totalAmount} | ${pax} | ${walletBalance} `;
    default:
      return `${id} | ${pax} | ${walletBalance} `;
  }
};

const sendFirebaseDataToNative = async ({
  id = '',
  holidayCity = '',
  depDate = null,
  returnDate = null,
  pax = 0,
  fromPage = '',
  branch = '',
  totalAmount = '',
  paymentReferenceId = '',
}) => {
  const params = {};
  let userDetailsObj = await fetchKafkaGenericDataMap();
  const walletBalance = userDetailsObj?.userDetails?.usr_wal_balnc || 0;
  params[constants.ATTRIBUTE_1] = id;
  params[constants.ATTRIBUTE_2] = holidayCity || '';
  if (depDate) {
    params[constants.ATTRIBUTE_3] = depDate;
  }
  params[constants.ATTRIBUTE_4] = returnDate;
  params[constants.ATTRIBUTE_5] = getAttributeValueByPageName({
    id,
    pax,
    fromPage,
    totalAmount,
    walletBalance,
    paymentReferenceId,
  });
  const EVENT_NAME = await getEventNameByPage(fromPage, branch);
  sendFirebaseEvent(EVENT_NAME, params);
  sendFaceBookEvent(EVENT_NAME, params);
  return null;
};

export {
  sendFirebaseDataToNative,
  sendFirebaseEvent,
  getEventNameByPage,
  getAttributeValueByPageName,
  getDaySpentValue,
  sendFaceBookEvent,
};
