import { NativeModules, Platform } from 'react-native';
import isEmpty from 'lodash/isEmpty';
import cloneDeep from 'lodash/cloneDeep';
import { isArray, isString, startsWith } from 'lodash';
import {
  ADD_SHORTLISTED_URL,
  AFFILIATES,
  BASE_HOLIDAY_SERVICE_URL,
  <PERSON>ETCH_SHORTLISTED_URL,
  <PERSON><PERSON><PERSON>_SIMILAR_PACKAGES_URL,
  FUNNELS,
  HOL_SHORTLIST_LOB,
  HOLIDAY_CORRELATION_GROUPING_KEY,
  HOLIDAY_CORRELATION_KEY,
  HOLIDAY_LISTING_META_URL_NEW,
  HOLIDAY_USER_PACKAGES_URL,
  INIT_CHAT_URL,
  NO_DEPARTURE_CITY,
  PDT_ERROR_API,
  PDT_ERROR_TIMEOUT,
  pdtErrorSev,
  REFERRAL_SERVICE_URL,
  REMOVE_SHORTLISTED_URL,
  REQUEST_LOB,
  REQUEST_WEBSITE,
  USER_DEFAULT_CITY,
  USER_DETAILS_URL,
  HOL_REQUEST_TYPE,
  PAGE_API_VERSIONS,
  PAGE_TYPES_FOR_API,
  DEST_SEARCH_TYPES,
  FARE_BREAKUP_REQUEST_SOURCE,
  MMT_BLACK_REVIEW_PAGE_POPUP_FLAG,
  HLD_PAGE_NAME,
  PAGE_API_VERSIONSV2,
} from '../HolidayConstants';
import { MAX_RECENTLY_SEEN_PACKAGES_GROUPING } from '../Grouping/HolidayGroupingConstants';
import {
  getBaseVisaCondition,
  getCMPMeta,
  getCorrelationUUID,
  getDepartureCity,
  getFunnelBindingUUID,
  isAndroidClient, isIosClient,
  getPlatformIdentifier,
  getSubFunnelName,
  isMobileClient,
  isNonMMTAffiliate,
  isNotNullAndEmptyCollection,
  isNullOrEmptyCollection,
  isRawClient,
  setListingPersonalisedData,
} from './HolidayUtils';
import { getPokusExpVarientKey } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {
  itineraryUnitTypes,
  MIN_CHILD_AGE,
  packageActionComponent,
  packageActions,
  SECTIONS_LIST,
} from '../PhoenixDetail/DetailConstants';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { getDataFromStorage, KEY_HOL_META } from '@mmt/legacy-commons/AppState/LocalStorage';
import HolidayDataHolder from './HolidayDataHolder';
import NetworkModule from '@mmt/legacy-commons/Native/NetworkModule';
import { sectionCodes } from '../LandingNew/LandingConstants';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import HolidayCache from './Cache/HolidayCache';
import moment from 'moment';
import {
  getApiPokusListing,
  getDisableUserGroupHol,
  getShowNewActivityDetail,
  showNewRVSSection,
} from './HolidaysPokusUtils';
import { REVIEW_PDT_PAGE_NAME } from '../Review/HolidayReviewConstants';
import {getAffiliate} from "../theme";
import { getPdtId, pdtIdNames, pdtIds } from './PdtDataHolder';
import HolidayPDTMetaIDHolder from './HolidayPDTMetaIDHolder';

const getHeaders = async (
  newFunnelBindingKey,
  funnelBindingKeyRequired,
  newCorrelationKey,
  correlationKeyParam,
) => {
  const { HolidayModule, PdtV2Module } = NativeModules;
  const headers = await HolidayModule.getRequestHeader();
  const holMetaObj = await getDataFromStorage(KEY_HOL_META);
  const userCity = await getDepartureCity();
  if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate && !isRawClient())) {
    delete headers['mmt-auth'];
  }
  const sessionId = await PdtV2Module?.getSessionId();
  const kafkaDataMap = await HolidayModule.createKafkaGenericDataMap();
  if (kafkaDataMap) {
    updateHeaders(headers, kafkaDataMap);
  }
  headers.userCity = userCity;
  if (funnelBindingKeyRequired) {
    const funnelBindingKey = await getFunnelBindingUUID(newFunnelBindingKey);
    headers.funnel_binding_key = funnelBindingKey;
  }
  const correlationKey = await getCorrelationUUID(newCorrelationKey, correlationKeyParam);
  headers.correlation_id = correlationKey;

  const request_id = getPdtId({ pdtId: pdtIdNames.REQUESTID });
  const journey_id = getPdtId({ pdtId: pdtIdNames.JOURNEYID });
  const pdt_request_id = HolidayPDTMetaIDHolder.getInstance().getPdtId();
  return { ...headers, request_id, journey_id, session_mmt_id: sessionId  ,meta_req_id_ls: pdt_request_id};
};

export const updateHeaders = (headers, kafkaDataMap) => {
  const userDetailsMap = kafkaDataMap.userDetails;
  if (userDetailsMap) {
    if (userDetailsMap.uuid != null) {
      headers.uuid = userDetailsMap.uuid;
    }
    if (userDetailsMap.login_channel != null) {
      headers.login_channel = userDetailsMap.login_channel;
    }
    if (userDetailsMap.marketing_cloud_id != null) {
      headers.mcid = userDetailsMap.marketing_cloud_id;
    }
    if (isRawClient() && userDetailsMap.session_id != null) {
      headers.session_mmt_id = userDetailsMap.session_id;
    }
  }

  const deviceDetailsMap = kafkaDataMap.appAndDeviceDetails;
  if (deviceDetailsMap) {
    if (deviceDetailsMap.lat != null) {
      headers.latitude = deviceDetailsMap.lat;
    }
    if (deviceDetailsMap.long != null) {
      headers.longitude = deviceDetailsMap.long;
    }
    if (deviceDetailsMap.browser != null) {
      headers.browser = deviceDetailsMap.browser;
    }
    if (deviceDetailsMap.internet_connection != null) {
      headers.connection_type = deviceDetailsMap.internet_connection;
    }
    if (deviceDetailsMap.app_version != null) {
      headers.application_version = deviceDetailsMap.app_version;
    }
    if (deviceDetailsMap.dvc_did != null) {
      headers.device_id = deviceDetailsMap.dvc_did;
    }
    if (deviceDetailsMap.device_name != null) {
      headers.model = deviceDetailsMap.device_name;
    }
    if (deviceDetailsMap.carrier != null) {
      headers.carrier = deviceDetailsMap.carrier;
    }
    if (isMobileClient()) {
      headers.dvc_type = 'Mobile';
    } else {
      headers.dvc_type = 'PWA';
    }
  }

  return headers;
};

export const DEFAULT_TIMEOUT = 60000;

export const getRequestHeaders = async (
  newFunnelBindingKey = false,
  funnelBindingKeyRequired = true,
  newCorrelationKey = true,
  correlationKeyParam = HOLIDAY_CORRELATION_KEY,
) => {
  return await getHeaders(
    newFunnelBindingKey,
    funnelBindingKeyRequired,
    newCorrelationKey,
    correlationKeyParam,
  );
};
export const getAPIRequestHeader = async (params = {}) => {
  return { ...(await getRequestHeaders()), ...params };
};
export const createBaseRequest = async () => {
  const holMetaObj = await getDataFromStorage(KEY_HOL_META);
  const requestObject = {
    website: REQUEST_WEBSITE,
    lob: 'Holidays',
    channel: getPlatformIdentifier(),
    subFunnel: getSubFunnelName(),
    funnel: holMetaObj && holMetaObj.funnel ? holMetaObj.funnel : FUNNELS.HOLIDAY,
    affiliate: holMetaObj && holMetaObj.affiliate ? holMetaObj.affiliate : AFFILIATES.MMT,
  };
  const cmpObj = await getCMPMeta();
  if (!isEmpty(cmpObj)) {
    requestObject.campaignDetails = cmpObj;
  }
  return requestObject;
};
export const createBasePageRequest = () => {
  const pageName=HolidayDataHolder.getInstance().getCurrentPageNameV2()
  return {
      "pageName":pageName ,
      "version": PAGE_API_VERSIONSV2[pageName],
      "funnelType": "Holidays"
  }
}

export const createBaseRequestWithRequestSource = async (requestSource) => {
  const requestObj = await createBaseRequest();
  if (!isEmpty(requestSource)) {
    requestObj.requestSource = requestSource;
  }
  return requestObj;
};

export const createBaseRequestWOCmp = async () => {
  const holMetaObj = await getDataFromStorage(KEY_HOL_META);
  const { affiliate, funnel } = holMetaObj || {}
  const requestObject = {
    website: REQUEST_WEBSITE,
    channel: getPlatformIdentifier(),
    subFunnel: getSubFunnelName(),
    funnel: holMetaObj && holMetaObj.funnel ? holMetaObj.funnel : FUNNELS.HOLIDAY,
    affiliate: holMetaObj && holMetaObj.affiliate ? holMetaObj.affiliate : AFFILIATES.MMT,
  };
  return requestObject;
};

export const fetchChangeFlights = async (dynamicPackageId) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/flight/change/listing`;
    const request = await createBaseRequest();
    request.dynamicPackageId = dynamicPackageId;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    return responseBody;
  } catch (e) {
    return null;
  }
};

/*
  Api function to get the travel Plan option in detail page
*/
export const fetchTravelPlans = async (ticketId, tagDestination, quoteRequestId) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/presales/listing`;
    const request = await createBaseRequest();
    request.ticketId = ticketId;
    request.tagDestination = tagDestination;
    if (quoteRequestId) {
      request.quoteRequestId = quoteRequestId;
    }

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const fetchAgentStatus = async (agentUserName, page, ticketRequestId) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/presales/agent/detail`;
    const request = await createBaseRequest();
    request.agentUserName = agentUserName;
    request.page = page;
    request.ticketId = ticketRequestId;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const fetchPackagesMetaData = async (
  holidayLandingData,
  selectedFiltersList,
  errorBody,
  setPersonalisedData = false,
  resetIfPackageNotFound = false,
) => {
  let initialFilters = [];
  if (isNotNullAndEmptyCollection(holidayLandingData.filters)) {
    initialFilters = holidayLandingData.filters;
  }
  if (isNotNullAndEmptyCollection(selectedFiltersList)) {
    initialFilters = initialFilters.concat(selectedFiltersList);
  }
  const metaUrl = getMetaUrl();
  const request = await createBaseRequest();
  let departureCity = await getDepartureCity();
  if (holidayLandingData.departureCity) {
    departureCity = holidayLandingData.departureCity;
  } else if (holidayLandingData.departureLocusCode) {
    departureCity = undefined;
  }
  request.lob = REQUEST_LOB;
  request.departureCity = departureCity;
  request.destinationCity = holidayLandingData.destinationCity;
  request.criterias = initialFilters;
  request.filterSorterParam = true;
  request.fromDate = holidayLandingData.fromDate;
  request.toDate = holidayLandingData.toDate;
  request.packageDate = holidayLandingData.packageDate;
  request.departureLocusCode = holidayLandingData.departureLocusCode;
  request.destinationLocusCode = holidayLandingData.destinationLocusCode;
  if (holidayLandingData?.packageIds) {
    request.packageIds = holidayLandingData?.packageIds;
  }

  if (holidayLandingData.campaign) {
    request.campaign = holidayLandingData.campaign;
  }

  try {
    // Setting data at APP home page
    if (setPersonalisedData) {
      setListingPersonalisedData(request);
    }

    const metaResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(metaUrl, {
        method: 'POST',
        headers: await getRequestHeaders(true),
        body: JSON.stringify(request),
      }),
    );

    if (!metaResponse || !metaResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }

    const responseBody = await metaResponse.json();
    if (
      !responseBody ||
      !responseBody.statusCode ||
      responseBody.statusCode === 0 ||
      !responseBody.success ||
      !responseBody.details ||
      isNullOrEmptyCollection(responseBody.details)
    ) {
      if (responseBody && responseBody.error) {
        updateErrorBody(
          errorBody,
          responseBody.error.code,
          responseBody.error.message,
          pdtErrorSev.LISTING,
          PDT_ERROR_API,
        );
      } else if (responseBody.numFound < 1 && resetIfPackageNotFound) {
        return responseBody;
      } else {
        updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      }
      return null;
    }
    return responseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchGroupingSections = async (
  holidayLandingData = {},
  fromDetails = false,
  detailsData = {},
  tagDestination,
) => {
  const request = await createBaseRequest();
  const departureCity = await getDepartureCity();
  request.lob = REQUEST_LOB;
  request.departureCity = departureCity;
  request['experimentKey'] = getPokusExpVarientKey(PokusLobs.HOLIDAY, 'NA');
  if (!fromDetails) {
    request.tagDestination = holidayLandingData.destinationCity;
  } else {
    request.tagDestination = tagDestination;
  }
  request.page = fromDetails ? 'DETAILS' : 'LISTING';
  request.marketChannel = '';
  if (holidayLandingData.campaign) {
    request.campaign = holidayLandingData.campaign;
  }

  if (fromDetails) {
    const { travelDate, dynamicPackageId } = detailsData || {};
    (request.travelDate = travelDate), (request.dynamicPackageId = dynamicPackageId);
  }
  request.pageInfo=createBasePageRequest()
  const url = `${BASE_HOLIDAY_SERVICE_URL}/section/fetch`;
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(false, true, true, HOLIDAY_CORRELATION_GROUPING_KEY),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    const responseJson = await response.json();
    if (responseJson.success) {
      return responseJson.sections;
    }
  } catch (e) {
    return null;
  }
};
export const fetchGroupingData = async (
  holidayLandingData,
  offset,
  selectedFiltersList,
  errorBody,
  searchQuery,
  newKeyRequired,
) => {
  let initialFilters = [];
  if (isNotNullAndEmptyCollection(holidayLandingData.filters)) {
    initialFilters = holidayLandingData.filters;
  }
  if (isNotNullAndEmptyCollection(selectedFiltersList)) {
    initialFilters = initialFilters.concat(selectedFiltersList);
  }
  const request = await createBaseRequest();
  const departureCity = await getDepartureCity();
  const disableUserGroup = getDisableUserGroupHol();
  const apiPokusListing = getApiPokusListing();
  request.lob = REQUEST_LOB;
  request.departureCity = holidayLandingData.departureCity || departureCity;
  request.destinationCity = holidayLandingData.destinationCity;
  request.departureLocusCode = holidayLandingData.departureLocusCode;
  request.destinationLocusCode = holidayLandingData.destinationLocusCode;
  request.criterias = initialFilters;
  request.sorterCriterias = holidayLandingData.sorterCriterias;
  request.offset = offset;
  request.fromDate = holidayLandingData.fromDate;
  request.toDate = holidayLandingData.toDate;
  request.packageDate = holidayLandingData.packageDate;
  request.disableUserGroup = disableUserGroup;

  if (holidayLandingData?.rooms) {
    request.rooms = holidayLandingData.rooms;
  }

  if (apiPokusListing) {
    request.apiPokus = apiPokusListing;
  }

  if (searchQuery) {
    request.searchText = searchQuery;
  }

  if (holidayLandingData.campaign) {
    request.campaign = holidayLandingData.campaign;
  }

  const groupingUrl = `${BASE_HOLIDAY_SERVICE_URL}/listing/packages/grouped`;
  try {
    const groupingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(groupingUrl, {
        method: 'POST',
        headers: await getRequestHeaders(
          false,
          true,
          newKeyRequired,
          HOLIDAY_CORRELATION_GROUPING_KEY,
        ),
        body: JSON.stringify(request),
      }),
    );

    if (!groupingResponse || !groupingResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }
    const groupingResponseBody = await groupingResponse.json();
    if (
      !groupingResponseBody ||
      !groupingResponseBody.statusCode ||
      groupingResponseBody.statusCode === 0 ||
      !groupingResponseBody.success ||
      !groupingResponseBody.packageDetails ||
      isNullOrEmptyCollection(groupingResponseBody.packageDetails)
    ) {
      if (groupingResponseBody && groupingResponseBody.error) {
        updateErrorBody(
          errorBody,
          groupingResponseBody.error.code,
          groupingResponseBody.error.message,
          pdtErrorSev.LISTING,
          PDT_ERROR_API,
        );
        return groupingResponseBody;
      } else {
        updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      }
      return null;
    }
    return groupingResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchPersuasionData = async (page, dynamicPackageId, tagDestination) => {
  const request = await createBaseRequest();
  request.page = page;
  request.departureCity = await getDepartureCity();
  if (dynamicPackageId) {
    request.dynamicPackageId = dynamicPackageId;
  }
  if (tagDestination) {
    request.tagDestination = tagDestination;
  }

  const persuasionUrl = `${BASE_HOLIDAY_SERVICE_URL}/personalization/persuasions/fetch`;
  try {
    const persuasionResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(persuasionUrl, {
        method: 'POST',
        headers: { ...(await getRequestHeaders()), 'API-Version': 2 },
        body: JSON.stringify(request),
      }),
    );

    if (!persuasionResponse || !persuasionResponse.ok) {
      return null;
    }
    const persuasionResponseBody = await persuasionResponse.json();
    if (
      !persuasionResponseBody ||
      !persuasionResponseBody.statusCode ||
      persuasionResponseBody.statusCode === 0 ||
      !persuasionResponseBody.success ||
      !persuasionResponseBody.persuasionsDetail
    ) {
      return null;
    }
    return persuasionResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchOfferSectionData = async (
  packageId,
  dynamicPackageId,
  fromMimaPreSales = false,
) => {
  const request = await createBaseRequest();
  if (dynamicPackageId) {
    request.dynamicPackageId = dynamicPackageId;
  }
  if (packageId) {
    request.packageId = packageId;
  }
  request.sections = SECTIONS_LIST;
  if (fromMimaPreSales) {
    request.requestSource = sectionCodes.PRESALES;
  }
  request.pageInfo=createBasePageRequest()
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.DETAIL);
  const offerSectionUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/section/fetch`;
  try {
    const offerSectionResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(offerSectionUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!offerSectionResponse || !offerSectionResponse.ok) {
      return null;
    }
    const offerSectionResponseBody = await offerSectionResponse.json();
    if (
      !offerSectionResponseBody ||
      !offerSectionResponseBody.statusCode ||
      offerSectionResponseBody.statusCode === 0 ||
      !offerSectionResponseBody.success ||
      (!offerSectionResponseBody.paymentScheduleOptions && !fromMimaPreSales)
    ) {
      return null;
    }
    return offerSectionResponseBody;
  } catch (e) {
    return null;
  }
};

export const setOfferSection = async (
  action,
  coupon,
  dynamicPackageId,
  fromMimaPreSales = false,
) => {
  const request = await createBaseRequest();
  if (fromMimaPreSales) {
    request.requestSource = sectionCodes.PRESALES;
  }
  if (dynamicPackageId) {
    request.dynamicPackageId = dynamicPackageId;
  }
  if (action) {
    request.action = action;
  }
  if (coupon) {
    request.coupon = coupon;
  }
  request.pageInfo=createBasePageRequest()
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.DETAIL);
  const offerSectionUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/detail/coupon/select`;
  try {
    const offerSectionResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(offerSectionUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    return await offerSectionResponse.json();
  } catch (e) {
    return null;
  }
};

export const fetchInterventionData = async (page, pkgId, destination) => {
  const request = await createBaseRequest();
  request.page = page;
  if (pkgId) {
    request.pkgId = pkgId;
  }
  if (destination) {
    request.destination = destination;
  }

  const interventionUrl = `${BASE_HOLIDAY_SERVICE_URL}/intervention/fetchRules`;
  try {
    const interventionResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(interventionUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!interventionResponse || !interventionResponse.ok) {
      return null;
    }
    const interventionResponseBody = await interventionResponse.json();
    if (
      !interventionResponseBody ||
      !interventionResponseBody.statusCode ||
      interventionResponseBody.statusCode === 0 ||
      !interventionResponseBody.interventionRules
    ) {
      return null;
    }
    return interventionResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchFabData = async (page, destination, cmp = '', pkgId = '') => {
  const request = await createBaseRequestWOCmp();
  request.page = page;
  request.cmp = cmp ? cmp : undefined;
  if (pkgId) {
    request.pkgId = pkgId;
  }
  if (destination) {
    request.destination = destination;
  }

  const ctaUrl = `${BASE_HOLIDAY_SERVICE_URL}/contact/management/cta`;
  try {
    const ctaResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(ctaUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!ctaResponse || !ctaResponse.ok) {
      return null;
    }
    const ctaResponseBody = await ctaResponse.json();
    if (
      !ctaResponseBody ||
      !ctaResponseBody.statusCode ||
      ctaResponseBody.statusCode === 0 ||
      !ctaResponseBody.cta
    ) {
      return null;
    }
    return ctaResponseBody;
  } catch (e) {
    return null;
  }
};

// Function to fetch and process FabCta data
export const fetchFabCta = async (data, page, packageId) => {
  // Handle null or undefined data parameter
  if (!data) {
    return {
      showFab: false,
      showCall: false,
      showQuery: false,
      showChat: false,
      branchLocator: false,
    };
  }

  // Destructure data object for easy access to its properties
  const { showFabFromDeeplink, destinationCity, cmp } = data || {};

  // Initialize fabCta object to store the final result
  const fabCta = {};

  // Check if showFabFromDeeplink property is available
  if (showFabFromDeeplink) {
    // Destructure showFabFromDeeplink string into an array of booleans for showQuery, showCall, and showChat
    const [showQuery, showCall, showChat] = showFabFromDeeplink
      .split('')
      .map((char) => char === '1');

    // Assign the properties to fabCta object
    Object.assign(fabCta, { showQuery, showCall, showChat });

    // Set showFab property based on the presence of any of the three flags (showQuery, showCall, showChat)
    fabCta.showFab = showQuery || showCall || showChat;
  } else {
    // Fetch the ctaData if showFabFromDeeplink is not available
    const { cta = {} } = (await fetchFabData(page, destinationCity, cmp, packageId)) || {};
    // Destructure cta object for easy access to its properties
    const { phone, webQuery, chat, branchLocator, formId } = cta || {};

    // Assign the properties to fabCta object
    Object.assign(fabCta, {
      showFab: phone || webQuery || chat || branchLocator,
      showCall: phone,
      showQuery: webQuery,
      showChat: chat,
      branchLocator: branchLocator,
      formId: formId,
    });
  }

  // Return the final fabCta object
  return fabCta;
};

export const fetchRecentlySeenPackages = async (
  holidayLandingGroupDto,
  { checkPokusForRVS = false, sectionTypes = [] } = {},
) => {
  let { destinationCity } = holidayLandingGroupDto;
  const loggedIn = await isUserLoggedIn();
  const url = BASE_HOLIDAY_SERVICE_URL + HOLIDAY_USER_PACKAGES_URL;
  const isShowingNewRVS = showNewRVSSection() && checkPokusForRVS;
  const request = await createBaseRequest();
  if (!isEmpty(destinationCity)) {
    const tagDestinations = destinationCity.split(',');
    request.tagDestinations = tagDestinations;

    if (holidayLandingGroupDto.campaign) {
      request.campaign = holidayLandingGroupDto.campaign;
    }

    if (isShowingNewRVS) {
      request.sectionTypes = sectionTypes;
    }
  }
  request['experimentKey'] = getPokusExpVarientKey(PokusLobs.HOLIDAY, 'NA');
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    if (
      !responseBody.statusCode ||
      responseBody.statusCode === 0 ||
      !responseBody.success ||
      (!responseBody.userPackageMetas && !responseBody?.sections)
    ) {
      return null;
    }

    if (isShowingNewRVS) {
      let userPackagesList = responseBody.sections?.[0]; // assume we are only using the first section PDO
      return userPackagesList;
    }
    let userPackagesList = responseBody.userPackageMetas;
    if (userPackagesList && userPackagesList.length > MAX_RECENTLY_SEEN_PACKAGES_GROUPING) {
      userPackagesList = userPackagesList.slice(0, MAX_RECENTLY_SEEN_PACKAGES_GROUPING);
    }

    return userPackagesList;
  } catch (e) {
    return null;
  }
};

export const fetchListingMetaData = async (
  holidayListingData,
  criterias,
  sorterCriterias,
  errorBody,
) => {
  const departureCity = await getDepartureCity();
  const listingStaticRequest = await createHolidaysListingStaticRequest(
    holidayListingData,
    departureCity,
  );
  listingStaticRequest.criterias = criterias;
  //todo need to check if metadata has to be deleted or not
  delete listingStaticRequest.rooms;
  listingStaticRequest.sorterCriterias = sorterCriterias;
  if (holidayListingData.packageIds) {
    listingStaticRequest.packageIds = holidayListingData.packageIds.split(',');
    if (holidayListingData.defPkgId) {
      if (!listingStaticRequest.packageIds.includes(holidayListingData.defPkgId)) {
        listingStaticRequest.packageIds.unshift(holidayListingData.defPkgId);
      }
    }
  }

  listingStaticRequest.filterSorterParam = true;
  listingStaticRequest.metadataSortRequired = true;

  if (holidayListingData.campaign) {
    listingStaticRequest.campaign = holidayListingData.campaign;
  }
  if (holidayListingData.departureCity) {
    listingStaticRequest.departureCity = holidayListingData.departureCity;
  } else if (holidayListingData.departureLocusCode) {
    listingStaticRequest.departureCity = undefined;
  }

  const staticUrl = getMetaUrl();
  try {
    // Setting data at APP home page
    if (!holidayListingData.personalisedData) {
      setListingPersonalisedData(listingStaticRequest);
      holidayListingData.personalisedData = true;
    }

    const listingMetaDataResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(staticUrl, {
        method: 'POST',
        headers: await getRequestHeaders(true),
        body: JSON.stringify(listingStaticRequest),
      }),
    );

    if (!listingMetaDataResponse || !listingMetaDataResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }
    const listingMetaDataResponseBody = await listingMetaDataResponse.json();
    if (
      !listingMetaDataResponseBody ||
      !listingMetaDataResponseBody.statusCode ||
      listingMetaDataResponseBody.statusCode === 0 ||
      !listingMetaDataResponseBody.success ||
      !listingMetaDataResponseBody.details
    ) {
      if (listingMetaDataResponseBody && listingMetaDataResponseBody.error) {
        updateErrorBody(
          errorBody,
          listingMetaDataResponseBody.error.code,
          listingMetaDataResponseBody.error.message,
          pdtErrorSev.LISTING,
          PDT_ERROR_API,
        );
      } else {
        updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      }
      return null;
    }
    return listingMetaDataResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const updateErrorBody = (errorBody, code, msg, severity, source) => {
  errorBody.error_code = code;
  errorBody.error_message = msg;
  errorBody.error_severity = severity;
  errorBody.error_source = source;
};

export const timeout = (ms, promise) =>
  new Promise((resolve, reject) => {
    setTimeout(() => {
      reject(new Error(PDT_ERROR_TIMEOUT));
    }, ms);
    promise.then(resolve, reject);
  });

export const fetchListingDataUsingRequest = async (
  listingRequest,
  errorBody,
  newCorrelationKey,
  correlationKey,
) => {
  const listingUrl = `${BASE_HOLIDAY_SERVICE_URL}/listing/packages`;
  const apiPokusListing = getApiPokusListing();
  if (apiPokusListing) {
    listingRequest.apiPokus = apiPokusListing;
  }
  try {
    const headers = (await getRequestHeaders(false, true, newCorrelationKey, correlationKey)) || {};
    const listingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(listingUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(listingRequest),
      }),
    );
    if (!listingResponse || !listingResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }
    const listingResponseBody = await listingResponse.json();
    if (
      !listingResponseBody ||
      !listingResponseBody.statusCode ||
      listingResponseBody.statusCode === 0 ||
      !listingResponseBody.success ||
      !listingResponseBody.packageDetails
    ) {
      if (listingResponseBody && listingResponseBody.error) {
        updateErrorBody(
          errorBody,
          listingResponseBody.error.code,
          listingResponseBody.error.message,
          pdtErrorSev.LISTING,
          PDT_ERROR_API,
        );
        return listingResponseBody;
      } else {
        updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      }
      return null;
    }
    return listingResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_TIMEOUT);
    return null;
  }
};
const createHolidaysListingStaticRequest = async (holidayListingData, departureCity) => {
  const listingStaticRequest = await createBaseRequest();
  listingStaticRequest.departureCity = holidayListingData.departureCity || departureCity;
  listingStaticRequest.destinationCity = holidayListingData.dest;
  listingStaticRequest.channel = getPlatformIdentifier();
  listingStaticRequest.fromDate = holidayListingData.fromDate;
  listingStaticRequest.toDate = holidayListingData.toDate;
  listingStaticRequest.packageDate = holidayListingData.packageDate;
  listingStaticRequest.departureLocusCode = holidayListingData.departureLocusCode;
  listingStaticRequest.destinationLocusCode = holidayListingData.destinationLocusCode;

  if (holidayListingData?.rooms) {
    listingStaticRequest.rooms = holidayListingData.rooms;
  }

  if (holidayListingData.campaign) {
    listingStaticRequest.campaign = holidayListingData.campaign;
  }

  return listingStaticRequest;
};

export const createListingRequest = async (
  holidayListingData,
  packageIds,
  criterias,
  sorterCriterias,
  departureCity,
  offset = 0,
) => {
  const listingRequest = await createHolidaysListingStaticRequest(
    holidayListingData,
    departureCity,
  );
  listingRequest.offset = offset;
  listingRequest.criterias = criterias;
  listingRequest.sorterCriterias = sorterCriterias;
  listingRequest.packageIds = packageIds;
  if (holidayListingData.searchQuery) {
    listingRequest.searchText = holidayListingData.searchQuery;
  }

  if (holidayListingData.campaign) {
    listingRequest.campaign = holidayListingData.campaign;
  }

  return listingRequest;
};

export const fetchShortListedPackages = async () => {
  if (isRawClient()) {
    return new Set();
  }
  const requestHeaders = await getRequestHeaders();
  try {
    const deviceId = isRawClient() ? requestHeaders.visitorId : requestHeaders.deviceid;
    const fetchShortListPkgsResp = await timeout(
      DEFAULT_TIMEOUT,
      fetch(`${FETCH_SHORTLISTED_URL}&deviceId=${deviceId}`, {
        method: 'GET',
        headers: requestHeaders,
      }),
    );
    if (!fetchShortListPkgsResp || !fetchShortListPkgsResp.ok) {
      return new Set();
    }
    const fetchShortListPkgsBody = await fetchShortListPkgsResp.json();
    if (
      !fetchShortListPkgsBody ||
      !fetchShortListPkgsBody.success ||
      !fetchShortListPkgsBody.shortlists ||
      fetchShortListPkgsBody.shortlists.length === 0 ||
      !fetchShortListPkgsBody.shortlists[0].shortlistedSKUs ||
      fetchShortListPkgsBody.shortlists[0].shortlistedSKUs.length === 0
    ) {
      return new Set();
    }
    return createShortListedPkgsSetFromResp(fetchShortListPkgsBody.shortlists[0].shortlistedSKUs);
  } catch (e) {
    return new Set();
  }
};

export const fetchSimilarPackages = async (detailRespBody, holidayDetailData) => {
  try {
    const fetchSimilarPkgsResp = await timeout(
      DEFAULT_TIMEOUT,
      fetch(`${BASE_HOLIDAY_SERVICE_URL}${FETCH_SIMILAR_PACKAGES_URL}`, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createSimilarPkgsRequest(detailRespBody, holidayDetailData)),
      }),
    );
    if (!fetchSimilarPkgsResp || !fetchSimilarPkgsResp.ok) {
      return [];
    }
    const fetchSimilarPkgsBody = await fetchSimilarPkgsResp.json();
    if (
      !fetchSimilarPkgsBody ||
      !fetchSimilarPkgsBody.statusCode ||
      fetchSimilarPkgsBody.statusCode === 0 ||
      !fetchSimilarPkgsBody.success ||
      !fetchSimilarPkgsBody.similarPackages
    ) {
      return [];
    }
    let storyImageSize = null;
    if (fetchSimilarPkgsBody.attributes && fetchSimilarPkgsBody.attributes.values) {
      storyImageSize = fetchSimilarPkgsBody.attributes.values.StoryImageSize;
    }
    return {
      similarPackages: fetchSimilarPkgsBody.similarPackages,
      storyImageSize,
    };
  } catch (e) {
    return [];
  }
};

const createShortListedPkgsSetFromResp = (shortlistedSKUs) => {
  const shortListedPackages = new Set();
  for (let i = 0; i < shortlistedSKUs.length; i += 1) {
    shortListedPackages.add(parseInt(shortlistedSKUs[i].skuId[0], 10));
  }
  return shortListedPackages;
};

export const shortListPackage = async (id, name) => {
  const requestHeaders = await getRequestHeaders();
  const deviceId = isRawClient() ? requestHeaders.visitorId : requestHeaders.deviceid;
  const saveShortListPkgsReq = {
    combinedPrice: '0',
    lob: HOL_SHORTLIST_LOB,
    deviceId,
    searchCriteria: {
      dateOfJourney1: '01012099',
      dateOfJourney2: '01022099',
      destination: {
        cityCode: HOL_SHORTLIST_LOB,
        cityName: HOL_SHORTLIST_LOB,
        countryCode: HOL_SHORTLIST_LOB,
      },
      lob: HOL_SHORTLIST_LOB,
    },
    skuDetails: [
      {
        availability: true,
        displayName: name,
        price: '0',
      },
    ],
    skuIds: [id],
  };
  try {
    await timeout(
      DEFAULT_TIMEOUT,
      fetch(ADD_SHORTLISTED_URL, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(saveShortListPkgsReq),
      }),
    );
  } catch (e) {
    // do nothing
  }
};

export const removePackageFromShortList = async (id) => {
  const requestHeaders = await getRequestHeaders();
  const deviceId = isRawClient() ? requestHeaders.visitorId : requestHeaders.deviceid;
  const removeShortListPackageReq = {
    deviceId,
    lob: HOL_SHORTLIST_LOB,
    skuId: [id],
  };
  try {
    await timeout(
      DEFAULT_TIMEOUT,
      fetch(REMOVE_SHORTLISTED_URL, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(removeShortListPackageReq),
      }),
    );
  } catch (e) {
    // do nothing
  }
};

export const fetchSearchWidgetMetaData = async (request, errorBody, isFromLanding = false) => {
  const url = getMetaUrl();
  const requestObj = await createBaseRequest();
  request.website = requestObj.website;
  request.channel = requestObj.channel;
  request.funnel = requestObj.funnel;
  request.subFunnel = requestObj.subFunnel;
  request.affiliate = requestObj.affiliate;
  request.campaignDetails = requestObj.campaignDetails;
  request.page = isFromLanding ? HLD_PAGE_NAME.LANDING : '';
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(true, !isFromLanding),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }
    return response;
  } catch (e) {
    return null;
  }
};

// Map Page Filter Meta API
// Copy of fetchSearchWidgetMetaData with different endpoint
export const fetchSearchWidgetMetaDataMapPage = async (request, errorBody, isFromLanding) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/map/filter/metadata`;
  const requestObj = await createBaseRequest();
  request.website = requestObj.website;
  request.channel = requestObj.channel;
  request.funnel = requestObj.funnel;
  request.affiliate = requestObj.affiliate;
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(true, true),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }
    return response;
  } catch (e) {
    return null;
  }
};

export const fetchSearchHistoryData = async (errorBody) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/personalization/searchHistory`;
  const request = await createBaseRequest();
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.LISTING, PDT_ERROR_API);
      return null;
    }
    return response;
  } catch (e) {
    return null;
  }
};

export const fetchPrebookFormContent = async (holidayReviewData) => {
  const request = await createBaseRequest();

  request.dynamicPackageId = holidayReviewData.dynamicPackageId;

  let url = `${BASE_HOLIDAY_SERVICE_URL}/package/travellerform/prebook/get`;
  if (holidayReviewData.fromAmendment) {
    url = `${BASE_HOLIDAY_SERVICE_URL}/amendment/dateChange/travellerform/prebook/get`;
  }
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  try {
    const reviewResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );
    const reviewResponseBody = await reviewResponse.json();
    return reviewResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchPackageReview = async (
  packageIds,
  requestId,
  oldQuoteRequestId,
  errorBody,
  reviewType,
  { holidayReviewData },
  modificationAllowed = false,
  reviewRequestSource
) => {
  const { trackingData = {}, source = '', requestType = '', docId = '' } = holidayReviewData || {};
  const { categoryTrackingEvent = '' } = trackingData || {};
  const pageName = `${REVIEW_PDT_PAGE_NAME}${
    reviewType === sectionCodes.PRESALES ? '-v1-Presales' : ''
  }`;
  const request = await createBaseRequest();
  const evar108 = `Holidays|${pageName}|${fetchAffiliateData()}${source ? `|${source}` : ''}`;
  request.evar79 = getPokusExpVarientKey(PokusLobs.HOLIDAY, 'NA');
  request.evar83 = HolidayDataHolder.getInstance().getBanner();
  request.evar57 = categoryTrackingEvent || '';
  request.evar108 = evar108 || '';
  const trackingInfoMap = {
    evar79: getPokusExpVarientKey(PokusLobs.HOLIDAY, 'NA'),
    evar83: HolidayDataHolder.getInstance().getBanner(),
    evar57: categoryTrackingEvent || '',
    source: source || '',
    evar108,
  };
  request.trackingInfoMap = trackingInfoMap;
  request.modificationAllowed = modificationAllowed;
  request.reviewRequestSource = reviewRequestSource;
  if (packageIds.dynamicPackageId) {
    request.dynamicPackageId = packageIds.dynamicPackageId;
  } else if (packageIds.reviewPackageId) {
    request.reviewPackageId = packageIds.reviewPackageId;
  }
  if (requestId) {
    request.quoteRequestId = requestId;
  }
  
  if (oldQuoteRequestId && reviewType === sectionCodes.PRESALES) {
    request.oldQuoteRequestId = oldQuoteRequestId;
  }
  if(holidayReviewData.trackStatus)
  {
    request.quoteTrackFlag=trackStatus;
  }
  if (reviewType) {
    request.reviewType = reviewType;
  }

  if (docId) {
    request.docId = docId;
  }
  let reviewUrl;
  if (reviewType === sectionCodes.PRESALES) {
    request.apiVersion = 2;
    reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/presales/review`;
  } else if (holidayReviewData?.fromAmendment) {
    reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/amendment/dateChange/review`;
  } else if (requestType === HOL_REQUEST_TYPE.DROP_OFF) {
    reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/dropoff/review`;
  } else {
    request.pageInfo=createBasePageRequest()
    reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/review/v2`;
  }
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  try {
    const reviewResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(reviewUrl, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(request),
      }),
    );

    if (!reviewResponse || !reviewResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const reviewResponseBody = await reviewResponse.json();
    if (
      !reviewResponseBody ||
      !reviewResponseBody.statusCode ||
      reviewResponseBody.statusCode === 0 ||
      !reviewResponseBody.success ||
      !reviewResponseBody.reviewDetail
    ) {
      if (reviewResponseBody && reviewResponseBody.error) {
        updateErrorBody(
          errorBody,
          reviewResponseBody.error.code,
          reviewResponseBody.error.message,
          pdtErrorSev.OTHER,
          PDT_ERROR_API,
        );
        return reviewResponseBody;
      }
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
      return null;
    }
    return reviewResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchFphPackageReview = async (packageIds, errorBody, criteria) => {
  const request = await createBaseRequest();
  if (packageIds.dynamicPackageId) {
    request.dynamicPackageId = packageIds.dynamicPackageId;
  } else if (packageIds.reviewPackageId) {
    request.reviewPackageId = packageIds.reviewPackageId;
  }

  if (criteria) {
    request.criteria = criteria;
  }

  const reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/fph/review/customizable`;
  try {
    const reviewResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(reviewUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!reviewResponse || !reviewResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const reviewResponseBody = await reviewResponse.json();
    if (
      !reviewResponseBody ||
      !reviewResponseBody.statusCode ||
      reviewResponseBody.statusCode === 0 ||
      !reviewResponseBody.success ||
      !reviewResponseBody.reviewDetail
    ) {
      if (reviewResponseBody && reviewResponseBody.error) {
        updateErrorBody(
          errorBody,
          reviewResponseBody.error.code,
          reviewResponseBody.error.message,
          pdtErrorSev.OTHER,
          PDT_ERROR_API,
        );
        return reviewResponseBody;
      }
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
      return null;
    }
    return reviewResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchValidateCoupon = async (dynamicPackageId, action, couponCode, fromPresales) => {
  const request = await createBaseRequest();
  if (fromPresales) {
    request.requestSource = sectionCodes.PRESALES;
  }
  request.dynamicPackageId = dynamicPackageId;
  request.action = action;
  request.coupon = couponCode;
  request.fromQuote = false;
  request.pageInfo=createBasePageRequest()

  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  const reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/review/coupon/select`;
  const errorBody = {};
  try {
    const headers = (await getRequestHeaders()) || {};
    const couponResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(reviewUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    if (!couponResponse || !couponResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const couponResponseBody = await couponResponse.json();
    if (
      !couponResponseBody ||
      !couponResponseBody.statusCode ||
      couponResponseBody.statusCode === 0 ||
      !couponResponseBody.success
    ) {
      if (couponResponseBody && couponResponseBody.error) {
        return couponResponseBody;
      }
      return null;
    }
    return couponResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};
export const fetchEmiOptions = async (dynamicPackageId, pagename) => {
  const request = await createBaseRequest();
  request.dynamicPackageId = dynamicPackageId;
  request.page = pagename;

  const reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/emi/details/fetch`;
  const errorBody = {};
  try {
    const headers = (await getRequestHeaders()) || {};
    const emiResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(reviewUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      }),
    );

    if (!emiResponse || !emiResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const emiResponseBody = await emiResponse.json();
    if (
      !emiResponseBody ||
      !emiResponseBody.statusCode ||
      emiResponseBody.statusCode === 0 ||
      !emiResponseBody.success
    ) {
      if (emiResponseBody && emiResponseBody.error) {
        return emiResponseBody;
      }
      return null;
    }
    return emiResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchZeroCancellationSelect = async (dynamicPackageId, action, zcType) => {
  const request = await createBaseRequest();
  request.dynamicPackageId = dynamicPackageId;
  request.action = action;
  request.zcType = zcType;
  request.lob = 'Holidays';
  request.funnel = 'HLD';
  request.affiliate = 'MMT';

  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/review/zeroCancellation/select`;
  const errorBody = {};
  try {
    const header = (await getRequestHeaders()) || {};
    // const sensorHeader = await botmanSensorHeader();
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: { ...header },
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const couponResponseBody = await response.json();
    if (
      !couponResponseBody ||
      !couponResponseBody.statusCode ||
      couponResponseBody.statusCode === 0 ||
      !couponResponseBody.success
    ) {
      if (couponResponseBody && couponResponseBody.error) {
        return couponResponseBody;
      }
      return null;
    }
    return couponResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const prebookSubmitDynamic = async (request, holidayReviewData) => {
  let prebookUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/travellerform/prebook/submit`;
  if (holidayReviewData?.fromAmendment) {
    prebookUrl = `${BASE_HOLIDAY_SERVICE_URL}/amendment/dateChange/travellerform/prebook/submit`;
  }
  const errorBody = {};
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  try {
    const prebookResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(prebookUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!prebookResponse || !prebookResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const prebookResponseBody = await prebookResponse.json();
    if (
      !prebookResponseBody ||
      !prebookResponseBody.statusCode ||
      prebookResponseBody.statusCode === 0 ||
      !prebookResponseBody.success
    ) {
      if (prebookResponseBody && prebookResponseBody.error) {
        return prebookResponseBody;
      }
      return null;
    }
    return prebookResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const prePaymentDynamic = async (request, holidayReviewData) => {
  let prePaymentUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/prepayment/v2`;
  if (holidayReviewData?.fromAmendment) {
    prePaymentUrl = `${BASE_HOLIDAY_SERVICE_URL}/amendment/dateChange/prepayment`;
  }

  const errorBody = {};
  request.pageInfo=createBasePageRequest()
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  try {
    const prePaymentResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(prePaymentUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!prePaymentResponse || !prePaymentResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const prePaymentResponseBody = await prePaymentResponse.json();
    if (
      !prePaymentResponseBody ||
      !prePaymentResponseBody.statusCode ||
      prePaymentResponseBody.statusCode === 0 ||
      !prePaymentResponseBody.success
    ) {
      if (prePaymentResponseBody && prePaymentResponseBody.error) {
        return prePaymentResponseBody;
      }
      return null;
    }
    return prePaymentResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

/**
 * Fetches the fare breakup details for a given dynamic package.
 *
 * @param {Object} props - The properties for the request.
 * @param {string} props.requestSource - The source of the request, defaults to HOLIDAYS_PACKAGE_REVIEW.
 * @param {string} props.dynamicPackageId - The dynamic package id for which the fare breakup is to be fetched.
 * @returns {Object|null} - The response object if successful, otherwise null.
 */
export const fetchFareBreakUp = async (props) => {
  const { requestSource = FARE_BREAKUP_REQUEST_SOURCE.HOLIDAYS_PACKAGE_REVIEW, dynamicPackageId } =
    props;
  // URL for the API endpoint
  let url = `${BASE_HOLIDAY_SERVICE_URL}/package/getFareBreakup`;
  // Request body
  const baseRequest = await createBaseRequest();
  const request = { requestSource, dynamicPackageId, ...baseRequest };

  try {
    // Fetch the fare breakup data with a timeout
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );
    // Check if the response is not OK
    if (!response?.ok) {
      return null;
    }
    // Parse the JSON response
    const res = await response.json();
    // Validate the response structure and status
    if (!res?.success || res.statusCode === 0) {
      // Return the error if present in the response
      return res?.error ? res : null;
    }
    // Return the successful response
    return res;
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const prePayment = async (request, holidayReviewData) => {
  let prePaymentUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/prepayment`;
  if (holidayReviewData?.fromAmendment) {
    prePaymentUrl = `${BASE_HOLIDAY_SERVICE_URL}/amendment/dateChange/prepayment`;
  }
  const errorBody = {};
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  try {
    const prePaymentResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(prePaymentUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!prePaymentResponse || !prePaymentResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const prePaymentResponseBody = await prePaymentResponse.json();
    if (
      !prePaymentResponseBody ||
      !prePaymentResponseBody.statusCode ||
      prePaymentResponseBody.statusCode === 0 ||
      !prePaymentResponseBody.success
    ) {
      if (prePaymentResponseBody && prePaymentResponseBody.error) {
        return prePaymentResponseBody;
      }
      return null;
    }
    return prePaymentResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchSelectPaymentOptionResponse = async (request) => {
  const selectedPaymentUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/review/payment/select`;
  request.pageInfo=createBasePageRequest()
  const errorBody = {};
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.REVIEW);
  try {
    const selectPaymentResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(selectedPaymentUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!selectPaymentResponse || !selectPaymentResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      return null;
    }
    const selectPaymentResponseBody = await selectPaymentResponse.json();
    if (
      !selectPaymentResponseBody ||
      !selectPaymentResponseBody.statusCode ||
      selectPaymentResponseBody.statusCode === 0 ||
      !selectPaymentResponseBody.success
    ) {
      if (selectPaymentResponseBody && selectPaymentResponseBody.error) {
        updateErrorBody(
          errorBody,
          selectPaymentResponseBody.error.code,
          selectPaymentResponseBody.error.message,
          pdtErrorSev.OTHER,
          PDT_ERROR_API,
        );
      } else {
        updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_API);
      }
      return null;
    }
    return selectPaymentResponseBody;
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.OTHER, PDT_ERROR_TIMEOUT);
    return null;
  }
};

const createGetExcludedDatesRequest = async (
  departurecityId,
  packageID,
  categoryID,
  rooms = [],
) => {
  const detailRequest = await createBaseRequest();
  detailRequest.categoryId = categoryID;
  detailRequest.departureCityId = departurecityId;
  detailRequest.packageId = packageID;
  if (!isEmpty(rooms)) {
    detailRequest.rooms = rooms;
  }
  return detailRequest;
};

const createTravellerFormRequest = async (requestObj) => {
  let validateRequest = await createBaseRequest();
  validateRequest = { ...validateRequest, ...requestObj, lob: 'Holidays' };
  return validateRequest;
};

export const getPackageExcludedDates = async (
  departureCityId,
  packageID,
  categoryID,
  rooms = [],
) => {
  try {
    const pathURL = '/packageCalendar/fetch/dates';
    const url = BASE_HOLIDAY_SERVICE_URL + pathURL;
    const response = await fetch(url, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(
        await createGetExcludedDatesRequest(departureCityId, packageID, categoryID, rooms),
      ),
    });
    if (!response) {
      return [];
    }
    const result = await response.json();
    if (result && result.packageAvailabilityDetail) {
      return result.packageAvailabilityDetail.availabilityRanges;
    }
    return result;
  } catch (e) {
    // Do Nothing
  }
  return null;
};

export const validateSinglePaxRequest = async (requestObj) => {
  try {
    const pathURL = '/package/travellerform/prebook/validatepax';
    const url = BASE_HOLIDAY_SERVICE_URL + pathURL;
    const response = await fetch(url, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(await createTravellerFormRequest(requestObj)),
    });
    if (!response) {
      return { success: false };
    }
    const result = await response.json();

    if (result && result.success) {
      return result;
    }
  } catch (e) {
    // Do Nothing
  }
  return { success: false };
};

export const fetchPaxData = async () => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/section/pax/guidelines`;
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers: await getRequestHeaders(),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    if (
      !responseBody ||
      !responseBody.statusCode ||
      responseBody.statusCode === 0 ||
      !responseBody.success
    ) {
      return null;
    }
    return responseBody;
  } catch (e) {
    console.log(e);
    return null;
  }
};

export const fetchDetailDataUsingRequest = async (holidayDetailData, roomDetails, errorBody) => {
  const detailUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/dynamic/details`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(detailUrl, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createDetailRequest(holidayDetailData, roomDetails)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        updateErrorBody(
          errorBody,
          detailResponseBody.error.code,
          detailResponseBody.error.message,
          pdtErrorSev.DETAIL,
          PDT_ERROR_API,
        );
        return detailResponseBody;
      }
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const updateItinerary = (detailResponseBody) => {
  const { packageDetail } = detailResponseBody || {};
  if (packageDetail?.itineraryDetail?.dynamicItinerary?.dayItinerariesV2?.length > 0) {
    packageDetail.itineraryDetail.dynamicItinerary.dayItineraries =
      packageDetail.itineraryDetail.dynamicItinerary.dayItinerariesV2;
  }
  return detailResponseBody;
};

export const undoPackage = async (dynamicPackageId, errorBody, requestSource = '') => {
  const detailUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/undo`;
  try {
    const baseRequest = await createBaseRequestWithRequestSource(requestSource);
    baseRequest.dynamicPackageId = dynamicPackageId;
    attachVersionInRequest(baseRequest, PAGE_TYPES_FOR_API.DETAIL);
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(detailUrl, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(baseRequest),
      }),
    );
    if (!response || !response.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    const detailResponseBody = await response.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        updateErrorBody(
          errorBody,
          detailResponseBody.error.code,
          detailResponseBody.error.message,
          pdtErrorSev.DETAIL,
          PDT_ERROR_API,
        );
        return detailResponseBody;
      }
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchReviewResponseUsingRequest = async (reviewRequest) => {
  const reviewUrl = `${BASE_HOLIDAY_SERVICE_URL}/bookingReview/review`;
  try {
    const reviewResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(reviewUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(reviewRequest),
      }),
    );
    if (!reviewResponse || !reviewResponse.ok) {
      return null;
    }
    const reviewResponseBody = await reviewResponse.json();
    if (
      !reviewResponseBody ||
      !reviewResponseBody.statusCode ||
      reviewResponseBody.statusCode !== 1
    ) {
      return null;
    }
    return reviewResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchRefreshDetailDataUsingRequest = async (
  holidayDetailData,
  dynamicDispatchId,
  roomDetails,
  isActionApiCalled,
  errorBody,
) => {
  const detailUrl = isActionApiCalled
    ? `${BASE_HOLIDAY_SERVICE_URL}/package/peek`
    : `${BASE_HOLIDAY_SERVICE_URL}/package/reload`;
  try {
    const data = await createRefreshDetailRequest(
      holidayDetailData,
      dynamicDispatchId,
      updateRoomDetailsForInfants(roomDetails),
      isActionApiCalled,
    );
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(detailUrl, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(
          await createRefreshDetailRequest(
            holidayDetailData,
            dynamicDispatchId,
            updateRoomDetailsForInfants(roomDetails),
            isActionApiCalled,
          ),
        ),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        updateErrorBody(
          errorBody,
          detailResponseBody.error.code,
          detailResponseBody.error.message,
          pdtErrorSev.DETAIL,
          PDT_ERROR_API,
        );
        return detailResponseBody;
      }
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const updateRoomDetailsForInfants = (roomDetails) => {
  const newRooms = cloneDeep(roomDetails);
  newRooms.forEach((element) => {
    if (element.listOfAgeOfChildrenWB.length > 0) {
      const tempArr = [];
      element.listOfAgeOfChildrenWB.forEach((item) => {
        if (item >= MIN_CHILD_AGE) {
          tempArr.push(item);
        }
      });
      element.listOfAgeOfChildrenWB = tempArr;
    }
  });
  return newRooms;
};

export const fetchSavedPackageDetailData = async (
  savePackageId = '',
  fphSavePackageObject = {},
  errorBody,
) => {
  const detailUrl = `${BASE_HOLIDAY_SERVICE_URL}/savepackage/v2/view`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(detailUrl, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(
          await createSavePackageDetailRequest(savePackageId, fphSavePackageObject),
        ),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        updateErrorBody(
          errorBody,
          detailResponseBody.error.code,
          detailResponseBody.error.message,
          pdtErrorSev.DETAIL,
          PDT_ERROR_API,
        );
        return detailResponseBody;
      }
      updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_API);
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    updateErrorBody(errorBody, null, null, pdtErrorSev.DETAIL, PDT_ERROR_TIMEOUT);
    return null;
  }
};

export const fetchCancellationDetails = async (dynamicPackageId) => {
  const fetchPackageShareRespUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/cancellationDetails`;
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(fetchPackageShareRespUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(requestObj),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    if (
      !responseBody ||
      !responseBody.statusCode ||
      responseBody.statusCode === 0 ||
      !responseBody.success
    ) {
      return null;
    }
    return responseBody;
  } catch (e) {
    return null;
  }
};

export const fetchCommuteChangeListing = async (dynamicPackageId) => {
  const commuteListingUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/commute/change/listing`;
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  requestObj.pageInfo=createBasePageRequest()
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(commuteListingUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(requestObj),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    if (
      !responseBody ||
      !responseBody.statusCode ||
      responseBody.statusCode === 0 ||
      !responseBody.success
    ) {
      return null;
    }
    return responseBody;
  } catch (e) {
    return null;
  }
};

export const fetchPackageDetailShareUrl = async (dynamicPackageId) => {
  const fetchPackageShareRespUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/share`;
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  requestObj.pageInfo = createBasePageRequest()
  try {
    const shareUrlResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(fetchPackageShareRespUrl, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(requestObj),
      }),
    );
    if (!shareUrlResponse || !shareUrlResponse.ok) {
      return null;
    }
    const shareUrlResponseBody = await shareUrlResponse.json();
    if (
      !shareUrlResponseBody ||
      !shareUrlResponseBody.statusCode ||
      shareUrlResponseBody.statusCode === 0 ||
      !shareUrlResponseBody.success ||
      !shareUrlResponseBody.url
    ) {
      return null;
    }
    return shareUrlResponseBody.url;
  } catch (e) {
    return null;
  }
};

export const fetchFlightBaggageInfo = async (dynamicPackageId) => {
  const body = {
    packageDetail: { dynamicId: dynamicPackageId, packageInclusionsDetail: { flights: true } },
  };
  const responseBody = await fetchPackageContent(body, true);
  if (responseBody && responseBody.success) {
    if (responseBody.flightContent.baggageInfoMap) {
      return responseBody.flightContent.baggageInfoMap;
    }
  }
  return null;
};

export const fetchReviewContent = async (dynamicPackageId) => {
  const body = {
    packageDetail: {
      dynamicId: dynamicPackageId,
      packageInclusionsDetail: { flights: true, carItinerary: true },
    },
  };
  const responseBody = await fetchPackageContent(body, true);
  if (responseBody && responseBody.success) {
    return responseBody;
  }
  return null;
};

export const fetchPackageContent = async (detailRespBody, isWG) => {
  try {
    const packageContentResponse = await fetchPackageContentUsingRequest(detailRespBody, isWG);
    if (!packageContentResponse) {
      return null;
    }
    return packageContentResponse;
  } catch (e) {
    return null;
  }
};

export const fetchHotelDetails = async (packageDetailDTO, hotelSellableId, hotelSequence) => {
  const detailUrl = `${BASE_HOLIDAY_SERVICE_URL}/hotel/change/details`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(detailUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(
          await createHotelDetailRequest(
            packageDetailDTO.dynamicPackageId,
            hotelSellableId,
            hotelSequence,
          ),
        ),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.hotelDetailsData
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return detailResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchHotelAutoSuggest = async (dynamicPackageId, hotelSequence, searchText) => {
  const detailUrl = `${BASE_HOLIDAY_SERVICE_URL}/hotel/change/autosuggest`;
  try {
    const autoSuggestResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(detailUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(
          await createHotelAutoSuggestRequest(dynamicPackageId, hotelSequence, searchText),
        ),
      }),
    );
    if (!autoSuggestResponse || !autoSuggestResponse.ok) {
      return null;
    }
    const responseBody = await autoSuggestResponse.json();
    if (
      !responseBody ||
      !responseBody.statusCode ||
      responseBody.statusCode === 0 ||
      !responseBody.success
    ) {
      if (responseBody && responseBody.error) {
        return responseBody;
      }
      return null;
    }
    return responseBody;
  } catch (e) {
    return null;
  }
};

export const packageComponentToggleRequest = async (actionData, packageComponent) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createToggleRequest(actionData, packageComponent)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const fetchFlightListing = async (dynamicId, flightRequestObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/flight/change/listing/v2`;
  try {
    const requestBody = await createFlightListingRequest(dynamicId, flightRequestObject);
    requestBody.pageInfo=createBasePageRequest()
    const flightListingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(requestBody),
      }),
    );
    if (!flightListingResponse || !flightListingResponse.ok) {
      return null;
    }
    const flightListingResponseBody = await flightListingResponse.json();
    if (
      !flightListingResponseBody ||
      !flightListingResponseBody.statusCode ||
      !flightListingResponseBody.success ||
      isNullOrEmptyCollection(flightListingResponseBody.flightListingData)
    ) {
      if (flightListingResponseBody && flightListingResponseBody.error) {
        return flightListingResponseBody;
      }
      return null;
    }
    return flightListingResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchFlightsPriceMap = async (dynamicId, flightRequestObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/flight/change/listing/updates`;
  try {
    const requestBody = await createFlightListingRequest(dynamicId, flightRequestObject);
    const flightsPriceMapResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(requestBody),
      }),
    );
    if (!flightsPriceMapResponse || !flightsPriceMapResponse.ok) {
      return null;
    }
    const flightsPriceMapResponseBody = await flightsPriceMapResponse.json();
    if (
      !flightsPriceMapResponseBody ||
      !flightsPriceMapResponseBody.statusCode ||
      !flightsPriceMapResponseBody.success ||
      isNullOrEmptyCollection(flightsPriceMapResponseBody.flightListingData)
    ) {
      if (flightsPriceMapResponseBody && flightsPriceMapResponseBody.error) {
        return flightsPriceMapResponseBody;
      }
      return null;
    }
    return flightsPriceMapResponseBody;
  } catch (e) {
    return null;
  }
};
export const changeAirportTransferRequest = async (packageDetailDTO) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/transfer/change/listing`;
  try {
    const changeTransferResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createChangeTransferRequest(packageDetailDTO.dynamicPackageId)),
      }),
    );
    if (!changeTransferResponse || !changeTransferResponse.ok) {
      return null;
    }
    const changeTransferResponseBody = await changeTransferResponse.json();
    if (
      !changeTransferResponseBody ||
      !changeTransferResponseBody.statusCode ||
      changeTransferResponseBody.statusCode === 0 ||
      !changeTransferResponseBody.success ||
      !changeTransferResponseBody.transfersListingData ||
      isNullOrEmptyCollection(
        changeTransferResponseBody.transfersListingData.airportTransferChangeList,
      )
    ) {
      if (changeTransferResponseBody && changeTransferResponseBody.error) {
        return changeTransferResponseBody;
      }
      return null;
    }
    return changeTransferResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchAddActivityListingResponse = async (packageDetailDTO, day, staySequence) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/change/listing`;
  try {
    const addActivityListingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(
          await createAddActivityListingRequest(
            packageDetailDTO.dynamicPackageId,
            day,
            staySequence,
          ),
        ),
      }),
    );
    if (!addActivityListingResponse || !addActivityListingResponse.ok) {
      return null;
    }
    const changeActivityResponseBody = await addActivityListingResponse.json();
    if (
      !changeActivityResponseBody ||
      !changeActivityResponseBody.statusCode ||
      changeActivityResponseBody.statusCode === 0 ||
      !changeActivityResponseBody.success ||
      !changeActivityResponseBody.activityListingData ||
      isNullOrEmptyCollection(changeActivityResponseBody.activityListingData.listingActivities)
    ) {
      if (changeActivityResponseBody && changeActivityResponseBody.error) {
        return changeActivityResponseBody;
      }
      return null;
    }
    return changeActivityResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchActivityListingResponse = async (reqObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/change/listing`;
  try {
    const activityListingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createAPIRequest(reqObject)),
      }),
    );
    if (!activityListingResponse || !activityListingResponse.ok) {
      return null;
    }
    const activityResponseBody = await activityListingResponse.json();
    if (
      !activityResponseBody ||
      !activityResponseBody.statusCode ||
      activityResponseBody.statusCode === 0 ||
      !activityResponseBody.success ||
      !activityResponseBody.activityListingData ||
      isNullOrEmptyCollection(activityResponseBody.activityListingData.listingActivities)
    ) {
      if (activityResponseBody && activityResponseBody.error) {
        return activityResponseBody;
      }
      return null;
    }
    return activityResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchActivityListingResponseForTidbits = async (reqObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/change/listing`;
  try {
    const activityListingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createAPIRequest(reqObject)),
      }),
    );
    if (!activityListingResponse || !activityListingResponse.ok) {
      return null;
    }
    const activityResponseBody = await activityListingResponse.json();
    if (
      !activityResponseBody ||
      !activityResponseBody.statusCode ||
      activityResponseBody.statusCode === 0 ||
      !activityResponseBody.success ||
      (isNullOrEmptyCollection(activityResponseBody.activityListingData?.listingActivities) &&
        isNullOrEmptyCollection(activityResponseBody.transferListingData?.listingActivities) &&
        isNullOrEmptyCollection(activityResponseBody.mealListingData?.listingActivities))
    ) {
      if (activityResponseBody && activityResponseBody.error) {
        return activityResponseBody;
      }
      return null;
    }
    return activityResponseBody;
  } catch (e) {
    return null;
  }
};
export const fetchActivityListingResponseForTidbitsV2 = async (reqObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/change/listing/v2`;
  try {
    const activityListingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createAPIRequest(reqObject)),
      }),
    );
    if (!activityListingResponse || !activityListingResponse.ok) {
      return null;
    }
    const activityResponseBody = await activityListingResponse.json();
    if (
      !activityResponseBody ||
      !activityResponseBody.statusCode ||
      activityResponseBody.statusCode === 0 ||
      !activityResponseBody.success ||
      (isNullOrEmptyCollection(activityResponseBody.activityListingData) &&
        isNullOrEmptyCollection(activityResponseBody.transferListingData) &&
        isNullOrEmptyCollection(activityResponseBody.mealListingData))
    ) {
      if (activityResponseBody && activityResponseBody.error) {
        return activityResponseBody;
      }
      return null;
    }
    return activityResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchActivityDetailsResponse = async (reqObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/change/details`;
  try {
    const viewActivityDetailsResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createAPIRequest(reqObject)),
      }),
    );
    if (!viewActivityDetailsResponse || !viewActivityDetailsResponse.ok) {
      return null;
    }
    const viewActivityDetailsResponseBody = await viewActivityDetailsResponse.json();
    if (
      !viewActivityDetailsResponseBody ||
      !viewActivityDetailsResponseBody.statusCode ||
      viewActivityDetailsResponseBody.statusCode === 0 ||
      !viewActivityDetailsResponseBody.success ||
      !viewActivityDetailsResponseBody.activity ||
      isNullOrEmptyCollection(viewActivityDetailsResponseBody.activity.activityOptions)
    ) {
      if (viewActivityDetailsResponseBody && viewActivityDetailsResponseBody.error) {
        return viewActivityDetailsResponseBody;
      }
      return null;
    }
    return viewActivityDetailsResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchViewActivityDetailsResponse = async (
  packageDetailDTO,
  activityCode,
  day,
  staySequence,
) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/change/details`;
  try {
    const viewActivityDetailsResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(
          await createViewActivityDetailsRequest(
            packageDetailDTO.dynamicPackageId,
            activityCode,
            day,
            staySequence,
          ),
        ),
      }),
    );
    if (!viewActivityDetailsResponse || !viewActivityDetailsResponse.ok) {
      return null;
    }
    const viewActivityDetailsResponseBody = await viewActivityDetailsResponse.json();
    if (
      !viewActivityDetailsResponseBody ||
      !viewActivityDetailsResponseBody.statusCode ||
      viewActivityDetailsResponseBody.statusCode === 0 ||
      !viewActivityDetailsResponseBody.success ||
      !viewActivityDetailsResponseBody.activity ||
      isNullOrEmptyCollection(viewActivityDetailsResponseBody.activity.activityOptions)
    ) {
      if (viewActivityDetailsResponseBody && viewActivityDetailsResponseBody.error) {
        return viewActivityDetailsResponseBody;
      }
      return null;
    }
    return viewActivityDetailsResponseBody;
  } catch (e) {
    return null;
  }
};

export const changeCarItineraryTransferRequest = async (packageDetailDTO, transferObj) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/carItinerary/change/listing`;
  try {
    const changeTransferResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(
          await createChangeTransferRequest(packageDetailDTO.dynamicPackageId, transferObj),
        ),
      }),
    );
    if (!changeTransferResponse || !changeTransferResponse.ok) {
      return null;
    }
    const changeTransferResponseBody = await changeTransferResponse.json();
    if (
      !changeTransferResponseBody ||
      !changeTransferResponseBody.statusCode ||
      changeTransferResponseBody.statusCode === 0 ||
      !changeTransferResponseBody.success ||
      !changeTransferResponseBody.carListingData
    ) {
      if (changeTransferResponseBody && changeTransferResponseBody.error) {
        return changeTransferResponseBody;
      }
      return null;
    }
    return changeTransferResponseBody;
  } catch (e) {
    return null;
  }
};

const createDetailRequest = async (holidayDetailData, roomDetails) => {
  let detailRequest = await createBaseRequest();
  detailRequest.travelDate = holidayDetailData.departureDetail.departureDate;
  if (holidayDetailData.selectedDate) {
    detailRequest.searchDate = holidayDetailData.selectedDate;
  }
  detailRequest.departureCityLocusCode = holidayDetailData.departureDetail.departureCityLocusCode;
  detailRequest.packageId = holidayDetailData.packageId;
  detailRequest.categoryId = holidayDetailData.categoryId;
  if (roomDetails) {
    detailRequest.rooms = roomDetails;
  }
  if (holidayDetailData.departureDetail.departureCity) {
    detailRequest.departureCity = holidayDetailData.departureDetail.departureCity;
  } else if (holidayDetailData.departureDetail.departureCityLocusCode) {
    detailRequest.departureCity = undefined;
  } else {
    detailRequest.departureCity = await getDepartureCity();
  }
  const requestMetaDetails = holidayDetailData?.requestMeta || {};

  attachVersionInRequest(detailRequest, PAGE_TYPES_FOR_API.DETAIL);
  detailRequest = {
    ...detailRequest,
    ...requestMetaDetails,
    pageInfo:createBasePageRequest()
  };
  return detailRequest;
};

const createPackageContentRequest = async (detailRespBody, isWG) => {
  const packageContentRequest = await createBaseRequest();
  packageContentRequest.dynamicPackageId = detailRespBody.packageDetail.dynamicId;
  const { packageDetail = {} } = detailRespBody || {};
  const { basePackageInclusionsDetail = {} } = packageDetail || {};
  const packageComponents = [];
  packageComponents.push('HOTEL');
  if (detailRespBody.packageDetail.packageInclusionsDetail) {
    if (detailRespBody.packageDetail.packageInclusionsDetail.carItinerary) {
      packageComponents.push('CAR_ITINERARY');
    }
    if (
      detailRespBody.packageDetail.packageInclusionsDetail.visa ||
      getBaseVisaCondition(basePackageInclusionsDetail)
    ) {
      packageComponents.push('VISA');
    }
    if (detailRespBody.packageDetail.packageInclusionsDetail.flights) {
      packageComponents.push('FLIGHT');
    }
  }
  if (isNotNullAndEmptyCollection(packageComponents)) {
    packageContentRequest.packageComponents = packageComponents;
  }
  return packageContentRequest;
};

const createSimilarPkgsRequest = async (detailRespBody, holidayDetailData) => {
  const similarPkgRequest = await createBaseRequest();
  similarPkgRequest.dynamicPackageId = detailRespBody.packageDetail.dynamicId;
  similarPkgRequest.departureCity = holidayDetailData.departureDetail.departureCity;
  if (similarPkgRequest.departureCity === NO_DEPARTURE_CITY) {
    similarPkgRequest.departureCity = await getDepartureCity();
  }
  return similarPkgRequest;
};

const createRefreshDetailRequest = async (
  holidayDetailData,
  dynamicDispatchId,
  roomDetails,
  isActionApiCalled = false,
) => {
  const detailRequest = await createBaseRequest();
  detailRequest.dynamicPackageId = dynamicDispatchId;
  attachVersionInRequest(detailRequest, PAGE_TYPES_FOR_API.DETAIL);
  if (!isEmpty(holidayDetailData.selectedDate)) {
    detailRequest.searchDate = holidayDetailData.selectedDate;
  }
  if (!isActionApiCalled) {
    detailRequest.travelDate = holidayDetailData.departureDetail.departureDate;
    detailRequest.rooms = roomDetails;
    detailRequest.pageInfo=createBasePageRequest()
    detailRequest.departureCityLocusCode = holidayDetailData.departureDetail.departureCityLocusCode;
    if (holidayDetailData.departureDetail.departureCity) {
      detailRequest.departureCity = holidayDetailData.departureDetail.departureCity;
    } else if (holidayDetailData.departureDetail.departureCityLocusCode) {
      detailRequest.departureCity = undefined;
    } else {
      detailRequest.departureCity = await getDepartureCity();
    }
  }

  return detailRequest;
};

const createSavePackageDetailRequest = async (savePackageId, fphSavePackageObject) => {
  const detailRequest = await createBaseRequest();
  if (savePackageId) {
       // Replace all spaces with '+' to ensure valid base64 for API
       detailRequest.savePackageId = savePackageId.replace(/ /g, '+');
  } else {
    detailRequest.dynamicPackageId = fphSavePackageObject.fphSavePackageId;
  }
  attachVersionInRequest(detailRequest, PAGE_TYPES_FOR_API.DETAIL);
  return detailRequest;
};

export const selectInsuranceAddOnPost = async (selectInsuranceRequestBody) => {
  const selectInsuranceUrl = `${BASE_HOLIDAY_SERVICE_URL}/addon/review/select`;
  try {
    const baseRequest = await createBaseRequest();
    const selectRequest = { ...baseRequest, ...selectInsuranceRequestBody };
    const packageContentResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(selectInsuranceUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(selectRequest),
      }),
    );
    if (!packageContentResponse || !packageContentResponse.ok) {
      return null;
    }
    const packageContentResponseBody = await packageContentResponse.json();
    if (
      !packageContentResponseBody ||
      !packageContentResponseBody.statusCode ||
      packageContentResponseBody.statusCode === 0 ||
      !packageContentResponseBody.success
    ) {
      return null;
    }
    return packageContentResponseBody;
  } catch (e) {
    return null;
  }
};

export const validateAddonRequestPost = async (validateAddonRequest) => {
  const validateAddOnUrl = `${BASE_HOLIDAY_SERVICE_URL}/addon/validateAddons`;
  try {
    const baseRequest = await createBaseRequest();
    const finalValidateAddOnRequest = { ...baseRequest, ...validateAddonRequest };

    const packageContentResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(validateAddOnUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(finalValidateAddOnRequest),
      }),
    );

    if (!packageContentResponse || !packageContentResponse.ok) {
      return null;
    }
    const packageContentResponseBody = await packageContentResponse.json();
    if (
      !packageContentResponseBody ||
      !packageContentResponseBody.statusCode ||
      packageContentResponseBody.statusCode === 0 ||
      !packageContentResponseBody.success
    ) {
      return null;
    }
    return packageContentResponseBody;
  } catch (e) {
    return null;
  }
};

/**
 * Creates a promise that rejects after a specified timeout.
 *
 * @param {number} ms - The number of milliseconds to wait before rejecting the promise.
 * @returns {Promise<never>} A promise that rejects with an error after the specified timeout.
 */
const createTimeout = (ms) =>
  new Promise((_, reject) => setTimeout(() => reject(new Error('Request timed out')), ms));

/**
 * Makes an API call with a POST request and handles timeout.
 *
 * @async
 * @function makeApiCall
 * @param {string} url - The URL of the API endpoint.
 * @param {Object} headers - The headers to be included in the API call.
 * @returns {Promise<Object>} The response body from the API call.
 * @throws {Error} If the response is not ok or if the request times out.
 */
const makeApiCall = async (url, baseRequest, headers, method) => {
  const fetchOptions = {
    method: method,
    headers,
  };
  if (baseRequest) {
    fetchOptions.body = JSON.stringify(baseRequest);
  }
  const response = await Promise.race([
    fetch(url, fetchOptions),
    createTimeout(DEFAULT_TIMEOUT),
  ]);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * Main API function to attach a gift card via API.
 *
 * @async
 * @function attachGiftCardViaApi
 * @returns {Promise<Object>} The response object containing the success status and any error message.
 */
export const attachGiftCardViaApi = async () => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/mmtBlack/attach/card`;
  try {
    const baseRequest = await createBaseRequest();
    const headers = await getRequestHeaders();

    const responseBody = await makeApiCall(url, null , headers,'GET');
    return validateApiResponse(responseBody);
  } catch (e) {
    console.error('Error in attachGiftCardViaApi:', e.message);
    return { success: false, error: { message: e.message } };
  }
};

/**
 * Validates the API response.
 *
 * @param {Object} responseBody - The response body from the API.
 * @param {number} [responseBody.statusCode] - The status code of the response.
 * @param {boolean} [responseBody.success] - Indicates if the response was successful.
 * @param {Object} [responseBody.error] - The error object from the API response.
 * @param {string} [responseBody.error.message] - The error message from the API response.
 * @returns {Object} The validated response body.
 * @throws {Error} If the response is invalid.
 */
const validateApiResponse = (responseBody) => {
  if (!responseBody?.statusCode || responseBody.statusCode === 0 || !responseBody.success) {
    const errorMessage = responseBody?.error?.message || 'Invalid response received';
    throw new Error(errorMessage);
  }
  return responseBody;
};

const fetchPackageContentUsingRequest = async (detailRespBody, isWG) => {
  const packageContentUrl = `${BASE_HOLIDAY_SERVICE_URL}/package/content/fetch`;
  const contentRequest = await createPackageContentRequest(detailRespBody, isWG);
  if (isWG && isNullOrEmptyCollection(contentRequest.packageComponents)) {
    return null;
  }
  try {
    const packageContentResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(packageContentUrl, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(contentRequest),
      }),
    );
    if (!packageContentResponse || !packageContentResponse.ok) {
      return null;
    }
    const packageContentResponseBody = await packageContentResponse.json();
    if (
      !packageContentResponseBody ||
      !packageContentResponseBody.statusCode ||
      packageContentResponseBody.statusCode === 0 ||
      !packageContentResponseBody.success
    ) {
      return null;
    }
    return packageContentResponseBody;
  } catch (e) {
    return null;
  }
};

const createToggleRequest = async (actionData, packageComponent) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionData;
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.TOGGLE,
    component: packageComponent,
    actionData,
  };
  requestObj.pageInfo = createBasePageRequest()
  if (packageComponent === packageActionComponent.ACTIVITY) {
    requestObj.componentAction.action = packageActions.REMOVE;
  }
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

const createChangeTransferRequest = async (dynamicPackageId, transferObj) => {
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  if (transferObj && transferObj.carItinerary) {
    requestObj.startDay = transferObj.startDay;
    requestObj.sellableId = transferObj.carItinerary.sellableId;
    requestObj.pageInfo=createBasePageRequest()
  }
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

const createAddActivityListingRequest = async (dynamicPackageId, day, staySequence) => {
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  requestObj.staySequence = staySequence;
  requestObj.day = day;
  requestObj.pageInfo=createBasePageRequest()
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

const createAPIRequest = async (reqObject) => {
  const requestObj = await createBaseRequest();
  return {
    ...requestObj,
    ...reqObject,
    pageInfo:createBasePageRequest()
  };
};
export const fetchActivityFilterV2 = async (reqObject) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/activity/metadata`
  try {
    const headers = await getRequestHeaders();
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body:JSON.stringify(await createAPIRequest(reqObject))
    });
    const data = await response.json();
    return data
  } catch (error) {
    return {
      success: false,
    }
  }
};

const createViewActivityDetailsRequest = async (
  dynamicPackageId,
  activityCode,
  day,
  staySequence,
) => {
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  requestObj.staySequence = staySequence;
  requestObj.activityCode = activityCode;
  requestObj.day = day;
  return requestObj;
};

const createFlightListingRequest = async (dynamicId, flightRequestObject) => {
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicId;
  requestObj.lob = 'Holidays';
  requestObj.flightSelections = flightRequestObject.flightSelections;
  requestObj.listingFlightSequence = flightRequestObject.listingFlightSequence;
  requestObj.overnightDelays = flightRequestObject.overnightDelays;
  return requestObj;
};

const createFlightBaggageInfoRequest = async (flightRequestObject) => {
  const requestObj = await createBaseRequest();
  return {
    ...requestObj,
    lob: 'Holidays',
    ...flightRequestObject,
  };
};

export const mealChangeRequest = async (actionData) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createMealChangeRequest(actionData)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const hotelChangeRequest = async (actionRequest) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createHotelChangeRequest(actionRequest)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const transferChangeRequest = async (actionData, packageComponent) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createTransferChangeRequest(actionData, packageComponent)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const fetchAddActivityResponse = async (actionData, packageComponent) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createActivityAddRequest(actionData, packageComponent)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const actionApi = async (requestBody) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  requestBody.pageInfo = createBasePageRequest()
  attachVersionInRequest(requestBody, PAGE_TYPES_FOR_API.DETAIL);
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(requestBody),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const fetchFlightBaggageDetail = async (flightRequestObject, change = true) => {
  const url = change
    ? `${BASE_HOLIDAY_SERVICE_URL}/flight/change/baggageDetail`
    : `${BASE_HOLIDAY_SERVICE_URL}/package/content/fetch`;
  try {
    const requestBody = await createFlightBaggageInfoRequest(flightRequestObject);
    if (change) {
      requestBody.pageInfo = createBasePageRequest();
    }
    const flightBaggageDetailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(requestBody),
      }),
    );
    if (!flightBaggageDetailResponse || !flightBaggageDetailResponse.ok) {
      return null;
    }
    const flightBaggageDetailResponseBody = await flightBaggageDetailResponse.json();
    if (
      !flightBaggageDetailResponseBody ||
      !flightBaggageDetailResponseBody.statusCode ||
      !flightBaggageDetailResponseBody.success ||
      isNullOrEmptyCollection(flightBaggageDetailResponseBody)
    ) {
      return null;
    }
    return flightBaggageDetailResponseBody;
  } catch (e) {
    return null;
  }
};

export const createFlightChangeRequest = async (actionRequest) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionRequest;
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.CHANGE,
    component: itineraryUnitTypes.FLIGHT,
    actionData: actionRequest,
  };
  requestObj.pageInfo = createBasePageRequest()
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

const createMealChangeRequest = async (actionData) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionData;
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.CHANGE,
    component: itineraryUnitTypes.MEAL,
    actionData: actionData,
  };
  requestObj.pageInfo = createBasePageRequest()
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

export const createHotelChangeRequest = async (actionRequest) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionRequest;
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.CHANGE,
    component: itineraryUnitTypes.HOTEL,
    actionData: actionRequest,
  };
  requestObj.pageInfo = createBasePageRequest()
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

const createTransferChangeRequest = async (actionData, packageComponent) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionData;
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.CHANGE,
    component: packageComponent,
    actionData: actionData,
  };
  requestObj.pageInfo = createBasePageRequest()
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

const createActivityAddRequest = async (actionData, packageComponent) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionData;
  if (requestSource) {
    requestObj.requestSource = requestSource;
  }
  requestObj.componentAction = {
    action: packageActions.ADD,
    component: packageComponent,
    actionData: actionData,
  };
  requestObj.pageInfo = createBasePageRequest()
  if (actionData.optionRecheckKey) {
    requestObj.componentAction.action = actionData.action;
  }
  attachVersionInRequest(requestObj, PAGE_TYPES_FOR_API.DETAIL);
  return requestObj;
};

export const createActivityModifyRequest = async (actionData, packageComponent) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionData || {};
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.MODIFY,
    component: packageComponent,
    actionData: actionData,
  };
  return requestObj;
};

export const createCommuteModifyRequest = async (actionData, packageComponent) => {
  const requestObj = await createBaseRequest();
  const { requestSource } = actionData || {};
  requestObj.requestSource = requestSource && requestSource;
  requestObj.componentAction = {
    action: packageActions.CHANGE,
    component: packageComponent,
    actionData,
  };
  return requestObj;
};

const createHotelDetailRequest = async (dynamicPackageId, hotelSellableId, hotelSequence) => {
  const detailRequest = await createBaseRequest();
  detailRequest.dynamicPackageId = dynamicPackageId;
  detailRequest.hotelSellableId = hotelSellableId;
  detailRequest.hotelSequence = hotelSequence;
  detailRequest.pageInfo=createBasePageRequest()
  return detailRequest;
};

const createHotelAutoSuggestRequest = async (dynamicPackageId, hotelSequence, searchText) => {
  const requestObj = await createBaseRequest();
  requestObj.dynamicPackageId = dynamicPackageId;
  requestObj.hotelSequence = hotelSequence;
  requestObj.searchText = searchText;
  return requestObj;
};

export const fetchUserDetails = async (mmtAuth, userDetailsRequest) => {
  try {
    const userDetailsResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(USER_DETAILS_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          authorization: 'h4nhc9jcgpAGIjp',
          deviceid: 'deviceId',
          'mmt-auth': mmtAuth,
          os: 'os',
          tid: 'deviceId',
          'user-identifier': JSON.stringify({
            ipAddress: 'ipAddress',
            imie: 'imie',
            appVersion: 'appVersion',
            deviceId: 'deviceId',
            os: 'Android',
            osVersion: 'osVersion',
            timeZone: 'timeZone',
            type: 'mmt-auth',
            value: mmtAuth,
          }),
          'usr-mcid': s.visitor.getMarketingCloudVisitorID(),
          vid: '',
          'visitor-id': '',
        },
        body: JSON.stringify(userDetailsRequest),
      }),
    );

    if (!userDetailsResponse || !userDetailsResponse.ok) {
      return null;
    }
    const userDetailsResponseBody = await userDetailsResponse.json();

    if (
      !userDetailsResponseBody ||
      !userDetailsResponseBody.result ||
      !userDetailsResponseBody.result.extendedUser
    ) {
      return null;
    }
    return userDetailsResponseBody.result.extendedUser;
  } catch (e) {
    return null;
  }
};

export const initDianaChat = async (initChatRequest) => {
  try {
    const { UserSessionModule } = NativeModules;
    const initChatResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(INIT_CHAT_URL, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          deviceid: s.visitor.getMarketingCloudVisitorID(),
          mmtauth: await UserSessionModule.getMmtAuth(),
        },
        body: JSON.stringify(initChatRequest),
      }),
    );

    if (!initChatResponse || !initChatResponse.ok) {
      return null;
    }
    const initChatResponseBody = await initChatResponse.json();

    if (!initChatResponseBody || !initChatResponseBody.conversation_id) {
      return null;
    }
    return initChatResponseBody.conversation_id;
  } catch (e) {
    return null;
  }
};

export const fetchBookingDetail = async (payId) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/package/booking/detail?payId=${payId}`;
    const bookingDetailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'GET',
        headers: await getRequestHeaders(),
      }),
    );

    if (!bookingDetailResponse || !bookingDetailResponse.ok) {
      return null;
    }
    const bookingDetailResponseBody = await bookingDetailResponse.json();

    if (
      !bookingDetailResponseBody ||
      !bookingDetailResponseBody.statusCode ||
      bookingDetailResponseBody.statusCode !== 1
    ) {
      return null;
    }
    return bookingDetailResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchQueueIdentifier = async (page) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/chat/allocation/detail`;

    const chatAllocationResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createChatAllocationRequest(page)),
      }),
    );
    if (!chatAllocationResponse || !chatAllocationResponse.ok) {
      return null;
    }

    const chatResponseBody = await chatAllocationResponse.json();

    if (
      !chatResponseBody ||
      !chatResponseBody.statusCode ||
      chatResponseBody.statusCode !== 1 ||
      !chatResponseBody.allocationDetail ||
      !chatResponseBody.allocationDetail.queueIdentifier
    ) {
      return null;
    }
    return chatResponseBody;
  } catch (e) {
    return null;
  }
};

export const createChatAllocationRequest = async (page) => {
  const requestObj = await createBaseRequest();
  requestObj.page = page;
  requestObj.pageInfo=createBasePageRequest()
  const campaign = HolidayDataHolder.getInstance().getCampaign();
  const cmp = HolidayDataHolder.getInstance().getCmp();
  if (!isEmpty(cmp)) {
    requestObj.marketingChannel = cmp;
  } else if (!isEmpty(campaign)) {
    requestObj.marketingChannel = campaign;
  }
  return requestObj;
};

export const fetchBranchLocatorData = async (fromCity = '') => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/branchlocator/details`;

    const branchLocatorResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(await createBranchLocatorRequest(fromCity)),
      }),
    );
    if (!branchLocatorResponse || !branchLocatorResponse.ok) {
      return null;
    }

    const branchResponseBody = await branchLocatorResponse.json();

    if (
      !branchResponseBody ||
      !branchResponseBody.statusCode ||
      branchResponseBody.statusCode !== 1 ||
      !branchResponseBody.branchDetails ||
      isNullOrEmptyCollection(branchResponseBody.branchDetails.details)
    ) {
      return null;
    }
    return branchResponseBody.branchDetails;
  } catch (e) {
    return null;
  }
};

const createBranchLocatorRequest = async (fromCity) => {
  const { HolidayModule } = NativeModules;
  const requestObj = await createBaseRequest();
  const depCity = await getDepartureCity();
  const kafkaDataMap = await HolidayModule.createKafkaGenericDataMap();
  if (kafkaDataMap && kafkaDataMap.appAndDeviceDetails) {
    if (kafkaDataMap.appAndDeviceDetails.lat != null) {
      requestObj.latitude = parseFloat(kafkaDataMap.appAndDeviceDetails.lat);
    }
    if (kafkaDataMap.appAndDeviceDetails.long != null) {
      requestObj.longitude = parseFloat(kafkaDataMap.appAndDeviceDetails.long);
    }
  }

  if (fromCity && fromCity !== NO_DEPARTURE_CITY) {
    requestObj.fromCity = fromCity;
  } else if (depCity && depCity !== NO_DEPARTURE_CITY) {
    requestObj.fromCity = depCity;
  } else {
    requestObj.fromCity = USER_DEFAULT_CITY;
  }
  return requestObj;
};

export const fetchCOVIDSafeData = async () => {
  let channel = '';
  if (isIosClient()) {
    channel = 'iphone';
  } else if (isAndroidClient()) {
    channel = 'android';
  } else {
    channel = 'RAW';
  }

  const url = `${BASE_HOLIDAY_SERVICE_URL}/content/covid/banner?channel=${channel}`;
  try {
    const covidResponse = await timeout(
      30000,
      fetch(url, {
        method: 'GET',
        headers: { ...(await getRequestHeaders()) },
      }),
    );

    if (!covidResponse || !covidResponse.ok) {
      return null;
    }
    const covidResponseBody = await covidResponse.json();
    //Status check
    return covidResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchChangeHotelList = async (
  dynamicPackageId,
  sequence,
  requestType,
  searchType,
  code,
) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/hotel/change/listing`;
    const request = await createBaseRequest();
    request.dynamicPackageId = dynamicPackageId;
    request.hotelSequence = sequence;
    request.requestType = requestType || '';
    request.pageInfo=createBasePageRequest()

    if (!isEmpty(searchType) && !isEmpty(code)) {
      request.searchType = searchType;
      request.code = code;
    }

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    return responseBody;
  } catch (e) {
    return null;
  }
};
export const fetchSMEDetails = async (profileId) => {
  try {
    if (isEmpty(profileId)) {
      return null;
    }
    const url = `${BASE_HOLIDAY_SERVICE_URL}/sme/profile/details`;
    const request = await createBaseRequest();
    request.profileId = profileId;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const fetchSimilarSME = async (profileId) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/sme/profile/similar`;
    const request = await createBaseRequest();
    request.profileId = profileId;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const fetchSMEPackages = async (profileId, offset) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/sme/packages`;
    const request = await createBaseRequest();
    request.smeId = profileId;
    request.offset = offset;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }

    return await response.json();
  } catch (e) {
    return null;
  }
};

export const fetchLandingMenu = async () => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/landing/menu`;
    const request = await createBaseRequest();
    request.lob = REQUEST_LOB;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const fetchMarkersAPI = async (holidayMapObject) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/map/marker/details`;
    const request = await createBaseRequest();
    request.criterias = holidayMapObject.criterias;

    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );

    if (!response || !response.ok) {
      return null;
    }

    const responseBody = await response.json();
    return responseBody;
  } catch (e) {
    return null;
  }
};

export const fetchHotelReviews = async ({ hotelCode, offset, ratingType = 'MMT' } = {}) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/hotel/reviews?htlId=${hotelCode}&type=${ratingType}&start=${offset}&affiliate=MMT&funnel=HLD&channel=B2CNLR&website=IN`;
  try {
    const response = await timeout(
      30000,
      fetch(url, {
        method: 'GET',
        headers: { ...(await getRequestHeaders()) },
      }),
    );

    if (!response || !response.ok) {
      return null;
    }
    const responseBody = await response.json();
    //Status check
    return responseBody;
  } catch (e) {
    return null;
  }
};

export const validateGuest = async (roomDetails, packageId, travelDate) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/validate/pax`;
  const reqObj = await createBaseRequest();
  reqObj.rooms = roomDetails;
  reqObj.packageId = packageId;
  reqObj.travelDate = travelDate;

  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(reqObj),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};

export const packageVariantsPromise = async (dynamicPackageId) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/dfd/package/variants`;
  const request = await createBaseRequest();
  request.dynamicPackageId = dynamicPackageId;
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.DETAIL);
  return fetch(url, {
    method: 'POST',
    headers: await getRequestHeaders(),
    body: JSON.stringify(request),
  });
};

export const packageMealsPromise = async (dynamicPackageId) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/dfd/meal/listing`;
  const request = await createBaseRequest();
  request.dynamicPackageId = dynamicPackageId;
  return fetch(url, {
    method: 'POST',
    headers: await getRequestHeaders(),
    body: JSON.stringify(request),
  });
};

export const packageVisaPromise = async (dynamicPackageId) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/visa/v2/listing`;
  const request = await createBaseRequest();
  request.dynamicPackageId = dynamicPackageId;
  return fetch(url, {
    method: 'POST',
    headers: await getRequestHeaders(),
    body: JSON.stringify(request),
  });
};

export const flightChangeRequest = async (actionRequest) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/action`;
  try {
    const detailResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getAPIRequestHeader({ 'backend-flags': 'of=1' }),
        body: JSON.stringify(await createFlightChangeRequest(actionRequest)),
      }),
    );
    if (!detailResponse || !detailResponse.ok) {
      return null;
    }
    const detailResponseBody = await detailResponse.json();
    if (
      !detailResponseBody ||
      !detailResponseBody.statusCode ||
      detailResponseBody.statusCode === 0 ||
      !detailResponseBody.success ||
      !detailResponseBody.packageDetail
    ) {
      if (detailResponseBody && detailResponseBody.error) {
        return detailResponseBody;
      }
      return null;
    }
    return updateItinerary(detailResponseBody);
  } catch (e) {
    return null;
  }
};

export const getParamValues = (param) => {
  if (isString(param)) {
    return param;
  } else if (isArray(param)) {
    return param.join('&');
  } else {
    return '';
  }
};
export const appendParamToUrl = (url, param) => {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}${getParamValues(param)}`;
};

export const convertUrlToHttps = (url) => {
  if (startsWith(url, 'https')) {
    return url;
  } else if (startsWith(url, 'http')) {
    return url.replace(/^http:\/\//i, 'https://');
  } else {
    return url;
  }
};

export const fetchComparisonOptions = async (ticketId, tagDestination, quoteIdsList) => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/presales/itineraries/compare`;
    const request = await getAPIRequestHeader({ 'backend-flags': 'of=1' });
    request.ticketId = ticketId;
    request.tagDestination = tagDestination;
    if (quoteIdsList) {
      request.quoteRequestIds = quoteIdsList;
    }
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};
export const fetch6EGroupingData = async (
  holidayLandingData,
  selectedFiltersList,
  newKeyRequired,
) => {
  let initialFilters = [];
  if (isNotNullAndEmptyCollection(holidayLandingData.filters)) {
    initialFilters = holidayLandingData.filters;
  }
  if (isNotNullAndEmptyCollection(selectedFiltersList)) {
    initialFilters = initialFilters.concat(selectedFiltersList);
  }
  const request = await createBaseRequest();
  const departureCity = await getDepartureCity();
  const disableUserGroup = getDisableUserGroupHol();
  const apiPokusListing = getApiPokusListing();
  request.lob = REQUEST_LOB;
  request.departureCity = holidayLandingData.departureCity || departureCity;
  request.destinationCity = holidayLandingData.destinationCity;
  request.departureLocusCode = holidayLandingData.departureLocusCode;
  request.destinationLocusCode = holidayLandingData.destinationLocusCode;
  request.criterias = initialFilters;
  request.offset = 0;
  request.fromDate = holidayLandingData.fromDate;
  request.toDate = holidayLandingData.toDate;
  request.packageDate = holidayLandingData.packageDate;

  request.disableUserGroup = disableUserGroup;
  request.affiliate = AFFILIATES.INDIGO;
  request.groupCriterias = [{ urlParam: 'tags', values: ['INDIGO'] }];
  request.pageSize = 1;

  if (holidayLandingData.campaign) {
    request.campaign = holidayLandingData.campaign;
  }
  if (holidayLandingData.rooms) {
    request.rooms = holidayLandingData.rooms;
  }
  if (apiPokusListing) {
    request.apiPokus = apiPokusListing;
  }

  const groupingUrl = `${BASE_HOLIDAY_SERVICE_URL}/listing/packages/grouped`;
  try {
    const groupingResponse = await timeout(
      DEFAULT_TIMEOUT,
      fetch(groupingUrl, {
        method: 'POST',
        headers: await getRequestHeaders(
          false,
          true,
          newKeyRequired,
          HOLIDAY_CORRELATION_GROUPING_KEY,
        ),
        body: JSON.stringify(request),
      }),
    );
    if (!groupingResponse || !groupingResponse.ok) {
      return null;
    }
    const groupingResponseBody = await groupingResponse.json();

    if (
      !groupingResponseBody ||
      !groupingResponseBody.statusCode ||
      groupingResponseBody.statusCode === 0 ||
      !groupingResponseBody.success ||
      !groupingResponseBody.packageDetails ||
      isNullOrEmptyCollection(groupingResponseBody.packageDetails)
    ) {
      return null;
    }
    return groupingResponseBody;
  } catch (e) {
    return null;
  }
};

export const fetchZCPolicyDetails = async (dynamicPackageId) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/cancellationDetails`;
  try {
    const request = await createBaseRequest();
    if (dynamicPackageId) {
      request.dynamicPackageId = dynamicPackageId;
    }
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: await getRequestHeaders(),
        body: JSON.stringify(request),
      }),
    );
    if (!response || !response.ok) {
      return null;
    }
    return await response.json();
  } catch (e) {
    return null;
  }
};
export const fetchVariantData = async ({ id, departureCity, depDate, rooms, variantId = '' }) => {
  const requestHeaders = await getRequestHeaders();

  const url = `${BASE_HOLIDAY_SERVICE_URL}/package/variants`;
  const request = await createBaseRequest();
  request.packageId = id;
  request.departureCity = departureCity;
  request.travelDate = depDate;
  request.variantId = variantId || '';
  attachVersionInRequest(request, PAGE_TYPES_FOR_API.DETAIL);
  if (rooms) {
    request.rooms = rooms;
  }
  try {
    const response = await timeout(
      DEFAULT_TIMEOUT,
      fetch(url, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(request),
      }),
    );
    const resp = await response.json();
    if (!response || !response.ok) {
      if (!isEmpty(resp)) {
        return resp;
      } else {
        return { error: resp };
      }
    }
    return await resp;
  } catch (e) {
    return { error: e };
  }
};

export const getInterventionData = async (data) => {
  try {
    const request = await createBaseRequestWOCmp();
    const {
      displayedInterventions,
      pageName,
      tagDestination,
      sessionCount,
      cmp,
      departureDate,
      packageId,
      roomData,
      campaign,
    } = data || {};
    if (displayedInterventions) {
      request.displayedInterventions = displayedInterventions;
    }
    if (departureDate) {
      request.departureDate = departureDate;
    }
    if (cmp) {
      request.cmp = cmp;
    }
    if (packageId) {
      request.packageId = packageId;
    }
    if (roomData) {
      request.rooms = roomData;
    }
    if (campaign) {
      request.campaign = campaign;
    }
    request.page = pageName;
    request.departureCity = await getDepartureCity();
    request.tagDestination = tagDestination;
    (request.campaignId = ''), (request.sessionId = 'uuid1'), (request.sessionNo = sessionCount);

    const url = `${BASE_HOLIDAY_SERVICE_URL}/intervention/v1/applicable`;
    const response = await fetch(url, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(request),
    });
    if (!response) {
      return null;
    }
    const result = await response.json();

    if (result && result.success) {
      return result;
    }
  } catch (e) {
    return { success: false };
  }
  return { success: false };
};
export const getAddonStaticDetail = async (additionalRequestParams) => {
  const url = `${BASE_HOLIDAY_SERVICE_URL}/addon/static/detail`;
  const [baseRequest, headers] = await Promise.all([createBaseRequest(), getRequestHeaders()]);
  const request = {
    ...baseRequest,
    ...additionalRequestParams,
  };
  const options = {
    method: 'POST',
    headers,
    body: JSON.stringify(request),
  };
  return { url, options };
};

export const getActiveIntervention = async () => {
  try {
    const url = `${BASE_HOLIDAY_SERVICE_URL}/intervention/v1/active`;
    const response = await fetch(url, {
      method: 'GET',
      headers: await getRequestHeaders(),
    });
    if (!response) {
      return null;
    }
    const result = await response.json();

    if (result && result.success) {
      return result;
    }
  } catch (e) {
    return { success: false };
  }
  return { success: false };
};

const getMetaUrl = () => {
  return `${BASE_HOLIDAY_SERVICE_URL}${HOLIDAY_LISTING_META_URL_NEW}`;
};

export const getLandingMetaData = async ({
  departure,
  destination,
  rooms,
  travelDate,
  campaign,
}) => {
  try {
    const request = await createBaseRequest();
    const url = `${BASE_HOLIDAY_SERVICE_URL}/landing/metadata`;
    if (departure) {
      request.departure = departure;
    }
    if (travelDate) {
      request.travelDate = travelDate;
    }
    if (destination) {
      request.destination = destination;
    }
    if (campaign) {
      request.campaign = campaign;
    }
    if (rooms) {
      request.rooms = rooms;
    }
    request.page = HLD_PAGE_NAME.LANDING;
    const response = await fetch(url, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(request),
    });
    if (!response) {
      return null;
    }
    const result = await response.json();
    if (result) {
      return result;
    }
  } catch (e) {
    return { success: false };
  }
  return { success: false };
};

export const saveRecentSearch = (holidayLandingData) => {
  const { destinationCityData } = holidayLandingData || {};
  const { destinationCity } = destinationCityData || {};
  const time = Date.now();
  if (destinationCity) {
    const cache = HolidayCache.getInstance().getCache();
    cache.set(destinationCity, { ...destinationCityData, time });
  }
};

export const getRecentSearchHeaderObj = () => {
  const RECENT_SEARCH = 'RECENT SEARCH';
  const NO_PACKAGE = 0;
  const HEADER = 'HEADER';

  return {
    name: RECENT_SEARCH,
    type: HEADER,
    packages: NO_PACKAGE,
    displayName: RECENT_SEARCH,
  };
};

/**
 * Retrieves the flag indicating whether the Holiday mmt black popup should be shown on review page.
 *
 * @returns A Promise that resolves to a boolean value.
 *          True if the popup should be shown, false otherwise.
 */
export const getMmtBlackReviewPagePopupFlag = async () => {
  // Get the singleton instance of HolidayCache and access its cache
  const cache = HolidayCache.getInstance().getCache();

  try {
    // Attempt to retrieve the flag value from the cache
    const value = await cache.get(MMT_BLACK_REVIEW_PAGE_POPUP_FLAG);

    // Return true only if the value is exactly true
    return value === true;
  } catch (error) {
    // Log any errors that occur during the retrieval process
    console.error('Error getting Holiday popup flag:', error);

    // Default to not showing the popup if there's an error
    return false;
  }
};

/**
 * Sets the flag indicating whether the Holiday mmt black popup should be shown on review page.
 *
 * @param shouldShow - Boolean value indicating if the popup should be displayed.
 */
export const setMmtBlackReviewPagePopupFlag = (shouldShow) => {
  // Get the singleton instance of HolidayCache and access its cache
  const cache = HolidayCache.getInstance().getCache();

  // Set the flag in the cache
  cache.set(MMT_BLACK_REVIEW_PAGE_POPUP_FLAG, shouldShow);
};

const isDataNotExpired = (date, DATA_AGE_IN_MONTHS) => {
  const UNIT_IN_MONTHS = 'months';
  const dataExpiryDate = moment().subtract(DATA_AGE_IN_MONTHS, UNIT_IN_MONTHS);
  return moment(date).isAfter(dataExpiryDate);
};

export const getRecentSearchObj = async () => {
  const MAX_DATA_AGE_IN_MONTHS = 6;
  const FORBIDDEN_KEYS = ['correlation_key', 'correlation_grouping_key', 'correlation_listing_key'];
  const cache = HolidayCache.getInstance().getCache();
  const data = [];
  const entries = await cache.getAll();
  Object.entries(entries).forEach(([k, v]) => {
    if (!FORBIDDEN_KEYS.includes(k)) {
      const { created, value } = JSON.parse(v) || {};
      if (created && isDataNotExpired(created, MAX_DATA_AGE_IN_MONTHS)) {
        const { destinationCity, displayNameOnSelect, type, branch, time } = value || {};
        if (destinationCity) {
          data.push({
            name: destinationCity,
            branch,
            type,
            displayName: displayNameOnSelect ? displayNameOnSelect : destinationCity,
            data: value,
            time,
            searchType: DEST_SEARCH_TYPES.RECENT_SEARCH,
          });
        }
      }
    }
  });
  const sortedData = data.sort((a, b) => {
    return a?.time < b?.time ? 1 : -1;
  });
  return sortedData;
};

export const getRecentSearchData = async () => {
  const recentSearchData = await getRecentSearchObj();
  const isDataValid = recentSearchData && recentSearchData.length > 0;
  return isDataValid ? [getRecentSearchHeaderObj(), ...recentSearchData] : [];
};

export const isAffiliateUser = () => {
  return isMobileClient() ? NetworkModule.isAffiliateUser() : !isEmpty(getAffiliate());
};

export const getAffiliateName = () => {
  return isMobileClient() ? NetworkModule.getAffiliateName() : getAffiliate();
};

export const fetchAffiliateData = () => {
  return isRawClient() && isAffiliateUser() ? getAffiliateName() : 'MMT';
};

export const attachVersionInRequest = (request = {}, pageType = '') => {
  if (isEmpty(request)) {
    return request;
  }
  if (getShowNewActivityDetail()) {
    request.verIdentifier = PAGE_API_VERSIONS?.[pageType] || '';
  }
  return request;
};

export const validateAddons = async (dynamicPackageId, cmp = 'email', cmpCreatedTime = Date.now()) => {
  try {
    const validateAddonsRequest = {
      ...(await createBaseRequest()),
      dynamicPackageId: dynamicPackageId,
      requestSource: 'PRE_SALES',
      campaignDetails: {
        cmp: cmp,
        cmpCreatedTime: cmpCreatedTime,
      },
    };

    const response = await fetch(`${BASE_HOLIDAY_SERVICE_URL}/addon/detail/validateAddons`, {
      method: 'POST',
      headers: await getRequestHeaders(),
      body: JSON.stringify(validateAddonsRequest),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    if (__DEV__) {
      console.error('validateAddons API error:', error);
    }
    return null;
  }
};
