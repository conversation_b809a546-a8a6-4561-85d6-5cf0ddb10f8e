import { generateUUID } from '@mmt/navigation/src/util';
export default class HolidayPDTMetaIDHolder {
    static sInstance = null;

    constructor() {
        this.meta_req_id = '';
        this.pdtId = ''; // Add a property to store the PDT ID
    }

    static getInstance() {
        if (HolidayPDTMetaIDHolder.sInstance == null) {
            HolidayPDTMetaIDHolder.sInstance = new HolidayPDTMetaIDHolder();
        }
        return this.sInstance;
    }


    // Method to set the PDT ID
    setPdtId() {
            this.pdtId = generateUUID()
    }

    // Method to get the PDT ID
    getPdtId() {
        return [this.pdtId];
    }

    // Other existing methods remain unchanged...
}
