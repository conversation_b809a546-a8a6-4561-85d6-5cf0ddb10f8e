import {
  <PERSON><PERSON>,
  Device<PERSON>ventEmitter,
  Dimensions,
  Linking,
  NativeModules,
  PermissionsAndroid,
  Platform,
  Share,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import _, {isBoolean, isUndefined} from 'lodash';
import fecha from 'fecha';
import isEmpty from 'lodash/isEmpty';
import isDate from 'lodash/isDate';
import {
  AFFILIATES,
  AVERAGE_RATING,
  BASE_IMAGE_PATH,
  BELOW_AVERAGE_RATING,
  CARD_IMAGE_SIZE,
  DEST_SEARCH_TYPES,
  DETAILS_DEPARTUTE_DATE_FORMAT,
  DOM_BRANCH,
  FAB_CTA_TYPE,
  FabT2QConfigDefaultData,
  FILTER_WEEKEND_GETAWAYS,
  FUNNELS,
  GOOD_RATING,
  HOLIDAY_PACKAGE_IMAGE_BASE_URL,
  IPHONE,
  LuxFabT2QConfigDefaultData,
  MAP_PARAMS,
  MAP_PARAMS_FULL_SCREEN,
  MAX_DESTINATION_CITIES,
  MAX_DESTINATIONS_COUNT,
  PACKAGE_TYPE_FIT,
  RAW_CHANNEL_CODE,
  RAW_PLATFORM_NAME,
  SERVICELINE_PHONE_NONDELHI,
  SUB_FUNNELS_TYPES,
  USER_DEFAULT_CITY,
  WEEKEND_GETAWAY_PAGE_TYPE,
} from '../HolidayConstants';
import {
  DEVICE_TYPE,
  getDataFromStorage,
  KEY_AFF_REF_ID,
  KEY_APP_LAUNCH_COUNT,
  KEY_CMP_META,
  KEY_HOL_CUES_STEPS_SHOWN,
  KEY_HOL_META,
  KEY_HOL_ONBOARDING_PAGE_VISIT,
  KEY_USER_DEP_CITY,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import {fetchPackageDetailShareUrl, fetchQueueIdentifier, getRequestHeaders,} from './HolidayNetworkUtils';
import {AbConfigKeyMappings, getPokusConfig,} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {getUserDetails, isUserLoggedIn} from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

import {
  getBudgetUniqueLabel,
  getDurationUniqueLabel,
  getGeneralTagsUniqueLabel,
  getHolidayTypeUniqueLabel,
  getHotelChoiceUniqueLabel,
  getPlacesUniqueLabel,
  getSuitableForUniqueLabel,
  getThemesUniqueLabel,
} from '../SearchWidget/utils/SearchWidgetUtil';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {deepLinkParams, extraInfoRefs, INCLUSIONS_TYPE, OBT_BRANCH,} from '../PhoenixDetail/DetailConstants';
import {PokusLobs} from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import LocationHelperModule from '@mmt/legacy-commons/Native/LocationHelperModule';
import HolidayDataHolder from './HolidayDataHolder';
import {CANCELLATION_OVERLAY, DATE_CHANGE_OVERLAY, PERSUASION_PAGE_NAME_REVIEW} from '../Review/HolidayReviewConstants';
import {addDays, diffDays, today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {HOLIDAY_ROUTE_KEYS, HolidayNavigation} from '../Navigation';
import {rupeeFormatterWithLocale} from '@mmt/legacy-commons/Helpers/currencyUtils';
import {findDaysBetweenDates, getNewDate} from '@mmt/legacy-commons/Common/utils/DateUtils';
import {
  getAIXCustomerCareNum,
  getCoachMarkDaysDelayHol,
  getDetailDefaultTabDayPlan,
  getGICustomerCareNum,
  getHDFCCustomerCareNum,
  getIGOCustomerCareNum,
  getOpenIndigoPackageoutApp,
  getShowFreebie,
  showNewCYSSection,
  showNewRVSSection
} from './HolidaysPokusUtils';
import {NO_LOCATION_PERM} from '@mmt/legacy-commons/Helpers/locationHelper';
import {sectionCodes} from '../LandingNew/LandingConstants';
import {getAffiliate, setAffiliate} from "../theme";
import {getdeviceType, setdeviceType} from './HolidayDevicesTypes';
import {TRACKING_EVENTS} from '../HolidayTrackingConstants';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import {CDN_IMAGES} from '../utils/HolidayImageUrls';
//import {DATE_CHANGE_TAB_INDEX, ZC_TAB_INDEX} from "../Detail/Components/HolidayCancellationOverlay";
const backarrowios = { uri: CDN_IMAGES.BACK_ARROW_IOS }

const backIconIosBlack = require('@mmt/legacy-assets/src/ic-back-i-os.webp');
const backIconAndroidBlack = require('@mmt/legacy-assets/src/backArrowAndroid.webp');

export const appendRupeeSymbol = (amount) => {
  try {
    return (`₹ ${amount}`);
  } catch (e) {
    return '';
  }
};

export const formatDate = (date, format) => {
  try {
    if (!date || date === '') {
      return '';
    }
    return fecha.format(getNewDate(date), format);
  } catch (e) {
    return '';
  }
};

export const convertDate = (date) => {
  let dd = date.getDate();

  let mm = date.getMonth() + 1;
  const yyyy = date.getFullYear();
  if (dd < 10) {
    dd = `0${dd}`;
  }

  if (mm < 10) {
    mm = `0${mm}`;
  }
  return (`${dd}-${mm}-${yyyy}`);
};

export const priceDiff = (oldPrice, newPrice, discountPriceFactor = 1) => {
  if (oldPrice > newPrice) {
    return `- ${appendRupeeSymbol(Math.ceil((oldPrice - newPrice) * discountPriceFactor))}`;
  }
  return `+ ${appendRupeeSymbol(Math.ceil((newPrice - oldPrice) * discountPriceFactor))}`;
};

export const getPriceDiff = (oldPrice, newPrice, discountPriceFactor = 1) => {
  if (oldPrice > newPrice) {
    return -1 * Math.round((oldPrice - newPrice) * discountPriceFactor);
  }
  return Math.round((newPrice - oldPrice) * discountPriceFactor);
};

export const getDiscountedPrice = (price, discountPriceFactor = 1) => {
  return Math.round(price * discountPriceFactor);
};
export const getOptimisedUrlForFastImage = (normalisedSource, dimensions) => {
  const DOWNSIZE_FACTOR = 3;
  const DOWNSIZE_QUERY = 'downsize';
  const DOWNSIZE_WITH_AMPERSAND = '&downsize=';
  const DOWNSIZE_WITH_QUESTION_MARK = '?downsize=';
  if (!normalisedSource || normalisedSource.includes(DOWNSIZE_QUERY)) {
    return normalisedSource;
  }
  const downsizeString = normalisedSource.includes('?')
    ? DOWNSIZE_WITH_AMPERSAND
    : DOWNSIZE_WITH_QUESTION_MARK;
  const { width, height } = dimensions || {};
  const normalisedCondition = !isEmpty(dimensions) && width > 0 && height > 0;
  const url = normalisedCondition
    ? `${normalisedSource}${downsizeString}${DOWNSIZE_FACTOR * width}:${DOWNSIZE_FACTOR * height}`
    : normalisedSource;
  return url;
};
export const getPlatformIdentifier = () => ((isIosClient()) ?
  IPHONE : ((Platform.OS === RAW_PLATFORM_NAME) ? RAW_CHANNEL_CODE : Platform.OS));

export const isMobileClient = () => isIosClient() || isAndroidClient();

/*
* We will be needing the url in case of logining in from RNWeb, as you go to native na d come to RNWeb.
* */
export const getReturnUrl = (dynamicPackageId) => (isRawClient() ?
  `${window.location.href}&dynamicPackageId=${dynamicPackageId}` : null);

export const getReturnUrlForQuotesListing = (ticketId, tagDestination) => (isRawClient() ?
    `${window.location.href}` : null);

export const getReturnUrlForQuotesDetail = (quoteRequestId) => (isRawClient() ?
    `${window.location.href}` : null);

export const isAndroidClient = () => Platform.OS === 'android';

export const isRawClient = () => Platform.OS === RAW_PLATFORM_NAME;

export const isIosClient = () => Platform.OS === 'ios';

export const showAlert = (message) => {
  if (isRawClient()) {
    showShortToast(message);
  } else {
    Alert.alert(message);
  }
};

export const getPackageImage = (cardDetails) => {
  if (cardDetails && cardDetails.imageDetails
    && cardDetails.imageDetails.mainImage
    && cardDetails.imageDetails.mainImage.fullPath
    && cardDetails.imageDetails.mainImage.name) {
    const imageUrl = cardDetails.imageDetails.mainImage.fullPath;
    return `${imageUrl}?${CARD_IMAGE_SIZE}`;
  }
  return HOLIDAY_PACKAGE_IMAGE_BASE_URL;
};
export const getPackageImageV2 = (cardDetails, resizeQuery) => {
  if (cardDetails && cardDetails.imageDetails
    && cardDetails.imageDetails.mainImage
    && cardDetails.imageDetails.mainImage.fullPath
    && cardDetails.imageDetails.mainImage.name) {
    const imageUrl = cardDetails.imageDetails.mainImage.fullPath;
    return `${imageUrl}?${resizeQuery}`;
  }
  return HOLIDAY_PACKAGE_IMAGE_BASE_URL;
};

export const getMainImage = (cardDetails) => {
  if (cardDetails && cardDetails.imageDetails
    && cardDetails.imageDetails.mainImage
    && cardDetails.imageDetails.mainImage.fullPath
    && cardDetails.imageDetails.mainImage.name) {
    return cardDetails.imageDetails.mainImage.fullPath;
  }
  return HOLIDAY_PACKAGE_IMAGE_BASE_URL;
};

export const getPackageImageForSimilarPackage = (cardDetails) => {
  if (cardDetails && cardDetails.imageDetail
    && cardDetails.imageDetail.mainImage
    && cardDetails.imageDetail.mainImage.fullPath
    && cardDetails.imageDetail.mainImage.name) {
    const imageUrl = cardDetails.imageDetail.mainImage.fullPath;
    return `${imageUrl}?${CARD_IMAGE_SIZE}`;
  }
  return HOLIDAY_PACKAGE_IMAGE_BASE_URL;
};

export const getDestinationsLabel = (destinationDetail) => {
  if (!destinationDetail
    || !destinationDetail.destinations
    || destinationDetail.destinations.length === 0) {
    return '';
  }
  let destinations = destinationDetail.destinations;
  if (destinationDetail.showCountryNames) {
    destinations = [];
    let name;
    let nights = 0;
    destinationDetail.destinations.forEach((element) => {
      if (!name) {
        name = element.countryName;
      }
      if (element.countryName !== name) {
        destinations.push({
          name,
          nights,
        });
        name = element.countryName;
        nights = 0;
      }
      nights += element.nights;
    });
    destinations.push({
      name,
      nights,
    });
  }
  let destinationLabel = '';
  let index = 0;
  destinations.forEach((destinationSeq) => {
    if (destinationSeq) {
      destinationLabel += `${destinationSeq.nights}N ${destinationSeq.name} . `;
      index++;
    }
  });
  return destinationLabel.substr(0, destinationLabel.length - MAX_DESTINATIONS_COUNT);
};

export const getDestinationsLabelForSimilarPackage = (destinationDetail) => {
  if (!destinationDetail
    || destinationDetail.length === 0) {
    return '';
  }
  let destinations = destinationDetail;
  if (destinationDetail.showCountryNames) {
    destinations = [];
    let name;
    let nights = 0;
    destinationDetail.destinations.forEach((element) => {
      if (!name) {
        name = element.countryName;
      }
      if (element.countryName !== name) {
        destinations.push({
          name,
          nights,
        });
        name = element.countryName;
        nights = 0;
      }
      nights += element.nights;
    });
    destinations.push({
      name,
      nights,
    });
  }
  let destinationLabel = '';
  let index = 0;
  destinations.forEach((destinationSeq) => {
    if (destinationSeq) {
      destinationLabel += `${destinationSeq.nights}N ${destinationSeq.name} . `;
      index++;
    }
  });
  return destinationLabel.substr(0, destinationLabel.length - MAX_DESTINATIONS_COUNT);
};

export const moreDestinationsCount = (destinationDetail) => {
  if (!destinationDetail
    || !destinationDetail.destinations
    || destinationDetail.destinations.length === 0) {
    return 0;
  }
  return destinationDetail.destinations.length > MAX_DESTINATION_CITIES ? destinationDetail.destinations.length - MAX_DESTINATION_CITIES : 0;
};

export const getDepartureCity = async () => {
  const depCity = await fetchUserDepCityFromStorage();
  const departureCity = isEmpty(depCity) ? USER_DEFAULT_CITY : depCity
  HolidayDataHolder.getInstance().setDepartureCity(departureCity);
  return departureCity;
};

export const fetchUserDepCityFromStorage = async () => {
  const responseBody = await getDataFromStorage(KEY_USER_DEP_CITY);
  return responseBody;
};


export const doCall = async (branch = 'DOM') => {
  let number = SERVICELINE_PHONE_NONDELHI;
  const getPhoneNumber = async(giPhoneNumbers) => {
        if (giPhoneNumbers !== '') {
         const phoneNumberArr = giPhoneNumbers.split(';');
          if (phoneNumberArr.length === 2) {
            for (let i = 0; i < 2; i += 1) {
              const phoneNumberEntity = phoneNumberArr[i].split(':');
              if ((phoneNumberEntity[0].toUpperCase() === DOM_BRANCH.toUpperCase()
                && (branch === null || branch.toUpperCase() === DOM_BRANCH.toUpperCase())) ||
                 (phoneNumberEntity[0].toUpperCase() === OBT_BRANCH.toUpperCase()
                  && branch !== null && branch.toUpperCase() === OBT_BRANCH.toUpperCase())) {
                number = phoneNumberEntity[1];
              }
            }
          }
       }
      }
  try {
    const holMetaObj = await getDataFromStorage(KEY_HOL_META);
    const aff = (holMetaObj && holMetaObj.affiliate) ? holMetaObj.affiliate : AFFILIATES.MMT;
    if (aff === AFFILIATES.GI) {
        const customerCarePhoneNumbers = getGICustomerCareNum();
        await getPhoneNumber(customerCarePhoneNumbers);
    } else if (aff === AFFILIATES.INDIGO) {
        const customerCarePhoneNumbers = getIGOCustomerCareNum();
        await getPhoneNumber(customerCarePhoneNumbers);
    } else if (aff === AFFILIATES.HDFC) {
        const customerCarePhoneNumbers = getHDFCCustomerCareNum();
        await getPhoneNumber(customerCarePhoneNumbers);
    } else if (aff === AFFILIATES.AIX) {
        const customerCarePhoneNumbers = getAIXCustomerCareNum();
        await getPhoneNumber(customerCarePhoneNumbers);
      }
  } catch (e) {
    console.error(e);
  }
  Linking.openURL(`tel:${number}`);
};

export const startReactChat = async (chatDto) => {
  const {
    destinationCity, branch, pageName, travelDate, filters, cmp, packageId, packageName  = '', dynamicPackageId,
    paxConfig, chatId, categoryTrackingEvent = '', eventData = {},
  } = chatDto;
  let mmtAuth = '';
  let email = '';
  let phoneNumber = '';
  let uuid = '';
  const userLoggedIn = await isUserLoggedIn();
  if (userLoggedIn) {
    const userDetails = await getUserDetails();
    if (userDetails) {
      mmtAuth = userDetails.mmtAuth;
      email = userDetails.email;
      uuid = userDetails.uuid ? userDetails.uuid : '';
      if (isMobileClient() && userDetails.mobile && userDetails.mobile.mobileNumber) {
        phoneNumber = userDetails.mobile.mobileNumber;
      }
    }
  }

  const requestHeaders = await getRequestHeaders();
  let deviceId = '';
  if (isAndroidClient()) {
    deviceId = requestHeaders.deviceid;
  } else if (isIosClient()) {
    deviceId = requestHeaders.deviceId;
  } else {
    deviceId = requestHeaders.visitorId;
  }
  const departureCity = await getDepartureCity();
  let identifier = '';
  const qIdentifier = await fetchQueueIdentifier(pageName);
  if (qIdentifier && qIdentifier.allocationDetail && qIdentifier.allocationDetail.queueIdentifier) {
    identifier = qIdentifier.allocationDetail.queueIdentifier;
  }
  let cmpValue = cmp;
  const campaign = HolidayDataHolder.getInstance().getCampaign();
  if (isEmpty(cmpValue) && !isEmpty(campaign)) {
    cmpValue = campaign;
  }

  const campaignDetails =  await getCMPMeta();


  if (isMobileClient()) {
    const {HolidayModule} = NativeModules;
    const params = {
      page: pageName,
      deviceId,
      branch,
      authToken: mmtAuth,
      appVersion: isRawClient() ? '' : (await fetchAppVersion()),
      dest: destinationCity,
      device: Platform.OS,
      emailId: email,
      depCity: departureCity,
      phoneNumber,
      travelDate,
      filters,
      uuid,
      chatId,
      cmp: cmpValue,
      packageId,
      packageName,
      dynamicPackageId,
      paxConfig: '2|0|0',
      eventData: {
        ...eventData,
        ...(qIdentifier?.experimentVariable && {
          experimentVariable: qIdentifier?.experimentVariable,
        }),
      },
      entityKey: destinationCity,
    };
    if (paxConfig) {
      params.paxConfig = paxConfig;
    }
    params.evar83 = HolidayDataHolder.getInstance().getBanner();
    params.evar57 = categoryTrackingEvent || '';
    if (!isEmpty(campaignDetails)){
      params.longSessionCmp = campaignDetails.cmp;
      params.longSessionCmpCreatedTime = campaignDetails?.cmpCreatedTime?.toString();
    }
    const source = HolidayDataHolder.getInstance().getSource();
    if(!isEmpty(source)) {
      infoAttrObj.source = source;
    }
    if (identifier) {
      params.queueIdentifier = identifier;
    }
    HolidayModule.openMyraChat(params);
  } else if (isRawClient()) {
    let myraUrl = 'https://myra.makemytrip.com/chat?channel=PWA&entityType=Funnel_Holiday';
    const holMetaObj = await getDataFromStorage(KEY_HOL_META);
    if(holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.INDIGO) {
      myraUrl = 'https://6eholidays.makemytrip.com/chat?channel=PWA&entityType=Funnel_Holiday';
    } else if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.GI) {
    myraUrl = 'https://giholidays.makemytrip.com/chat?channel=PWA&entityType=Funnel_Holiday';
    } else if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.HDFC) {
      myraUrl = 'https://hdfcsmartbuyholidays.makemytrip.com/chat?channel=PWA&entityType=Funnel_Holiday';
    } else if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.AIX) {
      myraUrl = 'https://airindiaexpressholidays.makemytrip.com/chat?channel=PWA&entityType=Funnel_Holiday';
    }
    myraUrl = `${myraUrl}&pageIdentifier=${chatDto.pageName}&entityKey=${destinationCity}`;
    if(holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.INDIGO) {
      myraUrl = `${myraUrl}&tenant=2`;
    } else if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.GI) {
      myraUrl = `${myraUrl}&tenant=1`;
    } else if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.HDFC) {
      myraUrl = `${myraUrl}&tenant=4`;
    } else if (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate) && holMetaObj.affiliate == AFFILIATES.AIX) {
      myraUrl = `${myraUrl}&tenant=8`;
    }
    let infoAttrObj = {};
    infoAttrObj.attr4 = identifier;
    infoAttrObj.attr3 = 'chat';
    infoAttrObj.attr2 = destinationCity;
    infoAttrObj.destination = destinationCity;
    infoAttrObj.fromCity = departureCity;
    infoAttrObj.attr1 = branch;
    infoAttrObj.pageName = pageName;
    infoAttrObj.device = 'mSite';
    infoAttrObj.chatId = chatId;
    infoAttrObj.evar83 = await updatedValuesforvariable_m_v83({ [TRACKING_EVENTS.M_V83]:HolidayDataHolder.getInstance().getBanner() })
    infoAttrObj.evar57 = categoryTrackingEvent;
    if (!isEmpty(cmpValue)) {
      infoAttrObj.evar17 = cmpValue;
      infoAttrObj.evar81 = cmpValue;
    }
    infoAttrObj.proactive = false;
    infoAttrObj = {
      ...infoAttrObj,
      ...eventData,
    };
    myraUrl = `${myraUrl}&infoAttr=${JSON.stringify(infoAttrObj)}`;
    window.location.href = myraUrl;
  } else {
    showShortToast('Some error occurred in opening Chat!. Please try again');
  }
};
export const  handleQueryFormDeeplink = async(queryDto) => {
  if (!isEmpty(queryDto) ) {
    const departureCity = await getDepartureCity();
    doQuery({...queryDto,departureCity});
  }
};

export const doQuery = async (queryDto, fromErrorPage = false) => {
  const { destinationCity } = queryDto;
  const departureCity = await getDepartureCity();
  HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.QUERY_FORM, {
    destination: destinationCity,
    searchDate: queryDto?.packageDate,
    departureCity,
    ...queryDto,
    fromErrorPage
  });
};

export const entryLocalNotification = (destinationCity, pageName) => {
  if (isAndroidClient()) {
    const {HolidayModule} = NativeModules;
    HolidayModule.entryLocalNotification({
      dest: destinationCity,
      page: pageName,
    });
  }
};

export const exitLocalNotification = (pageName) => {
  if (isAndroidClient()) {
    const {HolidayModule} = NativeModules;
    HolidayModule.exitLocalNotification({
      page: pageName,
    });
  }
};
export const openPackageDetailNewWithParams =
    (
       { packageDetails, userPackageMeta, packageEvent, packageCmp, searchCriteriaObj,
        lastPageName, refreshLanding, fromSeo, ptParam, images, affParam, host,params,categoryId, notFromDeeplink = true, rooms, selectedDate,
        trackingData = {},
      }
    ) => {
      let depDate = '';
      if (params){
        depDate = getNewDate(params.departureDate);
      }
      else {
        if (packageDetails && packageDetails.priceDetails
          && isNotNullAndEmptyCollection(packageDetails.priceDetails.categoryPrices)) {
        depDate = packageDetails.priceDetails.categoryPrices.filter(categoryPrice =>
            categoryPrice.categoryId ===
            packageDetails.categoryDetails.defaultCategoryId)[0].departureDate;
        depDate = fecha.format(getNewDate(depDate), DETAILS_DEPARTUTE_DATE_FORMAT);
      }
      }
      const holidaysDetailData = userPackageMeta || {
        packageId: params ? params.id : packageDetails.id,
        selectedDate,
        branch: packageDetails.branch,
        name: packageDetails.name,
        packageType: packageDetails.packageType,
        bundled:packageDetails.bundled,
        categoryId: params ? params.defaultCategoryId : categoryId,
        departureDetail: {
          departureCity:params ? params.departureCity : packageDetails.departureDetails.cityName,
          departureDate:params ? params.departureDate : depDate,
        },
        destinationDetail: {
          tagDestination: packageDetails.tagDestination,
          duration: params ? params.duration : packageDetails.nights,
        },
        images,
        storyEnabled: packageDetails.storyEnabled,
      };
      holidaysDetailData.pt = ptParam;
      holidaysDetailData.aff = affParam;

      if (((userPackageMeta && userPackageMeta.dynamicId) ||
          (packageDetails && packageDetails.packageType === PACKAGE_TYPE_FIT))) {
        holidaysDetailData.event = packageEvent;
        holidaysDetailData.cmp = packageCmp;
        holidaysDetailData.lastPageName = lastPageName;
        holidaysDetailData.searchCriteriaObj = searchCriteriaObj;
        holidaysDetailData.refreshLanding = refreshLanding;
        if (userPackageMeta && userPackageMeta.customizationDetail
            && userPackageMeta.customizationDetail.savePackageId) {
          holidaysDetailData.savePackageId = userPackageMeta.customizationDetail.savePackageId;
        }
        if (rooms) {
          holidaysDetailData.rooms = rooms;
        }
        if (fromSeo )  {
          const {
            packageId, categoryId, departureDetail, cmp, pt, aff,
          } = holidaysDetailData;
          const {departureCity, departureDate} = departureDetail;
          let query = `id=${packageId}`;
          if (departureCity) {
            query += `&fromCity=${departureCity}`;
          } else {
            query += '&fromCity=New Delhi';
          }
          if (categoryId) {
            query += `&category=${categoryId}`;
          }
          if (departureDate) {
            query += `&depDate=${departureDate}`;
          }
          if (cmp) {
            query += `&cmp=${cmp}`;
          }
          if (pt) {
            query += `&pt=${pt}`;
          }
          if (aff) {
            query += `&aff=${aff}`;
          }
          query += '&pkgType=FIT&fromSeo=true';
          window.location.href = `https://holidayz.makemytrip.com/holidays/international/package?${query}`;
        } else if (packageDetails && packageDetails.packageType === PACKAGE_TYPE_FIT && !isEmpty(host) && host == AFFILIATES.INDIGO) {
          const {
            packageId, categoryId, departureDetail, cmp, pt, aff,
          } = holidaysDetailData;
          const {departureCity, departureDate} = departureDetail;
          let query = `id=${packageId}`;
          if (departureCity) {
            query += `&fromCity=${departureCity}`;
          } else {
            query += '&fromCity=New Delhi';
          }
          if (categoryId) {
            query += `&category=${categoryId}`;
          }
          if (departureDate) {
            query += `&depDate=${departureDate}`;
          }
          if (cmp) {
            query += `&cmp=${cmp}`;
          }
          if (pt) {
            query += `&pt=${pt}`;
          }
          if (aff) {
            query += `&aff=${aff}`;
          }
      let openOutside = getOpenIndigoPackageoutApp();
          if (openOutside){
            query += '&open=outside';
          }
          query += '&pkgType=FIT';
          let urlValue = `https://6eholidays.makemytrip.com/holidays/international/package?${query}`;
          const { HolidayModule } = NativeModules;
          HolidayModule.handleWebDeeplink({ url: urlValue });
        } else {
          HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
          [deepLinkParams.deepLink]: false,
          notFromDeeplink,
          [deepLinkParams.deepLink]: false,
          holidaysDetailData,
          trackingData,
        });
        }
      } else {
        showAlert('This package is not available.');
      }
    };

export const openPackageDetailNew =
    (
        {packageDetails, userPackageMeta, packageEvent, packageCmp, searchCriteriaObj,
        lastPageName, refreshLanding, fromSeo, ptParam, images, affParam, host, rooms, notFromDeeplink = true, selectedDate,
        trackingData = '',detailsData = {}}
    ) => {
      let depDate = '';
      if (packageDetails && packageDetails.priceDetails
          && isNotNullAndEmptyCollection(packageDetails.priceDetails.categoryPrices)) {
        depDate = packageDetails.priceDetails.categoryPrices.filter(categoryPrice =>
            categoryPrice.categoryId ===
            packageDetails.categoryDetails.defaultCategoryId)[0].departureDate;
            depDate = getNewDate(depDate);
        depDate = fecha.format(depDate, DETAILS_DEPARTUTE_DATE_FORMAT);
      }
      const holidaysDetailData = userPackageMeta || {
        packageId: packageDetails.id,
        branch: packageDetails.branch,
        selectedDate,
        name: packageDetails.name,
        packageType: packageDetails.packageType,
        bundled:packageDetails.bundled,
        inclusionsDetail: {
          flights: packageDetails?.flightDetails?.included || false,
        },
        categoryId: packageDetails.categoryDetails.defaultCategoryId,
        departureDetail: {
          departureCity: packageDetails.departureDetails.cityName,
          departureDate: depDate,
        },
        destinationDetail: {
          tagDestination: packageDetails.tagDestination,
          duration: packageDetails.nights,
        },
        images,
        storyEnabled: packageDetails.storyEnabled,
        ...detailsData,
      };
      holidaysDetailData.pt = ptParam;
      holidaysDetailData.aff = affParam;
      if (((userPackageMeta && userPackageMeta.dynamicId) ||
          (packageDetails && packageDetails.packageType === PACKAGE_TYPE_FIT))) {
        holidaysDetailData.event = packageEvent;
        holidaysDetailData.cmp = packageCmp;
        holidaysDetailData.lastPageName = lastPageName;
        holidaysDetailData.searchCriteriaObj = searchCriteriaObj;
        holidaysDetailData.refreshLanding = refreshLanding;
        if (rooms){
          holidaysDetailData.rooms = rooms;
        }
        if (userPackageMeta && userPackageMeta.customizationDetail
            && userPackageMeta.customizationDetail.savePackageId) {
          holidaysDetailData.savePackageId = userPackageMeta.customizationDetail.savePackageId;
        }
        if (fromSeo) {
          const {
            packageId, categoryId, departureDetail, cmp, pt, aff,
          } = holidaysDetailData;
          const {departureCity, departureDate} = departureDetail;
          let query = `id=${packageId}`;
          if (departureCity) {
            query += `&fromCity=${departureCity}`;
          } else {
            query += '&fromCity=New Delhi';
          }
          if (categoryId) {
            query += `&category=${categoryId}`;
          }
          if (departureDate) {
            query += `&depDate=${departureDate}`;
          }
          if (cmp) {
            query += `&cmp=${cmp}`;
          }
          if (pt) {
            query += `&pt=${pt}`;
          }
          if (aff) {
            query += `&aff=${aff}`;
          }
          query += '&pkgType=FIT&fromSeo=true';
          window.location.href = `https://holidayz.makemytrip.com/holidays/international/package?${query}`;
        } else if (packageDetails && packageDetails.packageType === PACKAGE_TYPE_FIT && !isEmpty(host) && host == AFFILIATES.INDIGO) {
          const {
            packageId, categoryId, departureDetail, cmp, pt, aff,
          } = holidaysDetailData;
          const {departureCity, departureDate} = departureDetail;
          let query = `id=${packageId}`;
          if (departureCity) {
            query += `&fromCity=${departureCity}`;
          } else {
            query += '&fromCity=New Delhi';
          }
          if (categoryId) {
            query += `&category=${categoryId}`;
          }
          if (departureDate) {
            query += `&depDate=${departureDate}`;
          }
          if (cmp) {
            query += `&cmp=${cmp}`;
          }
          if (pt) {
            query += `&pt=${pt}`;
          }
          if (aff) {
            query += `&aff=${aff}`;
          }
      let openOutside = getOpenIndigoPackageoutApp();
          if (openOutside){
            query += '&open=outside';
          }
          query += '&pkgType=FIT';
          let urlValue = `https://6eholidays.makemytrip.com/holidays/international/package?${query}`;
          const { HolidayModule } = NativeModules;
          HolidayModule.handleWebDeeplink({ url: urlValue });
        } else {
          HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
          [deepLinkParams.deepLink]: false,
          notFromDeeplink,
          [deepLinkParams.deepLink]: false,
          holidaysDetailData,
          trackingData,
        });
        }
      } else {
        showAlert('This package is not available.');
      }
    };

export const openPackageDetail =
  (
    packageDetails, userPackageMeta, packageEvent, packageCmp, searchCriteriaObj,
    lastPageName, refreshLanding, fromSeo, ptParam, images, affParam,notFromDeeplink = true, trackingData = {}
  ) => {
    let depDate = '';
    if (packageDetails && packageDetails.priceDetails
      && isNotNullAndEmptyCollection(packageDetails.priceDetails.categoryPrices)) {
      depDate = packageDetails.priceDetails.categoryPrices.filter(categoryPrice =>
        categoryPrice.categoryId ===
        packageDetails.categoryDetails.defaultCategoryId)[0].departureDate;
      depDate = fecha.format(getNewDate(depDate), DETAILS_DEPARTUTE_DATE_FORMAT);
    }
    const holidaysDetailData = userPackageMeta || {
      packageId: packageDetails.id,
      branch: packageDetails.branch,
      name: packageDetails.name,
      packageType: packageDetails.packageType,
      bundled:packageDetails.bundled,
      inclusionsDetail: {
        flights: packageDetails?.flightDetails?.included || false,
      },
      categoryId: packageDetails.categoryDetails.defaultCategoryId,
      departureDetail: {
        departureCity: packageDetails.departureDetails.cityName,
        departureDate: depDate,
      },
      destinationDetail: {
        tagDestination: packageDetails.tagDestination,
        duration: packageDetails.nights,
      },
      images,
      storyEnabled: packageDetails.storyEnabled,
    };
    holidaysDetailData.pt = ptParam;
    holidaysDetailData.aff = affParam;
    if (((userPackageMeta && userPackageMeta.dynamicId) ||
      (packageDetails && packageDetails.packageType === PACKAGE_TYPE_FIT))) {
      holidaysDetailData.event = packageEvent;
      holidaysDetailData.cmp = packageCmp;
      holidaysDetailData.lastPageName = lastPageName;
      holidaysDetailData.searchCriteriaObj = searchCriteriaObj;
      holidaysDetailData.refreshLanding = refreshLanding;
      if (userPackageMeta && userPackageMeta.customizationDetail
        && userPackageMeta.customizationDetail.savePackageId) {
        holidaysDetailData.savePackageId = userPackageMeta.customizationDetail.savePackageId;
      }
      if (fromSeo || (isRawClient() &&isMMTMainDomain({ url: window.location.href }))) {
        const {
          packageId, categoryId, departureDetail, cmp, pt, aff,
        } = holidaysDetailData;
        const {departureCity, departureDate} = departureDetail;
        let query = `id=${packageId}`;
        if (departureCity) {
          query += `&fromCity=${departureCity}`;
        } else {
          query += '&fromCity=New Delhi';
        }
        if (categoryId) {
          query += `&category=${categoryId}`;
        }
        if (departureDate) {
          query += `&depDate=${departureDate}`;
        }
        if (cmp) {
          query += `&cmp=${cmp}`;
        }
        if (pt) {
          query += `&pt=${pt}`;
        }
        if (aff) {
          query += `&aff=${aff}`;
        }
        if(fromSeo)query += '&pkgType=FIT&fromSeo=true';
        window.location.href = `https://holidayz.makemytrip.com/holidays/international/package?${query}`;
      } else {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
          [deepLinkParams.deepLink]: false,
          notFromDeeplink,
          [deepLinkParams.deepLink]: false,
          holidaysDetailData,
          trackingData,
        });
      }
    } else {
      showAlert('This package is not available.');
    }
  };
export const isNullOrEmptyCollection = collection => !collection || collection.length === 0;

export const isNotNullAndEmptyCollection = collection => collection && collection.length > 0;

export const getUserMetaPackageImage = (cardDetails) => {
  if (cardDetails && cardDetails.imageDetail
    && cardDetails.imageDetail.mainImage
    && cardDetails.imageDetail.mainImage.fullPath
    && cardDetails.imageDetail.mainImage.name) {
    const imageUrl = cardDetails.imageDetail.mainImage.fullPath;
    return `${imageUrl}?${CARD_IMAGE_SIZE}`;
  }
  return HOLIDAY_PACKAGE_IMAGE_BASE_URL;
};

export const getUserMetaDestinationsLabel = (destinationDetail) => {
  if (!destinationDetail
    || !destinationDetail.destinations
    || destinationDetail.destinations.length === 0) {
    return '';
  }
  let destinationLabel = '';
  let index = 0;
  destinationDetail.destinations.forEach((destination) => {
    if (index > MAX_DESTINATIONS_COUNT) {
      return true;
    }
    if (destination) {
      destinationLabel += `${destination.duration}N ${destination.destination} . `;
      index++;
    }
  });
  return destinationLabel.substr(0, destinationLabel.length - MAX_DESTINATIONS_COUNT);
};

export const updateSelectedMonthsList = (selectedTravellingMonthUniqueId, travellingMonthsOptions) => {
  let foundActive = false;
  let activated = false;
  let fromDate,
    fromDateFound = false,
    first = true;
  const selectedMonthsWithDate = {};
  for (const month in travellingMonthsOptions) {
    if (!activated) {
      if (travellingMonthsOptions[month].isActive === true && first) {
        foundActive = true;
        fromDateFound = true;
        first = false;
        selectedMonthsWithDate.fromDate = travellingMonthsOptions[month].fromDate;
      }
    } else {
      travellingMonthsOptions[month].isActive = false;
    }

    if (travellingMonthsOptions[month].uniqueId === selectedTravellingMonthUniqueId) {
      travellingMonthsOptions[month].isActive = true;
      selectedMonthsWithDate.toDate = travellingMonthsOptions[month].toDate;
      fromDate = travellingMonthsOptions[month].fromDate;
      activated = true;
    }

    if (!activated && foundActive) {
      travellingMonthsOptions[month].isActive = true;
    }
  }
  if (!foundActive && !fromDateFound) {
    selectedMonthsWithDate.fromDate = fromDate;
  }

  selectedMonthsWithDate.travellingMonthsOptions = travellingMonthsOptions;

  return selectedMonthsWithDate;
};

export const createSearchCriteriaAppliedMap = (holidayDetailData) => {
  const searchCriteriaAppliedMap = {};
  if (holidayDetailData && holidayDetailData.searchCriteriaObj) {
    const {destinationCity} = holidayDetailData.searchCriteriaObj;
    let destinationList = [];
    if (destinationCity) {
      destinationList = destinationCity.split(',');
    }
    const filterIdNameMap = holidayDetailData.searchCriteriaObj.filterIdMap;
    const {criterias} = holidayDetailData.searchCriteriaObj;
    if (criterias && filterIdNameMap) {
      const filterNameValuesMap = getFilterNameValuesMap(criterias, filterIdNameMap);

      let values = filterNameValuesMap.get(getBudgetUniqueLabel());
      searchCriteriaAppliedMap.budget = values;
      values = filterNameValuesMap.get(getThemesUniqueLabel());
      searchCriteriaAppliedMap.themes = values;
      values = filterNameValuesMap.get(getSuitableForUniqueLabel());
      searchCriteriaAppliedMap.suitableFor = values;
      values = filterNameValuesMap.get(getHolidayTypeUniqueLabel());
      searchCriteriaAppliedMap.holidayType = values;
      values = filterNameValuesMap.get(getGeneralTagsUniqueLabel());
      searchCriteriaAppliedMap.generalTags = values;
      values = filterNameValuesMap.get(getDurationUniqueLabel());
      searchCriteriaAppliedMap.duration = values;
      values = filterNameValuesMap.get(getHotelChoiceUniqueLabel());
      searchCriteriaAppliedMap.hotelChoice = values;
      values = filterNameValuesMap.get(getPlacesUniqueLabel());
      if (values) {
        destinationList = destinationList.concat(values);
      }
      searchCriteriaAppliedMap.dest_list = destinationList;
    }
  }
  return searchCriteriaAppliedMap;
};

export const openSeoQueryDeepLink = (dest, branch) => {
  window.location.href = `https://holidayz.makemytrip.com/holidays/sendQuery30!packageSendQueryForm?dest=${dest}&Branch=${branch}&fromSeo=true`;
};

const getFilterNameValuesMap = (criterias, filterIdNameMap) => {
  const filterNameValuesMap = new Map();
  for (let index = 0; index < criterias.length; index += 1) {
    const filterName = getFilterName(filterIdNameMap, criterias[index].id);
    filterNameValuesMap.set(filterName, criterias[index].values);
  }
  return filterNameValuesMap;
};

const getFilterName = (filterIdNameMap, filterId) => {
  const objKeys = Object.keys(filterIdNameMap);
  for (let index = 0; index < objKeys.length; index += 1) {
    if (parseInt(objKeys[index], 10) === filterId) {
      return filterIdNameMap[objKeys[index]];
    }
  }
};


export const getSurgeKey = (surgeFactor) => {
  if (surgeFactor % 1 === 0) {
    return Math.trunc(surgeFactor);
  }
  return surgeFactor;
};

export const getWalletSurgetTimeLeft = (expiryTime) => {
  const timeLeftInMillis = new Date(expiryTime).getTime() - new Date().getTime();
  const {
    days, hours, minutes, seconds,
  } = getRemainingTimeParts(timeLeftInMillis);
  if (days > 0) {
    return `${days} ${getQuantityString(days, 'day')} ${hours} ${getQuantityString(hours, 'hour')} ${
      minutes} ${getQuantityString(minutes, 'minute')}`;
  } else if (hours > 0) {
    return `${hours} ${getQuantityString(hours, 'hour')} ${
      minutes} ${getQuantityString(minutes, 'minute')}`;
  }
  return `${minutes} ${getQuantityString(minutes, 'minute')}`;
};

const getQuantityString = (quantity, unit) => (quantity > 1 ? `${unit}s` : unit);

export const fetchUserDetails = async () => {
  const userDetails = await getUserDetails();
  return userDetails;
};

export const fetchAppVersion = async () => {
  const {GenericModule} = NativeModules;
  const appVersion = await GenericModule.getAppVersion();
  return appVersion;
};

export const createChatID = (appendPrefix = true) => {
  const time = Date.now();
  var minm = 100000;
  var maxm = 999999;
  const randomNumber = Math.floor(Math.random() * (maxm - minm + 1)) + minm;
  const prefix = appendPrefix ? 'HLD:' : '';
  const result = prefix + 'chatId_' + time + '_' + randomNumber;
  return result;
};

export const getOmniturePageName = (branch, pageName) => `mob:funnel:${branch} holidays:${pageName}`;
export const isHolidaysPage = page => page && (page === 'holidaysGrouping'
    || page === 'holidaysLanding'
    || page === 'holidaysListing'
    || page === 'holidaysDetail'
    || page === 'holidaysSearchWidget'
    || page === 'holidaysReview'
    || page === 'holidaysQueryForm');

export const createRandomString = () => Math.random()
  .toString()
  .split('.')[1];

export const createImagePath = (name, path, size, fullPath) => {
  if (fullPath) {
    if (fullPath.indexOf('rimgak') !== -1 && fullPath.indexOf('?') === -1) {
      return `${fullPath}?${CARD_IMAGE_SIZE}`;
    }
    return fullPath;
  } else if (name && path && size) {
    return `${BASE_IMAGE_PATH + path}/${name.split('.')[0]}${size}.${name.split('.')[1]}`;
  } else if (path) {
    return path;
  }
  return BASE_IMAGE_PATH;
};

export const sharePackage = async ({tagDestination, dynamicId}) => {
  const url = await fetchPackageDetailShareUrl(dynamicId);
  if (tagDestination && tagDestination.name && url) {
    const message = tagDestination.name;
    if (isIosClient()) {
      Share.share({
        message,
        url,
      }, {
        // iOS only:
        subject: tagDestination.name,
      })
        .catch();
    } else {
      const {HolidayModule} = NativeModules;
      HolidayModule.sharePackageDetail({
        TAG_DESTINATION: tagDestination.name,
        PACKAGE_URL: url,
      });
    }
  }
};

export const shareScreenShot = () => {
  if (isIosClient()) {
    ViewControllerModule.shareScreenShot();
  } else {
    const {HolidayModule} = NativeModules;
    HolidayModule.shareScreenShot();
  }
};

export const goToHolidayLanding = () => {
  const {HolidayModule} = NativeModules;
  HolidayModule.goToAppHome();
};

export const removeHTMLTags = text => (
  text?.replace(/(<([^>]+)>)/ig, '')
);

export const isEmptyString = string => !string;

export const containsStringIgnoreCase = (arr, str) => {
  if (isNotNullAndEmptyCollection(arr) && str) {
    const strU = str.toUpperCase();
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].toUpperCase() === strU) {
        return true;
      }
    }
  }
  return false;
};

export const filterListIgnoreCase = (arr = [], str) => {
  if (isEmptyString(str)) {
    return [...arr];
  }
  const strU = str?.toUpperCase();
  return arr.filter(item => item?.toUpperCase().startsWith(strU));
};

export const createCtaOpts = (showCall, showQuery, showChat) => {
  let ctaOpts = '';
  if (showCall) {
    ctaOpts += 'C';
  }
  if (showQuery) {
    ctaOpts += 'Q';
  }
  if (showChat) {
    ctaOpts += 'Ch';
  }
  return ctaOpts;
};

export const getDaysDiff = (startTime, endTime) => {
  const msPerDay = 1000 * 60 * 60 * 24;
  const utc1 = Date.UTC(startTime.getFullYear(), startTime.getMonth(), startTime.getDate());
  const utc2 = Date.UTC(endTime.getFullYear(), endTime.getMonth(), endTime.getDate());
  return Math.floor((utc2 - utc1) / msPerDay);
};

export const getThemeFilterName = () => {
  if (isIosClient()) {
    return 'themes_iphone';
  } else if (isAndroidClient()) {
    return 'themes_android';
  } else if (isRawClient()) {
    return 'themes_android';
  }
  return 'themes';
};

export const getTypeFilterName = () => {
  if (isIosClient()) {
    return 'holiday_type_iphone';
  } else if (isAndroidClient()) {
    return 'holiday_type_android';
  } else if (isRawClient()) {
    return 'holiday_type_android';
  }
  return 'holiday_type';
};

export const addFilterData = (filters, name, values, listingFilters) => {
  let id;
  listingFilters.forEach((element) => {
    if (element.uniqueName === name) {
      id = element.id;
    }
  });
  if (id) {
    filters.push({
      id,
      values,
    });
    return true;
  }
  return false;
};

export const wgFilterNotAdded = (listingFilters, criterias) => {
  let id;
  listingFilters.forEach((element) => {
    if (element.uniqueName === getTypeFilterName()) {
      id = element.id;
    }
  });
  if (id) {
    for (let i = 0; i < criterias.length; i += 1) {
      if (criterias[i].id === id && criterias[i].values.length === 1
        && criterias[i].values[0] === FILTER_WEEKEND_GETAWAYS) {
        return false;
      }
    }
  }
  return true;
};

export const createWGFilter = (listingFilters) => {
  let id;
  listingFilters.forEach((element) => {
    if (element.uniqueName === getTypeFilterName()) {
      id = element.id;
    }
  });

  if (id) {
    return ({
      id,
      values: [FILTER_WEEKEND_GETAWAYS],
    });
  }
  return null;
};

export const findAndRemoveWGFilter = (id, criterias) => {
  if (id) {
    for (let i = 0; i < criterias.length; i += 1) {
      if (criterias[i].id === id && criterias[i].values.length > 1) {
        const tempArr = [];
        for (let j = 0; j < criterias[i].values.length; j += 1) {
          if (criterias[i].values[j] !== FILTER_WEEKEND_GETAWAYS) {
            tempArr.push(criterias[i].values[j]);
          }
        }
        criterias[i].values = tempArr;
        return false;
      }
    }
  }
};

export const unescapeHTML = (str) => {
  const escapeChars = {
    lt: '<',
    gt: '>',
    quot: '"',
    apos: '\'',
    amp: '&',
  };
  return str.replace(/\&([^;]+);/g, (entity, entityCode) => {
    let match;

    if (entityCode in escapeChars) {
      return escapeChars[entityCode];
    } else if (match = entityCode.match(/^#x([\da-fA-F]+)$/)) {
      return String.fromCharCode(parseInt(match[1], 16));
    } else if (match = entityCode.match(/^#(\d+)$/)) {
      return String.fromCharCode(~~match[1]);
    }
    return entity;
  });
};

export const getPaxConfig = (pageDataMap) => {
  const adults = pageDataMap && pageDataMap.packageDetails && pageDataMap.packageDetails.pd_px_ad ?
    pageDataMap.packageDetails.pd_px_ad : 2;
  const children = pageDataMap && pageDataMap.packageDetails && pageDataMap.packageDetails.pd_px_ch ?
    pageDataMap.packageDetails.pd_px_ch : 0;
  const infants = pageDataMap && pageDataMap.packageDetails && pageDataMap.packageDetails.pd_px_inf ?
    pageDataMap.packageDetails.pd_px_inf : 0;
  return `${adults}|${children}|${infants}`;
};

export const getPaxText = ({trvInfo, seprator = ''}) => {
  let paxText;
  if (trvInfo) {
    try {
      if (trvInfo.totalPax) {
        paxText = trvInfo.adult > 1
          ? `${seprator}${trvInfo.adult} Adults`
          : `${seprator}${trvInfo.adult} Adult`;
        if (trvInfo.totalChild) {
          paxText = paxText + (trvInfo.totalChild > 1 ? `, ${trvInfo.totalChild} Children` :  `, ${trvInfo.totalChild} Child`);
        }
      }
    } catch (error) {
    }
  }
  return paxText;
};


export const getPaxStringWithDetail = ({adult = 0, child = 0, infant = 0, seprator = '' }) => {
  let paxText;
  let totalPax = adult + child + infant;
  let totalChild = child + infant;
    try {
      if (totalPax > 0) {
        paxText = adult > 1
          ? `${seprator}${adult} Adults`
          : `${seprator}${adult} Adult`;
        if (totalChild > 0) {
          paxText = paxText + (totalChild > 1 ? `, ${totalChild} Children` :  `, ${totalChild} Child`);
        }
      }
    } catch (error) {
    }
  return paxText;
};

export const getPaxStringWithRoomWithDetail = ({adult = 0, child = 0, infant = 0, room = 0, seprator = '' }) => {
  let paxText;
    try {
      paxText  = getPaxStringWithDetail({adult, child, infant, seprator});
      paxText = paxText + (room > 1 ? ` | ${room} Rooms` :  ` | ${room} Room`);
    } catch (error) {
    }
  return paxText;
};

export const getPaxWithRoomText = ({trvInfo, seprator = ' | '}) => {
  let paxText;
  if (trvInfo) {
    try {
      if (trvInfo.totalPax) {
        paxText = trvInfo.adult > 1
          ? ` ${seprator}${trvInfo.adult} Adults`
          : ` ${seprator}${trvInfo.adult} Adult`;
        if (trvInfo.totalChild) {
          paxText = paxText + (trvInfo.totalChild > 1 ? `, ${trvInfo.totalChild} Children` :  `, ${trvInfo.totalChild} Child`);
        }
      }
      paxText = paxText + (trvInfo.roomCount > 1 ? `${seprator}${trvInfo.roomCount} Rooms` :  `${seprator}${trvInfo.roomCount} Room`);
    } catch (error) {
    }
  }
  return paxText;
};

export const setListingPersonalisedData = (listingRequest) => {
  if (isMobileClient()) {
    const {HolidayModule} = NativeModules;
    HolidayModule.setListingPersonalisedData({
      listingRequest: JSON.stringify(listingRequest),
    });
  }
};

/**
 * Function to convert date to string in YYYY-MM-DD format. Example '2018-07-31'
 * @param {date} d Date Object
 * @returns {string} Date in string format
 */
export const formatDateToString = (d) => {
  if (!(isDate(d))) {
    return '';
  }
  return fecha.format(d, 'YYYY-MM-DD');
};

export const checkIfWG = data => (data && data.pt === WEEKEND_GETAWAY_PAGE_TYPE);

export const saveHolMeta = (isWG, affiliate, pt, cmp) => {
  const { HOLIDAY, WEEKEND_GETAWAY } = FUNNELS;
  const funnel = isWG ? WEEKEND_GETAWAY : pt || HOLIDAY;

  if (isRawClient() && /*affiliate !== undefined*/ !isEmpty(affiliate)) {
    setAffiliate(affiliate);
  }
  setDataInStorage(KEY_HOL_META, {
    funnel,
    affiliate,
  });
  saveCMPMeta(cmp);
};

export const updatedValuesforvariableforv83 =  (banner) => {
  const aff = getAffiliate();
  if(isNonMMTAffiliate(aff)) {
    const device = getdeviceType()
    const affDeviceDetail = `${aff}_${ device ? device : 'mSite'}`
    return `${affDeviceDetail}|${banner ? banner : 'NA'}`;
  }
  return banner;
};
const fetchAffiliateName = async () => {
  const holMetaObj = await getDataFromStorage(KEY_HOL_META);
  return holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate)
  ? `${holMetaObj.affiliate}_${getdeviceType() ? getdeviceType() : 'mSite'}`
  : '';
};
export const updatedValuesforvariable_m_v83 = async (params) => {
  const temp = await fetchAffiliateName()
  if(temp) {
    return `${temp}|${params.m_v83}`;
  }
return `${params.m_v83}`;
};
export const saveCMPMeta = (cmp) => {
  if (cmp) {
    let date = Date.now();
    setDataInStorage(KEY_CMP_META, {
      cmp,
      cmpCreatedTime:date,
    });
  }
};
export const getCMPMeta = async () => {
  const cmpMetaObj = await getDataFromStorage(KEY_CMP_META);
  return cmpMetaObj;
};
export const saveDeviceTypeInStorage = async(device) =>{
  const deviceType= await getDataFromStorage(DEVICE_TYPE);
  if(platform && !deviceType){
    setDataInStorage(DEVICE_TYPE,device)
    setdeviceType(device)
  }
}


export const updateDeviceType = async(deviceType) =>{
  if(isEmpty(deviceType)) {
    const device=await getDataFromStorage(DEVICE_TYPE)
    setdeviceType(deviceType)
    return device;
  } else {
    await setDataInStorage(DEVICE_TYPE, deviceType)
    setdeviceType(deviceType)
    return deviceType;
  }
}

export const saveAffRefId = (affRefId) => {
  setDataInStorage(KEY_AFF_REF_ID, affRefId);
};

export const getStaticMapUriForCoordinatesList = (coordinatesList, fullScreen = false,googleApiKey = '') => {
  if (isNullOrEmptyCollection(coordinatesList)) {
    return '';
  }
  const {
    BASE_URL, SIZE, SCALE, MAP_TYPE, MARKER_SIZE, MARKER_COLOR, MARKER_ICON,
  } = fullScreen ? MAP_PARAMS_FULL_SCREEN : MAP_PARAMS;
  let markers = '';
  let markersSpecs = '';
  if (fullScreen) {
    markersSpecs = `&markers=icon:${MARKER_ICON}|`;
  } else {
    markersSpecs = `&markers=size:${MARKER_SIZE}|color:${MARKER_COLOR}|`;
  }
  for (let index = 0; index < coordinatesList.length; index += 1) {
    markers += `${markersSpecs + coordinatesList[index].lat},${coordinatesList[index].lon}`;
  }
  return `${BASE_URL}?size=${SIZE}&scale=${SCALE}&maptype=${MAP_TYPE}${markers}&key=${googleApiKey}`;
};

export const getStaticMapUriForCoordinatesListV2 = (coordinatesList, params, googleApiKey) => {
  if (isNullOrEmptyCollection(coordinatesList)) {
    return '';
  }
  const {
    BASE_URL, SIZE, SCALE, MAP_TYPE, MARKER_SIZE, MARKER_COLOR, MARKER_ICON,
  } = params;
  let markers = '';
  let markersSpecs = `&markers=size:${MARKER_SIZE}|color:${MARKER_COLOR}|`;
  for (let index = 0; index < coordinatesList.length; index += 1) {
    markers += `${markersSpecs + coordinatesList[index].lat},${coordinatesList[index].lon}`;
  }
  return `${BASE_URL}?size=${SIZE}&scale=${SCALE}&maptype=${MAP_TYPE}${markers}&key=${googleApiKey}`;
};

export const getGoogleAPIKeyForAllPlarforms = async () => {
  let GOOGLE_API_KEY = '';
  if (Platform.OS === 'android') {
    const {GenericModule} = NativeModules;
    GOOGLE_API_KEY = await GenericModule.getReactNativeGoogleAPIKey();
  } else if (isIosClient()) {
    const {HolidayModule} = NativeModules;
    GOOGLE_API_KEY = await HolidayModule.getReactNativeGoogleAPIKey();
  } else {
    GOOGLE_API_KEY = getPokusConfig(PokusLobs.HOLIDAY, AbConfigKeyMappings.rNWebGoogleApiKey, '');
  }
  return GOOGLE_API_KEY;
};

export const getMapNavigationUri = (coordinates, locationName = '') => {
  if (!coordinates || !_.has(coordinates, 'lat') || !_.has(coordinates, 'lon')) {
    return '';
  }
  const {lat, lon} = coordinates;
  const scheme = Platform.select({ios: 'maps:0,0?q=', android: 'geo:0,0?q='});
  const latLon = `${lat},${lon}`;
  const url = Platform.select({
    ios: `${scheme}${locationName}@${latLon}`,
    android: `${scheme}${latLon}(${locationName})`,
  });
  return url;
};

export const getScreenWidth = () => isIosClient() ? Dimensions.get('screen').width : Dimensions.get('window').width;

export const DEVICE_WINDOW = isIosClient() ? Dimensions.get('screen') : Dimensions.get('window');

export const isNonMMTAffiliate = aff => (
  aff === AFFILIATES.GI || aff === AFFILIATES.TP || aff === AFFILIATES.INDIGO || aff === AFFILIATES.HDFC || aff === AFFILIATES.AIX
);
export const isNonGIAffiliate = aff =>(
   aff === AFFILIATES.TP || aff === AFFILIATES.INDIGO || aff === AFFILIATES.HDFC || aff === AFFILIATES.AIX
);
export const isAppPlatform = deviceType =>(
    deviceType !== 'web' && !isEmpty(deviceType)
);
export const isEnableFabCTA = (aff, page, cta) => {
  if (isNonMMTAffiliate(aff)) {
     if (cta === FAB_CTA_TYPE.CALL || cta === FAB_CTA_TYPE.CHAT) {
        return false;
      }
    }
    return true;
  };
export const isdisplayPoweredbyMMT = (aff, deviceType, pageName = '') => {
  if (!isNonMMTAffiliate(aff)){
    return false;
  }
  if (pageName === PERSUASION_PAGE_NAME_REVIEW){
    return true;
  }
  if (isNonGIAffiliate(aff)) {
      if (isAppPlatform(deviceType)) {
        return true;
      }
  }
  return false;
};

export const isdisplayPoweredbyMMTHeader = (aff, deviceType, pageName = '') => {
  if (isNonGIAffiliate(aff) ) {
    if (!isAppPlatform(deviceType)) {
      return true;
    }
  }
  return false;
};
export const isGIdisplayPoweredbyMMTHeader = (aff, deviceType, pageName = '') => {
  if (isNonMMTAffiliate(aff) && !isNonHeaderAffiliate(aff)) {
    if (!isAppPlatform(deviceType)) {
      return true;
    }
  }
  return false;
};

export const isBacktoLanding = (aff, deviceType,pageName = '') => {

  if (isNonMMTAffiliate(aff)) {
    if (isAppPlatform(deviceType)) {
      return true;
    }
  }
  return false;
};

export const isdisplayPoweredbyMMTReview = (aff, deviceType,pageName = '') => {
  if (pageName == PERSUASION_PAGE_NAME_REVIEW && aff == AFFILIATES.GI){
    return true;
  }
  if (isNonMMTAffiliate(aff)) {
    if (isAppPlatform(deviceType)) {
      return true;
    }
  }
  return false;
};


export const is6EAffiliate = aff =>(
   aff === AFFILIATES.INDIGO
);
export const isLoginAffiliate = aff => (
   aff === AFFILIATES.INDIGO
);
export const isRecentPackageAffiliate = aff => (
    aff === AFFILIATES.INDIGO
);

export const isenabledlogoAffiliate = aff => (
    aff === AFFILIATES.TP
);
export const isGIAffiliate = aff => (
    aff === AFFILIATES.GI
);
export const getDimensionsForQueryFormAd = ()=>{
  const widthForAd = 320;
  const heightForAd = 50;
  const w =  getScreenWidth() - 32;
  const h = w * (heightForAd / widthForAd);
  return {width:w,height:h};
};

export const isReturningBackRaw = props => (props.history && props.history.action && props.history.action === 'POP');

export const secondsToTime = (millis) => {
  let timeStr = '';
  const {
    days, hours, minutes, seconds,
  } = getRemainingTimeParts(millis);
  if (days > 0) {
    timeStr = `${leading0(days)}d : ${leading0(hours)}h : ${leading0(minutes)}m : ${leading0(seconds)}s`;
  } else if (hours > 0) {
    timeStr = `${leading0(hours)}h : ${leading0(minutes)}m : ${leading0(seconds)}s`;
  } else if (minutes > 0) {
    timeStr = `${leading0(minutes)}m : ${leading0(seconds)}s`;
  } else if (seconds > 0) {
    timeStr = `${leading0(minutes)}m : ${leading0(seconds)}s`;
  } else {
    timeStr = '';
  }
  return timeStr;
};

export const getRemainingTimeParts = (millis) => {
  let leftSecs = millis / 1000;
  const days = Math.floor(leftSecs / (60 * 60 * 24));
  leftSecs %= (60 * 60 * 24);
  const hours = Math.floor(leftSecs / (60 * 60));
  leftSecs %= (60 * 60);
  const minutes = Math.floor(leftSecs / 60);
  leftSecs %= 60;
  const seconds = Math.ceil(leftSecs);
  return {
    days, hours, minutes, seconds,
  };
};

export const leading0 = num => (num < 10 ? `0${num}` : num);

export const getBackButtonBlack = () => (isIosClient() ? backIconIosBlack : backIconAndroidBlack);

export const getFunnelBindingUUID = async (newKeyRequired) => {
  const existingGuid = await AsyncStorage.getItem('HOLIDAY_FUNNEL_BINDING_KEY');
  if (newKeyRequired || isEmpty(existingGuid)) {
    const guid = getNewUUID();
    await AsyncStorage.setItem('HOLIDAY_FUNNEL_BINDING_KEY', guid);
    return guid;
  }
  return existingGuid;
};

export const getNewUUID = () => {
  const s4 = () => Math.floor((1 + Math.random()) * 0x10000)
    .toString(16)
    .substring(1);
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4() + s4() + s4()}`;
};

export const getCorrelationUUID = async (newKeyRequired, correlationKeyParam) => {
  const existingGuid = await AsyncStorage.getItem(correlationKeyParam);
  if (newKeyRequired || isEmpty(existingGuid)) {
    const guid = getNewUUID();
    await AsyncStorage.setItem(correlationKeyParam, guid);
    return guid;
  }
  return existingGuid;
};

export const getExperimentValue = (key, defaultValue) => getPokusConfig(PokusLobs.HOLIDAY, key, defaultValue);

export const getExperimentValueWithLob = ({lob, key, defaultValue}) => getPokusConfig(lob, key, defaultValue);


export const getCurrentPosition = async () => LocationHelperModule.fetchCurrentLocation();

export const getAcmeXSellConfig = () => getPokusConfig(PokusLobs.ACME, AbConfigKeyMappings.acmeXsell_config, null);
export const getLocationPermissionStatus = async () => {
      return await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);
};
export const getStoragePermissionStatus = async () => {
  return await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE);
};

export const getLocationWithPerm = async () => {
  let eventApplicationWillEventForegroundListener;
  if (isAndroidClient()) {
    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);
    if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
      throw new Error(granted);
    }
  } else if (isRawClient()) {
    return await NativeModules.LocationHelperModule.fetchCurrentLocation();
  } else {
    const hasPerm = await LocationHelperModule.isLocationServicesEnabled();
    if (!hasPerm) {
      return new Promise((resolve, reject) => {
        LocationHelperModule.enableLocationServices();
        const callback = async () => {
          const hasPerm = await LocationHelperModule.isLocationServicesEnabled();
          if(eventApplicationWillEventForegroundListener.remove) {
            eventApplicationWillEventForegroundListener.remove();
            // DeviceEventEmitter.removeListener('EVENT_APPLICATON_WILL_ENTER_FOREGROUND', callback);
          }
          if (!hasPerm) {
            reject(new Error(NO_LOCATION_PERM));
            return;
          }
          try {
            resolve(await getCurrentPosition());
          } catch (e) {
            reject(e);
          }
        };
        eventApplicationWillEventForegroundListener = DeviceEventEmitter.addListener('EVENT_APPLICATON_WILL_ENTER_FOREGROUND', callback);
      });
    } else {
      try {
        const currentPosition = await getCurrentPosition();
        return (currentPosition);
      } catch (e) {
        throw (e);
      }
    }
  }
  return getCurrentPosition();
};

export const shouldShowFreebies = (freebiesDetails) => {
  const showFreebie = getShowFreebie();
  if (showFreebie && freebiesDetails &&
    freebiesDetails.activityFreebies &&
    freebiesDetails.activityFreebies.length > 0) {
      return true;
    }
  return false;
};

export const getFirstFreebiesText = (freebiesDetails) => {
  if (shouldShowFreebies(freebiesDetails) &&
      freebiesDetails.activityFreebies[0].name) {
        return freebiesDetails.activityFreebies[0].name;
      }
  return null;
};

export const freebiesTextForListing = (freebiesDetails) => {
  if (shouldShowFreebies(freebiesDetails)) {
    if (freebiesDetails.activityFreebies.length === 1) {
      if (freebiesDetails.activityFreebies[0].name && freebiesDetails.activityFreebies[0].code){
        let freebieName = freebiesDetails.activityFreebies[0].name;
        let freebieCode = freebiesDetails.activityFreebies[0].code;
        if (freebieCode.startsWith('TRF')) {
          return 'Transfer is free!';
        } else {
          return `${freebieName} is free!`;
        }
      }
    } else {
      return `Hurray! ${freebiesDetails.activityFreebies.length} activities are free with this package`;
    }
  }
  return null;
};

export const freebiesTextForDetail = (freebiesDetails) => {
  if (shouldShowFreebies(freebiesDetails)) {
    if (freebiesDetails.activityFreebies.length === 1) {
      if (freebiesDetails.activityFreebies[0].name){
        let freebieName = freebiesDetails.activityFreebies[0].name;
        return `Hurray! ${freebieName} is free`;
      }
    } else {
      if (freebiesDetails.activityFreebies[0].name){
        let freebieName = freebiesDetails.activityFreebies[0].name;
        return `Hurray! ${freebieName} and ${freebiesDetails.activityFreebies.length - 1} more ${freebiesDetails.activityFreebies.length > 2 ? 'activities are' : 'activity is' } free`;
      }
    }
  }
  return null;
};


export const getHotelRatingColor = (userRating) => {
  let color = '';
  if (userRating < BELOW_AVERAGE_RATING) {
    color = '#eb2026';
  } else if (userRating >= BELOW_AVERAGE_RATING && userRating < AVERAGE_RATING) {
    color = '#f5a623';
  } else if (userRating >= AVERAGE_RATING && userRating < GOOD_RATING) {
    color = '#33d18f';
  } else {
    color = '#249995';
  }
  return color;
};

export const getDynamicHeight = (height, width, screenWidth) => {
  if (_.isNumber(width) && _.isNumber(height)) {
    return screenWidth * (height / width);
  }
  return 'auto';
};

export const isZCAvailable = (zcOptions) => {
  if (!zcOptions) {
    return false;
  }
  for (let i = 0; i < zcOptions.length; i++) {
    const { type, available } = zcOptions[i];
    if (type === 'ZC' && available) {
      return true;
    }
  }
  return false;
};
export const isFlexiDateAvailable = (zcOptions) => {
  if (!zcOptions) {
    return false;
  }
  for (let i = 0; i < zcOptions.length; i++) {
    const { type, available } = zcOptions[i];
    if (type === 'FlexiDate' && available) {
      return true;
    }
  }
  return false;
};

/**
 * Cancellation policy options are considered as persuasions on the UI.
 * Below function returns a colon separated string containing
 * zc option type and default position on UI as 0 (default position of zc banner on the UI).
 * JIRA- HLD-9140 Point 9
 */
export const getCancellationPolicyPdt = (cancellationPolicyData) => {
  const persuasionList = [];
  const {penaltyDetail} = cancellationPolicyData || {};
  const {zcOptions} = penaltyDetail || {};
  if (zcOptions) {
    zcOptions.forEach((item) => {
      const {type, available} = item;
      if (available) {
        persuasionList.push(`${type}:0`);
      }
    });
  }
  return persuasionList.join('|');
};

export const getActiveTabIndexForCancellationOverlay = (cancellationPolicyData, state = '') => {
  // If state is available refer state to decide active tab index.
  if (!isEmpty(state)) {
    if (state === CANCELLATION_OVERLAY || state === extraInfoRefs.CANCELLATION_POLICY) {
      return 0;
    } else if (state === DATE_CHANGE_OVERLAY) {
      return 1;
    }
  }

  const {penaltyDetail} = cancellationPolicyData || {};
  const {zcOptions} = penaltyDetail || {};
  const zc = isZCAvailable(zcOptions);
  const flexiDate = isFlexiDateAvailable(zcOptions);
  if (zc) {
    return 0;
  } else if (flexiDate) {
    return 1;
  } else {
    return 0;
  }
};

export const getRating = (ratings) => {
  if (ratings) {
    return ratings.split(',')[0];
  }
  return null;
};

/** Util function to convert covid-content api heading to an array of maxSize 3;
 * Array Indices:
 * 1. Bold & Colored Text
 * 2. Bold
 * 3. Normal Text
*/
export const getHeading = (heading) => {
  let arr = [];
  if (heading && heading.length > 0) {
    arr.push(heading[0]);
    if (heading.length >= 2) {
      arr.push(heading[1]);
    }
    if (heading.length >= 3) {
      arr.push(heading.slice(2, heading.length).join(' '));
    }
  } else {
    arr.push('');
  }
  return arr;
};
/**
 * Creates and returns final cue step to show on boarding of a screen
 * @param {*} pokusSteps an array of cue steps that are configured on pokus page/screen wise
 * @param {*} localSteps an array of cue steps that are configured locally
 * @returns Final cue steps which are according to pokus order and having text defined in pokus
 */
export const createCuesSteps = async ({pokusSteps, localSteps, isDynamic, pageName = ''}) => {
    const stepsAlreadyShown = (await getDataFromStorage(KEY_HOL_CUES_STEPS_SHOWN))?.[pageName] || {};
    const finalSteps = [];
    if (pokusSteps)
    {
      // filter steps defined in pokus and are not already shown to the user
      pokusSteps.forEach(pokusStep => {
        localSteps.forEach(localStep => {
          if (pokusStep.key === localStep.key && !stepsAlreadyShown[localStep.key]) {
            localStep.label.text = pokusStep.text || localStep.label.text;
            finalSteps.push(localStep);
          }
        });
      });
    } else {
      // if pokus has no config for steps then filter local steps if they are not already shown to the user
      localSteps.forEach(localStep => {
        if (!stepsAlreadyShown[localStep.key]) {
          finalSteps.push(localStep);
        }
      });
    }
    if (isDynamic) {
      finalSteps.filter(step=> step.type === 'dynamic');
    }
    return finalSteps;
};
/**
 * Checks and return last visit time for the given pageName
 * @param {*} pageName for which last visit time will be checked
 * @returns last visit time in ms otherwise false
 */
export const hasOnBoardingCuesLastVisit = async(pageName) => {
  const lastVisitObj = await getDataFromStorage(KEY_HOL_ONBOARDING_PAGE_VISIT);
  if (lastVisitObj && lastVisitObj[pageName]) {
    return lastVisitObj[pageName];
  }
  return null;
};
/**
 *
 * @param {*} pageName for which last visit time will be used
 * @returns  true is delay is over otherwise false
 */
export const isOnBoardingCuesDelayOver = async(pageName) => {
  const maxDays = 999;
  const daysDelay = getCoachMarkDaysDelayHol();
  const currentDate = new Date();
  currentDate.setDate(currentDate.getDate());
  if (daysDelay < maxDays && daysDelay > 0) {
    const lastVisit = await hasOnBoardingCuesLastVisit(pageName);
    if (!lastVisit) {
      return false;
    }
    const thenDate = new Date();
    thenDate.setTime(lastVisit);
    const diff = diffDays(currentDate, thenDate);
    return diff >= daysDelay;
  }
  return true;
};
export const removeCuesStepsShown = async(pageName) => {
  const stepsAlreadyShown = await getDataFromStorage(KEY_HOL_CUES_STEPS_SHOWN) || {};
  delete stepsAlreadyShown[pageName];
  await setDataInStorage(KEY_HOL_CUES_STEPS_SHOWN, stepsAlreadyShown);
};

let checkListingOrGrouping = '';
export const setPageName = (name) => {
  checkListingOrGrouping = name;
};
export const getPageName = () => {
  return checkListingOrGrouping;
};

export const getTravelDateForRecentPackages = ({ date, fallBackDate }) => {
  return date ? new Date(date) : fallBackDate ? new Date(fallBackDate) : null;
};

export const getLowestPackagePrice = ({ packages, sendRecentSearchesData }) => {
  let minPrice = Number.MAX_SAFE_INTEGER;
  packages.map((packageItem) => {
    const defaultCategoryId = packageItem.categoryDetails.defaultCategoryId;
    const defaultPackage = packageItem.priceDetails.categoryPrices.find(
      (category) => category.categoryId === defaultCategoryId,
    );

    if (defaultPackage.discountedPrice < minPrice) {
      minPrice = defaultPackage.discountedPrice;
    }
  });
  sendRecentSearchesData({ startingPrice: minPrice });
};
export const isMMTMainDomain = ({ url = '' } = {}) => {
  const domains = [
    "www.makemytrip.com/holidays-india",
    "www.makemytrip.com/holidays-international",
    "www.makemytrip.com/holidays/"
  ];
  return domains.some((element) => url?.includes(element))
};

export const isNewAppLaunch = async () => {
  const appLaunchCount = await getAppLaunchCount();
  const lastCount = await getDataFromStorage(KEY_APP_LAUNCH_COUNT);
  if (lastCount === null || appLaunchCount > lastCount) {
    await setDataInStorage(KEY_APP_LAUNCH_COUNT, appLaunchCount);
    return true;
  }
  return false;
};

export const getAppLaunchCount = async () => {
  const DEFAULT_LAUNCH_COUNT = 1;
  const {HolidayModule, NetworkModule} = NativeModules;
  if (isIosClient()) {
    return await NetworkModule.getAppLaunchCount();
  } else if (isAndroidClient()) {
    return await HolidayModule.getAppLaunchCount();
  } else {
    return DEFAULT_LAUNCH_COUNT;
  }
};
export const getLegalUrl = (aff) => {
  let legalUrlObj = {
    tos: 'https://www.makemytrip.com/legal/user_agreement.html#tos-obt',
    userAgreement: 'https://www.makemytrip.com/legal/user_agreement.html',
    privacyPolicy: 'https://www.makemytrip.com/legal/privacy_policy.html'
  };

  let affLegalUrlObj = {
    IGO: {
      tos: 'https://6eholidays.makemytrip.com/legal/igo/user_agreement.html#tos-obt',
      userAgreement: 'https://6eholidays.makemytrip.com/legal/igo/user_agreement.html',
      privacyPolicy: 'https://6eholidays.makemytrip.com/legal/igo/privacy_policy.html'
    },
    GI: {
      tos: 'https://giholidays.makemytrip.com/legal/gi/user_agreement.html#tos-obt',
      userAgreement: 'https://giholidays.makemytrip.com/legal/gi/user_agreement.html',
      privacyPolicy: 'https://giholidays.makemytrip.com/legal/gi/privacy_policy.html'
    }

  };

  if (!isEmpty(affLegalUrlObj[aff])) {
    legalUrlObj = affLegalUrlObj[aff];
  }
  return legalUrlObj;
};

export const rupeeFormatterUtils = rupees => rupeeFormatterWithLocale(rupees);

export const getItineraryComponents = (packageInclusionsDetail) => {
  const itineraryComponents = {
    F: 0,
    H: 0,
    A: 0,
    T: 0,
    S: 0,
    V: 0,
  };
  let string = '';
  Object.keys(packageInclusionsDetail)?.map((inclusion) => {
    switch (inclusion) {
      case INCLUSIONS_TYPE.FLIGHTS:
        itineraryComponents.F = 1;
        break;
      case INCLUSIONS_TYPE.HOTELS:
        itineraryComponents.H = 1;
        break;
      case INCLUSIONS_TYPE.ACTIVITIES:
        itineraryComponents.A = 1;
        break;
      case INCLUSIONS_TYPE.AIRPORT_TRANSFER:
      case INCLUSIONS_TYPE.CITY_DROPS:
      case INCLUSIONS_TYPE.CAR_ITINERARY:
        itineraryComponents.T = 1;
        break;
      case INCLUSIONS_TYPE.SIGHTSEEING:
        itineraryComponents.S = 1;
        break;
      case INCLUSIONS_TYPE.VISA:
        itineraryComponents.V = 1;
        break;
    }
  });
  Object.keys(itineraryComponents)?.map((key, index) => {
    string = `${string}${itineraryComponents[key] ? key : 'U'}${
      index === Object.keys(itineraryComponents)?.length - 1 ? '' : '_'
    }`;
  });
  return string;
};

export const getItineraryThemeTags = (packageDetailData = {}) => {
  let themeTags = '';
  if (packageDetailData?.tagDetails?.tags?.length > 0) {
    let themeValues =
      packageDetailData?.tagDetails?.tags.find((tag) => tag.tag === 'Theme')?.values || [];
    if (themeValues.length > 0) {
      themeTags = `Theme:${themeValues.toString().trim()}`;
    }
  }
  return themeTags;
};

export const setThemeTagsMetric = ({ packageDetails = {} }) => {
  let theme_tags = '';
  if (packageDetails?.pkg_themeTags) {
    theme_tags = packageDetails?.pkg_themeTags || '';
  }
  theme_tags = `${theme_tags ? `${theme_tags}|` : ''}Tags:${
    packageDetails?.bundled ? 'Bundled' : 'False'
  },${packageDetails?.premium ? 'Premium' : 'False'}`;
  return theme_tags;
};
export const formatTime = (time = 0) => {
  const symbol = '';
  const byTime = 60;
  const padding = '0';
  const formattedMinutes = _.padStart(Math.floor(time / byTime).toFixed(0), 2, padding);
  const formattedSeconds = _.padStart(Math.floor(time % byTime).toFixed(0), 2, padding);

  return `${symbol}${formattedMinutes}:${formattedSeconds}`;
};
export const getTimeZone = ({ date }) => {
  if (isEmpty(date)) {
    return '';
  }
  const dateAsString = date.toString();
  const timezone = dateAsString.match(/\(([^\)]+)\)$/)[1];
  let matches = timezone.match(/\b(\w)/g);
  let abbreviations = matches.join('');
  return abbreviations;
};

export const sortByKey = ({ obj, key }) => {
  if (isEmpty(key) || isEmpty(obj)) {
    return obj;
  }
  const arr =  _.sortBy(obj, (o) => o[key]);
  return arr;
};

export const ischeckinBaggageInfoAvailable = (flightMetadataDetail = null) => {
  if (isEmpty(flightMetadataDetail)) {
    return false;
  }

  const { checkInBaggage = null, baggageMessage = '' } = flightMetadataDetail || {};
  if (!isBoolean(checkInBaggage)) {
    return false;
  }
  return true;
};

export const getAPWindow = (selectedDate) => {
  const todayDate = new Date().toJSON().slice(0, 10);
  return selectedDate ? `AP_${findDaysBetweenDates(selectedDate, todayDate)}` : 'NULL';
};

export const getNearByCityDataForPdt = nearByCities => {
  if (!nearByCities || nearByCities?.length === 0) {
    return;
  }

  return nearByCities.map(city =>{
    const {cityName, distance} = city || {};
    const {value, unit} = distance || {};
    return `${cityName}:${value}${unit}`;
  }).toString();
};

export const setCategoryTrackingEvent = ({ campaign, filters, destinationThemes }) => {
  const getCategoryFromFilters = (filters) => {
    let category = '';
    filters.forEach((filter) => {
      if (filter.urlParam === 'themes') {
        filter.values.forEach((value) => {
          category = `${category ? `${category},` : ''}${value}`;
        });
      }
    });

    return category;
  };
  const cityLevelCategory = !isEmpty(destinationThemes) ? destinationThemes.join(',') : '';
  const filterValues = campaign ?? getCategoryFromFilters(filters);
  const eventName =
    cityLevelCategory || filterValues
      ? `mcat:${cityLevelCategory ? `${cityLevelCategory}${filterValues ? ',' : ''}` : ''}${
          filterValues ?? ''
        }`
      : '';
  return eventName;
};

export const getBaseVisaCondition = (basePackageInclusionsDetail = {}) => {
  const { visa  = false} = basePackageInclusionsDetail || {};
  return visa;
};

export const getDateForCalendar = () => addDays(today(), 0);


export const getPaxCount = (roomDetails = [], paxType = '') => {
  if (!Array.isArray(roomDetails)) {
    return -1;
  }

  return roomDetails.reduce((noOfPax, room) => noOfPax + (room[paxType] || 0), 0);
};

export const getPaxDetails = ({ roomDetails = [] }) => {
  const childCount = getPaxCount(roomDetails, 'noOfChildrenWOB') + getPaxCount(roomDetails, 'noOfChildrenWB');
  const infantCount = getPaxCount(roomDetails, 'noOfInfants');
  return {
    adult: getPaxCount(roomDetails, 'noOfAdults'),
    child: childCount,
    infantCount,
    noOfRooms: roomDetails.length,
    kidsCount: childCount + infantCount,
  };
};

export const getDimensionsForAd = ()=>{
  const widthForAd = 328;
  const heightForAd = 120;
  const w =  getScreenWidth() - 32;
  const h = w * (heightForAd / widthForAd);
  return {width:w,height:h};
};

export const removeSubstringAfterHyphen = str => str.split('-')[0];

function checkDateInRanges(dateToCheck, dateRanges) {
  // Convert the input date to a Date object
  const dateToCheckObj = new Date(dateToCheck);

  // Iterate through each date range in the array
  for (const dateRange of dateRanges) {
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);

      // Check if the date falls within the current date range
      if (startDate <= dateToCheckObj && dateToCheckObj <= endDate) {
          return true;
      }
  }

  // If the date doesn't fall within any of the ranges
  return false;
}

export const getSearchTravelDate = (date, metaData) => {
  const availableDates = metaData?.dates?.availableDates || [];
  if (!date) {
    return null;
  }

  return checkDateInRanges(date, availableDates) ? date : null;
};

export const padToKDigits = (num, k) => {
  return num.toString().padStart(k, '0');
};


export const getAndroidBottomBarHeight = () => {
  const screenHeight = Dimensions.get('screen').height;
  const windowHeight = Dimensions.get('window').height;
  const navbarHeight = screenHeight - windowHeight;
  if (navbarHeight === 0) {
    return 25;
  } else if (navbarHeight > 30) {
    return 28;
  } else {
    return 42;
  }
};

export const isNonHeaderAffiliate = aff => (
  aff === AFFILIATES.HDFC
);


export async function sha256(message) {
  try {
    // encode as UTF-8
    const msgBuffer = new TextEncoder().encode(message);

    // hash the message
    const hashBuffer = await crypto.subtle.digest("SHA-256", msgBuffer);

    // convert ArrayBuffer to Array
    const hashArray = Array.from(new Uint8Array(hashBuffer));

    // convert bytes to hex string
    const hashHex = hashArray
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("");
    return hashHex;
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error("exception while encrypting", e);
    return "";
  }
}
export const sendWhatsAppMessage = link => {
  if (!isUndefined(link)) {
   Linking.canOpenURL(link)
    .then(supported => {
      if (!supported) {
       Alert.alert('Please install whatsapp to send direct message via whatsapp'
       );
     } else {
       return Linking.openURL(link);
     }
   })
   .catch(err => console.error('An error occurred', err));
 } else {
   console.log('sendWhatsAppMessage -----> ', 'message link is undefined');
  }
 };

 export const capitalize = str => {
  let updatedString = str;
  updatedString = updatedString.toLowerCase();
  return updatedString.charAt(0).toUpperCase() + updatedString.slice(1);
 }

 export const getLeftBackIcon = ({ onBackPress = () => {}} = {}) => {
  return {
    icon: isIosClient() ? backarrowios  :require('@mmt/legacy-assets/src/backArrowAndroid.webp'),
    onPress: onBackPress
  };
 }

 export const getShareIcon = ({ onSharePress = () => {}} = {}) => {
  return {
    icon: require('@mmt/legacy-assets/src/ic_whatsapp.webp'),
    onPress: onSharePress
  };
 }
 export const isPokusEnabledForSection = ({ sectionCode }) => {
  switch (sectionCode) {
    case sectionCodes.CONTINUE_YOUR_SEARCH:
      return showNewCYSSection();
    case sectionCodes.PAYMENT_DROP_OFF:
      return showNewRVSSection();
    case sectionCodes.RECENTLY_VIEWED_PACKAGES:
      return !showNewRVSSection();
    default:
      return true;
  }
};

export const getCityWiseDataForPDO = (cards = []) => {
  const ALL_CITY = 'ALL';
  if(isEmpty(cards)) {
    return {}
  }
  const cityData = {
    [ALL_CITY]: cards,
  };
  cards?.forEach((item) => {
    if (item.tagDestination) {
      if (cityData[item.tagDestination]) {
        cityData[item.tagDestination].push(item);
      } else {
        cityData[item.tagDestination] = [item];
      }
    }
  });

  return cityData;
};


export const updateDestSearchList = ({ searchList, text}) => {
  searchList.forEach((listItem)=>{
    if(listItem.type === 'DEST') {
      listItem.searchType = text ? DEST_SEARCH_TYPES.SEARCH : DEST_SEARCH_TYPES.POPULAR_DESTINATION
    }
  })

  return searchList;
}
export const prepareDisabledDays = (dateArr = []) => {
  const disabledDays = {};
  dateArr.map(row => {
    const rowDate = fecha.parse(row, 'YYYY-MM-DD').setHours(0, 0, 0, 0);
    disabledDays[fecha.format(rowDate, 'YYYYMMDD')] = true;
  });
  return disabledDays;
};

export const addDaysMethod = (days, theDate) => {
  const newDate = new Date();
  newDate.setHours(0, 0, 0, 0);
  const changeDate = theDate || newDate;
  return new Date(changeDate.getTime() + (days * 24 * 60 * 60 * 1000));
};

/**
 * Checks if there are more elements in the array after the current index.
 *
 * @param {number} index - The current index being processed.
 * @param {Array} array - The array being iterated over.
 * @returns {boolean} True if there are more elements after the current index, false otherwise.
 * @throws {TypeError} If index is not a number or array is not an Array.
 * @throws {RangeError} If index is negative or not an integer.
 */
export const hasMoreElements = (index, array) => {
  // Check if array is actually an array
  if (!Array.isArray(array)) {
    throw new TypeError('Second argument must be an array');
  }

  // Check if index is a number
  if (typeof index !== 'number') {
    throw new TypeError('First argument must be a number');
  }

  // Check if index is a non-negative integer
  if (index < 0 || !Number.isInteger(index)) {
    throw new RangeError('Index must be a non-negative integer');
  }

  // Perform the check
  return index < array.length - 1;
};

export const isSummaryTabDefaultOpen = ({ fromPreSales = false }) => {
  const { details_V3_presales = '', detailsV3 = '' } = getDetailDefaultTabDayPlan();
  return fromPreSales ? details_V3_presales === 'SUMMARY' : detailsV3 === 'SUMMARY';
};

export const openGenericDeeplink = ({ url = '' }) => {
  if (isRawClient()) {
    const { HolidayModule } = NativeModules;
    HolidayModule.openWebView({ url });
  } else {
    GenericModule.openDeepLink(tncLink);
  }
};

export const getSubFunnelName = () => {
  if(isRawClient())return 'MMT';
  return HolidayDataHolder.getInstance().getSubFunnel();
};

export const isLuxeFunnel = () => {
  const subFunnel = getSubFunnelName();
  return subFunnel === SUB_FUNNELS_TYPES.LUXE;
};


export const getFabT2QconfigDefaultData = () => {
  if(isLuxeFunnel()){
    return LuxFabT2QConfigDefaultData;
  }
  return FabT2QConfigDefaultData;
}


