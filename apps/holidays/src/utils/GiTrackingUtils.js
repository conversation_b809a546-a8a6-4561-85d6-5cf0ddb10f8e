import {getPaxCountInfoForTotalRooms} from '../PhoenixDetail/Utils/HolidayDetailUtils';

export const formatDataForGi = ({ fromCity, toCity, paxSearched, dateOfSearch, withFlight, isError }) => {
    const googleTagParams = {};

    const YES = 'YES';
    const NO = 'NO';
    const IS_ERROR = 'Is_Error';

    if (fromCity) {googleTagParams.From_City = fromCity;}
    if (toCity) {googleTagParams.To_City = toCity;}
    if (paxSearched) {googleTagParams.Pax_Searched = paxSearched;}
    if (dateOfSearch) {googleTagParams.Date_Of_Search = dateOfSearch;}
    if (withFlight) {googleTagParams.With_Flight = withFlight ? YES : NO;}
    if (isError !== undefined) {googleTagParams[IS_ERROR] = isError ? YES : NO;}

    return googleTagParams;
};


export const getGiTrackingDataFromHolidayLandingGroupDto = ({holidayLandingGroupDto}) => {
    const { userDepCity, destinationCity, rooms, packageDate } = holidayLandingGroupDto || {};
    const paxSearched = getPaxCountInfoForTotalRooms(rooms);
    return formatDataForGi({fromCity: userDepCity, toCity: destinationCity, paxSearched, dateOfSearch: packageDate});
};


export const getGiTrackingDataFromHolidayDetailData = ({holidayDetailData, rooms}) => {
    const { departureDetail, destinationDetail } = holidayDetailData || {};
    const { departureCity, departureDate } = departureDetail || {};
    const { tagDestination } = destinationDetail || {};
    const paxSearched = getPaxCountInfoForTotalRooms(rooms);
    return formatDataForGi({fromCity: departureCity, toCity: tagDestination, paxSearched, dateOfSearch: departureDate});
};

export const getGiTrackingDataFromHolidayReviewData = ({holidayReviewData}) => {
    const { roomDetails: rooms } = holidayReviewData || {};
    return getGiTrackingDataFromHolidayDetailData({holidayDetailData: holidayReviewData, rooms});
};
