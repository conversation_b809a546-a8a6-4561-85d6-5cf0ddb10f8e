import { formatClickEventName, formatPageLoadEventName, GI_PAGE_NAMES, sendGIEvents } from '../../utils/ThirdPartyUtils'

const pageName = GI_PAGE_NAMES.GROUPING
export const trackGroupingGILoadEvent = ({isError = false, giData = {}} = {}) => {
  sendGIEvents({
    eventName: formatPageLoadEventName({pageName}),
    data: {
      pageName: `${pageName}${isError ? '_error' : ''}`,
      googleTagParams : {...giData, "Page_Name": pageName},
      fb_destination : giData?.To_City || "NA",
      fb_source : giData?.From_City || "NA",
    }
  })
}

export const trackGroupingGIClickEvent = ({ eventName, ...rest} = {}) => {
  sendGIEvents({
  eventName: formatClickEventName({ string: eventName, pageName }),
  data: {
    pageName,
    ...rest,
  }
})
}
