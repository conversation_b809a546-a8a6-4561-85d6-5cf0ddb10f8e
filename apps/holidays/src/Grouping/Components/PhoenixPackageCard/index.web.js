import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import {Image, Text, TouchableOpacity, View, FlatList} from 'react-native';
import { StyleSheet } from 'react-native';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { htmlStylePersuasions, IMAGE_HEIGHT, IMAGE_SIGNATURE_HEIGHT, PACKAGE_OUTER_CARD_HEIGHT, PACKAGE_OUTER_CARD_WIDTH } from './styles';
import { getPackageImageV2, rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import { getInclusionArray, INCLUSIONS } from './Inclusions';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';
import HTMLView from 'react-native-htmlview';
import LinearGradient from 'react-native-linear-gradient';
import {SIGHT_SEEING_INCLUDED} from '../../../PhoenixDetail/DetailConstants';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import SafetyImg from '@mmt/legacy-assets/src/holidays/ic_mySafety.webp';
import iconPointer from '@mmt/legacy-assets/src/holidays/ic_location.webp';
import {has, isEmpty} from 'lodash';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {convertUrlToHttps} from '../../../utils/HolidayNetworkUtils';
import { isPackageMmtSignature, getTitles } from '../../Utils/HolidayGroupingUtils';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import YellowQuote from '@mmt/legacy-assets/src/holidays/quotes_light_yellow.webp';
import LocationIcon from '@mmt/legacy-assets/src/location_icon.webp';
import { AFFILIATES, variantEnum } from '../../../HolidayConstants';
import { borderRadiusValues, holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { sectionBottomSpacing, sectionHeaderSpacing } from '../../../Styles/holidaySpacing';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';


const MAX_ATTRACTIONS_TO_SHOW = 2;
const hotelTitle = 'More Hotel';
const OPTIONS_AVAILABLE = 'Options Available';
const OPTION_AVAILABLE = 'Option Available';

export default class PhoenixPackageCard extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      groupImageLoadFailed: false,
      showSMEIcon: false,
    };
  }
  isIndigoCollection = () => this.props.groupingData.host === AFFILIATES.INDIGO;

  _onGroupImageLoadError = () => {
    this.setState({ groupImageLoadFailed: true });
  };

  getDestinations = () => {
    const destinationDetail = this.props.item.destinationDetails;
    if (!destinationDetail
      || !destinationDetail.destinations
      || destinationDetail.destinations.length === 0) {
      return null;
    }
    let { destinations } = destinationDetail;
    if (destinationDetail.showCountryNames) {
      destinations = [];
      let name;
      let nights = 0;
      destinationDetail.destinations.forEach((element) => {
        if (!name) {
          name = element.countryName;
        }
        if (element.countryName !== name) {
          destinations.push({
            name,
            nights,
          });
          name = element.countryName;
          nights = 0;
        }
        nights += element.nights;
      });
      destinations.push({
        name,
        nights,
      });
    }
    return destinations;
  };


  onCardClick = () => {
    this.clickCard('CardClick');
  }
  onBookNowClick = () => {
    this.clickCard('BookNowClick');
  }
  onCustomizeClick = () => {
    this.clickCard('CustomizeClick');
  }
  clickCard = (clickAction) => {
    // storing this data for tracking usage
    this.props.groupingData.clickAction = clickAction;
    this.props.onPackageClicked(
      this.props.item, null, this.props.groupingData, this.props.fabCta,
      this.props.groupName, this.props.pt, this.props.aff,
      this.props.groupIndex, this.props.index, this.props.selectedDate
    );
  }
  splitDurationListMax = (durationDestMap) => {
  let destMax = 1;
  const MAX_TEXT = 31;
  let width = 0;

    durationDestMap.forEach((item, index) => {
        width = width + item?.name?.length + 3;
        const count = durationDestMap.length - (index + 1);
        if (count > 0) {
          width = width + 5;
        }
        if (width < MAX_TEXT) {
          destMax = index + 1;
        }
    });

    return destMax;
  }
  splitDurationList = (durationDestMap, maxDest) => {
    return durationDestMap.map((item, index) => (
      <>
        {index < maxDest && (
          <>
            <Text style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
              <Text style={styles.durationNights}>{`${item.nights}N `}</Text>
              <Text style={styles.durationListLoc}>{item.name}</Text>
            </Text>
            {index !== durationDestMap.length - 1 && (
              <Text style={styles.durationSplitter}>
                <Text style={styles.durationSplittedText}>|</Text>
              </Text>
            )}
          </>
        )}
      </>
    ));
  };

  quotedTextView = (packageDetail) => {
    const {packageSynopsis, name} = packageDetail || {};
    if (packageSynopsis) {
      return (
        <View style={marginStyles.mb2}>
          <Image style={styles.yellowQuote} source={YellowQuote}/>
          <Text numberOfLines={2} ellipsizeMode={'tail'} style={styles.synopsysText}>{packageSynopsis}</Text>
          <Text numberOfLines={1} ellipsizeMode={'tail' }style={styles.synopsysName}>{name}</Text>
        </View>);
    }
    return ([]);
  };

  getStartingFromView = (packageDetail) => {
    const { departureDetails} = packageDetail || {};
    const emptyStrip = <View style ={{paddingVertical: 5}} />;
    if (!departureDetails) {
      return emptyStrip;
    }

    const {startingCity} = departureDetails || {};

    if (isEmpty(startingCity)) {
      return emptyStrip;
    }

    return (
      <View style={styles.signatureStartingFromContainer}>
        <Image style={styles.location} source={LocationIcon} />
        <Text style={styles.startingFrom}>
          Starting from : <Text style={styles.staringCity}>{startingCity}</Text>
        </Text>
      </View>
    );
  };
  renderIndigoIcon = () => (
    <View style={styles.indigoIconWrapper}>
      <Image style={styles.indigo} source={require('@mmt/legacy-assets/src/holidays/logo_indigo.webp')}/>
    </View>
  )
  itenaryList = () => {
    const packageDetail = this.props.item;

    if (isPackageMmtSignature(packageDetail)){
      return [];
    }

    const inclusions = getInclusionArray(packageDetail);
    const componentTags = packageDetail?.listingStaticData?.componentTags;

    return (
    <View style={styles.componentList}>
    {inclusions &&
      inclusions.map((item) => {
        if (item && has(item, 'title')) {
          const { title: itemTitle = '', count = 0, tag = '', icon = '' } = item || {};
          const title = itemTitle?.toLowerCase();
          const countNotAvailable = count <= 0;
          const isActivityAndInclusion = item.title === INCLUSIONS.activities.title && item?.isInclusionIncluded;
          const countText = `${count}${isActivityAndInclusion && count > 1 ? '+' : ''}`;
          return (
            <View style={styles.componentBox} key={item.title}>
              <View>
                <Text style={styles.premiumTxt}>{tag || componentTags?.[title]?.tag}</Text>
              </View>
              {title === 'flights' && this.isIndigoCollection() ? (
                this.renderIndigoIcon()
              ) : (
                <Image
                  source={icon}
                  style={[styles.componentImg, countNotAvailable ? styles.countNotAvailable : {}]}
                />
              )}
              <Text style={[styles.componentText, countNotAvailable ? styles.countNotAvailable : {}]}>
                {countText} {count > 1 ? itemTitle : getTitles(itemTitle)}
              </Text>
            </View>
          );
        }
        return [];
      })}
      </View>
    );
  }

  itineraryListForMmtSignature = (mmtSignaturePackage) => {
    if (!mmtSignaturePackage) {
      return [];
    }

    const packageDetail = this.props.item;
    const inclusions = getInclusionArray(packageDetail);
    const componentTags = packageDetail?.listingStaticData?.componentTags;
    return (<View style={styles.mmtSignatureItineraryContainer}>
      {inclusions &&
        inclusions.map((item) => {
          if (item && has(item, 'title')) {
            let title = item?.title?.toLowerCase();
            const count = componentTags?.[title]?.count;
            const countNotAvailable = count <= 0;
            return (
              <View style={styles.mmtSignatureComponentBox} key={item.title}>
                <Text
                  style={[
                    styles.componentText,
                    styles.mmtSignatureComponentText,
                    countNotAvailable ? styles.countNotAvailable : {},
                  ]}
                >
                  {count && count > 0 ? count : 0}
                </Text>
                <Image
                  source={item.icon}
                  style={[styles.componentImg, countNotAvailable ? styles.countNotAvailable : {}]}
                />
              </View>
            );
          }
          return [];
        })}
    </View>);
  };

  getPrice({packageDetail, mmtSignaturePackage}) {
    const packageCategoryPrice = getPackageCategoryPrice(
      this.props.item.categoryDetails.defaultCategoryId,
      packageDetail,
    );
    let packagePrice = packageCategoryPrice.price;
    let packageDiscountedPrice = packageCategoryPrice.discountedPrice;
    const bottomStyle = mmtSignaturePackage ? { bottom: 15 } : {};
    return (
      <View style={[styles.priceContainer,bottomStyle ]}>
        {Math.abs(packagePrice) > Math.abs(packageDiscountedPrice) && (
          <View>
            <Text style={styles.actualPrice}>{rupeeFormatterUtils(Math.abs(packagePrice))}</Text>
            <View style={styles.priceSlash} />
          </View>
        )}
        <Text style={styles.finalPrice}>
          {rupeeFormatterUtils(Math.abs(packageDiscountedPrice))}
        </Text>
        <Text style={styles.perPerson}>per person</Text>
      </View>
    );
  }
  getAttractions(packageDetail) {
    let { activityDetails } = packageDetail;
    let attractions = [];
    let total = MAX_ATTRACTIONS_TO_SHOW;
    if (activityDetails.sightSeeingIncluded) {
      attractions.push(SIGHT_SEEING_INCLUDED);
      total--;
    }
    if (activityDetails?.activities) {
      for (let activity of activityDetails.activities) {
        attractions.push(activity.metaName);
        total--;
        if (!total) { break; }
      }
    }
    return attractions;

  }
  renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.name === 'p') {
      return <Text numberOfLines={1} style={[htmlStylePersuasions.p]}>
        {defaultRenderer(node.children, parent)}
      </Text>;
    }
  }
  getBanner(isSafe, isPremium, packageDetail) {
    const startingCity = packageDetail?.departureDetails?.startingCity; {/* will be available only in landonly packages */}
    const mmtSignaturePackage = isPackageMmtSignature(packageDetail);
    const {smeDetail} = packageDetail || {};
    const {name, profileImage, workDescription, profileType = ''} = smeDetail || {};

    return (
      <View style={mmtSignaturePackage ? {paddingHorizontal:0} : {paddingHorizontal:0}}>
        <View style={[styles.bannerImgWrapper, (isPremium ? styles.premiumBanner : null), { height: mmtSignaturePackage ? 175 : 115}]}>
            <View style={styles.bannerTopContent}>
            {!mmtSignaturePackage && startingCity && (
              <View style={[styles.destinationStrip]}>
                <Image source={iconPointer} style={[styles.iconPointer, AtomicCss.marginRight5]} />
                <Text style={styles.startingFrom}>
                  Starting from :
                </Text>
                <Text style={styles.startingCity}>
                  {startingCity}
                </Text>
              </View>
            )}
              {isSafe && <Image source={SafetyImg} style={styles.safetyImg} />}
            </View>
            <HolidayImageHolder
              defaultImage={genericCardDefaultImage}
              imageUrl={getPackageImageV2(
                packageDetail,
                `resize=${PACKAGE_OUTER_CARD_WIDTH}:${IMAGE_HEIGHT}`,
              )}
              style={[styles.bannerImg]}
            />
            {isPremium ? (
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={[holidayColors.gold, '#B8860B']}
              style={styles.premiumBannerTitle}
            >
              <Text style={styles.premiumBannerText}>
                P R E M I U M
              </Text>
            </LinearGradient>
          ) : null}
            {this.isIndigoCollection() ? this.render6ESpecialBanner() : null}


          {mmtSignaturePackage && name ? (
            <View style={styles.mmtSignatureSMEContainer}>
              <HolidayImageHolder
                style={styles.mmtSignatureSMEImage}
                resizeMode="cover"
                imageUrl={profileImage}
                defaultImage={require('@mmt/legacy-assets/src/holidays/profile-sme.webp')}
              />
              <Text style={styles.signatureProfileType}>
                {profileType} :<Text style={styles.signatureProfileName}>{name}</Text>
              </Text>
              {!!workDescription && (
                <Text
                  style={[
                    styles.signatureWorkDescription,
                    workDescription ? AtomicCss.blackText : AtomicCss.greyText,
                    workDescription ? { marginRight: 47 } : {},
                  ]}
                  numberOfLines={1}
                  ellipsizeMode={'tail'}
                >
                  {workDescription}
                </Text>
              )}
            </View>
          ) : (
            []
          )}
          </View>
      </View>
    );

  }
  render6ESpecialBanner = () => {
    return (
      <View style={styles.indigoSpecialWrapper}>
          <Image style={styles.indigoSpecialIcon} source={require('@mmt/legacy-assets/src/holidays/logo_indigo.webp')}/>
          <Text style={styles.indigoSpecialText}>6E Special</Text>
      </View>
    );
  }
  getVisaView = (visas) => {
    if (visas || visas.length > 0) {
      const {visaCharges, visaSc} = visas[0] || {};
      let textToShow = '';

      if (visaCharges) {
        textToShow = 'VISA';
      } else if (visaSc) {
        textToShow = 'VISA Assistance';
      }

      if (!isEmpty(textToShow)) {
        return (
          <Text
            numberOfLines={1}
            style={styles.packageTextVisa}
          >
            + {textToShow}
          </Text>
        );
      }
    }
    return [];
  };

  getHeader(packageDetail) {
    const { visaDetails, genreDetails, tagDetails } = packageDetail;
    const {visas} =  visaDetails || {};
    const listingStaticData = packageDetail.listingStaticData;
    const {title, imageUrl} = listingStaticData?.packageType || {};

    let showMmtSignature = isPackageMmtSignature(packageDetail);

    if (showMmtSignature){
      return  this.getMmtSignatureView(genreDetails, tagDetails);
    }

    return (
      <View style={styles.headerWrap}>
        <View>
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <PlaceholderImageView style={styles.packageTypeIcon} url={imageUrl} />
            <Text style={styles.packageText}>{title?.toUpperCase()}</Text>
            {this.getVisaView(visas)}
          </View>
        </View>
        <View style={styles.packageName}>
          <Text
            numberOfLines={1}
            style={styles.packageNameText}
          >
            {packageDetail.name}
          </Text>
        </View>
      </View>
    );

  }

  getMmtSignatureView = (genreDetails, tagDetails) => {
    if (!genreDetails || genreDetails.length < 1){
      return [];
    }
    let tagText = '';
    const {genreType, genreImageURL, images } = genreDetails[0];
    const {RAW} = images || {};
    const {tags} = tagDetails || {};
    if (tags && tags.length > 0) {
      tags.forEach(item => {
        const {tag, values} = item || {};
        if (tag === 'Tags' && values && values.length > 1 && values[0] === 'Signature') {
          tagText = values[1];
        }
      });
    }

    return (
      <LinearGradient
        start={{x: 0.0, y: 0.0}}
        end={{x: 1.0, y: 0.0}}
        colors={['#a30768', '#ee4b60']}
        style={styles.mmtSignatureGradient}>
          <Image
            style={styles.mmtSignatureImage}
            resizeMode={'contain'}
            source={genreImageURL
              ? {uri: convertUrlToHttps(RAW)}
              : require('@mmt/holidays/src/PhoenixDetail/Components/images/signature-label.png')}
          />
          <Text style={styles.signatureTagText}>{tagText}</Text>
      </LinearGradient>
    );
  }

  viewPackages = (isHotel) => {
    this.props.viewPackages({
      isHotel: isHotel,
      groupName: this.props.groupName,
      groupIndex: this.props.groupIndex,
      index: this.props.index,
    });
  };
  checkHotelsOthers = (val)=>{
    return variantEnum.HOTELS === val || variantEnum.OTHERS === val;
   }
  render() {
    const packageDetail = this.props.item;
    const isSafe = packageDetail.safe === true;
    let isPremium = false;
    if (packageDetail.listingStaticData && packageDetail.listingStaticData.premium) {
      isPremium = packageDetail.listingStaticData.premium;
    }
    const mmtSignaturePackage = isPackageMmtSignature(packageDetail);

    const durationDestMap = this.getDestinations();


    let isPersuasion = false;
    let { persuasions} = packageDetail || {};
    if ((packageDetail.persuasions && packageDetail.persuasions.length > 0)) {
      isPersuasion = true;
      persuasions = persuasions[0];
    }
    const attractions = this.getAttractions(packageDetail);
    let showNextAttraction = true;
    const MAX_ATTRACTION_LENGTH = 22;
    const maxDest = this.splitDurationListMax(durationDestMap);

    const { packageVariant, isHotel, hotelLength, outerCardStyle, variantTypeFromPokus } =
      this.props || {};

      const checkIfHotelOrOthervariantdata =
      (packageVariant && !isHotel) || (isHotel && hotelLength > 0);

      const variantData = isHotel
      ? packageDetail?.hotelDetails?.categories || {}
      : packageVariant?.packageVariants || {};

      const variantAvailable =
      !!variantTypeFromPokus &&
      this.checkHotelsOthers(variantTypeFromPokus) &&
      checkIfHotelOrOthervariantdata &&
      variantData?.length > 1;

    return (
        <View>
        {variantAvailable && (
            <Fragment>
              <View style={styles.stack1} />
              <View style={styles.stack2} />
            </Fragment>
        )}
        <View style={[styles.cardOuterWrapper, outerCardStyle, isPersuasion ? styles.marginBottom30 : sectionBottomSpacing]}>
          {variantAvailable && (
            <View style={styles.infoPill}>
              <TouchableOpacity onPress={()=>this.viewPackages(isHotel)}>
              {!isHotel && (
                  <Text
                    style={[styles.pillData]}
                    numberOfLines={1}
                  >
                    {packageVariant?.packageVariants?.length - 1} More
                    <Text>
                      {' '}
                      {packageVariant?.packageVariants?.length - 1 > 1
                        ? OPTIONS_AVAILABLE
                        : OPTION_AVAILABLE}
                    </Text>
                  </Text>
                )}
                {!!isHotel && (
                  <Text
                    style={[styles.pillData, { color: '#008CFF' }, AtomicCss.boldFont]}
                    numberOfLines={1}
                  >
                    {packageDetail?.hotelDetails?.categories?.length - 1} More
                    <Text>
                      {packageDetail?.hotelDetails?.categories?.length - 1 > 1
                        ? OPTIONS_AVAILABLE
                        : OPTION_AVAILABLE}
                    </Text>
                  </Text>
                )}

              </TouchableOpacity>
            </View>
          )}
         <TouchableOpacity
            onPress={() => {
              variantAvailable
                ? this.viewPackages(isHotel)
                : this.onCardClick();
            }}
          >
          <View
              style={[
                styles.cardContainer,
                isPremium ? styles.premiumBorder : null,
                mmtSignaturePackage ? {} : styles.cardPadding,
              ]}
            >
            {/* header */}
            {this.getHeader(packageDetail)}
            {/* banner */}
            {this.getBanner(isSafe, isPremium, packageDetail)}

            <View style={styles.duration}>
                <Text style={styles.durationText}>
                  {`${packageDetail.nights}N/${packageDetail.nights + 1}D`}
                </Text>
              </View>
            {/* content */}
            <View style={mmtSignaturePackage ? styles.cardPadding : styles.content}>
              {/* itenaryList */}
             {this.itenaryList()}
              {mmtSignaturePackage && this.quotedTextView(packageDetail)}
              {/* split duration list  */}
              <View
                style={[
                  styles.splitDurationList,
                  mmtSignaturePackage
                    ? { paddingBottom: 8, paddingTop: 2 }
                    : { paddingBottom: 16,}
                ]}
              >
                  {this.splitDurationList(durationDestMap, maxDest)}
                  {Object.keys(durationDestMap).length > maxDest && (
                    <Text style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                      <Text style={styles.durationNights}>
                        +{Object.keys(durationDestMap).length - maxDest}
                      </Text>
                      <Text style={[styles.durationListLoc]}>{' more'}</Text>
                    </Text>
                  )}
                </View>

              {mmtSignaturePackage && this.getStartingFromView(packageDetail)}
              {/* price */}
              <View style={styles.priceWrapper}>
                {/* attractions */}
                {!mmtSignaturePackage && (
                  <View style={styles.attractionWrap}>
                    {attractions.map((item, index) => {
                      if (index === 0) {
                        if (showNextAttraction && item.length > MAX_ATTRACTION_LENGTH) {
                          showNextAttraction = false;
                          return (
                            <Text numberOfLines={2} key={index} style={styles.activitiesText}>
                              &#8226;  {item}
                            </Text>
                          );
                        } else {
                          return (
                            <Text numberOfLines={1} key={index} style={styles.activitiesText}>
                              &#8226;  {item}
                            </Text>
                          );
                        }
                      } else if (showNextAttraction) {
                        return (
                          <Text numberOfLines={1} key={index} style={styles.activitiesText}>
                            &#8226;  {item}
                          </Text>
                        );
                      } else {
                        return [];
                      }
                    })}
                  </View>
                )}
                {this.itineraryListForMmtSignature(mmtSignaturePackage)}
                {this.getPrice({packageDetail, mmtSignaturePackage})}
              </View>
            </View>
          </View>
          {/* Footer */}
       {/*    //todo ashish*/}
          {/*{isPersuasion && (
            <View
              style={[
                styles.cardFooter,
                isPremium || mmtSignaturePackage ? styles.premiumFooter : null,
              ]}
            >
              <HTMLView
                value={`<p>${persuasions}</p>`}
                stylesheet={htmlStylePersuasions}
                renderNode={this.renderNode}
              />
            </View>
          )}*/}
          </TouchableOpacity>
        </View>
        </View>
    );
  }


}

export const getPackageDetail = (packageId, packageDetails) =>
 packageDetails.find(packageDetail => packageDetail?.id === packageId);


export const getPackageCategoryPrice = (defaultCategoryId, packageDetail) => packageDetail.priceDetails.categoryPrices.filter(row =>
  row.categoryId === defaultCategoryId)[0];

const styles =  StyleSheet.create({
  cardOuterWrapper: {
    width: PACKAGE_OUTER_CARD_WIDTH,
    marginRight: 10,
  },
  cardContainer: {
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius16,
    height: PACKAGE_OUTER_CARD_HEIGHT,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    zIndex: 1,
    marginTop: 0,
  },
  premiumBorder: {
    borderColor: holidayColors.gold,
  },
  headerWrap: {
    paddingVertical: 5,
    paddingBottom:5,
    paddingRight: 10,
  },
  packageName: {
    marginVertical: 3,
  },
  packageNameText: {
    ...fontStyles.labelMediumBold,
    ...AtomicCss.blackText,
  },
  premiumBanner: {
    borderWidth: 1,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    borderColor: holidayColors.gold,
  },
  premiumBannerTitle: {
    paddingVertical: 6,
    paddingHorizontal: 5,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderBottomRightRadius: 4,
    borderBottomLeftRadius: 4,
  },
  premiumBannerText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  bannerImgWrapper: {
    width: '100%',
    position: 'relative',
  },
  bannerImg: {
    width: '100%',
    height: '100%',
    ...holidayBorderRadius.borderRadius16,
  },
  bannerTopContent: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    zIndex: 1,
  },
  safetyImg: {
    alignSelf: 'flex-end',
    width: 16,
    height: 16,
    marginTop: 5,
    marginRight: 5,
  },
  duration: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    backgroundColor: holidayColors.gray,
    alignSelf: 'flex-end',
    marginTop: -12,
    marginRight: 6,
  },
  durationText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
  componentBox: {
    alignItems: 'center',
  },
  mmtSignatureItineraryContainer: {
    flexDirection: 'row',
  },
  mmtSignatureComponentBox: {
    flexDirection: 'row',
    marginBottom: 10,
    marginRight: 8,
  },
  content: {

  },
  componentList: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...paddingStyles.pb16,
    marginTop: 0,
  },
  componentText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  mmtSignatureComponentText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    marginTop: 2,
    marginRight: 3,
  },
  componentImg: {
    width: 20,
    height: 20,
    marginBottom: 4,
  },
  splitDurationList: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cityDurationContainer: {
    paddingBottom: 8,
  },
  durationNights: {
    ...fontStyles.labelBaseBold,
    color: '#EB2026',
    paddingRight: 3,
  },
  durationListLoc: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  durationSplitter: {
    paddingHorizontal: 3,
  },
  durationSplittedText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  priceWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  priceContainer: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginBottom: 5,
  },
  actualPrice: {
    textAlign: 'center',
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  perPerson: {
    textAlign: 'center',
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginTop: -1,
  },
  finalPrice: {
    textAlign: 'center',
    ...fontStyles.headingBase,
    lineHeight: 19,
    color: holidayColors.black,
    marginTop: 3,
  },
  priceSlash: {
    top: 8,
    left: 0,
    width: '65%',
    height: 1,
    backgroundColor: '#ff0000',
    position: 'absolute',
    transform: [{ rotate: '170deg' }],
  },
  activitiesText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    flexWrap: 'wrap',
    flexDirection: 'row',
    marginVertical: 3,
  },
  cardFooter: {
    top: -10,
    backgroundColor: holidayColors.fadedGreen,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 10,
    paddingTop: 15,
    padding: 10,
    borderWidth: 1,
    borderTopWidth: 0,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#d1d1d1',
    width: '100%',
    height: 43,
  },
  premiumFooter: {
    backgroundColor: holidayColors.fadedYellow,
    borderColor: '#EDD287',
  },
  synopsysText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    ...sectionHeaderSpacing,
    zIndex: 2
  },
  synopsysName: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
  },
  yellowQuote: {
    position: 'absolute',
    width: 24,
    height: 17,
    marginTop: -6,
  },
  startingFrom: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  startingCity: {
    ...fontStyles.labelSmallBold,
  },
  signatureStartingFromContainer: {
    flexDirection: 'row',
    marginTop: 1,
    alignItems: 'center',
  },
  location: {
    width: 8,
    height: 10,
    marginEnd: 5,
    marginTop: 1,
  },
  destinationStrip: {
    backgroundColor: 'white',
    paddingLeft: 5,
    padding: 5,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: -8,
    marginLeft: -1,
    width: '102%',
  },
  attractionWrap: {
    height: 32,
    flexWrap: 'wrap',
    flexDirection: 'row',
    width: '60%',
  },
  packageTypeIcon: {
    width: 15,
    height: 15,
  },
  packageText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
    marginLeft: 5,
  },
  packageTextVisa: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
    marginLeft: 2,
  },
  iconPointer: {
    width: 6,
    height: 7.5,
  },
  premiumTxt: {
    ...fontStyles.labelSmallBold,
    color: '#D09F15',
    paddingBottom: 6,
    marginTop: -9,
  },
  signatureWorkDescription: {
    ...fontStyles.labelSmallRegular,
    marginLeft: 55,
  },
  signatureProfileType: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    marginLeft: 55,
  },
  signatureProfileName: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.red,
  },
  mmtSignatureGradient: {
    position: 'absolute',
    zIndex: 1,
    top: 0,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
    paddingTop: 5,
  },
  mmtSignatureImage: {
    width: 112,
    height: 25,
    marginLeft: 14,
  },
  signatureTagText: {
    fontSize: 9,
    color: holidayColors.white,
    marginEnd: 10,
    marginTop: -5,
  },
  indigoIconWrapper: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#00238E',
    alignItems: 'center',
    justifyContent: 'center',
  },
  indigo: {
    width: 16,
    height: 16,
    tintColor: '#ffffff',
  },
  indigoSpecialWrapper: {
    paddingVertical: 3,
    flexDirection: 'row',
    width: 70,
    position: 'absolute',
    top: 10,
    left: -3,
    backgroundColor: '#001B94',
    alignItems: 'center',
    justifyContent: 'center',
  },
  indigoSpecialIcon: {
    width: 16,
    height: 16,
    tintColor: '#fff',
  },
  indigoSpecialText: {
    fontSize: 8,
    fontWeight: '600',
    color: '#fff',
    fontFamily: 'Lato-Bold',
  },
  stack1: {
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    paddingVertical: 10,
    backgroundColor: '#DFDCDC',
    marginHorizontal: 20,
    opacity: 0.6,
    width: '75%',
  },
  stack2: {
    position: 'absolute',
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    paddingVertical: 5,
    backgroundColor: '#B7B7B7',
    marginHorizontal: 10,
    opacity: 0.4,
    width: '85%',
    top: 10,
  },
  outerCardMargin: { marginTop: 20 },
  infoPill: {
    backgroundColor: '#DAEEFF',
    position: 'absolute',
    minWidth: 120,
    maxWidth: 180,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 15,
    borderBottomLeftRadius: 20,
    height: 24,
    paddingHorizontal: 10,
    top: -12,
    right: 0,
    zIndex: 10,
    borderColor: '#008CFF',
    borderWidth: 1,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  countNotAvailable: {
    opacity: 0.3,
  },
  pillData: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  mmtSignatureSMEContainer: {
    paddingVertical: 5,
    paddingHorizontal: 5,
    position: 'absolute',
    bottom: -1,
    left: 0,
    right: 0,
    backgroundColor: holidayColors.fadedYellow,
    borderBottomRightRadius: 4,
    borderBottomLeftRadius: 4,
    justifyContent: 'center',
  },
  mmtSignatureSMEImage: {
    width: 45,
    height: 45,
    position: 'absolute',
    marginTop: 0,
    marginLeft:7,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: '#dddddd',
    borderBottomWidth: 0,
    shadowOpacity: 0.3,
    shadowRadius: 1,
    elevation: 1,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    },
    cardPadding: {
      paddingHorizontal: 16,
      paddingTop: 12,
      paddingBottom: 12,
    },
    marginBottom30: {
      marginBottom: 30,
    },
});

PhoenixPackageCard.propTypes = {
  packageDetailsList: PropTypes.array.isRequired,
  item: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  groupIndex: PropTypes.number.isRequired,
  onPackageClicked: PropTypes.func.isRequired,
  groupingData: PropTypes.object.isRequired,
  fabCta: PropTypes.object.isRequired,
  walletDetail: PropTypes.object,
  groupName: PropTypes.string.isRequired,
  pt: PropTypes.string,
  aff: PropTypes.string,
};

PhoenixPackageCard.defaultProps = {
  pt: null,
  aff: null,
  walletDetail: {},
};
