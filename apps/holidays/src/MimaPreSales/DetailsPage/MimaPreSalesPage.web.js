import React, {Component} from 'react';
import {
  <PERSON><PERSON>,
  Animated,
  <PERSON>Hand<PERSON>,
  DeviceEventEmitter,
  Dimensions,
  FlatList,
  Image,
  LayoutAnimation,
  Linking,
  NativeModules,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import cloneDeep from 'lodash/cloneDeep';
import isEmpty from 'lodash/isEmpty';
import set from 'lodash/set';
import {
  componentImageTypes,
  deepLinkParams,
  DEFAULT_TRAVELLER_COUNT,
  DETAIL_LOCAL_NOTIFICATION_PAGE_NAME,
  DETAIL_QUERY_PAGE_NAME,
  DETAIL_TRACKING_PAGE_NAME,
  detailReviewFailure,
  errorMessages,
  extraInfoRefs,
  itineraryUnitTypes,
  overlays,
  packageActionComponent,
  packageActions,
  PDTConstants,
} from '../../PhoenixDetail/DetailConstants';
import {
  createChatID,
  createCuesSteps,
  createRandomString,
  doCall,
  doQuery,
  exitLocalNotification,
  getPaxConfig,
  getReturnUrl,
  getReturnUrlForQuotesDetail,
  getStoragePermissionStatus,
  hasOnBoardingCuesLastVisit,
  isEmptyString, isIosClient,
  isMobileClient,
  isNotNullAndEmptyCollection,
  isOnBoardingCuesDelayOver,
  isRawClient,
  isSummaryTabDefaultOpen,
  openSeoQueryDeepLink,
  removeCuesStepsShown,
  saveHolMeta,
  sharePackage,
  startReactChat,
} from '../../utils/HolidayUtils';

import {
  CURRENT_PAGE_NAME,
  DOM_BRANCH,
  FUNNEL_ENTRY_TYPES,
  HLD_CUES_POKUS_KEYS,
  PDT_PAGE_EXIT_EVENT,
  WEEKEND_GETAWAY_PAGE_TYPE,
} from '../../HolidayConstants';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import {
  addPersuasionToDetailData,
  calculateFlightsCount,
  calculateTravellersCount,
  createHolidayDetailData,
  createLoggingMap,
  createRoomDetailsFromApi,
  createRoomDetailsFromRoomDataForPhoenix,
  createTravellerObjForLoader,
  fetchErrorMessage,
  getActionData,
  getEventName,
  getVideoUrl,
  openChangeHotelFromPhoenixPage,
  toggleDayPlan,
  updateEMIPriceForPersuasion,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import {HARDWARE_BACK_PRESS} from '../../SearchWidget/SearchWidgetConstants';
import DestDuration from '../../PhoenixDetail/Components/DestDuration';
import Duration from '../../PhoenixDetail/Components/Tags/Duration';
import PackageType from '../../PhoenixDetail/Components/Tags/PackageType';
import DetailPageHeader from '../../PhoenixDetail/Components/PageHeader';
import { trackDetailsLoadEvent, trackOmniClickEvent } from '../utils/MimaPreSalesTrackingUtils';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import LightHeader from '../Components/LightHeader';
import PackageHighlights from '../../PhoenixDetail/Components/PackageHighlights';
import SignatureBanner from '../../PhoenixDetail/Components/SignatureBanner';
import { colors, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import HolidayDetailLoader from '../../PhoenixDetail/Components/HolidayDetailLoader';
import EditOverlay from '../../PhoenixDetail/Components/EditOverlay/editBasePage';
import Accordian from '../../PhoenixDetail/Components/Accordian/Accordian';
import {ItineraryHeader} from '../../PhoenixDetail/Components/ItenaryCard';
import DateSection from '../../PhoenixDetail/Components/DayPlan/dateSection';
import {
  createChildAgeArrayFromApi,
  createChildAgeArrayFromRoomDataForPhoenix,
  createDestinationMap,
  createFlightRequestParams,
  createSightSeeingDayPlanDataMap,
  createStaticItineraryData,
  createSubtitleData,
  getComponentAccessRestrictions,
  getDates,
  getFilteredPackageFeatures,
  getHotelObject,
  getOptimizedPackageDetail, getPackageFeatureByKey, getActivityExtraData,
} from '../../PhoenixDetail/Utils/PhoenixDetailUtils';
import DayPlan from '../../PhoenixDetail/Components/DayPlan';
import {TABS} from '../../PhoenixDetail/Components/DayPlan/Sorter';
import BlackFooter from '../../PhoenixDetail/Components/BlackFooter';
import OfferOverlay from '../../DetailMimaComponents/OffersOverlayComponent/OfferOverlay';
import {createGenericTextualBottomSheet} from '../../utils/HolidayGenericViewUtils';
import DetailPersuasion from '../../PhoenixDetail/Components/HolidayDetailPersuasion';
import PricePersuasionOverlay from '../../PhoenixDetail/Components/PricePersuasionOverlay';
import GridGallery from '../../PhoenixDetail/Components/Gallery/GridGallery';
import mySafeImage from '../../PhoenixDetail/Components/images/my-safety.png';
import {showShortToast} from '@mmt/legacy-commons/Common/Components/Toast';
import FeatureList from '../../PhoenixDetail/Components/FDFeatureEdit/FeatureList';
import CustomizationPopup from '../../PhoenixDetail/CustomizationPopup';
import { initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import FloatingWidget from '../../PhoenixDetail/Components/FloatingWidget';
import FreeCancellationBanner from '../../ZeroCancellation/FreeCancellationBanner';
import SafeBanner from '../../PhoenixDetail/Components/SafeBanner';
import {setTrackingData} from '../../PhoenixDetail/Utils/PhoenixDetailTracking';
import PackageUpdatedToast from '../../PhoenixDetail/Components/PackageUpdatedToast';
import ReviewFailureHandlingPopup from '../../PhoenixDetail/Components/ReviewFailureHandlingPopup';
import HolidayDeeplinkParser from '../../utils/HolidayDeeplinkParser';
import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import {HOLIDAY_ROUTE_KEYS, HolidayNavigation} from '../../Navigation';
import {fonts} from '@mmt/legacy-commons/Common/Components/MIMABottomOverlay/OverlayMessage/globalStyles';
import PackageActions from '../Components/PackageActions';
import {sectionCodes} from '../../LandingNew/LandingConstants';
import FullPageError from '../../Common/Components/FullPageError';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import BottomSheet from '../../PhoenixDetail/Components/BottomSheet/BottomSheet';
import SelectOptionPopUp from '../Components/Popup/SelectOptionPopUp';
import {PRESALES_MIMA_DETAIL_PAGE} from '../utils/PreSalesMimaConstants';
import ExpiredPlanPopup from '../Components/Popup/ExpiredPlanPopup';
import SendUsQueryButton from '../Components/Popup/SendUsQueryButton';
import genericErrorIcon from '@mmt/legacy-assets/src/reactivate.webp';
import CloseIcon from '@mmt/legacy-assets/src/ic_cross_gray.webp';
import {populateDetailsParams} from '../utils/MimaPreSalesOmnitureUtils';
import FilterLoader from '../../SearchWidget/Components/FilterLoader';
import withIntervention from '../../Common/Components/Interventions/withIntervention';
import {getEvar108ForPSM, trackDeeplinkRececived, trackDetailsClickEvent} from '../../utils/HolidayTrackingUtils';
import withPermissionDialog from '@mmt/legacy-commons/Common/Components/PermissionDialog/withPermissionDialog';
import {clearCuesStepPositions, getValidCuesSteps } from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCueStepsUtils';
import {getUserDetails, isUserLoggedIn} from '@mmt/core/auth';
import YesNoPopUp from '../../Common/Components/YesNoPopUp';
import { Overlay } from '../../PhoenixDetail/Components/DetailOverlays/OverlayConstants';
import { downloadPdfWeb } from '../utils/mimaPreSalesUtil';
import { getNameForDownloadedFile } from '../utils/MimaPreSalesUtils';
import DetailOverlays from "../../PhoenixDetail/Components/DetailOverlays";
import BasicDetailsSection from '../../PhoenixDetail/Components/PackageInfoSection/BasicDetailsSection';
import {
  getCuesConfig,
  getHolShowStoryMob,
  getMaxUndoAllowed,
  getPokusForNewDetailContent,
  showMMTBlack,
} from '../../utils/HolidaysPokusUtils';
import FDFeatureListV2 from '../../PhoenixDetail/Components/PackageInfoSection/FDFeatureListV2';
import ModifySearchContainer from '../../PhoenixDetail/Components/PackageInfoSection/ModifySearchContainer';
import FDFeatureEditOverlayV2 from '../../PhoenixDetail/Components/FDFeatureEditOverlayV2';
import HolidayCancellationOverlayV2 from '../../PhoenixDetail/Components/HolidayCancellationOverlayV2';
import VisaContainer from '../../Common/Components/visa';
import { PACKAGE_FEATURES } from '../../PhoenixDetail/Utils/PheonixDetailPageConstants';
import PackageInfoSection from '../../PhoenixDetail/Components/PackageInfoSection';
import BottomSheetOverlay from '../../Common/Components/BottomSheetOverlay';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { initDetailPDTObj, logPhoenixDetailPDTEvents, onUpdateSearchWidgetPDTDetail } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { updateDetailPDTObj } from '../../utils/PhoenixDetailPDTDataHolder';
import SummaryView from '../../PhoenixDetail/Components/SummaryView';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import MMTBlackBottomSheet from '@mmt/holidays/src/Common/Components/Membership/BottomSheet';
import { getMemberShipCardArray } from '../../Common/Components/Membership/utils/MembershipUtils';
import MembershipCarouselV2 from '../../Common/Components/Membership/Carousel/MembershipCarouselV2';
import { CONTENT_TYPES } from '../../Common/Components/Membership/utils/constants';
import withBackHandler from '../../hooks/withBackHandler';
import { extractDataForPDTFromDetailResponse } from '../../PhoenixDetail/Utils/PhoenixDetailUtils';
import { validateAddons } from '../../utils/HolidayNetworkUtils';
import AddonsUpdatePopup from '../Components/AddonsUpdatePopup';
import { ADDON_TRACKING_VALUES } from '../../Common/Components/VisaProtectionPlan/VPPConstant';
import ValidationSummary from '../../Common/Components/ValidationSummary';
const SCREEN_WIDTH = Dimensions?.get('window')?.width || 32;


const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';
const CONTACT_EXPERT = 'contact_expert';
const MODIFY_PACKAGE = 'modify_package';
const OVERLAY_HEADING = 'Modify this travel plan';
const PRESALES_DETAIL = 'PRESALES_DETAIL';
const EXPIRED_PLAN_HEADING = 'Viewing an Expired Plan';
const EXPIRED_PLAN_SUBHEADING = 'Your travel plan has expired as holiday query has been closed. Please request a callback to send us a new query.';
const CONTINUE = 'CONTINUE';
const LOGIN_EVENT_DETAIL = 'login_event_detail';
const PRESALES_ERROR = 'PreSalesError021';
const PRESALES_ERROR_NONLOGIN = 'PreSalesError004';

let loginEventDetailListener;
class MimaPreSalesPage extends Component {
  flatListRef = React.createRef();
  activityFailedScrollRef = React.createRef();
  scrollNode = null;
  canScrollCalender = false;
  deepLinkSimilar = false;
  constructor(props) {
    super(props, 'detail');
    PerformanceMonitorModule.start('MimaPreSalesPage');
    this.replaced = !!this.props.replaced;
    if (this.props[deepLinkParams.deepLink]) {
      this.holidayDetailData = HolidayDeeplinkParser.parseDetailPageDeeplink(
        this.props[deepLinkParams.query],
        true,
      );
      this.deepLinkSimilar = true;
      if(this.holidayDetailData.cmp) {
        trackDeeplinkRececived({ [TRACKING_EVENTS.M_V81]: this.holidayDetailData.cmp })
      }
    } else {
      this.holidayDetailData = cloneDeep(this.props.holidaysDetailData);
      this.deepLinkSimilar = !!this.props.deepLinkSimilar;
    }
    if(this?.props?.detailData?.packageDetail){
      this.updateInterventionDataLocal()
    }
    this.holidayDetailData.isWG = this.holidayDetailData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    this.isLoading = true;
    this.showNewContentUI = false;
    this.state = {
      showMmtBlackBottomsheet: false,
      showSearch: false,
      showPageHeader: false,
      fixed: false,
      fab: false,
      popupData: {},
      cityId: '',
      popup: overlays.NONE,
      sectionToShow: '',
      openHalf: false,
      videoPaused: false,
      showActivityDetailForDay: false,
      showActivityDetailForInclusion: false,
      isItineraryVisible: true,
      currentActivePlanOnItinerary: isSummaryTabDefaultOpen({ fromPreSales : true})? TABS.SUMMARY : TABS.DAY,
      openFDFeatureEditVisibility: false,
      undoStack: [],
      isUndoVisible: false,
      fabTextShrinked: false,
      showPackageUpdatedToast: false,
      showingQueryCoachMark: false,
      reviewError: {},
      holidayDetailData: this.holidayDetailData,
      viewState:'',
      isOnline:false,
      agentData:{},
      expertModalVisibility:false,
      expertActiveOverlay:'',
      makeChangesMyself:false,
      showPriceHikePersuasion:true,
      showCoachMarks: false,
      userLoggedIn: false,
      userObj : null,
      showLoginPopUp: false,
      showAddonsUpdatePopup: false,
      addonsValidationDetail: {},
    };
    this.packageDetailDTO = {
      DFD: false,
      cmp: this.holidayDetailData.cmp ? this.holidayDetailData.cmp : 'detail_share',
      searchCriteria: this.holidayDetailData.searchCriteria,
      dynamicPackageId: this.holidayDetailData.dynamicPackageId,
      pageType: this.holidayDetailData.pt ? this.holidayDetailData.pt : '',
      pageName:PRESALES_MIMA_DETAIL_PAGE,
    };
    if (isEmpty(this.holidayDetailData.cmp) && !isEmpty(this.props.campaign)) {
      this.holidayDetailData.cmp = this.props.campaign;
    }
    HolidayDataHolder.getInstance().setCmp(this.holidayDetailData.cmp);
    HolidayDataHolder.getInstance().setCampaign(this.props.campaign);
    this.cusCountLimit = 3;
    if (isNotNullAndEmptyCollection(this.holidayDetailData.rooms)) {
      this.roomDetails = createRoomDetailsFromApi(this.holidayDetailData.rooms);
      this.childAgeArray = createChildAgeArrayFromApi(this.holidayDetailData.rooms);
    } else {
      this.roomDetails = [
        {
          noOfAdults: DEFAULT_TRAVELLER_COUNT,
          noOfChildrenWB: 0,
          noOfInfants: 0,
          listOfAgeOfChildrenWB: [],
          listOfAgeOfChildrenWOB: [],
          noOfChildrenWOB: 0,
        },
      ];
      this.childAgeArray = [];
    }
    const detailData = {
      requestId: createRandomString(),
      holidayDetailData: this.holidayDetailData,
      cmp: this.holidayDetailData.cmp,
      isWG: this.holidayDetailData.isWG,
    };
    detailData.pageDataMap = createLoggingMap(detailData, []);
    HolidayDataHolder.getInstance().setCurrentPage('holidaysDetail');
    saveHolMeta(this.holidayDetailData.isWG, this.holidayDetailData.aff, this.holidayDetailData.pt, this.holidayDetailData.cmp);
    this.calenderRef = React.createRef();
    this.props.clearDownloadShare();
  }

  updatePackage = (packageId) => {
    this.toggleFDFeatureBottomSheet(false);
    this.refreshDetails(false, false, packageId, false);
  };

  updateMeal = (mealCode) => {
    this.toggleFDFeatureBottomSheet(false);
    const actionData = {
      action: packageActions.CHANGE,
      dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
      mealCode: mealCode,
    };
    this.props.changeMeal(actionData, this.onPackageComponentToggled, this.onApiError);
  };

  updateVisa = (visaIncluded) => {
    this.toggleFDFeatureBottomSheet(false);
    this.checkAndUpdateReviewError(packageActionComponent.VISA);
    this.checkAndUpdatePrePaymentError({}, packageActionComponent.VISA);
    const actionData = {
      action: packageActions.TOGGLE,
      dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
      removed: !visaIncluded,
    };
    this.props.togglePackageComponent(
      actionData,
      this.onPackageComponentToggled,
      this.onApiError,
      packageActionComponent.VISA,
    );
  };

  showItinerary = () => {
    if (!this.state.isItineraryVisible) {
      this.captureClickEvents({
        eventName: 'navigation_',
        suffix: 'itinerary',
        value: 'navigation_itinerary'
      })
    }
    this.setState({ isItineraryVisible: !this.state.isItineraryVisible });
  };

  toggleDayPlan = (plan) => {
    this.captureClickEvents({eventName: 'inclusion_' , suffix : plan , value: 'inclusion_'+plan})
    this.setState({ currentActivePlanOnItinerary: plan });
  };

  handleMemberShipCardOnKnowMoreClick = (bottomSheetDetail, mmtbucketDetail, ctaText = '') => {
    const eventName = `GC_${ctaText.split(' ').join('_')}`;
    this.setState({ showMmtBlackBottomsheet: true });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
  };

  handleMmtBlackTogglePopup = () => {
    const eventName = 'GC_Popop_click_close';
    this.setState({ showMmtBlackBottomsheet: false });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
  };

  handleMmtBlackCtaButtonClick = () => {
    const eventName = 'GC_Popup_Click_got_it';
    this.setState({ showMmtBlackBottomsheet: false });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
  };

  handleTermConditionClick = (url) => {
    const eventName = 'GC_Popup_Click_t&c';
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
    this.setState({ showMmtBlackBottomsheet: false });
    GenericModule.openDeepLink(url);
  }

  trackMmtBlackClickEvent = ({eventName, prop1 = ''}) => {
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = this.props.mmtBlackDetail?.mmtBlackPdtData?.mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    this.trackLocalClickEvent(eventName, '', undefined, {omniData: {[TRACKING_EVENTS.M_V46]: evar46, [TRACKING_EVENTS.M_C1]: prop1}});
  }
  trackMemberShipLoadEvent = ({eventName, prop1, isPersonalisation}) => {
    if(!isPersonalisation){
      this.trackMmtBlackClickEvent({eventName, prop1});
      return;
    }
    this.trackLocalClickEvent(eventName, '', undefined, {omniData: {[TRACKING_EVENTS.M_C1]: prop1}});
  }

  async componentDidMount() {
    await HolidayDataHolder.getInstance().setCurrentPageNameV2(CURRENT_PAGE_NAME.HOLIDAY_DETAIL)
    HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.PSM)
    loginEventDetailListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(LOGIN_EVENT_DETAIL, this.onLoginEventReceived);
    isUserLoggedIn().then(loggedIn => {
        this.props.setReviewComponentFailureData(null);
        this.initAb().then(ret => {
          this.refreshDetails(
              false,
              !isEmptyString(this.holidayDetailData.dynamicPackageId),
              null,
              false,
          );
          HolidayDataHolder.getInstance().setSubFunnel();
    HolidayDataHolder.getInstance().setIsUserOnPreSales(true);
    initDetailPDTObj({holidayDetailData: this.holidayDetailData});
    this.lastPageName = HOLIDAY_ROUTE_KEYS.DETAIL;
          clearCuesStepPositions();
          this.handleLoggedInUser();
        });
      })
    }

  handleLoggedInUser = async () => {
    const isLoggedIn = await isUserLoggedIn();
    this.setState({ userLoggedIn: isLoggedIn });
    if (isLoggedIn) {
      const user = await getUserDetails();
      this.setState({ userObj: user });
    }
    this.isLoading = true;
    this.refreshDetails(
      false,
      !isEmptyString(this.holidayDetailData.dynamicPackageId),
      null,
      false,
    );
  }

  updateInterventionDataLocal(){
    const {detailData} = this.props || {};
    const {ticketId, additionalQuoteDetail,packageDetail:{id}} = detailData || {};
    const {agentUserName} = additionalQuoteDetail || {};
    const { tagDestinationName } = this.fetchTagDestAndBranch();
    this?.props?.updateInterventionData({quoteRequestId:this.holidayDetailData.quoteRequestId,
      ticketId:ticketId,
      agentUserName:agentUserName,
      destinationCity:tagDestinationName,
      packageId:id,
      cmp:this.holidayDetailData.cmp,
      departureDate: detailData?.packageDetail?.departureDetail?.departureDate,
      roomData: this.roomDetails,
    });
  }


  componentDidUpdate(prevProps) {
   if(prevProps?.detailData?.packageDetail?.dynamicId != this.props.detailData?.packageDetail?.dynamicId){
      this.updateInterventionDataLocal()
     }
    // Handle Login popup.
    if (prevProps?.error !== this.props.error){
      const {error, isSuccess} = this.props || {};
      const {userLoggedIn, userObj} = this.state || {};
      if (!isSuccess && error) {
        const {code} = error || {};
        if (userLoggedIn && userObj && code === PRESALES_ERROR) {
          this.setState({showLoginPopUp: true});
        } else if (!userLoggedIn && code === PRESALES_ERROR_NONLOGIN) {
         this.onLoginClicked();
        }
      }
    }
    // download PDF and share PDF logic for MWEB
    if((prevProps.downloadData?.pdf !== this.props.downloadData?.pdf) && this.props?.downloadData?.pdf && isRawClient()) {
      const fileName = `${getNameForDownloadedFile(this.holidayDetailData)}.pdf`
      downloadPdfWeb({ pdfURL: this.props.downloadData.pdf, fileName })
      this.props.clearDownloadShare();
    }
    if((prevProps.shareData?.url !== this.props.shareData?.url) && this.props?.shareData?.url && isRawClient()) {
      window.open(this.props.shareData?.url, '_blank');
      this.props.clearDownloadShare();
    }
    const activities = this?.newPackageDetail?.activityDetail?.cityActivities?.flatMap(cityActivity => cityActivity?.activities || []);
    const firstUnavailableActivity = activities?.find(activity => activity.unavailable === true);
    if (firstUnavailableActivity && this.activityFailedScrollRef.current) {
      const dayOfFirstUnavailableActivity = firstUnavailableActivity.day;
      this?.scrollToIndexFunc(dayOfFirstUnavailableActivity + 7, -this.activityIndexPerDay?.[dayOfFirstUnavailableActivity] * 150);
   }
  }

  onBackClick = ()=> {
    return this.onBackPressed();
  }

  componentWillUnmount() {
    HolidayDataHolder.getInstance().setIsUserOnPreSales(false);
    HolidayDataHolder.getInstance().clearQueryDetailForPSM();
    this.props.clearOverlays();
  }

  refreshDetails = (isChangingDate, isActionApiCalled = false, packageId, undo) => {
    this.fetchDetailDataFunc(isChangingDate, isActionApiCalled, packageId, undo);
    this.isLoading = false;
    this.setState({ fixed: false });
  };

  // Below function removes flight data present in this.props.componentFailureData;
  checkAndUpdatePrePaymentError = async (actionRequest, componentType) => {
    if (isEmpty(this.props.componentFailureData)) {
      return;
    }
    if (
      componentType === componentImageTypes.FLIGHT ||
      componentType === packageActionComponent.FLIGHT
    ) {
      const newReviewError = { ...this.props.componentFailureData };
      if (
        newReviewError &&
        newReviewError.componentErrors &&
        newReviewError.componentErrors.FLIGHT
      ) {
        delete newReviewError.componentErrors.FLIGHT;
        if (
          newReviewError.componentErrors.HOTEL &&
          newReviewError.componentErrors.HOTEL.length > 0
        ) {
          this.props.setReviewComponentFailureData(newReviewError);
        } else {
          this.props.setReviewComponentFailureData(null);
        }
      }
    } else if (componentType === componentImageTypes.HOTEL) {
      const newReviewError = { ...this.props.componentFailureData };
      const { componentErrors } = newReviewError;
      if (componentErrors && componentErrors.HOTEL && componentErrors.HOTEL.length) {
        componentErrors.HOTEL = componentErrors.HOTEL.filter((item) => {
          return !(
            item.sellableId !== actionRequest.sellableId &&
            item.sellableId === actionRequest.prevSellableId
          );
        });
        if (componentErrors.HOTEL.length === 0) {
          delete componentErrors.HOTEL;
        }
        if (isEmpty(componentErrors.HOTEL) && isEmpty(componentErrors.FLIGHT)) {
          this.props.setReviewComponentFailureData(null);
        } else {
          this.props.setReviewComponentFailureData(newReviewError);
        }
      }
    }
  };

  /**
   * Common function to update components in the package.
   **/
  onComponentChange = (actionRequest, componentType) => {
    this.isLoading = true;
    this.setState({ showReviewPopUp: false });
    switch (componentType) {
      case componentImageTypes.FLIGHT:
        this.onFlightSelected(actionRequest);
        break;
      case componentImageTypes.HOTEL:
        this.onHotelSelected(actionRequest);
        break;
      case componentImageTypes.TRANSFERS:
        this.onTransferSelected(actionRequest);
        break;
      case componentImageTypes.ACTIVITY:
        this.onActivitySelected(actionRequest);
        break;
      case componentImageTypes.COMMUTE:
        this.onCommuteSelected(actionRequest);
        break;
      default:
        break;
    }
    this.checkAndUpdateReviewError(componentType);
    this.checkAndUpdatePrePaymentError(actionRequest, componentType);
  };

  checkAndUpdateReviewError = async (componentType) => {
    if (isEmpty(this.state.reviewError)) {
      return;
    }
    const {error} = this.state.reviewError;
    const {errorType} = error || {};
    if (errorType === componentType) {
      this.setState({reviewError: {}});
    }
  }
  /**
   * Common function to remove/toggle a component from the package.
   * Same function as used in old detail page
   * */
  onPackageComponentToggle = (add, packageComponent, transferObj) => {
    this.isLoading = true;
    this.setState({ showReviewPopUp: false });
    const action = {};
    action.action = packageActions.TOGGLE;
    if (packageComponent === packageActionComponent.CAR) {
      action.sellableId = transferObj.carItinerary.sellableId;
      action.startDay = transferObj.startDay;
    } else if (packageComponent === packageActionComponent.ACTIVITY) {
      action.removeAll = true;
      action.action = packageActions.REMOVE;
    } else if (packageComponent === packageActionComponent.FLIGHT) {
      this.checkAndUpdatePrePaymentError({}, packageActionComponent.FLIGHT);
      this.checkAndUpdateReviewError(packageComponent);
    }
    action.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.props.togglePackageComponent(
      action,
      this.onPackageComponentToggled,
      this.onApiError,
      packageComponent,
    );
    const actionData = getActionData(add, packageComponent);
    this.actionLoadingText = actionData.actionLoadingText;
  };

  onFlightSelected = (actionRequest) => {
    actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.props.changeFlight(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating selected flight';
  };

  onHotelSelected = (actionRequest) => {
    actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.props.changeHotel(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating Hotel';
  };

  onTransferSelected = (actionRequest) => {
    actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;

    if (actionRequest.action === packageActions.TOGGLE) {
      this.props.togglePackageComponent(
        actionRequest,
        this.onPackageComponentToggled,
        this.onApiError,
        actionRequest.packageComponent,
      );
      this.actionLoadingText = 'Removing Transfers..';
    }

    if (actionRequest.action === packageActions.CHANGE) {
      this.props.changeTransfer(
        actionRequest,
        actionRequest.packageComponent,
        this.onPackageComponentToggled,
        this.onApiError,
      );
      this.actionLoadingText = 'Updating Transfers..';
    }
  };

  onActivitySelected = (actionRequest) => {
    this.props.modifyActivity(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating Activities...';
  };

  onCommuteSelected = (actionRequest) => {
    this.props.modifyCommute(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating Commute...';
  }

  onPackageComponentToggled = (response, lob, action) => {
    if (response) {
      if (lob === 'Activity') {
        const { actionResults } = response || {};
        let res = '';
        if (actionResults && actionResults.length > 0) {
          actionResults.forEach((item) => {
            res += item.errorMsg + '\n';
          });
          showShortToast(res);
        }
      }
      const priceChange =
        response.packageDetail.pricingDetail.categoryPrices[0].discountedPrice -
        this.packageDetailDTO.discountedPrice;
      this.pushToStack(priceChange, lob, action);
      this.refreshDetails(false, true, null, false);
      this.togglePopup('');
      this.setPackageUpdatedToastState(true);
    }
  };

  undoPackage = () => {
    this.refreshDetails(false, true, null, true);
  };

  pushToStack = (priceChange, lob, action) => {
    const tempStack = [...this.state.undoStack];
    tempStack.push({ priceChange: priceChange, lob: lob, action: action });
    if (tempStack.length > this.cusCountLimit) {
      tempStack.shift();
    }
    this.setState({
      undoStack: tempStack,
    });
  };

  popFromStack = () => {
    const tempStack = [...this.state.undoStack];
    tempStack.pop();
    this.setState({
      undoStack: tempStack,
      isUndoVisible: false,
    });
    this.setState({reviewError: {}});
    this.props.setReviewComponentFailureData({});
    this.refreshDetails(false, false, this.packageDetailDTO.dynamicPackageId, true);
    this.setPackageUpdatedToastState(true);
  };

  onApiError = (msg, alert, reload) => {
    if (msg) {
      if (alert) {
        Alert.alert('', msg);
      } else {
        showShortToast(msg);
      }
      this.isLoading = false;
      this.props.isLoading = false;
    }
    if (reload) {
      this.reloadOnError();
    } else {
      this.refreshDetails(false, false, false, false);
    }
  };

  reloadOnError = () => {
    this.packageDetailDTO.dynamicPackageId = '';
    this.refreshDetails(false, false, null, false);
  };

  fetchDetailDataFunc = async (isChangingDate, isActionApiCalled = false, packageId, undo, activityFailurePeek = false) => {
    if (packageId !== null) {
      this.holidayDetailData.packageId = packageId; //to handle update package type in case of FD packages
    }
    const response = await this.props.fetchDetailData(
      this.holidayDetailData,
      this.packageDetailDTO,
      this.packageDetailDTO.dynamicPackageId,
      this.roomDetails,
      isChangingDate,
      isActionApiCalled,
      this.actionLoadingText,
      undo,
      activityFailurePeek,
    );
    // Destructure values from response with default empty strings
    if (response){
    const {
      customerId = '',
      chatId = '',
      channel = '',
      agentId = '',
      channelCode = '',
      agentChannelCode = '',
      agentBucket = '',
      queryBucket = '',
      quoteRequestId= '',
      additionalQuoteDetail,
      ticketId='',
      queryId = ''
    } = response || {};
    const {quoteId=''}=additionalQuoteDetail || {}

    const queryDetailData={
      created_for: customerId,
      query_id: queryId || chatId,
      channel: channel,
      agent_id: agentId,
      channel_code: channelCode,
      agent_channel_code: agentChannelCode,
      agent_bucket: agentBucket,
query_bucket: queryBucket,
      quote_id:quoteId,
      quote_request_id:quoteRequestId,
      id:ticketId

    }
     HolidayDataHolder.getInstance().setQueryDetailForPSM(queryDetailData)
      }
    /********* Handle onboarding cues **********/
    const hasPageVisitTime = await hasOnBoardingCuesLastVisit(HLD_CUES_POKUS_KEYS.PSM);
    if (hasPageVisitTime) {
      const delayOver = await isOnBoardingCuesDelayOver(HLD_CUES_POKUS_KEYS.PSM);
      if (delayOver) {
        //  clean page keys, since we need to show it again
        await removeCuesStepsShown(HLD_CUES_POKUS_KEYS.PSM);
        await this.showCoachMarksOverlay(response);
      }
    } else {
      await this.showCoachMarksOverlay(response);
    }
    PerformanceMonitorModule.stop();
  };

  onScroll = (event) => {
    LayoutAnimation.configureNext(LayoutAnimation.create(100, 'easeInEaseOut', 'opacity'));
    const scrollIndex = event.nativeEvent.contentOffset.y;
    // Handle FAB button animation here
    if (scrollIndex > 50 && !this.state.fabTextShrinked) {
      this.setState({
        fabTextShrinked: true,
      });
    }
  };

  prepareDayPlanSections = (packageDetail,sightSeeingMap) => {
    const {currentActivePlanOnItinerary} = this.state;
    const summaryData = packageDetail?.packageSummary || [];
    if (currentActivePlanOnItinerary === TABS.SUMMARY && this.state.isItineraryVisible) {
      return [
        {
          id: 'Summary',
          sectionName: 'PackageSummaryTabSection',
          component: [
            <View style={styles.summaryContainer}>
              <SummaryView summaryData={summaryData} />
            </View>,
          ],
        },
      ];
    } else if (currentActivePlanOnItinerary === TABS.VISA && this.state.isItineraryVisible) {
      return [
        {
          id: 'visa',
          component: [
            <View style={styles.visaContainer}>
              <VisaContainer
                packageContent={this.props.packageContent}
                visaContentLoading={isEmpty(this.props.packageContent)}
                dynamicPackageId={this.props.detailData?.packageDetail?.dynamicId || ''}
                visaPackageFeature={getPackageFeatureByKey({
                  key: PACKAGE_FEATURES.VISA,
                  packageFeatures: this.props.detailData.packageDetail.packageFeatures,
                })}
                fromPresalesBase={true}
              />
              <View style={styles.seperator} />
            </View>,
          ],
        },
      ];
    }
    const { itineraryDetail, metadataDetail, imageDetail, destinationDetail, departureDetail } =
      packageDetail || {};
    const { images = [] } = imageDetail || {};
    const { bundled = false } = metadataDetail || {};
    const { dynamicItinerary, staticItinerary } = itineraryDetail || {};
    const { dayItineraries = [] } = dynamicItinerary || {};
    const {showOverlay, hideOverlays, clearOverlays} = this.props || {};
    const { duration, destinations } = destinationDetail || {};
    const destinationMap = createDestinationMap(duration, destinations);
    const staticData = createStaticItineraryData(staticItinerary, images);
    const flightReqParams = createFlightRequestParams(packageDetail);
    const sections = [];
    if (dayItineraries && dayItineraries.length > 0) {
      dayItineraries.forEach((data, index) => {
            // Find the index of activity unit type for each day
            let activityIndex = data.itineraryUnits.findIndex((unit) => {
              const { itineraryUnitType } = unit;
              return itineraryUnitType === itineraryUnitTypes.ACTIVITY;
            });
            // Store activity index per day in an object
            if (!this.activityIndexPerDay) {
              this.activityIndexPerDay = {};
            }
            this.activityIndexPerDay[data.day] = activityIndex;
        let indexItem = data.itineraryUnits.findIndex((unit, index) => {
          const { itineraryUnitType } = unit;
          return (
            itineraryUnitType === itineraryUnitTypes.TRANSFERS ||
            itineraryUnitType === itineraryUnitTypes.CAR
          );
        });

        if (Object.keys(sightSeeingMap).length > 0 && indexItem === -1) {
          if (sightSeeingMap[data.day]) {
            data.itineraryUnits.push(sightSeeingMap[data.day]);
          }
        } else if (Object.keys(sightSeeingMap).length > 0 && indexItem >= 0) {
          if (sightSeeingMap[data.day]) {
            data.itineraryUnits.splice(indexItem + 1, 0, sightSeeingMap[data.day]);
          }
        }
        let failedHotels = [];
        let ifFlightGroupFailed = false;
        if (!isEmpty(this.state.reviewError)) {
          if (this.state.reviewError.error) {
            if (
              this.state.reviewError.error.errorType ===
              detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL
            ) {
              if (this.state.reviewError.error.errorData) {
                failedHotels = this.state.reviewError.error.errorData.failedHotels || [];
              }
            } else if (
              this.state.reviewError.error.errorType ===
              detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT
            ) {
              if (this.state.reviewError.error.code) {
                ifFlightGroupFailed = true;
              }
            }
          }
        } else if (!isEmpty(this.props.componentFailureData)) {
          const { componentErrors = {} } = this.props.componentFailureData || {};
          const { FLIGHT = [], HOTEL = [] } = componentErrors;
          if (FLIGHT && FLIGHT.length > 0) {ifFlightGroupFailed = true;}
          if (HOTEL && HOTEL.length > 0) {failedHotels = HOTEL;}
        }

        const {detailData} = this.props  || {};
        const restrictions = {
          changeFlightRestricted: true,
          removeFlightRestricted: true,
          changeHotelRestricted: true,
          removeHotelRestricted: true,
          addActivityRestricted: true,
          ratePlanRestricted :false ,
          removeActivityRestricted: true,
          changeTransferRestricted: true,
          removeTransferRestricted: true,
          activityRestrictionReviewPage: true,
          psmAddActivityRestricted:true,
        };
        {/* Hardcoded for phase 1 */}
        if (detailData?.packageDetail){
        set(detailData.packageDetail, 'packageConfigDetail.componentAccessRestriction', restrictions);
      }
        sections.push({
          id: `DayplanSection_${index}`,
          component: [
            <DayPlan
              failedHotels={failedHotels}
              ifFlightGroupFailed={ifFlightGroupFailed}
              data={data}
              roomDetails={this.roomDetails}
              staticData={staticData}
              destinationMap={destinationMap}
              bundled={bundled}
              packageDetail={{
                ...packageDetail,
                packageConfigDetail: {
                  ...packageDetail.packageConfigDetail,
                  componentAccessRestriction: restrictions,
                },
              }}
              index={index + 1}
              totalDays={dayItineraries.length}
              isVisible={this.state.isItineraryVisible}
              currentActivePlanOnItinerary={this.state.currentActivePlanOnItinerary}
              togglePopup={this.togglePopup}
              onComponentChange={this.onComponentChange}
              onPackageComponentToggle={this.onPackageComponentToggle}
              packageDetailDTO={this.packageDetailDTO}
              flightReqParams={flightReqParams}
              branch={this.props.detailData.branch}
              trackLocalClickEvent={this.trackLocalClickEvent}
              trackLocalPageLoadEvent={this.trackLocalPageLoadEvent}
              lastPageName={this.lastPageName}
              packageContent={this.props.packageContent}
              isLoading={this.props.isLoading}
              detailData={detailData}
              hotelDetailLoading={this.props.hotelDetailLoading}
              fromPresales = {true}
              showOverlay={showOverlay}
              hideOverlays={hideOverlays}
              clearOverlays={clearOverlays}
            />,
          ],
        });
      });
    }
    return sections;
  };

  scrollToIndexFunc = (index,viewOffset = 125) => {
    if (this.flatListRef) {
      this.canScrollCalender = true;
      setTimeout(() => {
        this?.flatListRef?.scrollToIndex?.({ animated: true, index, viewOffset });
      }, 200);
    }
  };
  onScrollDragBegin = () => {
    this.canScrollCalender = false;
  };

  onViewableItemsChanged = ({ viewableItems, changed }) => {
    if (!this.canScrollCalender) {
      if (this.state.currentActivePlanOnItinerary === TABS.DAY) {
        const index = this.getDaySectionIndex(viewableItems);
        if (this.calenderRef && this.calenderRef.current && index != null) {
          this.calenderRef.current._updateSelectedDate(index);
        }
      }
    }
  };

  renderItem = ({ item }) => item.component;

  handlePDT = (eventName) => {
    this.captureClickEvents({ eventName });
  };

  editTravelDetails = () => {
    this.setState({ showSearch: false });
    this.setState({ showPageHeader: false });
    this.trackLocalClickEvent(PDTConstants.EDIT_INTENT, '');
    this.togglePopup(overlays.EDIT_OVERLAY);
  };

  captureClickEvents = ({eventName = '', value = '',actionType = {},  suffix = '',pageName = '', extraInfo = {}  } = {}) => {
    logPhoenixDetailPDTEvents({
      actionType :  !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value : value || eventName,
    })
    this.trackLocalClickEvent(eventName, suffix, pageName, extraInfo );
  }

  trackLocalClickEvent = (eventName, suffix = '', pageName = DETAIL_TRACKING_PAGE_NAME, extraInfo = {}) => {
    const {cmp, source = ''} = this.holidayDetailData || {};
    const { pageDataMap = {} } = this.props.detailData || {};
    const { trackingDetails } = pageDataMap || {};
    const { ticketSource = ''} = trackingDetails || {};
    const paramsObject = {
      omniPageName: pageName,
      omniEventName: eventName + suffix,
      suffix: '',
      omniData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForPSM({
          pageName,
          source: source || '',
          ticketSource,
        }),
        m_v81: eventName === 'DEEPLINK_RECEIVED' ? cmp : undefined,
      },
      pageDataMap: {
        ticketId: this.props.detailData?.ticketId,
        isPresales: true,
        cmp,
      },
      event: PDTConstants.PDT_RAW_EVENT,
      branch: this.props.detailData.branch,
      activity: eventName + suffix,
    };
    if (Object.keys(extraInfo).length > 0) {
      paramsObject.pageDataMap = {...paramsObject.pageDataMap,...extraInfo};
    }
    trackOmniClickEvent(populateDetailsParams,paramsObject);
  };

  trackLocalChatClickEvent = (eventName, suffix, pageName = DETAIL_TRACKING_PAGE_NAME) => {
    const { source = '' } = this.holidayDetailData || {};
    const paramsObject = {
      omniPageName: pageName,
      omniEventName: eventName + suffix,
      suffix: '',
      pageDataMap: {
        ...this.props.detailData.pageDataMap,
        ticketId: this.props.detailData?.ticketId,
        source,
      },
      eventType: PDTConstants.PDT_RAW_EVENT,
      branch: this.props.detailData.branch,
      activity: eventName + suffix,
      requestId: createRandomString(),
    };
    trackOmniClickEvent(populateDetailsParams,paramsObject);
  };

  trackLocalPageLoadEvent = (event, logOmni = false, pageName = '') => {
    trackDetailsLoadEvent({
      logOmni,
      omniPageName: pageName,
      pdtData: {
        pageDataMap: this.props.detailData ? this.props.detailData.pageDataMap : {},
        interventionDetails: this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
        activity: PDTConstants.PDT_RAW_EVENT,
        event: event,
        requestId: createRandomString(),
        branch: this.props.detailData ? this.props.detailData.branch : '',
        source: this.holidayDetailData.source || '',
      },
    });
  };

  togglePopup = (popupName, cityId, sectionToShow, openHalf = false) => {
    const eventName = getEventName(popupName, sectionToShow, this.placesName);
    const { source = '' } = this.holidayDetailData || {};
    this.setState({
      popup: popupName,
      popupData: {},
      cityId,
      sectionToShow,
      openHalf,
      videoPaused: true,
      showActivityDetailForInclusion: false,
      activityDetailData: {},
    });
    const paramsObject = {
      omniPageName: DETAIL_TRACKING_PAGE_NAME,
      omniEventName: eventName ,
      pageDataMap: {
        ...this.props.detailData.pageDataMap,
        ticketId: this.props.detailData?.ticketId,
        source,
      },
      event: PDTConstants.PDT_RAW_EVENT,
      branch: this.props.detailData.branch,
      activity: eventName,
      requestId: createRandomString(),
      interventionDetails: this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
    };
    if (this.props.detailData && this.props.detailData.pageDataMap) {
      trackOmniClickEvent(populateDetailsParams,paramsObject);
    }
  };

  packageReview = async (reviewRequestSource="DETAILS_PAGE_REVIEW") => {
    this.trackLocalClickEvent('Continue');

    // Proceed with addon validation and review
    await this.validateAndProceedToReview(reviewRequestSource);
  };

  validateAndProceedToReview = async (reviewRequestSource) => {
    const dynamicId = this.props.detailData?.packageDetail?.dynamicId;

    if (!dynamicId) {
      this.proceedToPackageReview(reviewRequestSource);
      return;
    }

    // Check if packageFeatures contains any ADDON type
    const packageFeatures = this.props.detailData?.packageDetail?.packageFeatures || [];
    const hasAddonFeature = packageFeatures.some(feature => feature.type === 'ADDON');

    // If no ADDON type exists in packageFeatures, skip validation and proceed to review
    if (!hasAddonFeature) {
      this.proceedToPackageReview(reviewRequestSource);
      return;
    }

    // Validate addons before proceeding
    const cmp = this.holidayDetailData?.cmp || 'email';
    const cmpCreatedTime = this.holidayDetailData?.cmpCreatedTime || Date.now();
    const validationData = await validateAddons(dynamicId, cmp, cmpCreatedTime);

    if (!validationData?.success) {
      // If validation fails or no data, proceed to review
      showShortToast('Something went wrong. Please try again.');
      return;
    }

    // Check if all addons are valid (CONTINUE action)
    const { addonsValidationDetail = [] } = validationData;
    const invalidAddons = addonsValidationDetail.filter(
      (addon) => addon.addonAction !== "CONTINUE"
    );

    if (invalidAddons.length === 0) {
      // All addons are valid, proceed to review
      this.proceedToPackageReview(reviewRequestSource);
    } else {
      // Show addons update popup for invalid addons
      this.setState({
        showAddonsUpdatePopup: true,
        addonsValidationDetail: validationData,
      });
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.contentSeen,
        value:ADDON_TRACKING_VALUES.POPUP_VIEW,
        errorDetails: !isEmpty(invalidAddons) ? invalidAddons.map(({ addonId, promptMessage }) => ({
            code: addonId,
            message: promptMessage?.messageSubHeadings?.[0] || '',
        })):[]
    });
    }
  };

  proceedToPackageReview = async (reviewRequestSource="DETAILS_PAGE_REVIEW") => {
    this.setState({ videoPaused: true });
    this.setState({ reviewError: {}, showReviewPopUp: false });
    if (!isEmpty(this.props.componentFailureData)) {
      this.props.setReviewComponentFailureData({});
    }
    const dynamicPackageId = this.props.detailData.packageDetail.dynamicId;
    if (dynamicPackageId) {
      const holidayReviewData = {};
      holidayReviewData.showChat =
        this.props.fabCta && this.props.fabCta.showChat ? this.props.fabCta.showChat : false;
      holidayReviewData.dynamicPackageId = dynamicPackageId;
      holidayReviewData.quoteRequestId = this.props.detailData.quoteRequestId;
      holidayReviewData.searchCriteria = this.holidayDetailData.searchCriteria;
      holidayReviewData.cmp = this.holidayDetailData.cmp;
      holidayReviewData.roomDetails = this.roomDetails;
      holidayReviewData.packageName = this.props.detailData.packageDetail.name;
      holidayReviewData.packageId = this.props.detailData.packageDetail.id;
      holidayReviewData.packageType = this.props.detailData.packageDetail.metadataDetail.packageType;
      holidayReviewData.categoryId = this.holidayDetailData.categoryId;
      holidayReviewData.duration = this.props.detailData.packageDetail.destinationDetail.duration;
      holidayReviewData.branch = this.props.detailData.packageDetail.metadataDetail.branch;
      holidayReviewData.tagDestination = this.props.detailData.packageDetail.tagDestination.name;
      holidayReviewData.isWG = this.props.detailData.isWG;
      holidayReviewData.pt = this.holidayDetailData.pt;
      holidayReviewData.aff = this.holidayDetailData.aff;
      holidayReviewData.reviewType = sectionCodes.PRESALES;
      holidayReviewData.oldQuoteRequestId = this.props.detailData.quoteRequestId;
      holidayReviewData.ticketId = this.props.detailData.ticketId;
      holidayReviewData.source = this.holidayDetailData.source || '';

      this.trackLocalClickEvent(PDTConstants.BOOK, '');
      this.trackPageExit();
      this.props.fetchReviewData(
        holidayReviewData,
        holidayReviewData.roomDetails,
        false,
        {},
        null,
        true,
        (reviewData) => {
          this.goToReview(holidayReviewData, reviewData);
        },
        this.handleReviewFailure,
        false,
        false,
        reviewRequestSource
      );
    }
  };

  pageName = () => {
    if (this.props.branch === DOM_BRANCH) {
      return 'funnel: in presales domestic holidays: details';
    }
    return 'funnel: in presales outbound holidays: details';
  };

  handleAddonsUpdateAcknowledge = () => {
    this.proceedToPackageReview();
    this.setState({ showAddonsUpdatePopup: false });
    logPhoenixDetailPDTEvents({
      actionType:PDT_EVENT_TYPES.buttonClicked,
      value:ADDON_TRACKING_VALUES.POPUP_CONTINUE,
      errorDetails:this.state.addonsValidationDetail.addonsValidationDetail.map(({ addonId, promptMessage }) => ({
          code: addonId,
          message: promptMessage?.messageSubHeadings?.[0] || '',
      }))

  })
  };

  handleAddonsPopupClose = () => {
    this.setState({ showAddonsUpdatePopup: false });
  };

  refreshFetchContent = (departureDate, departureCity, roomData, packagePaxDetail) => {
    this.roomDetails = createRoomDetailsFromRoomDataForPhoenix(roomData, packagePaxDetail);
    this.childAgeArray = createChildAgeArrayFromRoomDataForPhoenix(roomData);
    this.holidayDetailData.departureDetail.departureCity = departureCity;
    this.holidayDetailData.departureDetail.departureDate = departureDate;
    this.refreshDetails(false, false, false, false);
    this.togglePopup('');
  };

  onBackPressed = () => {
    const {popup} = this.state || {};
    if (this?.props?.leaveIntent?.toUpperCase() ===  'Y' && popup === '') {
      this?.props?.close(); {/* to handle intervention on back press */}
      return true;
    } else {
      if (popup !== '') {
        this.togglePopup('');
      } else if ((this.props[deepLinkParams.deepLink] || this.deepLinkSimilar) && !isRawClient()) {
        if (isIosClient()) {
          if (this.holidayDetailData.refreshLanding) {
            this.holidayDetailData.refreshLanding();
          }
          ViewControllerModule.popViewController(1);
        } else {
          BackHandler.exitApp();
        }
      } else {
        if (this.holidayDetailData.refreshLanding) {
          this.holidayDetailData.refreshLanding();
        }

        // performActionOnQuotesListing callback need to be sent from the page which has opened this screen.
        // This will be used to perform any action on previous page if user press back button and go back.
        // This handling is specific and needs to be done by caller.
        // http://jira.mmt.com/browse/HLD-11424
        const {performActionOnQuotesListing, ticketStatusOnListing} = this.props;
        if (performActionOnQuotesListing && ticketStatusOnListing === 'NEW') {
          performActionOnQuotesListing({refresh: true});
        }
         HolidayNavigation.pop();
      }
      exitLocalNotification(DETAIL_LOCAL_NOTIFICATION_PAGE_NAME);
      this.captureClickEvents({eventName: PDTConstants.BACK,});
      return true;
    }


  };

  getDaySectionIndex = (viewableItems) => {
    if (viewableItems && viewableItems.length > 0) {
      const { item } = viewableItems[0];
      const { id } = item || {};
      if (id && id.includes('DayplanSection_')) {
        const items = id.split('_');
        if (items.length > 1) {
          return parseInt(items[1]);
        }
      }
    }
    return null;
  };

  /**
   * This function returns persuasion at different indices.
   * Since persuasions position are dynamic on the detail page
   */
  getPersuasion = (persuasionData, index) => {
    if (persuasionData && Array.isArray(persuasionData) && index < persuasionData.length) {
      return <DetailPersuasion item={persuasionData[index]} togglePopup={this.togglePopup} />;
    }
    return [];
  };

  getZCPersuasion = () => {
    if (this.props.cancellationPolicyData && this.props.cancellationPolicyData.success) {
      return (
        <View style={{ paddingBottom: 10 }}>
          <FreeCancellationBanner
            togglePopup={this.openZCSection}
            sectionToShow={extraInfoRefs.FREE_CANCELLATION_BANNER}
            cancellationPolicyData={this.props.cancellationPolicyData}
          />
        </View>
      );
    }
    return [];
  };

  openZCSection = () => {
    this.togglePopup('');
    this.setState({ popup: extraInfoRefs.CANCELLATION_POLICY });
  };

  updateDepDate = (newDate) => {
    this.holidayDetailData.departureDetail.departureDate = newDate;
    this.togglePopup('');
    this.refreshDetails(true, false, null, false);
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.pageWrap} />
        {(this.isLoading || this.props.isLoading || this.props.showHorizontalLoader)
          && !this.state.showLoginPopUp
          && this.renderProgressView()
        }
        {this.props.isError && !this.state.showLoginPopUp && this.state.userLoggedIn && this.renderError()}
        {this.props.isError && !this.state.showLoginPopUp && !this.state.userLoggedIn && this.renderErrorOnNonLogin()}
        {this.state.showLoginPopUp && !this.props.isSuccess &&
        <BottomSheetOverlay
            title={'Switch Account'}
            onBackPressed={() => {this.setState({showLoginPopUp:false});}}
            visible={this.state.showLoginPopUp && !this.props.isSuccess}
            toggleModal={() => {this.setState({showLoginPopUp:false})}}
            containerStyle={{ padding: 0}}
            fixChild={true}>
          <YesNoPopUp
              description={this.props?.error?.message}
              onAccept={this.onConfirmSwitchAccount}
              onDeny={this.onDenySwitchAccount}
          />
        </BottomSheetOverlay>
        }
        {!this.isLoading &&
          this.props.isSuccess &&
          this.props.detailData?.packageDetail &&
          this.renderContent()}
      </View>
    );
  }

  openCustomizationPopup = () => {
    if (this.state.undoStack && this.state.undoStack.length > 0) {
      this.trackLocalClickEvent('expand_customizations', '');
      this.toggleUndoBottomSheet(true);
    }
  };

  checkAndUpdateCurrentActivePlan = () => {
    const { currentActivePlanOnItinerary } = this.state;
    const { detailData } = this.props;
    const { packageDetail } = detailData || {};
    const { packageInclusionsDetail, basePackageInclusionsDetail } = packageDetail || {};
    toggleDayPlan(currentActivePlanOnItinerary, packageInclusionsDetail, this.toggleDayPlan, basePackageInclusionsDetail);
  };

  // Index should be size of flatListData length
  scrollToBottom = () => {
    const index = this.flatListDataSize - 1;
    if (this.flatListRef) {
      setTimeout(() => {
        this?.flatListRef?.scrollToIndex?.({ animated: true, index, viewOffset: 125 });
      }, 500);
    }
  };
  closePersuasion = () => {
    this.setState({showPriceHikePersuasion: false});
  };

  renderAddonsUpdatePopup = () => {
    const { addonsValidationDetail } = this.state;

    return (
      <AddonsUpdatePopup
        addonsValidationDetail={addonsValidationDetail}
        onClose={this.handleAddonsPopupClose}
        onAcknowledge={this.handleAddonsUpdateAcknowledge}
      />
    );
  };

  renderContent = () => {
    const {
      detailData,
      persuasionData,
      fabCta,
      similarPackages,
      cancellationPolicyData,
    } = this.props;


    {/* Hardcoded locked=true for activities restiction for phase 1*/}
    if (detailData?.packageDetail?.activityDetail?.cityActivities){
      const cityActivities = detailData?.packageDetail?.activityDetail?.cityActivities;
      cityActivities?.forEach((newActivity)=>{
        newActivity?.activities?.map((data)=>{
          let activityMetaData = data?.metaData;
          if (activityMetaData)
          {activityMetaData.locked = true;}
        });
      });
    }
    this.newPackageDetail = getOptimizedPackageDetail(
      detailData.packageDetail,
      this.props.packageContent,
    );
    const {
      name,
      itineraryDetail,
      departureDetail,
      additionalDetail,
      holidayExpert,
      destinationTips,
      metadataDetail,
      sightSeeingDetails,
    } = this.newPackageDetail || {};
    const {reactivateTicketDetail} = detailData || {};
    const { dynamicItinerary } = itineraryDetail || {};
    const { dayItineraries = [] } = dynamicItinerary || {};
    const { loggedIn, showToolTip, branch } = detailData || {};
    const isShortListed = this.isShortListedPackage();
    const travellerCount = calculateTravellersCount(this.roomDetails);
    const videoUrl = getVideoUrl(this.newPackageDetail);
    const showStory = this.getViewStoryEnabled(this.newPackageDetail);
    const isWG = this.holidayDetailData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    const { departureDate, packageDate } = departureDetail || {};
    const calenderDates = getDates(new Date(packageDate), dayItineraries, sightSeeingDetails);
    const { bundled } = metadataDetail || {};
    let disableBookNowButton = !isEmpty(this.state.reviewError);
    if (!isEmpty(this.props.componentFailureData)) {
      disableBookNowButton = true;
    }
    if (persuasionData) {
      addPersuasionToDetailData(detailData, persuasionData, cancellationPolicyData);
      updateEMIPriceForPersuasion(persuasionData, this.newPackageDetail);
    }
    this.checkAndUpdateCurrentActivePlan();
    setTrackingData(detailData.pageDataMap, branch, {source: this.holidayDetailData.source});
    const sightSeeingMap = createSightSeeingDayPlanDataMap(this.props.packageContent,departureDetail);
    const sightseeingCount = Object.keys(sightSeeingMap)?.length || 0;
    const memberShipCardData = {
      cards: getMemberShipCardArray({
        mmtBlackDetail: this.props.mmtBlackDetail,
        personalizationDetail: this.props.personalizationDetail,
      }),
    };
    const membershipCardWidth =  SCREEN_WIDTH - 32
    const flatListData = [
      { id: '0', component: [this.BasicDetails()] },
      {
        id: '1',
        component: (
          <PackageInfoSection
            fromPresales={true}
            newPackageDetail={this.newPackageDetail}
            toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
            toggleStoryPage={this.toggleStoryPage}
            showStory={showStory}
            editable={false}
            {...this.props}
          />
        ),
      },
      {
        id: '2',
        component: memberShipCardData && memberShipCardData?.cards?.length > 0 && (
          <View
            style={[
              styles.membershipCardContainer,
              !this.props.mmtBlackDetail?.section?.overlayImage ? { ...paddingStyles.pt10 } : {},
            ]}
          >
             <MembershipCarouselV2
                memberShipCardData={memberShipCardData}
                onKnowMorePress={this.handleMemberShipCardOnKnowMoreClick}
                mmtblackPdtEvents={logPhoenixDetailPDTEvents}
                trackMemberShipLoadEvent={this.trackMemberShipLoadEvent}
                contentType={CONTENT_TYPES.DETAIL}
                containerStyles={{ width: membershipCardWidth, ...marginStyles.mh16 }}
                showFullBorderGradient={true}
                cardItemStyle={{ paddingTop: 14 }}
                customCardsWprStyle = {{ paddingBottom: 10}}
              />
          </View>
        ),
      },
      { id: '3', component: [this.getPersuasion(persuasionData, 0)] },
      {
        id: '4',
        component: (
          <ItineraryHeader
            isVisible={this.state.isItineraryVisible}
            onToggle={this.showItinerary}
          />
        ),
      },
      /*As discussed with Mayank SME card has been disabled for Pre sales Mima*/
      {
        id: '5',
        component: null,
        // FeedbackCard was present here
      },
      {
        id: '6',
        component: <SafeBanner packageDetail={this.newPackageDetail} branch={branch} />,
      },
      {
        id: '7',
        component: (
          <DateSection
            failedItineraryName={this.getNameOfFailedItinerary(
              this.state.reviewError,
              this.newPackageDetail,
            )}
            reviewError={this.state.reviewError}
            componentFailureData={this.props.componentFailureData}
            ref={this.calenderRef}
            isVisible={this.state.isItineraryVisible}
            onPressHandler={this.scrollToIndexFunc}
            calenderDates={calenderDates}
            index={7}
            toggleDayPlan={this.toggleDayPlan}
            currentActivePlanOnItinerary={this.state.currentActivePlanOnItinerary}
            detailData={detailData}
            fromPreSales={true}
            componentCount={this.props.componentCount}
            sightseeingCount={sightseeingCount}
          />
        ),
      },
      {
        id: '8',
        component: (
          <ValidationSummary
            validationSummary={detailData?.packageDetail?.metadataDetail?.validationSummary}
            containerStyles={styles.messageStripContainer}
          />
        ),
      },
      ...this.prepareDayPlanSections(this.newPackageDetail, sightSeeingMap),
      { id: '9', component: [this.getZCPersuasion()] },
      { id: '10', component: [this.getPersuasion(persuasionData, 1)] },
      { id: '11', component: [this.getPersuasion(persuasionData, 2)] },
      {
        id: '12',
        component: (
          <Accordian
            openCustomizationPopup={this.openCustomizationPopup}
            onSimilarPackageClicked={this.onSimilarPackageClicked}
            onPolicyClicked={() => this.setState({ popup: extraInfoRefs.CANCELLATION_POLICY })}
            showSimilarPackage={similarPackages && similarPackages.length > 0 && !bundled}
            showPolicy={
              this.props.cancellationPolicyData && this.props.cancellationPolicyData.success
            }
            scrollToBottom={this.scrollToBottom}
            fromPreSales={true}
            summaryData={this.props.detailData?.packageDetail?.packageSummary}
          />
        ),
      },
    ];

    this.flatListDataSize = flatListData.length;
    return (
      <View style={{ flex: 1 }}>
        <Animated.View
          style={{
            zIndex: 99999999,
            elevation: 4,
          }}
        >
          <DetailPageHeader
            heading={name}
            showSearch={this.state.showSearch}
            showPageHeader={this.state.showPageHeader}
            onBackPress={this.onBackPressed}
            onSharePress={this.onSharePress}
            onFavPress={this.updateShortListedPackage}
            isShortListed={isShortListed}
            editTravelDetails={this.editTravelDetails}
          />
        </Animated.View>

        {this.state.popup !== '' &&
          this.state.popup === overlays.EDIT_OVERLAY &&
          this.state.popup !== overlays.IMAGE_OVERLAY &&
          createGenericTextualBottomSheet(this.state.popup, this.togglePopup)}

        {this.state.popup !== '' && this.state.popup === overlays.IMAGE_OVERLAY && (
          <GridGallery onBackPressed={() => this.togglePopup(overlays.NONE)} />
        )}

        <FlatList
          data={flatListData}
          renderItem={this.renderItem}
          keyExtractor={(item) => item.id}
          ref={(ref) => (this.flatListRef = ref)}
          onScroll={this.onScroll}
          onScrollToIndexFailed={() => {}}
          stickyHeaderIndices={[7]}
          onScrollBeginDrag={this.onScrollDragBegin}
          onViewableItemsChanged={this.onViewableItemsChanged}
          viewabilityConfig={{
            viewAreaCoveragePercentThreshold: 100,
            waitForInteraction: true,
          }}
          style={{ width: '100%' }}
        />
        {this.state.showMmtBlackBottomsheet && this.props.mmtBlackDetail?.bottomSheet && (
          <BottomSheet
            onBackPressed={this.handleMmtBlackTogglePopup}
            containerStyle={{ padding: 0 }}
          >
            <MMTBlackBottomSheet
              togglePopup={this.handleMmtBlackTogglePopup}
              ctaButtonClick={this.handleMmtBlackCtaButtonClick}
              bottomSheetDetail={this.props.mmtBlackDetail?.bottomSheet}
              mmtBlackPdtEvents={logPhoenixDetailPDTEvents}
              trackClickEvent={this.trackMmtBlackClickEvent}
              handleTermConditionClick={this.handleTermConditionClick}
            />
          </BottomSheet>
        )}
        {this.state.openFDFeatureEditVisibility &&
        detailData?.packageDetail?.packageFeatures?.length > 0 ? (
          <FDFeatureEditOverlayV2
            packageFeatures={getFilteredPackageFeatures(detailData.packageDetail.packageFeatures)}
            toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
            isOverlay={true}
            selectedMealCode={detailData.packageDetail.mealDetail?.meal.mealCode}
            updateMeal={this.updateMeal}
            updateVisa={this.updateVisa}
            dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
            activeIndex={this.state.activeFeature}
          />
        ) : null}
        {this.state.isUndoVisible && this.state.undoStack && this.state.undoStack.length > 0 && (
          <CustomizationPopup
            popFromStack={this.popFromStack}
            undoStack={this.state.undoStack}
            toggleUndoBottomSheet={this.toggleUndoBottomSheet}
          />
        )}

        <FloatingWidget
          handleClick={() => this.openCustomizationPopup()}
          count={this.state.undoStack.length}
        />

        <PackageUpdatedToast
          showPackageUpdatedToast={this.state.showPackageUpdatedToast}
          setToastMessageState={this.setPackageUpdatedToastState}
        />
        {this.state.showPriceHikePersuasion && (
          <View style={styles.pricePersuasion}>
            <Text numberOfLines={2} style={styles.persuasionText}>
              Prices and components of the package may vary subject to availability
            </Text>
            <TouchableOpacity onPress={this.closePersuasion} style={{ marginTop: 10 }}>
              <Image source={CloseIcon} style={styles.closeIcon} />
            </TouchableOpacity>
          </View>
        )}
        {!this.state.showCoachMarks
        && reactivateTicketDetail?.ticketStatus === 'QUOTE_SHARED'
        &&  (<BlackFooter
            togglePopup={this.togglePopup}
            packageReview={this.packageReview}
            categoryPrice={this.newPackageDetail.pricingDetail.categoryPrices[0]}
            dealDetail={this.newPackageDetail.dealDetail}
            travellerCount={travellerCount}
            loggedIn={loggedIn}
            aff={this.holidayDetailData.aff}
            disableBookNowButton={disableBookNowButton}
          />
        )}

        {!this.state.showCoachMarks
        && reactivateTicketDetail?.ticketStatus === 'CLOSED'
        && !(reactivateTicketDetail?.hasOtherActiveTickets)
        &&  (<SendUsQueryButton
                handleClick={()=> this.startQuery(false)}
                trackLocalClickEvent={trackDetailsClickEvent}
            />
        )}

        {this.state.popup === overlays.OFFER_OVERLAY && (
            <OfferOverlay
                togglePopup={this.togglePopup}
                dealDetail={this.newPackageDetail.dealDetail}
                loggedIn={loggedIn}
                onLoginClicked={this.onLoginClicked}
                travellerCount={travellerCount}
                categoryPrice={this.newPackageDetail.pricingDetail.categoryPrices[0]}
                aff={this.holidayDetailData.aff}
                trackLocalClickEvent={this.trackLocalClickEvent}
            />
        )}

        {this.state.popup === overlays.EDIT_OVERLAY && (
          <EditOverlay
            togglePopup={this.togglePopup}
            pageName={this.pageName()}
            handlePDT={this.handlePDT}
            departureDate={this.newPackageDetail.departureDetail.departureDate}
            departureCity={this.newPackageDetail.departureDetail.cityName}
            flightCount={calculateFlightsCount(this.newPackageDetail.flightDetail)}
            packageId={this.newPackageDetail.id ? this.newPackageDetail.id : -1}
            categoryId={this.holidayDetailData.categoryId}
            cityId={this.newPackageDetail.departureDetail.cityId}
            packagePaxDetail={this.newPackageDetail?.packageConfigDetail?.packagePaxDetail}
            roomDetails={this.roomDetails}
            childAgeArray={this.childAgeArray}
            refreshFetchContent={this.refreshFetchContent}
            onBackPressed={this.onBackPressed}
            isWG={isWG}
          />
        )}
        {/* Fab hidden */}
        {/* {(!this.state.showCoachMarks || this.state.showingQueryCoachMark) &&
          fabCta &&
          fabCta.showFab && (
            <HolidayFabAnimated
              textShrinked={this.state.fabTextShrinked}
              fabCta={fabCta}
              startCall={this.startCall}
              startQuery={this.startQuery}
              startChat={this.startChat}
              showLocator={this.showLocator}
              handleDefaultFabClick={this.handleDefaultNewFabClick}
            />
          )} */}
        {this.state.popup === extraInfoRefs.CANCELLATION_POLICY &&
          this.props?.cancellationPolicyData?.success && (
            <HolidayCancellationOverlayV2
              dynamicId={this.packageDetailDTO.dynamicPackageId}
              togglePopup={this.togglePopup}
              trackClickEvent={(event, suffix) => this.trackLocalClickEvent(event, suffix)}
              cancellationPolicyData={this.props.cancellationPolicyData}
            />
          )}
        {this.state.popup === overlays.PRICE_OVERLAY && (
          <PricePersuasionOverlay
            togglePopup={this.togglePopup}
            persuasionData={persuasionData}
            departureDate={departureDate}
            updateDepDate={this.updateDepDate}
          />
        )}
        <DetailOverlays/>
        {/*{this.state.popup === overlays.VISA_OVERLAY && (*/}
        {/*  <VisaOverlay*/}
        {/*    togglePopup={this.togglePopup}*/}
        {/*    packageContent={this.props.packageContent}*/}
        {/*    tourManagerExist={isTourManagerExist(this.newPackageDetail.tourManagerDetail)}*/}
        {/*  />*/}
        {/*)}*/}
        {this.props.isSuccess && this.state.showCoachMarks && this.renderCoachMarks()}

        {!this.isLoading && this.showReviewErrorPopup(this.state.reviewError, this.newPackageDetail)}

        {reactivateTicketDetail?.ticketStatus === 'CLOSED'
        && (<ExpiredPlanPopup
          message={EXPIRED_PLAN_HEADING} subMessage={EXPIRED_PLAN_SUBHEADING}
          btnText={CONTINUE} icon={genericErrorIcon}
          onBtnClick={()=>trackDetailsClickEvent(PDTConstants.CLOSE,PDTConstants.GENERIC_FORWARD_FLOW_ERROR)}
        />

        )}

        {this.state.showAddonsUpdatePopup && this.state.addonsValidationDetail && this.renderAddonsUpdatePopup()}
      </View>
    );
  };
  renderCoachMarks = () => {
    if (this.finalCuesSteps && this.finalCuesSteps.length) {
      const CoachMarks = require('@mmt/legacy-commons/Common/Components/CoachMarks').default;
      return (
        <CoachMarks
          steps={this.finalCuesSteps}
          onDone={this.hideCoachMarksOverlay}
          onSkip={this.hideCoachMarksOverlay}
          onStart={(step) =>
            setTimeout(() => {
              this.handleScrollForCoachMarks(step);
            }, 250)
          }
          onStepChange={(step) => this.handleScrollForCoachMarks(step)}
          pageName={HLD_CUES_POKUS_KEYS.PSM}
          trackEvent={this.handlePDT}
        />
      );
    }
    return null;
  };
  onLoginClicked = () => {
    const { HolidayModule } = NativeModules;
    if (isMobileClient()) {
      HolidayModule.onLoginUserDetail();
    } else {
      HolidayModule.onLoginUserDetail(getReturnUrl(this.holidayDetailData.quoteRequestId));
    }
    this.trackLocalClickEvent(PDTConstants.LOGIN, '');
  };

  onLoginEventReceived = async (response) => {
    if (response && response.loggedIn) {
      this.handleLoggedInUser();
    }
  };
  onSharePress = () => {
    this.trackLocalClickEvent('share', '');
    sharePackage(this.props.detailData.packageDetail);
  };

  isShortListedPackage = () => {
    const { detailData } = this.props;
    const { packageDetail } = detailData || {};
    let isShortListed = false;
    if (packageDetail && this.props.shortListedPackages) {
      isShortListed = this.props.shortListedPackages.has(packageDetail?.id);
    }
    return isShortListed;
  };

  handlePricePersuasion = (departureDate) => {
    if (this.holidayDetailData.departureDetail.departureDate !== departureDate) {
      this.holidayDetailData.selectedDate = departureDate;
      this.holidayDetailData.departureDetail.departureDate = departureDate;
      this.refreshDetails(false, false, false, false);
    }
  };

  updateShortListedPackage = (isShortList) => {
    this.trackLocalClickEvent('shortlist', '');
    const { detailData } = this.props;
    const { packageDetail } = detailData || {};
    const { id, name } = packageDetail || {};
    if (!this.props.shortListedPackages) {
      this.props.shortListedPackages = new Set();
    }
    if (isShortList) {
      this.props.shortListedPackages.add(id);
    } else {
      this.props.shortListedPackages.delete(id);
    }
    this.props.updateShortListedPackage(id, name, isShortList);
  };

  getViewStoryEnabled = (packageDetail) => {
    if (packageDetail) {
      const { metadataDetail } = packageDetail || {};
      const { enableStory } = metadataDetail || {};
      return !!enableStory;
    }
    return false;
  };

  renderProgressView = () => {
    let openingSavedPackage = false;
    if (this.holidayDetailData.savePackageId && !this.packageDetailDTO.dynamicPackageId) {
      openingSavedPackage = true;
    }
    if (this.props.showHorizontalLoader) {
      return (<View style={styles.horizontalLoader}>
                <FilterLoader
                  showCenterLoader={true}
                  loadingFirstTime={false}
                  />
              </View>);
    } else {
      return (
          <HolidayDetailLoader
              departureCity={this.holidayDetailData.departureDetail?.departureCity}
              departureDate={this.holidayDetailData.departureDetail?.departureDate}
              duration={this.holidayDetailData.destinationDetail?.duration}
              travellerObj={createTravellerObjForLoader(this.roomDetails)}
              openingSavedPackage={openingSavedPackage}
              showDateText={this.props.changingDate}
              changeAction={this.props.changeAction}
              loadingText={this.props.loadingText}
          />
      );
    }
  };

  renderErrorOnNonLogin = () => {
    const errorMessage = errorMessages.NON_LOGIN;
    let errorActions = [
      {
        displayText: 'Go Back',
        props: {
          onPress: () => this.onBackPressed(),
        },
      },
    ];
    errorActions = [
      {
        displayText: 'Login',
        props: {onPress: () => this.onLoginClicked()},
      },
      ...errorActions,
    ];
    return <FullPageError subTitle={errorMessage} actions={errorActions} onBackPressed={this.onBackPressed} onLoad={() => {}} />;
  };

  renderError = () => {
    const errorMessage = fetchErrorMessage(this.props.error);
    let errorActions = [
      {
        displayText: 'Go Back',
        props: {
          onPress: () => this.onBackPressed(),
        },
      },
    ];
      errorActions = [
        {
          displayText: 'REFRESH',
          props: {onPress: () => this.refreshDetails(false, false, null, false)},
        },
        ...errorActions,
      ];
    return <FullPageError subTitle={errorMessage} actions={errorActions} onBackPressed={this.onBackPressed} onLoad={() => {}} />;
  };

  handleDownLoadItinerary = () => {
    showShortToast('Handle download itineary functionality');
  };

  handleShareItinerary = () => {
    showShortToast('Handle Share itineary functionality');
  };

  handleNeverAskStoragePermission = () => {
    this.props.showPermissionDialog({
      message: 'Storage permission is permanently disabled, Please go to app settings and allow permission manually.',
      buttonPositive: { title: 'Settings', callback: ()=> Linking.openSettings() },
      buttonNegative: { title: 'Cancel', callback: ()=> {} },
    });
  }
  handleStoragePermission= async() => {
    const havingPermission = await getStoragePermissionStatus();
    if (havingPermission) {
      this.props.downloadPdf(this.holidayDetailData,this.props.downloadData,this.handleNeverAskStoragePermission);
    } else {
      this.props.showPermissionDialog({
        title: 'Storage Permission Required',
        message: 'Please allow storage permission to download file.',
        buttonPositive: { title: 'Go Ahead', callback: ()=> this.props.downloadPdf(this.holidayDetailData,this.props.downloadData,this.handleNeverAskStoragePermission) },
        buttonNegative: { title: 'Not Now', callback: ()=> {} },
      });
    }

  }
  BasicDetails = () => {
    const { detailData, travelPlanData, showOverlay, mmtBlackDetail } = this.props;
    const { packageDetail, loggedIn, showToolTip, reactivateTicketDetail }  = detailData || {};
    const { imageDetail, genreDetail, packageConfigDetail, metadataDetail } = packageDetail || {};
    const { gallerySections = {} } = imageDetail || {};
    const { bundled, safe } = metadataDetail || {};
    const { packageType } = packageConfigDetail || {};
    const isShortListed = this.isShortListedPackage();
    const videoUrl = getVideoUrl(packageDetail);
    const showStory = this.getViewStoryEnabled(packageDetail);
    const { tagDestinationName } = this.fetchTagDestAndBranch();
    const modifyPackage = () => {
      this.props.detailData.branch === DOM_BRANCH && this.trackLocalClickEvent('Edit_yourself','');
      this.setState({
        reviewError:{},
        expertModalVisibility:false,
      });
      const {detailData, performActionOnQuotesListing} = this.props || {};
      const {ticketId, additionalQuoteDetail} = detailData || {};
      const {agentUserName, quoteId} = additionalQuoteDetail || {};

      let holidayDetailDataObj = cloneDeep(this.state?.holidayDetailData);
      holidayDetailDataObj.ticketId = ticketId;
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL,
          {
            openMimaPreSalesEditDetail: true,
            openMimaPreSales: false,
            holidaysDetailData: {...holidayDetailDataObj, quoteId},
            ticketId,
            agentUserName,
            performActionOnQuotesListing,
          });
      this.captureClickEvents({eventName: 'edit_yourself'});
    };

    const onModifyPackagePlanSelected = (newQuoteRequestId, itineraryIndex = 0, quoteIndex = 0) => {
      this.holidayDetailData.quoteRequestId = newQuoteRequestId;
      const eventName = `Click_option_selected_itnineraryorder_${itineraryIndex + 1}_quoteorder_${quoteIndex + 1}`;
      this.captureClickEvents({ eventName });
      this.refreshDetails(
        false,
        !isEmptyString(this.holidayDetailData.dynamicPackageId),
        null,
        false,
      );
      this.props.clearDownloadShare();
      this.props.setReviewComponentFailureData(null);
      this.setState({
        reviewError:{},
      });
    };

    const handleModifyPackagePopupBackPressed = () => {
      this.setState({
        expertModalVisibility:false,
        expertActiveOverlay:'',
      });
    };

    const trackBackPressed = () => {
      const eventName = this.state.makeChangesMyself
        ? 'Modify_expert_close'
        : 'Contact_expert_close';
      this.captureClickEvents({ eventName });
    };

    return (
      <View>
        <LightHeader
            onBackPress={this.onBackPressed}
            onModifyPackagePlanSelected={onModifyPackagePlanSelected}
            travelPlanData={travelPlanData || {}}
            detailData={ detailData || {}}
            trackLocalClickEvent={this.trackLocalClickEvent}
        />

        {reactivateTicketDetail?.ticketStatus === 'CLOSED' && (
          <View style={styles.planExpiredBackGround}>
            <Text style={styles.planExpiredText}>Travel Plan Expired</Text>
          </View>
        )}
        { (!this.showNewContentUI || isEmpty(gallerySections))  &&
            <PackageHighlights
              fromPresales
              togglePopup={this.togglePopup}
              imageDetail={imageDetail}
              videoUrl={videoUrl}
              videoPaused={this.state.videoPaused}
              trackLocalClickEvent={this.trackLocalClickEvent}
            />
        }


        {this.showNewContentUI ? (
          <BasicDetailsSection
            fromPresales
            detailData={detailData}
            dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
            updatePackage={this.updatePackageWithV2}
            showOverlay={showOverlay}
            mmtBlackDetail={mmtBlackDetail}
          />
        ) : (
          <View>
            <SignatureBanner
              showStory={showStory && this.props.storySuccess}
              genreDetail={genreDetail}
              toggleStoryPage={() => {}}
            />
            <Text style={styles.pkgName}>{packageDetail.name}</Text>
            <DestDuration />
            <View style={styles.leftRightSection}>
              <View style={styles.row}>
                <View style={marginStyles.mr4}>
                  <Duration />
                </View>
                <View style={marginStyles.mr4}>
                  <PackageType
                    packageType={packageType}
                    trackLocalClickEvent={this.trackLocalClickEvent}
                  />
                </View>
                {safe && <Image style={{ width: 60, height: 16 }} source={mySafeImage} />}
              </View>
            </View>
          </View>
          )}
          <ModifySearchContainer
            fromPreSales
            editTravelDetails={this.editTravelDetails}
            roomDetails={this.roomDetails}
          />
        <View style={{ marginBottom:10 }}>
          {reactivateTicketDetail?.ticketStatus === 'QUOTE_SHARED'
          && <PackageActions
              downloadPdf={this.downloadPdf}
              sharePdf={this.sharePdf}
              isDownloadLoading={this.props.showHorizontalLoader}
              isShareLoading={this.props.showHorizontalLoader}
              handleContactExpertClick={this.handleContactExpertClick}
          />}

        {this.state.viewState === VIEW_STATE_NO_NETWORK && showShortToast('Network Error')}
        {this.state.viewState === VIEW_STATE_ERROR && showShortToast('Something went wrong! Please try again!')}
        {this.state.expertModalVisibility && this.state.viewState === VIEW_STATE_SUCCESS ? (
          <BottomSheet
            title={OVERLAY_HEADING}
            titleStyle={{textAlign: 'left', paddingTop: 5}}
            crossButtonStyle={{marginHorizontal:10, marginVertical:10}}
            isCloseBtnVisible={true}
            onBackPressed={handleModifyPackagePopupBackPressed}
            containerStyle={styles.containerStyle}
            headerStyle={{marginBottom:5}}
            trackBackPressed={trackBackPressed}
          >
            <SelectOptionPopUp
              actionType={this.state.expertActiveOverlay}
              agentData={this.state.agentData}
              isOnline={this.state.isOnline}
              isDomBranch={this.props.detailData.branch === DOM_BRANCH}
              modifyPackage={modifyPackage}
              trackLocalClickEvent={this.trackLocalClickEvent}
              makeChangesMyself={this.state.makeChangesMyself}
              destination={tagDestinationName}
              quoteId={this.holidayDetailData.quoteRequestId}
              ticketId={this.props.detailData?.ticketId}
            />
          </BottomSheet>
        ) : null}
        </View>
      </View>
    );
  };
onConfirmSwitchAccount = async () => {
  if (isRawClient() && MobileLoginModule) {
    const MobileLogin = await MobileLoginModule;
    MobileLogin.doLogout().finally(() => {
      this.onLoginClicked();
    });
  } else if (Platform.OS !== 'ios') {
      const { HolidayModule } = NativeModules;
      HolidayModule.logOutAccount();
    this.onLoginClicked();
    }
    this.setState({ showLoginPopUp: false });
    this.onLoginClicked();
    }

  onDenySwitchAccount = () => {
    if(isRawClient()) {
      const { HolidayModule } = NativeModules;
      HolidayModule.goToAppHome();
    } else {
      this.setState({ showLoginPopUp: false });
      HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.LANDING_NEW);
    }
  }

  handleContactExpertClick = async (makeChangesMyself = false) => {
     this.setState({
       makeChangesMyself:makeChangesMyself,
     });
     const {detailData} = this.props || {};
    const {ticketId, additionalQuoteDetail} = detailData || {};
    const {agentUserName} = additionalQuoteDetail || {};

    if (this.state.viewState === VIEW_STATE_LOADING) {
      return;
    }
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState:VIEW_STATE_NO_NETWORK,
      });
      return;
    }
    this.setState({
      viewState:VIEW_STATE_LOADING,
    });
    const response = await this.props.fetchAgentStatus(agentUserName, PRESALES_DETAIL, ticketId);
    if (!response) {
      this.setState({
        viewState:VIEW_STATE_ERROR,
      });
      return null;
    }
    const { statusCode, success, agentDetail } = response || {};
    const { availability } = agentDetail || '';
    if (statusCode !== 1 || !success) {
      this.setState({
        viewState:VIEW_STATE_ERROR,
      });
      return null;
    }
    this.setState({
      isOnline:availability === 'ONLINE',
      viewState:VIEW_STATE_SUCCESS,
      agentData:agentDetail,
      expertModalVisibility:true,
      expertActiveOverlay:CONTACT_EXPERT,
    });
    const event = makeChangesMyself ? MODIFY_PACKAGE : CONTACT_EXPERT;
    this.captureClickEvents({eventName : event});
  };

  sharePdf = () => {
    const {sharePdf, shareData} = this.props || {};
    sharePdf(this.holidayDetailData, shareData);
    this.captureClickEvents({eventName: 'Share_itinerary'});
  }

  downloadPdf = () => {
    this.handleStoragePermission();
    this.captureClickEvents({eventName : 'Download_itinerary'});
  }

  onSimilarPackageClicked = (packageDetails, storyImageSize, index) => {
    const holidaysDetailData = createHolidayDetailData(
      packageDetails,
      this.holidayDetailData,
      this.roomDetails,
      storyImageSize,
      this.holidayDetailData.departureDetail.departureCity,
    );
    if (isRawClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
        holidaysDetailData,
      });
    } else if (this.replaced) {
      HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.DETAIL, {
        replaced: false,
        [deepLinkParams.deepLink]: false,
        deepLinkParams: null,
        holidaysDetailData,
        deepLinkSimilar: this.deepLinkSimilar,
      });
    } else {
      HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.DETAIL, {
        replaced: true,
        [deepLinkParams.deepLink]: false,
        deepLinkParams: null,
        holidaysDetailData,
        deepLinkSimilar: this.deepLinkSimilar,
      });
    }
    this.trackLocalClickEvent(
      PDTConstants.SIMILAR_PACKAGE,
      `_${index}_${packageDetails.packageId}`,
    );
  };

  toggleFDFeatureBottomSheet = (visibility) => {
    this.trackLocalClickEvent('edit_fd', );
    this.setState({
      openFDFeatureEditVisibility: visibility,
    });
  };

  toggleUndoBottomSheet = (visibility) => {
    this.setState({
      isUndoVisible: visibility,
    });
  };

  async initAb() {
    await initAbConfig();
    this.cusCountLimit = getMaxUndoAllowed();
    this.showNewContentUI = getPokusForNewDetailContent(true);
    const showStoryMob = getHolShowStoryMob();
    if (this.props[deepLinkParams.deepLink]) {
      await setTimeout(() => {
        if (showStoryMob) {
          this.setState({
            showStoryAB: true,
          });
        }
      }, 500);
    } else {
      if (showStoryMob) {
        this.setState({
          showStoryAB: true,
        });
      }
    }
  }
  showCoachMarksOverlay = async (response) => {
    const cuesConfig = getCuesConfig();
    const localSteps = require('./MimaPreSalesPageCoachMark.json');
    this.finalCuesSteps = await createCuesSteps({ pokusSteps: cuesConfig[HLD_CUES_POKUS_KEYS.PSM], localSteps, pageName: HLD_CUES_POKUS_KEYS.PSM });
    this.finalCuesSteps = getValidCuesSteps(this.finalCuesSteps);
    this.setState({ showCoachMarks: this.finalCuesSteps && this.finalCuesSteps.length > 0 });
  };

  hideCoachMarksOverlay = () => {
    this.setState(
      {
        showCoachMarks: false,
        currentActivePlanOnItinerary: TABS.DAY,
        showingQueryCoachMark: false,
      },
      () => {
        setTimeout(() => {
          if (this.flatListRef) {
            this?.flatListRef?.scrollToIndex?.({ animated: true, index: 0 });
          }
        }, 500);
      },
    );
  };
  handleScrollForCoachMarks = (step) => {
    if (step.key === 'optionHeader') {
      this.setState({ showingQueryCoachMark: false }, () => {
        this?.flatListRef?.scrollToIndex?.({ animated: true, index: 0 });
      });
    } else if (step.key === 'modifyItinerary') {
      this.setState({ showingQueryCoachMark: false }, () => {
        this?.flatListRef?.scrollToIndex?.({ animated: true, index: 0 });
      });
    }
  };
  getFDFeatureList = (detailData) => {
    const { packageDetail = {} } = detailData || {};
    const { packageFeatures } = packageDetail || {};
    if (packageFeatures)
      { return this.showNewContentUI ? (
        <View style={styles.featureListBorder}>
          <FDFeatureListV2
            packageDetail={packageDetail}
            packageFeatures={packageFeatures}
            isOverlay={false}
            toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
            containerStyles = {{ marginBottom: 16 }}
            editable={false}
            updateVisa={this.updateVisa}
          />
        </View>
      ) : (
        <FeatureList
          packageFeatures={packageFeatures}
          isOverlay={false}
          toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
          editable={false}
        />
      );}
    return [];
  };

  setPackageUpdatedToastState = (status) => {
    this.setState({
      showPackageUpdatedToast: status,
    });
  };

  trackPageExit = () => {
    trackDetailsLoadEvent({
      omniPageName: '',
      pdtData: {
        pageDataMap: this.props.detailData ? this.props.detailData.pageDataMap : {},
        interventionDetails: this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
        activity: PDTConstants.PDT_RAW_EVENT,
        event: PDT_PAGE_EXIT_EVENT,
        requestId: createRandomString(),
        branch: this.props.detailData ? this.props.detailData.branch : '',
      },
    });
     // Implement the new PDT
     const { pricingData } = extractDataForPDTFromDetailResponse(this.props.detailData, this.props.offerSection, this.holidayDetailData);

     // Log page exit event with pricing data
     logPhoenixDetailPDTEvents({
       actionType: PDT_EVENT_TYPES.pageExit,
       value: PDT_PAGE_EXIT_EVENT,
       pricingData, // Include pricing data for page exit event
     });
  };

  fabHandle = () => {
    const { fabCta } = this.props;
    if (fabCta.showQuery && !fabCta.showCall && !fabCta.showChat) {
      this.startQuery(true);
    } else if (!fabCta.showQuery && fabCta.showCall && !fabCta.showChat) {
      this.startCall(true);
    } else if (!fabCta.showQuery && !fabCta.showCall && fabCta.showChat) {
      this.startChat(true);
    } else {
      this.handleDefaultFabClick();
    }
  };

  startCall = (fromIcon) => {
    if (!fromIcon) {
      this.setState({ fab: !this.state.fab });
    }
    const { branchName } = this.fetchTagDestAndBranch();
    doCall(branchName);
    this.handleDefaultFabClick();
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    this.trackLocalClickEvent(eventName, PDTConstants.CALL_SUFFIX);
    this.trackPageExit();
    this.setState({ videoPaused: true });
  };

  startQuery = (fromIcon) => {
    if (!fromIcon) {
      this.setState({ fab: !this.state.fab });
    }
    const {
      tagDestinationName,
      branchName,
      packageId,
      packageName,
      pkgType,
      dynamicPackageId,
    } = this.fetchTagDestAndBranch();
    if (this.holidayDetailData.fromSeo) {
      openSeoQueryDeepLink(tagDestinationName, branchName);
    } else {
      const queryDto = {};
      queryDto.destinationCity = tagDestinationName;
      queryDto.branch = branchName;
      queryDto.packageId = packageId;
      queryDto.packageName = packageName;
      queryDto.pageName = PRESALES_DETAIL;
      queryDto.funnelStep = PRESALES_DETAIL;
      queryDto.pkgType = pkgType;
      queryDto.dynamicPackageId = dynamicPackageId;
      queryDto.isWG = this.holidayDetailData.isWG;
      queryDto.aff = this.holidayDetailData.aff;
      queryDto.cmp = this.holidayDetailData.cmp ? this.holidayDetailData.cmp : '';
      if (queryDto.cmp === '') {
        queryDto.cmp = this.holidayDetailData.initId ? this.holidayDetailData.initId : '';
      }
      if (this?.props?.fabCta?.formId) {
        queryDto.formId = this.props?.fabCta?.formId;
      }
      doQuery(queryDto);
    }
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    let querySuffix = PDTConstants.QUERY_SUFFIX;
    if (this.props?.fabCta?.formId) {
      querySuffix = `${querySuffix}_${this.props?.fabCta?.formId}`;
    }
    this.trackLocalClickEvent(eventName, querySuffix);
    this.trackPageExit();
    this.setState({ videoPaused: true });
  };

  showLocator = (fromIcon) => {
    this.setState({
      popup: overlays.BRANCH_OVERLAY,
      openHalf: false,
      videoPaused: true,
    });
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    this.trackLocalClickEvent(eventName, PDTConstants.BRANCH_LOCATOR_SUFFIX);
    this.trackPageExit();
  };

  startChat = (fromIcon) => {
    this.props.unmountIntervention();
    if (!fromIcon) {
      this.setState({ fab: !this.state.fab });
    }
    const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
    let cmpValue = this.props.detailData.cmp ? this.props.detailData.cmp : '';
    if (cmpValue === '') {
      cmpValue = this.props.detailData.initId ? this.props.detailData.initId : '';
    }

    const chatIdentifier = createChatID();
    const chatDto = {
      destinationCity: tagDestinationName,
      branch: branchName,
      travelDate: this.props.detailData.pageDataMap.otherDetails.travel_start_date,
      packageId: `${this.props.detailData.packageDetail.id}`,
      dynamicPackageId: this.props.detailData.packageDetail.dynamicId,
      paxConfig: getPaxConfig(this.props.detailData.pageDataMap),
      cmp: cmpValue,
      chatId: chatIdentifier,
      pageName: DETAIL_QUERY_PAGE_NAME,
    };
    startReactChat(chatDto);
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    this.trackLocalClickEvent(eventName, PDTConstants.CHAT_SUFFIX);

    this.trackPageExit();
    this.setState({ videoPaused: true });
  };

  startNoPkCall = () => {
    const { branchName } = this.fetchTagDestAndBranch();
    doCall(branchName);
    this.trackLocalClickEvent(PDTConstants.CONTACT_ICON, PDTConstants.CALL_SUFFIX);
  };

  startNoPkQuery = () => {
    const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
    if (this.holidayDetailData.fromSeo) {
      openSeoQueryDeepLink(tagDestinationName, branchName);
    } else {
      const queryDto = {};
      queryDto.destinationCity = tagDestinationName;
      queryDto.branch = branchName;
      queryDto.pageName = 'details_nopk';
      queryDto.funnelStep = 'details_nopk';
      queryDto.isWG = this.holidayDetailData.isWG;
      queryDto.aff = this.holidayDetailData.aff;
      if (this.props?.fabCta?.formId) {
        queryDto.formId = this.props?.fabCta?.formId;
      }
      doQuery(queryDto);
    }
    this.trackLocalClickEvent(PDTConstants.CONTACT_ICON, PDTConstants.QUERY_SUFFIX);
  };

  startNoPkChat = () => {
    const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
    const chatDto = {
      destinationCity: tagDestinationName,
      branch: branchName,
      pageName: DETAIL_QUERY_PAGE_NAME,
    };
    startReactChat(chatDto);
    this.trackLocalClickEvent(PDTConstants.CONTACT_ICON, PDTConstants.CHAT_SUFFIX);
  };

  handleDefaultFabClick = () => {
    this.setState({ fab: !this.state.fab });
    const totalCtasToBeShown =
      (this.props.fabCta.showCall ? 1 : 0) +
      (this.props.fabCta.showQuery ? 1 : 0) +
      (this.props.fabCta.showChat ? 1 : 0);
    if (!this.state.fab && totalCtasToBeShown > 1) {
      this.trackLocalClickEvent(
        'fab',
        `_${this.props.fabCta.showCall ? 'C' : ''}${this.props.fabCta.showQuery ? 'Q' : ''}
        ${this.props.fabCta.showChat ? 'Ch' : ''}${this.props.fabCta.branchLocator ? 'B' : ''}`,
      );
    }
  };

  fetchTagDestAndBranch() {
    let tagDestinationName = '';
    let branchName = DOM_BRANCH;
    let packageId = '';
    let packageName = '';
    let pkgType = '';
    let dynamicPackageId = '';
    if (this.props.detailData && this.props.detailData.packageDetail) {
      tagDestinationName = this.props.detailData.packageDetail.tagDestination.name;
      branchName = this.props.detailData.packageDetail.metadataDetail.branch;
      packageId = this.props.detailData.packageDetail.id;
      packageName = this.props.detailData.packageDetail.name;
      dynamicPackageId = this.props.detailData.packageDetail.dynamicId;
      if (this.props.detailData.packageDetail.metadataDetail) {
        pkgType = this.props.detailData.packageDetail.metadataDetail.packageType;
      }
    } else if (this.holidayDetailData) {
      if (
        this.holidayDetailData.destinationDetail &&
        this.holidayDetailData.destinationDetail.tagDestination
      ) {
        tagDestinationName = this.holidayDetailData.destinationDetail.tagDestination;
      }
      if (this.holidayDetailData.branch) {
        branchName = this.holidayDetailData.branch;
      }
      if (this.holidayDetailData.packageId) {
        packageId = this.holidayDetailData.packageId;
      }
      if (this.holidayDetailData.name) {
        packageName = this.holidayDetailData.name;
      }
    }
    return {
      tagDestinationName,
      branchName,
      packageId,
      packageName,
      pkgType,
      dynamicPackageId,
    };
  }

  goToReview = (holidayReviewData, reviewData) => {
    const { packageConfigDetail } = this.newPackageDetail || {};
    const { componentAccessRestriction } = packageConfigDetail || {};
    this.trackLocalClickEvent(PDTConstants.BOOK, );
    this.trackPageExit();
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.REVIEW, {
      holidayReviewData,
      reviewData,
      fromDetails: true,
      openForwardFlowFromReviewPage: isRawClient() ? undefined : this.openForwardFlowFromReviewPage,
      componentAccessRestriction : isEmpty(componentAccessRestriction) ? getComponentAccessRestrictions() : componentAccessRestriction,
       handleContactExpertClick:isRawClient() ? undefined :this.handleContactExpertClick,
    });
  };
  handleExpertClickFromFailure=()=>{
    this.setState({
      showReviewPopUp:false,
    });
    this.handleContactExpertClick();
  }
  showReviewErrorPopup(reviewError, packageDetail) {
    if (reviewError && reviewError.error && this.state.showReviewPopUp) {
      return (
        <ReviewFailureHandlingPopup
          type={reviewError.error.errorType}
          reviewError={reviewError}
          packageReview={this.packageReview}
          name={this.getNameOfFailedItinerary(reviewError, packageDetail)}
          componentAccessRestriction={{...packageDetail?.packageConfigDetail?.componentAccessRestriction, changeVisaRestricted: true}}
          openCorrespondingListingPage={() => this.openCorrespondingListingPage(reviewError, packageDetail)}
          onReviewFailurePopupClosed={() => this.onReviewFailurePopupClosed(reviewError)}
          openListingPage={this.openListingPage}
          trackLocalClickEvent={this.trackLocalClickEvent}
          showPopupActionButton={false}
          contactExpert={true}
          handleContactExpertClick={this.handleExpertClickFromFailure}
        />
      );
    }
    return [];
  }

  onReviewFailurePopupClosed = (reviewError) => {
    switch (reviewError.error.errorType) {
      case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
        this.scrollToIndexFunc(6);
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
        this.scrollToIndexFunc(6);
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_VISA: {
        this.scrollToIndexFunc(6);
        break;
      }

    }
  };

  openListingPage = () => {
    const { notFromDeeplink } = this.props;
    const { holidayDetailData, packageDetailDTO } = this;
    const { departureDetail = {}, departureCity, destinationDetail = {} } = holidayDetailData || {};
    const { pt, aff } = holidayDetailData || {};

    const listingData = {
      dest: destinationDetail.tagDestination,
      destinationCity: destinationDetail.tagDestination,
      departureCity: departureCity,
      packageDate: departureDetail.departureDate,
      cmp: packageDetailDTO.cmp,
      pt,
      aff,
      fromDeepLink: true,
    };

    if (notFromDeeplink) {
      this.onBackPressed();
    } else {
      // try {
      //   HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.LISTING, { holidaysListingData: listingData });
      // } catch (e) {
      //   HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.LISTING, { holidaysListingData: listingData });
      // }
      try {
        HolidayNavigation.pop();
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, listingData);
      } catch (e) {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, listingData);
      }
    }
  };

  // Callback function to open forward flow overlays
  openForwardFlowFromReviewPage = () => {
    if (!isEmpty(this.props.componentFailureData)) {
      const { componentErrors } = this.props.componentFailureData || {};
      const { FLIGHT = [], HOTEL = [] } = componentErrors || {};

      //disabled as change flight ,hotel is not allowed
      // if (FLIGHT && FLIGHT.length > 0) {
      //   setTimeout(() => this.forwardFlowOpenFlightOverlay(), 1000);
      // } else if (HOTEL && HOTEL.length > 0) {
      //   setTimeout(() => this.forwardFlowOpenHotelOverlay(HOTEL[0].sellableId), 1000);
      // }
    }
  };

  forwardFlowOpenHotelOverlay = (hotelSellableId) => {
    const hotel = getHotelObject(this.newPackageDetail.hotelDetail, hotelSellableId);
    openChangeHotelFromPhoenixPage(hotel, this.packageDetailDTO, this.roomDetails, this.onComponentChange, this.lastPageName, '', hotelSellableId, this.props.showOverlay, this.props.hideOverlays, this.props.detailData);
  };

  forwardFlowOpenFlightOverlay = () => {
    const { packageConfigDetail, destinationDetail } = this.newPackageDetail;
    const { componentAccessRestriction = {} } = packageConfigDetail;
    const subtitleData = createSubtitleData(
      this.props.detailData.packageDetail.departureDetail,
      this.roomDetails,
    );
    const flightReqParams = createFlightRequestParams(this.newPackageDetail);
    const { flightSelections, overnightDelays } = flightReqParams;
    const requestParams = {};
    requestParams.listingFlightSequence = 1;
    if (flightSelections && flightSelections.length > 0) {
      requestParams.flightSelections = flightSelections;
    }
    if (overnightDelays) {
      requestParams.overnightDelays = overnightDelays;
    }
    const {showOverlay, hideOverlays, clearOverlays} = this.props || {};
    const flightOverlayProps={
      flightRequestObject: requestParams,
      dynamicId: this.packageDetailDTO.dynamicPackageId,
      pricingDetail: this.props.detailData.packageDetail.pricingDetail,
      onComponentChange: this.onComponentChange,
      onPackageComponentToggle: this.onPackageComponentToggle,
      subtitleData: subtitleData,
      accessRestriction: componentAccessRestriction,
      lastPage: this.lastPageName,
      packageDetailDTO: this.packageDetailDTO,
      roomDetails: this.roomDetails,
      trackLocalClickEvent: this.trackLocalClickEvent,
      trackLocalPageLoadEvent: this.trackLocalPageLoadEvent,
      isFlightFailed: true,
      showOverlay,
      hideOverlays,
      clearOverlays
    };
    isMobileClient() ?
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING, flightOverlayProps)
        : this.props.showOverlay(Overlay.FLIGHT_OVERLAY, flightOverlayProps);
  };

  openCorrespondingListingPage = (reviewError, optimizedPackageDetail) => {
    const { packageConfigDetail } = this.newPackageDetail;
    const { componentAccessRestriction = {} } = packageConfigDetail;
    switch (reviewError.error.errorType) {
      case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
        if (!componentAccessRestriction.changeFlightRestricted) {
          this.forwardFlowOpenFlightOverlay();
        } else {
          HolidayNavigation.pop();
        }
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
        if (!componentAccessRestriction.changeHotelRestricted) {
          const { failedHotels } = reviewError.error.errorData || [];
          this.forwardFlowOpenHotelOverlay(failedHotels[0].sellableId);
        } else {
          HolidayNavigation.pop();
        }
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_VISA: {
        HolidayNavigation.pop();
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY:
      case detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION:
      case detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER: {
        const { pricingDetail, departureDetail } = this.props.detailData.packageDetail;
        const day = reviewError?.error?.errorData?.dialogResponse?.content?.soldOutItems?.[0]?.day;
        const roomDetails = this.props.detailData?.roomDetails || this.roomDetails;
        const activityReqParams = getActivityExtraData(this.props.detailData.packageDetail, day);
        const subtitleData = createSubtitleData(departureDetail, roomDetails, {});
        this.activityFailedScrollRef.current = true;

        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRAVEL_TIDBITS, {
          departureDetail,
          activityReqParams,
          day: day,
          dynamicId: this.packageDetailDTO.dynamicPackageId,
          pricingDetail: pricingDetail,
          onComponentChange: this.onComponentChange,
          subtitleData: subtitleData,
          lastPage: HOLIDAY_ROUTE_KEYS.DETAIL,
          branch: this.props.detailData.branch,
          packageDetailDTO: this.packageDetailDTO,
          roomDetails: roomDetails,
          trackLocalClickEvent: this.trackLocalClickEvent,
          trackLocalPageLoadEvent: this.trackLocalPageLoadEvent,
          activityProductType: reviewError?.error?.errorData?.dialogResponse?.content?.soldOutItems?.[0]?.productType,
          currentActivePlanOnItinerary: this.state.currentActivePlanOnItinerary,
        });
        break;
      }
      default: {
        HolidayNavigation.pop();
      }
    }
  };

  getNameOfFailedItinerary = (reviewError, packageDetail) => {
    if (reviewError && reviewError.error && reviewError.error.errorType) {
      switch (reviewError.error.errorType) {
        case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
          let namesOfHotels = '';
          if (packageDetail) {
            if (packageDetail.hotelDetail) {
              if (packageDetail.hotelDetail) {
                const { failedHotels } = reviewError.error.errorData || [];
                if (failedHotels) {
                  for (let i = 0; i < failedHotels.length; i++) {
                    if (packageDetail.hotelDetail[failedHotels[i].sellableId]) {
                      namesOfHotels = packageDetail.hotelDetail[failedHotels[i].sellableId].name;
                      break;
                    }
                  }
                }
              }
            }
          }
          return namesOfHotels;
        }
        case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
          return 'Flights';
        }
        case detailReviewFailure.REVIEW_FAILED_TYPE_VISA:
        {
          return 'VISA';
        }
        case detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY:
        {
          return 'Activity';
        }

        default:
          return '';
      }
    } else if (!isEmpty(this.props.componentFailureData)) {
      /*
       * Below commented code enable highlighting error component in red color
       * and also shows a toast message in day plan
       */
      const { componentErrors } = this.props.componentFailureData || {};
      const { FLIGHT = [], HOTEL = [] } = componentErrors || {};
      if (HOTEL && HOTEL.length > 0) {return HOTEL[0].hotelName;}
      if (FLIGHT && FLIGHT.length > 0) {return 'Flights';}
    }
    return '';
  };

  handleReviewFailure = (reviewError) => {
    const { errorType = '' } = reviewError?.error || {};
    if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER) {
      logPhoenixDetailPDTEvents({actionType:PDT_EVENT_TYPES.contentSeen,value:PDTConstants.ACTIVITY_SOLD_OUT,shouldTrackToAdobe:false});
      this.fetchDetailDataFunc(
        false,
        true,
        null,
        false,
        true,
      );
    }
    this.setState({
      reviewError,
      showReviewPopUp: true,
    });
  };
}

const styles = StyleSheet.create({
  pageWrap: {
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
    backgroundColor: holidayColors.white,
  },
  summaryContainer: {
    ...paddingStyles.ph16,
    ...paddingStyles.pt8,
  },
  messageStripContainer: {
    marginTop: 0,
    paddingTop: 5,
  },
  visaContainer: {
    ...paddingStyles.ph16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftRightSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...paddingStyles.ph16,
    ...marginStyles.mt10,
  },
  pkgName: {
    ...marginStyles.mt20,
    ...paddingStyles.ph16,
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  planExpiredBackGround:{
    backgroundColor: holidayColors.fadedRed,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  planExpiredText: {
    color: holidayColors.red,
    ...fontStyles.labelSmallBold,
  },
  closeIcon: {
    tintColor: holidayColors.yellow,
    width: 16,
    height: 16,
  },
  pricePersuasion:{
    width:'100%',
    backgroundColor: holidayColors.fadedYellow,
    paddingVertical:10,
    paddingHorizontal:15,
    display:'flex',
    flexDirection:'row',
  },
  persuasionText:{
    color: holidayColors.yellow,
    ...fontStyles.labelBaseRegular,
    flex: 10,
  },
  horizontalLoader:{
    width: '100%',
    position: 'absolute',
    height: '100%',
    zIndex: 20,
    elevation: 30,
  },
  containerStyle:{ paddingLeft:0, paddingRight:0, paddingBottom:20},
  featureListBorder : {
    borderColor: holidayColors.grayBorder,
    borderWidth: 0,
    borderTopWidth: 1,
    marginTop: 10,
    paddingTop: 4,
  },
  persuasionContainer: {
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 5,
    marginBottom: 0,
  },
  membershipCard: {
    ...marginStyles.ml16,
    ...marginStyles.mr16,
  },
  membershipCardContainer:{
    borderTopColor: holidayColors.grayBorder,
    borderTopWidth: 4,
    ...paddingStyles.pb10,
    ...paddingStyles.pt10,
  },
  dotContainerStyle: {
    ...marginStyles.mh4,
  },
  paginationContainerStyle: {
    ...paddingStyles.pt0,
    ...paddingStyles.pb4,
  },
  membershipCardCarousel: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv4,
  },
});

export default withPermissionDialog(
  withIntervention(
    withBackHandler(MimaPreSalesPage, PRESALES_DETAIL)
  )
);

