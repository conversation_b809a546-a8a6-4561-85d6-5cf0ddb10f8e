import PropTypes from 'prop-types';
import isEmpty from 'lodash/isEmpty';
import LinearGradient from 'react-native-linear-gradient';
import React from 'react';
import { BackHandler, Image, NativeModules, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {
  PAYMENT_STATUS,
  PDT_PAGE_ENTRY_EVENT, PDT_PAGE_EXIT_EVENT,
  PDT_PAGE_LOAD,
  PDT_RAW_EVENT,
  THANKYOU_VIEW_STATE,
} from '../../HolidayConstants';
import StickyHeaderIos from './StickyHeaderIos';
import InfoCard from './InfoCard';
import {
  createRandomString,
  getRating,
  goToHolidayLanding,
  isRawClient,
  rupeeFormatterUtils,
  saveHolMeta,
  shareScreenShot,
} from '../../utils/HolidayUtils';
import { HDFC_FOREX_CARD_CLICK_URL, HDFC_FOREX_CARD_IMAGE_URL } from '../../Review/HolidayReviewConstants';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';
import { getEvar108ForFunnel, trackThankyouClickEvent, trackThankyouLoadEvent } from '../../utils/HolidayTrackingUtils';
import {
  addEmailHashToDataLayer,
  createBookingData,
  createErrorPmtResp,
  createLoggingMap,
  createPmtRespFromBkngDtls,
  getPackageBranch,
  isThankyouSuccess,
  showNpsLayout,
  THANKYOU_PDT_PAGE_NAME,
  trackThankYouGILoadEvent,
} from './HolidayThankyouUtils';
import { OBT_BRANCH } from '../../PhoenixDetail/DetailConstants';
import { fetchBookingDetail, fetchPersuasionData } from '../../utils/HolidayNetworkUtils';
import HolidaySafeMessage from '../../Common/Components/CovidSafety/HolidaySafeMessage';
import HolidayBlackPersuasion from '../../Common/Components/HolidayBlackPersuasion';
import { getThankuBannerDesciption } from '../../Common/Components/CovidSafety/HolidaySafeDataHolder';
import { HolidayNavigation } from '../../Navigation';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import HolidayModule from '@mmt/legacy-commons/Native/HolidayModule';
import { setFirebaseTrackingForThankyou } from '../../utils/FirebaseUtils/FirebaseTracking';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { smallHeightSeperator } from '../../Styles/holidaySpacing';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import PoweredByMMT from '../../Common/Components/PoweredByMMT';
import { getColor, getFontFamily } from '../../theme';
import { FONT_KEYS } from '../../theme/font';
import { PWArating } from '../../LandingNew/Components/MobileLoginModule';
import { deleteRecentSearchPackage, sendConversionEvent } from '../../utils/ThirdPartyUtils';
import { COLOR_KEYS } from '../../theme/colors';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { triggerEvent } from '../../utils/googleAdUtils';
import { initThankYouPDTObj, logThankYouPDTEvents } from './Utils/HolidayThankYouPDTTrackingUtils';
import withBackHandler from '../../hooks/withBackHandler';

const successBooking = require('@mmt/legacy-assets/src/successBooking.webp');
const failedBooking = require('@mmt/legacy-assets/src/failedBooking.webp');
const homeIcon = require('@mmt/legacy-assets/src/iconHome.webp');

class HolidayThankYou extends BasePage {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props, 'holidayThankYou');
    this.isSafePackage = false;
    if (props.payId) {
      this.paymentResponse = {};
    } else {
      this.paymentResponse = {...this.props.paymentResponse};
    }
    this.state = {
      viewState: THANKYOU_VIEW_STATE.LOADING,
      forexImageLoadFailed: false,
      blackPersuasion: null,
    };
    this.paymentFailed = false;
    this.previousPage = 'fromReview';
    saveHolMeta('', this.props.aff, '', this.props.cmp)
  }


  _updateThankYouData = () => {
    this.thankyouData = { ...this.paymentResponse, source: this.props.source };
    if (this.props.pageDataMap) {
      this.thankyouData.pageDataMap = { ...this.props.pageDataMap };
      this._fetchPersuasionData();
      this.thankyouData.pageDataMap.bookingDetails = createBookingData(this.thankyouData);
    } else {
      this.thankyouData.pageDataMap = createLoggingMap(this.thankyouData, this.props.isWG);
    }
    this.thankyouData.pageDataMap.purchase = isThankyouSuccess(this.paymentResponse);
    this.thankyouData.pageDataMap.safe = this.isSafePackage;
    this.thankyouData.branch = getPackageBranch(this.paymentResponse);
    deleteRecentSearchPackage({
      uniqueCityId: this.thankyouData?.pageDataMap?.packageDetails?.pkg_cities?.[0],
    });
    trackThankYouGILoadEvent();
    setFirebaseTrackingForThankyou({ thankyouData: this.thankyouData });

    initThankYouPDTObj({
      thankYouData: this.thankyouData,
      reviewDetail: this.props.reviewDetail,
    });

    logThankYouPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value: PDT_PAGE_LOAD,
      thankYouData: this.thankyouData,
      reviewDetail: this.props.reviewDetail,
      event_detail: this.props.pdtProductObject.event_detail,
    })
    const { currencyCode  = 'INR'} = this.props?.reviewDetail?.pricingDetail || {};

    sendConversionEvent({
      totalAmount: this.thankyouData?.totalAmount,
      paymentReferenceId: this.thankyouData?.paymentReferenceId,
      branch: this.thankyouData?.branch,
      currencyCode: currencyCode,
    })

    addEmailHashToDataLayer({
      userEmail:
        this.paymentResponse?.primaryTravellerDetail?.email || this.paymentResponse?.userEmailId,
    });

    trackThankyouLoadEvent({
      logOmni: true,
      omniPageName: THANKYOU_PDT_PAGE_NAME,
      pdtData: {
        pageDataMap: this.thankyouData ? this.thankyouData.pageDataMap : {},
        interventionDetails: {},
        activty: PDT_RAW_EVENT,
        event: PDT_PAGE_ENTRY_EVENT,
        requestId: createRandomString(),
        branch:
          this.thankyouData && this.thankyouData.branch ? this.thankyouData.branch : OBT_BRANCH,
      },
      omniData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForFunnel({
          source: this.props.source || '',
          ticketSource:
            this.props?.ticketSource || this.paymentResponse?.trackingInfo?.ticketSource || '',
          trackingPageName: THANKYOU_PDT_PAGE_NAME,
        }),
      },
    });
  };


  onBackClick(){
    return this.onHardBackPress();
    //Handle PageExit tracking
    logThankYouPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value: PDT_PAGE_EXIT_EVENT,
      thankYouData: this.thankyouData,
      reviewDetail: this.props.reviewDetail,
      event_detail: this.props.pdtProductObject.event_detail,
    });
  }

  componentDidMount() {
    super.componentDidMount();
    let payId = this.props.payId
      ? encodeURIComponent(this.props.payId)
      : this.parsePayIdFromRedirectURL();
    if (payId) {
      this._fetchThankYouData(payId);
    } else {
      this._updateThankYouData();
      this.setState({
        viewState: THANKYOU_VIEW_STATE.SUCCESS,
      });
    }
  }

  async _fetchPersuasionData() {
    const response = await fetchPersuasionData('THANKYOU', null, this.props.tagDestination);
    if (
      response &&
      response.persuasionsDetail &&
      response.persuasionsDetail.mmtBlackPersuasion &&
      response.persuasionsDetail.mmtBlackPersuasion.mmtBlackDetail
    ) {
      this.setState({
        blackPersuasion: response.persuasionsDetail.mmtBlackPersuasion.mmtBlackDetail,
      });
    }
  }

  async _fetchThankYouData(payId) {
    const response = await fetchBookingDetail(payId);
    if (response) {
      if (this.props.payId) {
        this.paymentResponse = createPmtRespFromBkngDtls(response);
        this.isSafePackage = this.paymentResponse.safe;
        this.safetyRating = getRating(this.paymentResponse.safetyRatings);
      } else {
        const { packageDetail = {}, primaryTravellerDetail = {} } = response;
        const { safe = false } = packageDetail;
        this.isSafePackage = safe;
        this.paymentResponse.primaryTravellerDetail = primaryTravellerDetail || {};
        this.safetyRating = getRating(this.paymentResponse.safetyRatings);
      }
    } else {
      if (this.props.payId) {
        this.paymentResponse = createErrorPmtResp();
      }
    }

    this._updateThankYouData();
    this.setState({
      viewState: THANKYOU_VIEW_STATE.SUCCESS,
    });
  }

  parsePayIdFromRedirectURL = () => {
    var { redirectUrl } = this.paymentResponse;
    console.log('Nitin : redirectUrl : ' + redirectUrl);
    if (redirectUrl && redirectUrl.includes('PayId')) {
      var payId = /PayId=([^&]+)/.exec(redirectUrl)[1];
      return payId;
    }
    return null;
  };

  _onForexImageLoadError = () => {
    this.setState({ forexImageLoadFailed: true });
  };

  handleForexClicked = () => {
    const { HolidayModule } = NativeModules;
    HolidayModule.openWebView({
      url: HDFC_FOREX_CARD_CLICK_URL,
    });
    this.trackThankyouLocalClickEvent('forex', '');
  };

  onHardBackPress = () => {
    if (this.paymentFailed) {
      HolidayNavigation.pop();
    } else {
      HolidayModule.goToAppHome();
    }
    return true;
  };

  openHome = () => {
    goToHolidayLanding();
    this.trackThankyouLocalClickEvent('home_button', '');
  };

  shareScreen = () => {
    shareScreenShot();
    this.trackThankyouLocalClickEvent('share', '');
  };
  
  closePWARating = () => {
    this.setState({
      openPWARating: false
    });
  };

  trackThankyouLocalClickEvent = (eventName, suffix) => {
    logThankYouPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
      thankYouData: this.thankyouData,
      reviewDetail: this.props.reviewDetail,
      event_detail: this.props.pdtProductObject.event_detail,
    });

    trackThankyouClickEvent({
      omniPageName: THANKYOU_PDT_PAGE_NAME,
      omniEventName: eventName + suffix,
      pdtData: {
        pageDataMap: this.thankyouData ? this.thankyouData.pageDataMap : {},
        interventionDetails: {},
        activty: PDT_RAW_EVENT,
        event: eventName,
        requestId: createRandomString(),
        branch: this.thankyouData?.branch ?? OBT_BRANCH,
      },
      omniData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForFunnel({
          source: this.props.source || '',
          trackingPageName: THANKYOU_PDT_PAGE_NAME,
          ticketSource:
            this.props?.ticketSource || this.paymentResponse?.trackingInfo?.ticketSource || '',
        }),
      },
    });
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        {this.state.viewState === THANKYOU_VIEW_STATE.LOADING && this.renderProgressView()}
        {this.state.viewState === THANKYOU_VIEW_STATE.SUCCESS && this.renderContent()}
      </View>
    );
  }

  renderProgressView() {
    return (
      <View style={styles.progressContainer}>
        <Spinner
          size={36}
          strokeWidth={4}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.primaryBlue}
        />
      </View>
    );
  }

  renderContent() {
    this.booking = this.paymentResponse;
    this.paymentSuccess = this.booking && this.booking.status === PAYMENT_STATUS.SUCCESS;
    this.paymentUnavailable = this.booking && this.booking.status === PAYMENT_STATUS.UNAVAILABLE;
    let partPayment = false;
    // let email = CONTACT_US_EMAIL_ID;
    // let phone = CONTACT_US_PHONE;
    let bookingIdAvailable = false;
    let bookingId = '';
    let lob = '';
    let category = '';
    const branch = getPackageBranch(this.paymentResponse);
    let bookingDetails={};
    const { userEmailId = '', userPhoneNo = ''} = this.paymentResponse || {};
    const { primaryTravellerDetail = {} } = this.booking || {};
    const { phoneNumber = '', email = '' } = primaryTravellerDetail || {};
    const shouldRenderHolidaySafeMessage =
      this.safetyRating && this.isSafePackage && getThankuBannerDesciption(branch);
    if (this.paymentSuccess) {
      // email = this.booking.contactUsEmailId;
      // phone = this.booking.contactUsPhoneNo;
      if (this.booking.amountPaid !== this.booking.totalAmount) {
        partPayment = true;
      }
      if (branch === OBT_BRANCH) {
        lob = 'Holidays_INTL';
        category = 'Holidays_INTL_ThankYouPage';
      } else {
        lob = 'Holidays';
        category = 'Holidays_ThankYouPage';
      }
      bookingId = this.booking.paymentReferenceId;
      if (isRawClient()) {
        bookingDetails = showNpsLayout(branch, lob, category, bookingId, partPayment);
      } else {
      showNpsLayout(branch, lob, category, bookingId, partPayment);
     }
   }
    if (!isEmpty(this.booking.paymentReferenceId)) {
      bookingIdAvailable = true;
      bookingId = this.booking.paymentReferenceId;
    }

    const handleOnPress = () => {
      let url = 'mmyt://support/booking/listing/';
      if (isRawClient()) {
        url = 'https://pwa-supportz.makemytrip.com/MyAccount/BookingSummary';
        const { HolidayModule } = NativeModules;
        HolidayModule.handleWebDeeplink({ url: url });
      }
      GenericModule.openDeepLink(url);
    };
    return (
      <View style={[AtomicCss.flex1, { backgroundColor: '#f2f2f2' }]}>
        <ScrollView stickyHeaderIndices={[0]}>
          {this.paymentSuccess && (
            <StickyHeaderIos shareScreen={this.shareScreen} openHome={this.openHome} />
          )}
          {this.paymentSuccess && (
            <View
              style={styles.gradientContainer}
            >
              <LinearGradient
                start={{ x: 1.0, y: 0.0 }}
                end={{ x: 0.0, y: 1.0 }}
                colors={getColor(COLOR_KEYS.THANK_YOU_GRAD, ['#3a7bd5','#00d2ff'])}
                style={styles.containerHeader}
              >
                <View style={styles.shareSection}>
                  <TouchableOpacity style={styles.iconHomeWrapper} onPress={this.openHome}>
                    <Image style={styles.iconHome} source={homeIcon} />
                  </TouchableOpacity>
                  {!isRawClient() && (
                    <TouchableOpacity style={AtomicCss.pushRight} onPress={this.shareScreen}>
                      <Text style={styles.shareTextPr}> SHARE</Text>
                    </TouchableOpacity>
                  )}
                </View>

                <Image style={styles.cardIconHeader} source={successBooking} />
                <Text style={[styles.headingHeader,{fontFamily:getFontFamily(FONT_KEYS.HEADING_FONT)}]}>Congratulations! </Text>
                <Text style={[styles.msgHeader,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>Your Holiday booking is successful!</Text>
                <Text style={[styles.infoHeader, AtomicCss.marginBottom3,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>
                  Details will be sent via{' '}
                  <Text style={[styles.infoHeaderBold,{fontFamily:getFontFamily(FONT_KEYS.TEXT_BOLD)}]}>SMS, Whatsapp to {phoneNumber || userPhoneNo}</Text> and{' '}
                  <Text style={[styles.infoHeaderBold,{fontFamily:getFontFamily(FONT_KEYS.TEXT_BOLD)}]}>email to {email || userEmailId}</Text>
                </Text>
                <View style={marginStyles.mt20}>
                  <Text style={styles.infoHeaderBold}> Booking ID: {bookingId}</Text>
                </View>
                <View style={{padding:20}}>
                  <PoweredByMMT isLanding/>
                </View>
              </LinearGradient>
            </View>
          )}

          {!this.paymentSuccess && (
            <View>
              <LinearGradient
                start={{ x: 1.0, y: 0.0 }}
                end={{ x: 0.0, y: 1.0 }}
                colors={['#ff3e5e', '#ff7f3f']}
                style={[styles.containerHeader, { marginTop: 0 }]}
              >
                <View style={styles.shareSection}>
                  <TouchableOpacity style={styles.iconHomeWrapper} onPress={this.openHome}>
                    <Image style={styles.iconHome} source={homeIcon} />
                  </TouchableOpacity>
                  {!isRawClient() && (
                    <TouchableOpacity style={AtomicCss.pushRight} onPress={this.shareScreen}>
                      <Text style={styles.shareTextPr}> SHARE</Text>
                    </TouchableOpacity>
                  )}
                </View>

                <Image style={styles.cardIconHeader} source={failedBooking} />
                {this.paymentUnavailable && <Text style={[styles.headingHeader, marginStyles.mb20]}>Not Found</Text>}
                {!this.paymentUnavailable && (
                  <Text style={[styles.headingHeader, marginStyles.mb20]}>Your booking has failed</Text>
                )}
                {this.paymentUnavailable && (
                  <Text style={styles.msgHeader}>
                    We are unable to fetch your details right now. Please get in touch with our
                    customer care.
                  </Text>
                )}
                {!this.paymentUnavailable && (
                  <Text style={styles.msgHeader}>
                    We are sorry, your booking has failed however your card may have been charged
                  </Text>
                )}
                {bookingIdAvailable && (
                  <Text style={styles.infoHeader}> Booking ID: {bookingId}</Text>
                )}
              </LinearGradient>
            </View>
          )}

          {this.booking.amountPaid > 0 && (
            <View style={styles.containerPrice}>
              <View style={styles.topSection}>
                <Text style={[styles.label,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>Amount Paid</Text>
                <Text style={[styles.amount,{fontFamily:getFontFamily(FONT_KEYS.TITLE_FONT)}]}>{rupeeFormatterUtils(this.booking.amountPaid)}</Text>
              </View>

              {!!partPayment && (
                <View style={styles.bottomSection}>
                  <Text style={[styles.info,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>Please pay remaining amount as per schedule.</Text>
                  <Text style={[styles.info,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>Have a great trip</Text>
                </View>
              )}
            </View>
          )}

          {this.paymentSuccess && (
            <View>
              <InfoCard booking={this.booking} />

              {shouldRenderHolidaySafeMessage && (
                  <View
                    style={[styles.containerPrice, paddingStyles.pv14, marginStyles.mh16]}
                  >
                    <HolidaySafeMessage
                      safetyRating={this.safetyRating}
                      thankYou
                      branch={this.thankyouData ? this.thankyouData.branch : OBT_BRANCH}
                    />
                  </View>
                )}

              {this.state.blackPersuasion && (
                <View>
                  <HolidayBlackPersuasion
                    blackPersuasionData={this.state.blackPersuasion}
                    style={marginStyles.mb10}
                  />
                </View>
              )}

              <View style={styles.containerMessage}>
                <Text style={[styles.text,{fontFamily:getFontFamily(FONT_KEYS.TEXT_DEFAULT)}]}>
                  We’ve sent a mail with your <Text style={[styles.textBold,{fontFamily:getFontFamily(FONT_KEYS.TEXT_BOLD)}]}>booking card </Text>and{' '}
                  <Text style={[styles.textBold,{fontFamily:getFontFamily(FONT_KEYS.TEXT_BOLD)}]}> invoice</Text>. In case of any further queries or
                  customization requests, please reach out to us via MyTrips section{' '}
                  <Text style={[styles.text, styles.blueText]} onPress={handleOnPress}>
                    here.
                  </Text>
                </Text>
              </View>
            </View>
          )}

          {this.paymentSuccess && (
            <TouchableOpacity activeOpacity={0.9} onPress={this.handleForexClicked}>
              <Image
                style={styles.forexImage}
                source={
                  !this.state.forexImageLoadFailed
                    ? { uri: HDFC_FOREX_CARD_IMAGE_URL }
                    : genericCardDefaultImage
                }
                onError={this._onForexImageLoadError}
              />
            </TouchableOpacity>
          )}
        </ScrollView>
        {isRawClient() && PWArating && this.state.openPWARating && this.paymentSuccess ? (
            <View style={styles.ratingDrawer}>
             <PWArating bookingDetails={bookingDetails} onFeedbackClose={this.closePWARating} />
            </View>
         ): null }
      </View>
    );
  }
}

const styles = StyleSheet.create({
  containerPrice: {
    backgroundColor: holidayColors.white,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
    marginBottom: 13,
  },
  topSection: {
    ...paddingStyles.pa16,
    ...smallHeightSeperator,
    flexDirection: 'row',
  },
  bottomSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  info: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  label: {
    color: holidayColors.gray,
    ...fontStyles.labelLargeRegular,
  },
  amount: {
    color: holidayColors.black,
    ...fontStyles.labelLargeBold,
    marginLeft: 'auto',
  },
  partialAmount: {
    color: holidayColors.black,
    ...fontStyles.labelLargeBold,
  },
  containerMessage: {
    alignItems: 'center',
    ...paddingStyles.ph16,
    ...paddingStyles.pv14,
    backgroundColor: holidayColors.white,
    borderBottomWidth: 1,
    borderTopWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...marginStyles.mb14,
  },
  text: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.black,
    lineHeight: 22,
  },
  blueText: {
    color: holidayColors.primaryBlue,
  },
  textBold: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    lineHeight: 22,
  },
  gradientContainer: {
      position: 'relative',
      zIndex: 10,
      marginTop: -67,
  },
  containerHeader: {
    paddingHorizontal: 15,
    paddingVertical: 27,
    alignItems: 'center',
    zIndex: 1,
    position: 'relative',
  },
  cardIconHeader: {
    width: 48,
    height: 45,
    marginTop: -5,
  },
  headingHeader: {
    marginBottom: 3,
    ...fontStyles.headingMedium,
    color: holidayColors.white,
    backgroundColor: holidayColors.transparent,
  },
  msgHeader: {
    marginBottom: 24,
    color: 'rgba(255,255,255,0.8)',
    ...fontStyles.labelMediumRegular,
    backgroundColor: holidayColors.transparent,
    textAlign: 'center',
  },
  infoHeader: {
    color: holidayColors.white,
    ...fontStyles.labelBaseRegular,
    backgroundColor: holidayColors.transparent,
    alignItems: 'center',
    textAlign: 'center',
  },
  infoHeaderBold: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBold,
    alignItems: 'center',
    textAlign: 'center',
  },
  iconHome: {
    width: 20,
    height: 20,
  },
  shareTextPr: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.white,
    backgroundColor: holidayColors.transparent,
  },
  shareSection: {
    flexDirection: 'row',
    width: '100%',
  },
  iconHomeWrapper: {
    paddingVertical: 4,
    paddingHorizontal: 4,
    marginTop: -4,
    marginLeft: -4,
  },
  forexImage: {
    width: '100%',
    height: 365,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    marginTop: 5,
    marginBottom: 10,
  },
  progressContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: '#ffffffd9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ratingDrawer : {
    position: 'fixed',
    bottom: 0,
  },
});

HolidayThankYou.propTypes = {
  paymentResponse: PropTypes.object.isRequired,
  tagDestination: PropTypes.string.isRequired,
};

export default withBackHandler(HolidayThankYou);
