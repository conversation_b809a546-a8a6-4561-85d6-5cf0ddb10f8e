import React, { useRef } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Animated,
  Easing,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Dimensions,
  Keyboard,
  Platform,
} from 'react-native';
import PropTypes from 'prop-types';

import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import {
  animateOverlay,
  getDiscountAmount,
  getWalletAmount,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconEmiTag from '@mmt/legacy-assets/src/icemi.webp';
import iconWallet from '@mmt/legacy-assets/src/holidays/wallet-icon.png';
import iconRedError from '@mmt/legacy-assets/src/info_red.webp';
import iconBlueDown from '@mmt/legacy-assets/src/ic_arrow_blue_down.webp';
import iconBlueUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import iconGreenTick from '@mmt/legacy-assets/src/green_tick.webp';
import CouponCard from '../../Common/Components/CouponOffers/CouponCard';
import { COUPON_APPLY, COUPON_REMOVE } from '../../Review/HolidayReviewConstants';
import { isMobileClient, isNotNullAndEmptyCollection, rupeeFormatterUtils } from '../../utils/HolidayUtils';
import EMIOption from '../../PhoenixReview/Components/EMIOptions';
import { PDTConstants } from '../../PhoenixReview/Utils/HolidayReviewConstants';
import FilterLoader from '../../SearchWidget/Components/FilterLoader';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import BottomSheetOverlay, { BottomSheetCross } from '../../Common/Components/BottomSheetOverlay';
import { isEmpty } from "lodash";
import TextButton from '../../Common/Components/Buttons/TextButton';
import { COUPON_BOX_PLACEHOLDER_TEXT, INVALID_COUPON_MESSAGE, SELECT_ACTIONS, SUCCESSFULLY_APPLIED_MESSAGE } from '../../PhoenixDetail/DetailConstants';
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { capitalizeText } from '../../utils/textTransformUtil';

const iconCross = require('@mmt/legacy-assets/src/iconCross.webp');
const COUPONS_MAX_LENGTH = 2;
const windowHeight = Dimensions.get('window').height;

const INPUT_STATES = {
  FOCUSED: 'focused',
  UNFOCUSED: 'unfocused',
}

class OfferOverlay extends React.Component {
  inputRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      overlayPosition: new Animated.Value(0),
      couponText: false,
      couponError: false,
      couponValue: '',
      viewMore: false,
      emiModal: false,
      inputState: INPUT_STATES.UNFOCUSED,
      isKeyboardOpen: false,
    };
    this.keyboardDidShowListener = null;
    this.keyboardDidHideListener = null;
  }

  static navigationOptions = { header: null };

  validateAndSetCouponCode() {
    const { offerSection = {} } = this.props;
    const { couponList = [], couponData = {} } = offerSection;
    const { couponCode = '' } = couponData;
    const selected = couponList.find((coupon) => coupon.selected);

    const valid = !selected && !isEmpty(couponCode);
    this.setState({
      couponText: valid,
      couponValue: valid ? couponCode : '',
    });
  }

  componentDidMount() {
    animateOverlay(this, 400);
    this.props.getEmiOptions();
    this.validateAndSetCouponCode();

    this.keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === 'android' ? 'keyboardDidShow' : 'keyboardWillShow',
      this.keyboardDidShow,
    );

    this.keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'android' ? 'keyboardDidHide' : 'keyboardWillHide',
      this.keyboardDidHide,
    );
  }

  componentWillUnmount() {
    if (isMobileClient()) {
      this.keyboardDidShowListener.remove();
      this.keyboardDidHideListener.remove();
    }
  }

  keyboardDidShow = () => {
    this.setState({ isKeyboardOpen: true, viewMore: false });
  };

  keyboardDidHide = () => {
    this.setState({ isKeyboardOpen: false });
  };

  componentDidUpdate(prevProps) {
    const { offerSection = {} } = this.props || {};
    const { couponData = {} } = offerSection || {};
    const { error: couponError } = couponData || {};
    if (prevProps.offerSection !== offerSection) {
      this.validateAndSetCouponCode();
      this.setState({ couponError });
    }
  }

  handleTextChange = (couponValue) => {
    const { couponError } = this.state || {};
    if (couponError) {
      this.setState({ couponError: false });
    }
    this.setState({ couponValue });
  };

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    }).start();
  }

  validateOwnCoupon(remove) {
    const couponVal = this.state.couponValue?.toUpperCase();
    this.setState({ couponError: false });
    this.setState({ inputState: INPUT_STATES.UNFOCUSED });
    if (couponVal) {
      const action = remove ? COUPON_REMOVE : COUPON_APPLY;
      this.props.applyOfferSection(couponVal, action);
      const PDT_ACTION = remove ? PDTConstants.REMOVE_COUPON : PDTConstants.APPLY_COUPON;
      this.captureClickEvents({ eventName: PDT_ACTION, suffix: `_${couponVal}` });
      this.inputRef.current.blur();
    }
  }

  captureClickEvents = ({ eventName = '', suffix = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
    });
    this.props.trackLocalClickEvent(eventName, suffix);
  };

  changeText(val) {
    if (!val) {
      this.setState({ couponError: false });
    }
    this.setState({ couponValue: val });
  }

  validateCoupon(item, action) {
    this.inputRef.current.blur();
    this.props.applyOfferSection(item?.couponCode, action);
    const PDT_ACTION = action === 'APPLY' ? PDTConstants.APPLY_COUPON : PDTConstants.REMOVE_COUPON;
    this.captureClickEvents({ eventName: PDT_ACTION, suffix: `_${item?.couponCode}` });
  }

  getCouponList(list) {
    return list.map((item, index) => {
      const {
        couponCode = '',
        selected = false,
        message = '',
        recommendationMessage = '',
        tncUrl = '',
      } = item;
      return (
        <CouponCard
          key={index}
          couponCode={couponCode}
          couponDesc={selected ? message : recommendationMessage}
          validateCoupon={(action) => this.validateCoupon(item, action)}
          offerPrice={'- ₹' + rupeeFormatter(getDiscountAmount(item, this.props.travellerCount))}
          selected={selected}
          tncUrl={tncUrl}
          removerError={() => this.setState({ couponError: false })}
        />
      );
    });
  }
  openEmiModal() {
    this.setState({ emiModal: true });
  }
  handleOnFocus() {
    this.captureClickEvents({ eventName: 'Enter_coupon_code' });
    this.setState({ inputState: INPUT_STATES.FOCUSED });
  }

  handleOnBlur() {
    this.setState({ inputState: INPUT_STATES.UNFOCUSED });
  }
  emiTag = () => {
    const { emiDetails } = this.props || {};
    return (
      <View style={styles.emiTag}>
        <View>
          <Image source={iconEmiTag} style={styles.iconEmiTag} />
        </View>
        <View>
          <Text style={styles.emi}>NO COST EMI @ {rupeeFormatterUtils(emiDetails?.emiAmount)}</Text>
          <View style={styles.emiOptions}>
            <Text style={styles.emiOptionsText}>Book your holidays with Easy</Text>
            <TouchableOpacity
              onPress={() => {
                this.openEmiModal();
                this.captureClickEvents({ eventName: PDTConstants.EMI_OPTIONS });
              }}
            >
              <Text style={styles.emiOptionsLink}> EMI options.</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  render() {
    const {
      togglePopup,
      dealDetail,
      travellerCount,
      offerSection,
      emiDetails,
      getEmiOptions,
      emiOptions,
      persuasion,
      showOfferHorizontalLoader,
    } = this.props || {};
    const { bankName = '' } = emiDetails || {};
    const { couponList = [] } = offerSection || {};
    let orderedCouponList = [...couponList];
    const selectedCouponIndex = couponList?.findIndex((coupon) => coupon.selected);
    const selectedCoupon = couponList?.find((coupon) => coupon.selected);
    if (selectedCouponIndex > 0) {
      orderedCouponList.splice(selectedCouponIndex, 1);
      orderedCouponList.unshift(selectedCoupon);
    }
    const { couponText, couponError, couponValue, overlayPosition, viewMore, emiModal } =
      this.state || {};

    const focusedState = this.state.inputState === INPUT_STATES.FOCUSED;
    const unfocusedState = this.state.inputState === INPUT_STATES.UNFOCUSED;
    const couponTyping = focusedState || couponValue;
    const errorState = unfocusedState && couponError;
    const placeholderText = unfocusedState
      ? COUPON_BOX_PLACEHOLDER_TEXT.UNFOCUSED
      : COUPON_BOX_PLACEHOLDER_TEXT.FOCUSED;
    const textBoxButtonText = couponText ? SELECT_ACTIONS.REMOVE : SELECT_ACTIONS.APPLY;
    const inputBoxStyle = errorState
      ? styles.errorTxtBox
      : couponTyping
      ? styles.textBoxWrapClick
      : styles.textboxWrap;

    const handleCloseBtn = () => {
      togglePopup('');
      this.captureClickEvents({ eventName: 'detail_offers_close' });
    };
    return (
      <View style={styles.overlayContainer}>
        {showOfferHorizontalLoader && (
          <View style={styles.horizontalLoader}>
            <FilterLoader loadingFirstTime={false} showCenterLoader={true} />
          </View>
        )}
        <TouchableOpacity onPress={() => togglePopup('')} style={styles.overlayBg} />
        <Animated.View style={[styles.overlayContent, { bottom: overlayPosition }]}>
          <View style={styles.popupHeader}>
            <Text style={styles.popupHeadingText}>Coupons & Offers</Text>
            <BottomSheetCross
              closeIconWrapper={styles.closeIconWrapper}
              toggleModal={handleCloseBtn}
            />
          </View>
          <ScrollView>
            <TouchableOpacity activeOpacity={1}>
              <View style={styles.textBoxContainer}>
                <View style={[styles.textBox, inputBoxStyle]}>
                  {/* {couponText && <Image source={iconGreenTick} style={styles.iconTick} />}   @todo rohit- need to check point
                  {couponError && <Image source={iconRedError} style={styles.rediconTick} />} */}
                  <TextInput
                    ref={this.inputRef}
                    autoCapitalize={'characters'}
                    placeholder={placeholderText}
                    value={couponValue}
                    placeholderTextColor={holidayColors.lightGray}
                    onChangeText={this.handleTextChange}
                    style={[styles.couponTxtbox, couponText ? styles.selectedText : {}]}
                    editable={!couponText}
                    onFocus={() => this.handleOnFocus()}
                    onBlur={() => this.handleOnBlur()}
                  />
                  {!couponTyping ? (
                    <TouchableOpacity onPress={() => this.handleOnFocus()}>
                      <Text style={[styles.applyTextValue]}>{capitalizeText('Enter Code')}</Text>
                    </TouchableOpacity>
                  ) : (
                    <TextButton
                      hitSlop={styles.couponTextContainer}
                      buttonText={capitalizeText(textBoxButtonText)}
                      handleClick={() => this.validateOwnCoupon(couponText)}
                      btnTextStyle={[styles.applyTextValue, couponValue ? {} : styles.applyTxt]}
                      disabled={!couponValue}
                    />
                  )}
                </View>
                {errorState && (
                  <View style={styles.alertWrapper}>
                    <Text style={styles.invalidText}>{INVALID_COUPON_MESSAGE}</Text>
                  </View>
                )}
                {couponText && (
                  <View style={styles.alertWrapper}>
                    <Text style={styles.applyText}>{SUCCESSFULLY_APPLIED_MESSAGE}</Text>
                  </View>
                )}
              </View>
              <View>
                <View style={styles.couponCardWrap}>
                  {this.getCouponList(orderedCouponList?.slice(0, COUPONS_MAX_LENGTH))}
                  {orderedCouponList?.length > COUPONS_MAX_LENGTH && (
                    <>
                      <TouchableOpacity
                        onPress={() => {
                          this.setState({ viewMore: true });
                          this.captureClickEvents({ eventName: 'View_More_Coupons' });
                        }}
                      >
                        {!viewMore && (
                          <View style={styles.viewTagWrap}>
                            <View style={[styles.toggleContainer]}>
                              <Text style={styles.viewMoreText}>
                                + {couponList?.length - COUPONS_MAX_LENGTH} MORE
                              </Text>
                            </View>
                          </View>
                        )}
                      </TouchableOpacity>
                      {viewMore && (
                        <View>
                          {this.getCouponList(
                            orderedCouponList?.slice(COUPONS_MAX_LENGTH, orderedCouponList?.length),
                          )}
                        </View>
                      )}

                      <TouchableOpacity
                        onPress={() => {
                          this.setState({ viewMore: false });
                          this.captureClickEvents({ eventName: 'View_Less_Coupons' });
                        }}
                      >
                        {viewMore && (
                          <View style={styles.viewTagWrap}>
                            <View style={[styles.toggleContainer]}>
                              <Text style={styles.viewMoreText}>SHOW LESS</Text>
                            </View>
                          </View>
                        )}
                      </TouchableOpacity>
                    </>
                  )}
                </View>
              </View>
            </TouchableOpacity>
            <View style={styles.couponCardContainer}>
              {dealDetail &&
                isNotNullAndEmptyCollection(dealDetail.categoryDiscountDetails) &&
                dealDetail.categoryDiscountDetails[0].walletAmount > 0 && (
                  <View style={[styles.couponCard]}>
                    <View style={styles.selectIconWrap}>
                      <Image source={iconWallet} style={styles.iconWallet} />
                    </View>
                    <View style={styles.cardDesc}>
                      <View>
                        <Text style={styles.couponDesc}>
                          You can use{' '}
                          <Text style={styles.couponDescBold}>
                            ₹{rupeeFormatter(getWalletAmount(dealDetail, travellerCount))} from your
                            wallet for this booking{' '}
                          </Text>
                        </Text>
                      </View>
                    </View>
                  </View>
                )}
            </View>
            {emiDetails?.emiAmount ? this.emiTag() : []}
            {persuasion ? persuasion : null}
          </ScrollView>
        </Animated.View>
        {emiModal && (
          <BottomSheetOverlay
            title={'EMI Options'}
            toggleModal={() => this.setState({ emiModal: false })}
            visible={emiModal}
            containerStyles={styles.emiOverlay}
            headingContainerStyles={styles.headingContainerStyles}
          >
            <EMIOption
              emiOptions={emiOptions}
              getEmiOptions={getEmiOptions}
              defaultBankName={bankName}
              fromDetails={true}
              closeModal={() => this.setState({ emiModal: false })}
            />
          </BottomSheetOverlay>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 15,
    elevation: 12,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    backgroundColor: holidayColors.white,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
    zIndex: 3,
    position: 'absolute',
    bottom: 0,
    marginBottom: -400,
    width: '100%',
    maxHeight: windowHeight * 0.9,
    shadowOffset: {
      width: 1,
      height: 0,
    },
  },
  emiOverlay: {
    backgroundColor: holidayColors.white,
    padding: 16
  },
  headingContainerStyles: {
    paddingBottom: 10
  },
  closeIconWrapper: {
    right: 20,
    top: 15
  },
  iconCross: {
    height: 22,
    width: 22,
  },
  popupHeader: {
    ...paddingStyles.pt16,
    ...paddingStyles.pb6,
    ...paddingStyles.ph20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  popupHeadingText: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    flex: 1,
  },
  couponDesc: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  couponDescBold: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  cardDesc: {
    flex: 1,
  },
  iconWallet: {
    width: 26,
    height: 26,
    resizeMode: 'cover',
    ...paddingStyles.pr20,
  },
  offerPrice: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    ...AtomicCss.flex1,
  },
  textBoxContainer: {
    ...paddingStyles.pv6,
    backgroundColor: holidayColors.white,
  },
  textBox: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textboxWrap: {
    ...paddingStyles.pa16,
    ...marginStyles.mh16,
  },
  errorTxtBox: {
    ...paddingStyles.pa16,
    borderWidth: 1,
    borderColor: holidayColors.red,
    ...marginStyles.mh16,
    backgroundColor: holidayColors.lightRedBg,
  },
  textBoxWrapClick: {
    ...paddingStyles.pa16,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    ...marginStyles.mh16,
    backgroundColor: holidayColors.lightBlueBg,
  },
  couponTxtbox: {
    color: holidayColors.black,
    ...fontStyles.labelSmallBold,
    borderWidth: 0,
    width: '70%',
    ...paddingStyles.pv0, // To remove default padding from android text input
  },
  selectedText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.green,
  },
  couponTextContainer: {
    top: 15,
    bottom: 15,
    left: 15,
    right: 15,
  },
  applyTextValue: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  applyTxt: {
    opacity: 0.3,
    color: holidayColors.black,
  },
  rediconTick: {
    width: 18,
    height: 18,
    ...marginStyles.mr40,
    resizeMode: 'cover',
    transform: [{ rotate: '180deg' }],
  },
  iconTick: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
  },
  couponCardWrap: {
    // paddingHorizontal: 15,
  },
  alertWrapper: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...paddingStyles.pl20,
    width: '100%',
    ...paddingStyles.pt10,
  },
  invalidText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.red,
  },
  applyText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.green,
  },
  couponCardContainer: {
    alignContent: 'flex-end',
  },
  couponCard: {
    backgroundColor: holidayColors.fadedYellow,
    ...holidayBorderRadius.borderRadius4,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.ph10,
    ...paddingStyles.pt6,
    ...paddingStyles.pb10,
    flexDirection: 'row',
    ...marginStyles.mt10,
  },
  selectIconWrap: {
    bottom: 5,
    ...paddingStyles.ph10,
  },
  emiTag: {
    backgroundColor: holidayColors.fadedYellow,
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pv16,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  iconEmiTag: {
    width: 64,
    height: 22,
    resizeMode: 'contain',
    ...marginStyles.mh4,
  },
  emi: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  emiOptionsText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  emiOptionsLink: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  emiOptionsContainer: {
    ...AtomicCss.marginLeft5,
    flex: 0.9,
  },
  emiOptions: {
    ...AtomicCss.paddingTop5,
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...AtomicCss.flexWrap,
  },
  viewMoreText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
  },
  toggleContainer: {
    ...paddingStyles.pa10,
    ...AtomicCss.flexRow,
    ...AtomicCss.flexWrap,
  },
  viewTagWrap: {
    justifyContent: 'center',
    ...marginStyles.mh10,
    ...marginStyles.mb10,
  },
  viewTagLine: {
    width: '100%',
    borderWidth: 0.5,
    borderColor: 'rgba(155,155,155,0.2)',
    position: 'absolute',
  },
  viewMoreIcon: {
    width: 18,
    height: 18,
  },
  horizontalLoader: {
    width: '100%',
    position: 'absolute',
    height: '100%',
    zIndex: 20,
    elevation: 30,
  },
});

OfferOverlay.propTypes = {
  togglePopup: PropTypes.func.isRequired,
  loggedIn: PropTypes.bool.isRequired,
  onLoginClicked: PropTypes.func.isRequired,
  travellerCount: PropTypes.number.isRequired,
  categoryPrice: PropTypes.object.isRequired,
  aff: PropTypes.string,
  dealDetail: PropTypes.object,
};

OfferOverlay.defaultProps = {
  dealDetail: null,
  aff: null,
};
export default OfferOverlay;
