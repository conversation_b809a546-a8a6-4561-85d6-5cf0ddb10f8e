import React from 'react';
import {View, StatusBar, TouchableOpacity, BackHandler, Animated, Easing, Image, Platform} from 'react-native';
import TimeBar from './TimeBar';
import InfoSection from './InfoSection';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {storyTypes} from '../StoryConstants';
import {isNonEmpty} from '@mmt/legacy-commons/Common/utils/StringUtils';
import ThemePage from './ThemePage';
import PackageSummaryPage from './PackageSummaryPage';
import HostPage from './HostPage';
import TripHighlights from './TripHighlights';
import ItineraryPage from './ItineraryPage';
import {
  StoryPage,
  ThemeCss,
  PackageSummaryCss,
  HostCss,
  TripHighLightsCss,
  BottomBarCss,
  ItineraryPageCss,
  Ip_BackgroundColor_1,
  Ip_HighlightColor_1,
  Ip_TipBackgroundColor_1,
  Ip_BackgroundColor_3,
  Ip_HighlightColor_3,
  Ip_TipBackgroundColor_3,
  Ip_BackgroundColor_2,
  Ip_HighlightColor_2,
  Ip_TipBackgroundColor_2,
  timeBar,
} from './StoryCss';
import {
  createStoryChangeStringForTracking,
  updateStoryStatus,
} from '../StoryUtils';
import {animateOverlay} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import {OVERLAY_ANIMATE_DELAY, OVERLAY_ANIMATE_DURATION, OVERLAY_FULL_BOTTOM} from '../../PhoenixDetail/DetailConstants';
import {isIosClient} from "../../utils/HolidayUtils";
import withBackHandler from '../../hooks/withBackHandler';


class HolidayStoryPage extends BasePage {
  constructor(props) {
    super(props);
    this.state = {
      active: 0,
      overlayPosition: new Animated.Value(0),
    };
    this.inTime = 0;
    this.outTime = 0;
    this.storyPageCount = null;
    this.maxPageIndex = 0;
    this.StoryPages = [];
    this.closePage = false;
  }

  onBackClick = ()=> {
    return this.onBackPressed();
  }

  componentDidMount() {
    super.componentDidMount();
    StatusBar.setHidden(true);
    animateOverlay(this, OVERLAY_FULL_BOTTOM);
  }

  startAnimate(bottom, duration, delay, callback = () => {}) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    }).start(callback);
  }

  componentWillUnmount() {
    StatusBar.setHidden(false);
  }

  // Prev Page
  changeLeft = () => {
    if (this.state.active === 0) {
      return;
    }
    this.props.captureClickEvents({
      eventName: createStoryChangeStringForTracking(this.StoryPages, this.state.active - 1),
    })
    this.setState({active: this.state.active - 1});
  }

  // Next Page
  changeRight = () => {
    if (this.state.active === this.storyPageCount - 1) {
      if (!this.closePage) {
        this.onBackPressed('load_details');
        this.closePage = true;
      }
      return;
    }
    const nextPage = this.state.active + 1;
    this.maxPageIndex = this.maxPageIndex < nextPage ? nextPage : this.maxPageIndex;
    this.props.captureClickEvents({
      eventName: createStoryChangeStringForTracking(this.StoryPages, nextPage),
    })
    this.setState({active: nextPage});
  }

  handlePressIn = () => {
    this.inTime = Date.now();
  }

  handlePressOut = (side) => {
    this.outTime = Date.now();
    const diff = this.outTime - this.inTime;
    if (diff <= 150) {
      if (side === 'prev') {
        this.changeLeft();
      } else {
        this.changeRight();
      }
    }
  }

  onBackPressed = (eventName = 'back') => {
    this.props.trackLocalClickEvent(eventName, '');
    updateStoryStatus(parseInt(this.props.packageId), this.maxPageIndex + 1, this.storyPageCount);
    this.startAnimate(
      0,
      OVERLAY_ANIMATE_DURATION,
      OVERLAY_ANIMATE_DELAY,
      () => this.props.toggleStoryPage()
    );
    return true;
  }

  renderStories = (item, index) => {
    if (this.state.active !== index) {
      return [];
    }
    if (isNonEmpty(item.sectionType)) {
      return this.getPackageStory(item, index);
    } else if (Number.isInteger(item.day)) {
      return this.getItineraryStory(item, index);
    }
  }

  getItineraryStory = (item, index) => {
    switch (item.day % 3) {
      case storyTypes.ITINERARY_1: {
        return (
          <ItineraryPage
            data={item}
            styles={ItineraryPageCss}
            backgroundColor={Ip_BackgroundColor_1}
            textHighlightColor={Ip_HighlightColor_1}
            tipBackgroundColor={Ip_TipBackgroundColor_1}
          />
        );
      }
      case storyTypes.ITINERARY_2: {
        return (
          <ItineraryPage
            data={item}
            styles={ItineraryPageCss}
            backgroundColor={Ip_BackgroundColor_2}
            textHighlightColor={Ip_HighlightColor_2}
            tipBackgroundColor={Ip_TipBackgroundColor_2}
          />
        );
      }
      case storyTypes.ITINERARY_3: {
        return (
          <ItineraryPage
            data={item}
            styles={ItineraryPageCss}
            backgroundColor={Ip_BackgroundColor_3}
            textHighlightColor={Ip_HighlightColor_3}
            tipBackgroundColor={Ip_TipBackgroundColor_3}
          />
        );
      }
      default: return [];
    }
  }

  getPackageStory = (item, index) => {
    switch (item.sectionType) {
      case storyTypes.THEME:
        return (
          <ThemePage data={item} styles={ThemeCss} />
        );
      case storyTypes.PACKAGE_SUMMARY:
        return (
          <PackageSummaryPage data={item} styles={PackageSummaryCss} />
        );
      case storyTypes.HOST:
        return (
          <HostPage data={item} styles={HostCss} />
        );
      case storyTypes.TRIP_HIGHLIGHT:
        return (
          <TripHighlights data={item} styles={TripHighLightsCss} />
        );
      default: return [];
    }
  }

  render() {
    this.StoryPages = [];
    if (this.props.storyData.packageSections && this.props.storyData.packageSections.length > 0) {
      this.StoryPages = this.StoryPages.concat(this.props.storyData.packageSections);
    }
    if (this.props.storyData.dayWiseItinerary && this.props.storyData.dayWiseItinerary.length > 0) {
      this.StoryPages = this.StoryPages.concat(this.props.storyData.dayWiseItinerary);
    }
    this.storyPageCount = this.StoryPages.length;
    if (this.StoryPages.length > 0) {
      return (
        <View style={StoryPage.container}>
          <Animated.View style={[StoryPage.overlayContent, {bottom: this.state.overlayPosition}]}>
            {isIosClient() &&
              <TouchableOpacity style={StoryPage.close} onPress={() => this.onBackPressed('back')}>
                <Image style={StoryPage.closeIcon} source={require('@mmt/legacy-assets/src/ic_close_black.webp')} />
              </TouchableOpacity>}
            <View style={StoryPage.barWrapper}>
              {this.StoryPages.map((item, index) => (
                <TimeBar
                  key={`${index}`}
                  active={this.state.active}
                  index={index}
                  styles={timeBar}
                />
              ))}
            </View>
            {this.StoryPages.map((item, index) => this.renderStories(item, index))}
            <View style={StoryPage.bookNowWrapper}>
              <InfoSection
                packageName={this.props.storyData.packageDescription}
                numberOfNights={this.props.storyData.numberOfNights}
                styles={BottomBarCss}
                onPress={() => this.onBackPressed('view_details')}
              />
            </View>
            <View style={StoryPage.tapChangeContainer}>
              <TouchableOpacity
                onPressOut={() => this.handlePressOut('prev')}
                onPressIn={this.handlePressIn}
                style={StoryPage.leftTapChange}
                activeOpacity={1}
              />
              <TouchableOpacity
                onPressOut={() => this.handlePressOut('next')}
                onPressIn={this.handlePressIn}
                style={StoryPage.rightTapChange}
                activeOpacity={1}
              />
            </View>
          </Animated.View>
        </View>
      );
    }
    return [];
  }
}
export default withBackHandler(HolidayStoryPage);
