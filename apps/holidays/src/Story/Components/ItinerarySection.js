import React from 'react';
import {Text, View} from 'react-native';
import PropTypes from 'prop-types';
import entities from 'entities';
import HTMLView from '../../Common/Components/HTML';
import {extraInfo} from '../StoryConstants';
import {descriptionStyles} from './StoryCss';

// Tabs design for Day wise itinerary and inclusions
// <View style={styles.tabWrapper}>
//   {data.tabs.map((item, index) => (
//       <TouchableOpacity style={styles.tabs}>
//         <Text style={styles.tabsTxt}>{item} </Text>
//       </TouchableOpacity>
//   ))}
// </View>
// <View style={styles.foodInformationContainer}>
//   <Image style={styles.iconStyle} source={breakFastIcon} />
//   <Text style={styles.foodInformationText}>BREAKFAST, LUNCH & DINNER INCLUDED</Text>
// </View>

const ItinerarySection = ({
  data, styles, textHighlightColor, tipBackgroundColor,
}) => {
  const TipSection = ({additionalInfo}) => {
    if (additionalInfo) {
      const key = Object.keys(additionalInfo)[0];
      const value = additionalInfo[key];
      if (value) {
        return (
          <View style={[styles.tipsWrapper, {backgroundColor: tipBackgroundColor}]}>
            <Text numberOfLines={2} style={styles.tipInfoTxt}>
              <Text style={styles.tipInfoTxtheading}>{extraInfo[key]} : </Text>
              {value}
            </Text>
          </View>
        );
      }
    }
    return [];
  };

  const renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.type === 'tag') {
      if (node.name === 'ul') {
        return (
          <View style={styles.moreInfoList}>
            {defaultRenderer(node.children, parent)}
          </View>
        );
      } else if (node.name === 'li') {
        return (
          <View style={styles.listItemContainer}>
            <View style={styles.bullet} />
            <Text style={styles.moreInfoListText}>{defaultRenderer(node.children, parent)}</Text>
          </View>
        );
      } else if (node.name === 'b') {
        return (
          <Text style={[styles.boldFont, {backgroundColor: textHighlightColor}]}>
            {defaultRenderer(node.children, parent)}
          </Text>
        );
      }
    } else if (node.type === 'text') {
      return entities.decodeHTML(node.data);
    }
    return undefined;
  };

  return (
    <View>
      <View style={styles.headingContainer}>
        <Text style={styles.heading}> {data.heading}</Text>
        <View style={[styles.shape, {backgroundColor: textHighlightColor}]} />
      </View>
        {!!data.description &&
          <HTMLView
            value={data.description.replace(/\r?\n|\r/g, '')}
            stylesheet={descriptionStyles}
            renderNode={renderNode}
          />}
        {data.additionalInfo && <TipSection additionalInfo={data.additionalInfo} />}
    </View>
  );
};

ItinerarySection.propTypes = {
  data: PropTypes.object.isRequired,
  styles: PropTypes.object.isRequired,
  textHighlightColor: PropTypes.string.isRequired,
  tipBackgroundColor: PropTypes.string.isRequired,
};

export default ItinerarySection;
