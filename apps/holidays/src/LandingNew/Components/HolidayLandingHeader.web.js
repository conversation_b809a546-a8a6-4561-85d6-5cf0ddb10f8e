import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Text } from 'react-native';
import { isGIAffiliate, isNonMMTAffiliate } from '../../utils/HolidayUtils';
import { getCitySearchType } from '../Utils/DestinationDepartureCityUtils';
import HolidayLandingStickyHeader from '../Components/HolidayLandingStickyHeader';
import { connect } from 'react-redux';
import { fetchLandingData, fetchLandingDataFromStorage } from '../Actions/HolidayLandingActions';
import styles from '../Components/HolidayLandingCss';
import PoweredByMMT from '../../Common/Components/PoweredByMMT';
import { updateDepCity } from '../../SearchWidget/Actions/HolidaySearchWidgetActions';
import searchIcon from '@mmt/legacy-assets/src/search_black_icon.webp'
import DownloadApp from '../../Common/Components/DownloadApp';
// const searchIcon = require('packages/legacy-assets/src/search_black_icon.webp');

function HolidayLandingHeader(props) {
  const {
    showFromCityPopup,
    onBackClick,
    userDepCity,
    updateDepCity,
    availableHubs,
    label,
    openDepartureCity,
    onCitySelect,
    closeCitySelection,
    onHeaderGoingToClicked,
    holidayLandingData,
    trackClickEvent,
    isSearchFilter,
    isHeaderHide,
    onLogoutClick,
  } = props || {};
  const refreshContent = (userDepCity) => {
    props.fetchLandingDataFromStorage(false);
    props.fetchLandingData({ ...holidayLandingData, userDepCity });
  };
  const onHeaderClicked = (e) => {
    e.preventDefault();
    onHeaderGoingToClicked();
  };
  const showBackBtn = !isNonMMTAffiliate(holidayLandingData.aff);
  const headerTitle = !isSearchFilter?.searchV2 ? 'Starting From' : 'Holiday Packages';
  const headerUserDepCity = !isSearchFilter?.searchV2 ? userDepCity : 'India and International';
  const showSearchContainer = !showFromCityPopup && !isSearchFilter?.searchV2;

  return (
    <>
      <DownloadApp />
      {!isHeaderHide &&             //only for gi mweb
        <HolidayLandingStickyHeader
          showBackBtn={showBackBtn}
          onBackPressed={onBackClick}
          title={headerTitle}
          userDepCity={headerUserDepCity}
          showLocationPopUp={showFromCityPopup}
          landingDataRefreshed={refreshContent}
          updateDepCity={updateDepCity}
          availableHubs={availableHubs}
          trackClickEvent={trackClickEvent}
          isSearchFilter={isSearchFilter?.searchV2}
          label={label}
          openDepartureCity={openDepartureCity}
          hideDepartureCity={closeCitySelection}
          // isSearchFilter={isSearchFilter}
          onLogoutClick={onLogoutClick}
          isAffiliate={isNonMMTAffiliate(holidayLandingData.aff)}
        />
      }
      {showSearchContainer && (
        <View style={styles.searchContainer}>
          <TouchableOpacity
            activeOpacity={0.9}
            style={styles.searchField}
            onPress={onHeaderClicked}
          >
            <Image style={styles.iconSearch} source={searchIcon} />
            <Text style={styles.searchText}>Search Destinations</Text>
          </TouchableOpacity>
        </View>
      )}

      {/*{isNonMMTAffiliate(holidayLandingData.aff) && (
        <View style={styles.poweredByMmtHeader}>
          <PoweredByMMT />
        </View>
      )}*/}
    </>
  );
}
const mapDispatchToProps = (dispatch) => ({
  updateDepCity: (depCity) => dispatch(updateDepCity(depCity)),
  fetchLandingDataFromStorage: (isWG) => dispatch(fetchLandingDataFromStorage(isWG)),
  fetchLandingData: (holidayLandingData, wgDepCity) =>
    dispatch(fetchLandingData(holidayLandingData, '')),
});
const mapStateToProps = (state) => ({
  ...state.holidaysLanding,
});
export default connect(mapStateToProps, mapDispatchToProps)(HolidayLandingHeader);
