import React from 'react';
import URL from 'url';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeviceEventEmitter,
  Modal,
  NativeModules,
  Platform,
  SafeAreaView,
  View,
} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import orderBy from 'lodash/orderBy';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import styles from './HolidayLandingCss';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import {
  DATE_WITH_DAY_FORMAT,
  DEFAULT_PAGE_NAME,
  HARDWARE_BACK_PRESS,
  TO,
} from '../../SearchWidget/SearchWidgetConstants';
import {
  getPokusConfigWaitingPromise,
  initAbConfigUsingPokusHLD,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import {
  getAPWindow,
  getDepartureCity,
  getNearByCityDataForPdt,
  isMobileClient,
  isRawClient,
  saveAffRefId,
  saveHolMeta,
  isGIAffiliate,
  updateDeviceType,
  getPaxDetails,
  getSearchTravelDate,
  isIosClient,
  isAppPlatform,
  isNonMMTAffiliate,
  isLuxeFunnel,
} from '../../utils/HolidayUtils';
import {
  createRoomDataFromRoomDetailsPhoenix,
  createRoomDetailsFromRoomDataForPhoenix,
  getPaxCount,
  getPaxCountInfoForTotalRooms,
  getRoomDataForPdt,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import {
  AFFILIATES,
  FUNNEL_ENTRY_TYPES,
  HLD_PAGE_NAME,
  PARAM_AFFILIATE,
  PARAM_BANNER,
  PARAM_CMP,
  PARAM_DEP_CITY,
  PARAM_PAGE_TYPE,
  PARAM_REFERRAL_LINK,
  PDT_PAGE_ENTRY_EVENT,
  REQUEST_LOB,
  USER_DESTINATION_DEFAULT_CITY,
  WEEKEND_GETAWAY_PAGE_TYPE,
  PARAM_SOURCE,
} from '../../HolidayConstants';
import fecha from 'fecha';
import SearchWidget from '../../SearchWidget/Components/HolidaySearchWidgetPage';
import { closeQueryByTicketId, getParamFromQuery } from '../../Query/Utils/HolidayQueryUtils';
import {
  getDataFromStorage,
  KEY_LANDING_META_DATA_HOLIDAY,
  KEY_LANDING_META_DATA_HOLIDAY_LUX,
  KEY_USER_CITY_LOCUS_ID,
  KEY_USER_CITY_SEARCH_TYPE,
  KEY_USER_CITY_SELECTION_TYPE,
  KEY_USER_DEP_CITY,
  KEY_USER_DEST_CITY,
  KEY_USER_ROOMS_CONFIG,
  KEY_USER_TRAVEL_DATE,
  removeDataFromStorage,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { LANDING_PAGE_NAME, LANDING_TRACKING_PAGE_NAME } from '../LandingConstants';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { DEFAULT_TRAVELLER_COUNT , PDTConstants} from '../../PhoenixDetail/DetailConstants';
import { fetchQueueIdentifier, getLandingMetaData } from '../../utils/HolidayNetworkUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import { showShortToast } from '@mmt/core/helpers/toast';
import withIntervention from '../../Common/Components/Interventions/withIntervention';
import { isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { getDateObject } from '../../Grouping/Components/ModifySearch/DateUtil';
import { fillDateAndTime } from '../../Common/HolidaysCommonUtils';
import {
  HOLIDAYS_BRANCH_NONE,
  trackDeeplinkRececived,
  trackSearchWidgetV2Landing,
} from '../../utils/HolidayTrackingUtils';
import { checkForLocationPermission } from '@mmt/legacy-commons/Helpers/locationHelper';
import { clearFiltersOptionsCache } from '../../SearchWidget/Components/PhoenixSearchPage/withFilterOptions';
import { getCityCampaignDisplayName } from '../Utils/DestinationDepartureCityUtils';
import {
  handleLocationPopUp,
  updateCityWithoutShowingPopup,
} from '../../Common/GeoLoc/GeoLocationUtils';
import BranchIOTracker from '../../utils/HolidayBranchSDKEventTracker';
import HolidayLandingHeader from './HolidayLandingHeader';
import HolidayLandingPopups from './HolidayLandingPopups/index';
import HolidayLandingContent from './HolidayLandingContent';
import {
  checkMenuTabsData,
  getFormattedTravelStartDate,
  handleDeeplink,
  trackLocalClickEvent,
  trackLocalErrorClickEvent,
} from '../Utils/HolidayLandingUtilsV2';
import { popupEnum } from './HolidayLandingPopups/PopupConstants';
import {
  getEnableGeoLoc,
  getIsSearchFilter,
  getShowWGBannerLandingHol,
  getTpPostSalesQueryNumHol,
  getPokusForMandatoryPaxDate,
  getLandingSupresedSections,
  getMyraChatIconPokus,
  removeLastPage,
  showNewRoomAndGuest,
  showFabAnimationExtended,
  showHolAgentOnLandingAndListingPage,
} from '../../utils/HolidaysPokusUtils';
import ProgressIndicator from './HolidayLandingProgressView';
import { holidayColors } from '../../Styles/holidayColors';
import {
  clearPageVisitResponses,
  sectionTrackingPageNames,
  setIsImageSearchFromBanner,
  isFromImageSearchBanner,
  formatSectionsDataForPDTLanding
} from '../../utils/SectionVisitTracking';
import HolidayFabAnimationContainer from '../../Common/Components/Widgets/FabAnimation/FabAnimationContainer';
import { createLandingFabData } from '../../Common/Components/Widgets/FabAnimation/FabAnimationUtils';
import HolidayDeeplinkParser from '../../utils/HolidayDeeplinkParser';
import {
  createChildAgeArrayFromApi,
  roomDefault,
  roomDefaultWithZeroAdults,
} from '../../utils/RoomPaxUtils';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import {
  initLandingPdtObj,
  logHolidaysLandingPDTEvents,
  setDefaultDestinationData,
  setSearchContextData,
  trackPageExit
} from '../Utils/HolidayLandingPdtTrackingUtils';
import { getLandingPDTObj, updateLandingPDTObj } from '../Utils/HolidayLandingPDTDataHolder';
import {  updatePDTJourneyId } from '../../utils/PdtDataHolder';
import { EVENT_NAMES, PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { getCityData } from '../../SearchWidget/utils/cityRepository';
import { updatePDTJourneyIdOnSearchChange } from '../../utils/HolidayPDTTrackingV3';
import {
  getPrefilledSearchModalValues,
  handleMmtBlackClickEvents,
} from '../Utils/HolidayLandingUtils';
import { PDT_PAGE_LOAD, PDT_PAGE_EXIT_EVENT } from '../../HolidayConstants';
import HolidayPDTMetaIDHolder from '../../utils/HolidayPDTMetaIDHolder';
import withBackHandler from '../../hooks/withBackHandler';
import {formatDataForGi} from "../../utils/GiTrackingUtils";
import { sendMMTGtmEvent } from '../../utils/ThirdPartyUtils';

const EVENT_LANDING_REFRESH = 'eventLandingRefresh';
const ENGLISH = 'eng';
export const LOGIN_EVENT_LANDING = 'login_event_landing';
export const GEO_LOC_AUTO_UPDATE_MESSAGE = ' city updated as per your location';

class HolidayLandingNew extends BasePage {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props, 'landing');
    PerformanceMonitorModule.start('PhoenixLandingPage');
    HolidayDataHolder.getInstance().setCurrentPage('holidaysLanding');
    this.state = {
      loaderText: 'Making your trip...',
      showWidget: '',
      popupVisible: '',
      showGoingTo: false,
      fixed: false,
      showPostSalesQryStrip: true,
      showPromoSection: true,
      showCovidBanner: false,
      popup: '',
      active: null,
      fabTextShrinked: false,
      deviceType:null,
      isHindi: false,
      openEditModal: true,
      showEditModalSummarisedView: false,
      metaData: {},
      quickFilter: null,
      filters: [],
      isFilterCTAExpanded: true,
      selectedQuickFilterIndex: 0,
      destinationCity: '',
      paxDetails: {},
      selectCityPopupData: {},
      searchFilterModalError: false,
      fromImageSearch: false,
      mmtBlackBottomSheetDetails: {},
    };
    this.searchFilterRef = React.createRef();
    getDataFromStorage(KEY_USER_CITY_SEARCH_TYPE).then((d) =>
      this.setState({ selectedCityType: d ? d : 'Airport' }),
    );
    getDataFromStorage(KEY_USER_CITY_SELECTION_TYPE).then((d) =>
      this.setState({ citySelectionType: d ? d : 'Auto-Detect' }),
    );
    this.isDefaultRoomDetail = true;
    this.defaultRoom = true;
    const metadataStorageKey = isLuxeFunnel() ? KEY_LANDING_META_DATA_HOLIDAY_LUX : KEY_LANDING_META_DATA_HOLIDAY
    getDataFromStorage(metadataStorageKey).then(async (initialMetaData) => {
      let date = await getDataFromStorage(KEY_USER_TRAVEL_DATE);
      date = getFormattedTravelStartDate(date);
      this.setState({
        metaData: initialMetaData,
        fromDateObj: getDateObject(getSearchTravelDate(date, initialMetaData)),
      });
    });

    this.roomDetails = [roomDefault];
    this.childAgeArray = createChildAgeArrayFromApi(this.roomDetails);

    getDataFromStorage(KEY_USER_ROOMS_CONFIG).then((roomConfig) => {
      this.defaultRoom = isEmpty(roomConfig);
      this.isDefaultRoomDetail = isEmpty(roomConfig);
      this.roomDetails = !isEmpty(roomConfig) ? roomConfig : [roomDefault];
      this.childAgeArray = createChildAgeArrayFromApi(this.roomDetails);
    });

    this.landingSectionsVisited = {};
    this.pdtObj = {};
    // this.isDefaultRoomDetail = true;
    // this.roomDetails = [roomDefault];
    // this.childAgeArray = [];

    this.requestInProgress = false;
    if (this.props.holidaysLandingData) {
      this.holidayLandingData = JSON.parse(this.props.holidaysLandingData);
      this.holidayLandingData.cmp = getParamFromQuery(this.holidayLandingData.query, PARAM_CMP);
      this.holidayLandingData.pt = getParamFromQuery(
        this.holidayLandingData.query,
        PARAM_PAGE_TYPE,
      );
      this.holidayLandingData.referralCode = getParamFromQuery(
        this.holidayLandingData.query,
        PARAM_REFERRAL_LINK,
      );
      this.holidayLandingData.aff = getParamFromQuery(
        this.holidayLandingData.query,
        PARAM_AFFILIATE,
      );
      this.holidayLandingData.depCity = getParamFromQuery(
        this.holidayLandingData.query,
        PARAM_DEP_CITY,
      );
      this.holidayLandingData.banner = getParamFromQuery(
        this.holidayLandingData.query,
        PARAM_BANNER,
      );
      this.holidayLandingData.source=getParamFromQuery(
        this.holidayLandingData.query,
        PARAM_SOURCE
      );
      if (this.holidayLandingData.depCity) {
        setDataInStorage(KEY_USER_DEP_CITY, this.holidayLandingData.depCity);
      }
      if (this.holidayLandingData.cmp) {
        trackDeeplinkRececived({ [TRACKING_EVENTS.M_V81]: this.holidayLandingData.cmp });
      }
    } else {
      this.holidayLandingData = {};
    }
    this.destinationCity = USER_DESTINATION_DEFAULT_CITY;

    this.destinationCityData = {
      destinationCity: USER_DESTINATION_DEFAULT_CITY,
      displayName: USER_DESTINATION_DEFAULT_CITY,
    };
    if (isEmpty(this.holidayLandingData.cmp) && !isEmpty(this.props.campaign)) {
      this.holidayLandingData.cmp = this.props.campaign;
    }
    HolidayDataHolder.getInstance().setCmp(this.holidayLandingData.cmp);
    saveHolMeta(this.holidayLandingData.isWG, this.holidayLandingData.aff, '',  this.holidayLandingData.cmp);
    saveAffRefId(this.holidayLandingData.affRefId);
    HolidayDataHolder.getInstance().setCampaign(this.props.campaign);
    HolidayDataHolder.getInstance().setSource(this.holidayLandingData.source);
    if (this.holidayLandingData.banner) {
      HolidayDataHolder.getInstance().setBanner(this.holidayLandingData.banner);
    }

    this.landingResponse = null;
    this.headerMenu = [];
    this.showWGBannerLandingHol = false;
    this.affPostSalesQueryNumHol = '';
    this.holidayLandingData.isWG = this.holidayLandingData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    this.holidayLandingData.affRefId = null;
    this.travelPlexAttr4 = null;
    this.isLoggedInUser = -1;
    this.isGdprRegion = false;
    if (isRawClient()) {
      const urlObj = URL.parse(window.location.href, window.location.search);
      this.holidayLandingData.aff = HolidayDeeplinkParser.getAffiliateFromUrl(window.location.href);
      const { query } = urlObj;
      if (query) {
        if (query.pt) {
          this.holidayLandingData.pt = query.pt;
          this.holidayLandingData.isWG = query.pt === WEEKEND_GETAWAY_PAGE_TYPE;
        }
        if (query.cmp) {
          this.holidayLandingData.cmp = query.cmp;
          trackLocalClickEvent({ omniData: { m_v81: query.cmp } });
        }
        if (query.affRefId !== undefined && query.affRefId !== null) {
          this.holidayLandingData.affRefId = query.affRefId;
        }
        this.holidayLandingData.depCity = query.depCity;
        if (this.holidayLandingData.depCity) {
          setDataInStorage(KEY_USER_DEP_CITY, this.holidayLandingData.depCity);
        }
        if (query.banner !== undefined && query.banner !== null) {
          this.holidayLandingData.banner = query.banner;
        }
      }
    }
    if (this.holidayLandingData.banner) {HolidayDataHolder.getInstance().setBanner(this.holidayLandingData.banner);}
    saveHolMeta(
      this.holidayLandingData.isWG,
      this.holidayLandingData.aff,
      '',
      this.holidayLandingData.cmp,
    );
    saveAffRefId(this.holidayLandingData.affRefId);

    this.showFabFromDeeplink = false;
  }
  trackClickEventLocal = (
    eventName = '',
    eventNameOmni = '',
    prop1 = '',
    pdtExtraData = {},
    { omniData = {} } = {},
  ) => {
    this.trackLocalClickEventWithPax({
      eventName,
      eventNameOmni,
      prop1,
      pdtExtraData,
      omniData,
    });
  };

  getDefaultRoomDetails = (roomConfig) => {
    return !isEmpty(roomConfig)
      ? roomConfig
      : showNewRoomAndGuest()
      ? [roomDefaultWithZeroAdults]
      : [roomDefault];
  };

  getDefaultChildAgeArray = ({ roomConfig, roomDetails }) => {
    return !isEmpty(roomConfig) ? createChildAgeArrayFromApi(roomDetails) : [];
  };

  async getDeviceType(query) {
    let device= HolidayDeeplinkParser.getDeviceType(query);
    let deviceType = await updateDeviceType(device);
    this.setState({deviceType:deviceType});
  }
  async componentDidMount() {
    super.componentDidMount();
    await HolidayDataHolder.getInstance().setCurrentPageNameV2('holidaysLanding')
    await this.initAb();
    await HolidayDataHolder.getInstance().setSubFunnel();
    await HolidayPDTMetaIDHolder.getInstance().setPdtId();
    HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.ONLINE);
    initLandingPdtObj();
    const prefilledValue = await getPrefilledSearchModalValues();
    trackLocalClickEvent({ eventName: 'search_field_prefilled', prop1: prefilledValue });
    this.pdtObj.preSelectedCity = this.props.userDepCity;
    this.pdtObj.locationPermission = (await checkForLocationPermission()) ? 'ON' : 'OFF';

    const cityData = await getDataFromStorage(KEY_USER_DEST_CITY);
    if (cityData) {
      this.destinationCityData = cityData;
      this.destinationCity = cityData.destinationCity;
      this.holidayLandingData.destinationCity=cityData.destinationCity
    }else{
      this.holidayLandingData.destinationCity=USER_DESTINATION_DEFAULT_CITY
    }
    if (showNewRoomAndGuest() && this.defaultRoom) {
      const roomConfig = await getDataFromStorage(KEY_USER_ROOMS_CONFIG);
      this.roomDetails = this.getDefaultRoomDetails(roomConfig);
      this.childAgeArray = this.getDefaultChildAgeArray({
        roomConfig,
        roomDetails: this.roomDetails,
      });
    }

    this.setState({
      paxDetails: {
        ...getPaxDetails({ roomDetails: this.roomDetails }),
        roomData: createRoomDataFromRoomDetailsPhoenix(this.roomDetails, this.childAgeArray, true),
      },
    });

    const { NetworkModule, UserSessionModule } = NativeModules || {};
    const config = await NetworkModule?.getAppCurrentConfig?.();
    this.isGdprRegion = await UserSessionModule?.needConsent?.();
    const { language: lang } = config || {};
    const isHindiLang = !isEmpty(lang) ? lang !== ENGLISH : false;
    this.setState({ isHindi: isHindiLang });
    this.userDepCity = await getDepartureCity();
    const isWG = this.checkIfWG();
    this.props.fetchLandingDataFromStorage(isWG);
   this.landingDataForPDT = await this.props.fetchLandingData(
      this.holidayLandingData,
      isWG && !isEmpty(this.holidayLandingData.depCity) ? this.holidayLandingData.depCity : '',this.getGiTrackingData()
    );
    const urlObj = URL.parse(window.location.href, window.location.search);
    const { query } = urlObj;

    await this.getDeviceType(query);
    if(!isEmpty(this.landingDataForPDT) && (this.landingDataForPDT?.sections || []).length > 0){
    this.updateSectionsInPDTHolder(this.landingDataForPDT.sections);

    }
    this.props.fetchMenuData();
    if (!isWG) {
      this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
    }
    let date = await getDataFromStorage(KEY_USER_TRAVEL_DATE);
    if (date) {
      date = fecha.format(fecha.parse(date, DATE_WITH_DAY_FORMAT), 'YYYY-MM-DD');
    }
    setDefaultDestinationData();
    const searchCriteria = `${this.userDepCity}_${
      this.destinationCity
    }_${date}_${getPaxCountInfoForTotalRooms(this.roomDetails)}_${this.roomDetails.length}`;
    updatePDTJourneyId({ searchCriteria });
    this.props.getSearchHistory();
    this.props.fetchRecentlySeenPackages();
    this.eventLandingRefreshListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(EVENT_LANDING_REFRESH, this.landingDataRefreshed);
    this.eventLandingLoginListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(LOGIN_EVENT_LANDING, this.onLoginEventReceived);
    BranchIOTracker.trackPageView({
      pageName: BranchIOTracker.PAGE.LANDING,
      [BranchIOTracker.KEYS.EXTRA_DATA]: {},
    });

    const loginValue = await isUserLoggedIn();
    const login = loginValue ? 1 : 0;

    if (this.isLoggedInUser !== -1 && this.isLoggedInUser !== login) {
      this.refreshContent(this.props.userDepCity);
    }

    if (!login && this.holidayLandingData.referralCode) {
      const { HolidayModule } = NativeModules;
      HolidayModule.onLoginUser();
    }
    this.isLoggedInUser = login;
    this.handleLocationPermission();

    if (isMobileClient()){
      logHolidaysLandingPDTEvents({
         actionType: PDT_EVENT_TYPES.pageEntry,
         value: PDT_PAGE_ENTRY_EVENT,
         shouldTrackToAdobe:false
       });
    this.focusListener = HolidayNavigation.getNavigationObj().addListener('focus', () => {
       HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.ONLINE)
      if (this.state.refreshLandingForEditQuery) {
          this.refreshContent(this.props.userDepCity);
          this.setState({ refreshLandingForEditQuery: false });
        }
      });
    }

    this.blurListener = HolidayNavigation.getNavigationObj().addListener('blur', () => {
      trackPageExit(this.props.landingData, PDT_PAGE_EXIT_EVENT);
    });

    // Fetch attr4 data for TravelPlex
    try {
      this.travelPlexAttr4 = await fetchQueueIdentifier('landing');
    } catch (error) {
      console.log('Error fetching TravelPlex attr4 data:', error);
    }

    await this.getMetaData();
  }
  getGiTrackingData = () => {
    const obj = {
      fromCity: this.userDepCity,
      toCity: getCityCampaignDisplayName(this.destinationCityData),
      paxSearched: getPaxCountInfoForTotalRooms(this.roomDetails),
      dateOfSearch: this.state?.fromDateObj?.selectedDate
    }
    return formatDataForGi(obj)
  }

  getMetaData = async () => {
    const metaRequestObject = await this.getLandingMetadataObject();
    const metaData = await getLandingMetaData(metaRequestObject);
    this.masterMetaResponse = null;
    clearFiltersOptionsCache();
    let date = await getDataFromStorage(KEY_USER_TRAVEL_DATE);
    if (date) {
      date = fecha.parse(date, DATE_WITH_DAY_FORMAT);
    }
    const metadataStorageKey = isLuxeFunnel() ? KEY_LANDING_META_DATA_HOLIDAY_LUX : KEY_LANDING_META_DATA_HOLIDAY
    setDataInStorage(metadataStorageKey, metaData);
    this.setState({
      metaData: metaData,
      fromDateObj: getDateObject(getSearchTravelDate(date, metaData)),
    });
  };

  handleFilterClick = (item, index) => {
    this.filterClick(item, index);
  };

  getLandingMetadataObject = async () => {
    const { campaign } = this.destinationCityData || {};
    const city = this.userDepCity ? this.userDepCity : await getDepartureCity();
    const locus = await getDataFromStorage(KEY_USER_CITY_LOCUS_ID);
    const locusCode = locus || 'CTDEL';

    return {
      departure: { text: city, locusCode },
      ...(this.destinationCity && { destination: { text: this.destinationCity } }),
      ...(campaign && { campaign }),
    };
  };

  filterClick = (quickFilter, index) => {
    this.trackClickEventLocal(quickFilter ? 'filter_quick_' + quickFilter?.urlParam : 'filter');
    this.setState(
      {
        quickFilter,
        fixed: false,
        isFilterCTAExpanded: false,
        selectedQuickFilterIndex: index || this.state.selectedQuickFilterIndex,
      },
      () => {
        this.setWidgetState(popupEnum.showFilterWidget, true);
        logHolidaysLandingPDTEvents({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          value: quickFilter ? 'quick_filter|' + quickFilter?.urlParam : 'filter',
        });
      },
    );
    HolidayDataHolder.getInstance().setCurrentPage('holidaysSearchWidget');
  };

  trackUpdateCityWithoutShowingPopup = (cityName) => {
    if (!isEmpty(cityName)) {
      showShortToast(cityName + GEO_LOC_AUTO_UPDATE_MESSAGE);
      this.trackClickEventLocal('From City_auto update');
    }
  };

  handleLocationPermission = async () => {
    const enableNewGeoLocation = getEnableGeoLoc();
    //Return if geo loc feature is not enabled.
    if (!enableNewGeoLocation) {
      return;
    }

    const popUpData = await handleLocationPopUp();
    const { showPopup, nearByCities, city, lat, lng } = popUpData || {};

    this.pdtObj.lattitude = lat;
    this.pdtObj.longitude = lng;

    if (showPopup) {
      this.setState({
        selectCityPopupData: popUpData,
      });
      this.setPopupState(popupEnum.SELECT_CITY, true);
    } else {
      updateCityWithoutShowingPopup(
        city,
        nearByCities,
        this.onCitySelectedFromPopUp,
        this.trackUpdateCityWithoutShowingPopup,
      );
    }
    if (city || nearByCities) {
      this.setState({ citySelectionType: 'Auto-detect' });
    }
  };

  hideSelectCityPop = () => {
    this.setState({
      showSelectCityPopUp: false,
    });
  };

  onCitySelectedFromPopUp = ({ city }) => {
    const { cityName, locusId, airportCode } = city || {};

    const selectedCityType = isEmpty(airportCode) ? 'Non-Airport' : 'Airport';
    this.setState({ selectedCityType });
    this.props.updateDepCity(cityName);

    setDataInStorage(KEY_USER_DEP_CITY, cityName);

    if (!isEmpty(locusId)) {
      setDataInStorage(KEY_USER_CITY_LOCUS_ID, locusId);
    } else {
      removeDataFromStorage(KEY_USER_CITY_LOCUS_ID, locusId);
    }

    this.refreshContent(city);
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.valueSelected,
      value: `select_hub_${cityName}`,
    });
    this.trackClickEventLocal(`select_hub_${cityName}`);
  };

  componentDidUpdate(prevProps, prevState) {
    if (
      prevProps.userDepCity !== this.props.userDepCity &&
      (!this.holidayLandingData.referralCode ||
        (this.holidayLandingData.referralCode && this.isLoggedInUser))
    ) {
      this.pdtObj.preSelectedCity = prevProps.userDepCity;
      this.props.updateInterventionData({
        fromCity: this.props.userDepCity,
        cmp: this.holidayLandingData.cmp,
        branch: HOLIDAYS_BRANCH_NONE,
        ...(this.isLoggedInUser && {
          referralCode: this.holidayLandingData.referralCode,
        }),
      });
      clearPageVisitResponses({
        pages: [
          sectionTrackingPageNames.LANDING_PAGE,
          sectionTrackingPageNames.LANDING_PAGE_RVS,
          sectionTrackingPageNames.LANDING_PAGE_RVS_V2,
        ],
      });
    }
 // Store sections data in PDT holder when landing data loads successfully
    const { showWidget, popupVisible } = this.state || {};
    const { showWidget: prevWidget, popupVisible: prevPopupVisible } = prevState || {};
    if (
      (!prevWidget && showWidget) ||
      (!prevPopupVisible && popupVisible) ||
      (!prevProps.isLoading && this.props.isLoading) ||
      (!prevProps.isError && this.props.isError)
    ) {
      this.props.pauseIntervention();
      this.interventionPaused = true;
    } else if (
      (prevWidget && !showWidget) ||
      (prevPopupVisible && !popupVisible) ||
      (prevProps.isLoading && !this.props.isLoading) ||
      (prevProps.isError && !this.props.isError)
    ) {
      this.interventionPaused = false;
      this.props.playIntervention();
    }
    const { availableHubs, userDepCity } = this.props || {};
    if (!isEmpty(availableHubs) && prevProps.availableHubs !== availableHubs) {
      const searchContext = setSearchContextData({
        depCityData: getCityData(availableHubs, userDepCity),
      });
      const pdtObj = {
        ...getLandingPDTObj(),
        search_context: searchContext,
      };
      updateLandingPDTObj({ pdtObj });
    }
  }
  updateSectionsInPDTHolder = (allSectionData) => {
    if (!allSectionData) {
      return;
    }

    // Static flag to ensure tracking only happens once
    if (this.pageSectionsTracked) {
      return;
    }
    this.pageSectionsTracked = true;

    // Get section data in PDT holder
    const sectionData = formatSectionsDataForPDTLanding(allSectionData);

    // Track page load completion event with PDT
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value: EVENT_NAMES.PAGE_RENEDERED,
      sectionData,
    });
  };
  componentWillUnmount() {

    if (this.eventLandingRefreshListener && this.eventLandingRefreshListener.remove) {
      this.eventLandingRefreshListener.remove();
    }

    if (this.eventLandingLoginListener && this.eventLandingLoginListener.remove) {
      this.eventLandingLoginListener.remove();
    }

    if (this.focusListener && this.focusListener.remove) {
      this.focusListener.remove();
    }
    if (this.blurListener && this.blurListener.remove) {
      this.blurListener.remove();
    }

    this.props.resetLandingData();
  }


  refreshContent = (userDepCity) => {
    this.props.fetchLandingDataFromStorage(false);
    this.props.fetchLandingData({ ...this.holidayLandingData, userDepCity, giData: this.getGiTrackingData() });
  };

  landingDataRefreshed = () => {
    this.props.fetchRecentlySeenPackages();
    if (this.holidayLandingData.showHandPickedHol) {
      this.props.fetchHandpickedPackages(this.props.userDepCity);
    }
  };

  async initAb() {
    if (isMobileClient()) {
      try {
        await getPokusConfigWaitingPromise(5000);
      } catch (error) {
        console.log('Pokus error');
      }
    } else {
      await initAbConfigUsingPokusHLD();
    }
    this.isSearchFilter = getIsSearchFilter();
    const sectionstoBeSupressed = getLandingSupresedSections();
    this.sectionsListToBeSupressed = sectionstoBeSupressed?.split(',') || [];
    this.setState({
      showPromoSection: '',
    });
    const shouldShowMyraChatIcon = getMyraChatIconPokus() && !isNonMMTAffiliate(this.holidayLandingData.aff)
    if(shouldShowMyraChatIcon) {
      const eventName = PDTConstants.CONTACT_ICON_CHATGPT_SHOWN;
      this.trackLocalClickEventWithPax({eventName});
      sendMMTGtmEvent({ eventName, data: { pageName: 'landing',branch: this?.holidayLandingData?.branch } });
    }
    this.showWGBannerLandingHol = getShowWGBannerLandingHol();
    if (this.holidayLandingData.aff === AFFILIATES.GI) {
      this.affPostSalesQueryNumHol = '';
    } else if (this.holidayLandingData.aff === AFFILIATES.TP) {
      this.affPostSalesQueryNumHol = getTpPostSalesQueryNumHol();
    }
  }
  render() {
    return (
      <View style={styles.overlayContainer}>
        {this.props.isLoading && !this.props.isSuccess && this.renderProgressView()}
        {this.props.isError && this.renderContent(false)}
        {this.props.isSuccess && !this.props.isError && this.renderContent(true)}
      </View>
    );
  }

  toggleWGCitySelectorVisibility = () =>
    this.setState({ showWGCitySelector: !this.state.showWGCitySelector, fixed: false });

  checkIfWG = () => this.holidayLandingData.isWG;

  closeGIPostSalesQry = () => this.setState({ showPostSalesQryStrip: false });

  closeHindiBanner = () => {
    this.setState({ isHindi: false });
  };
  toggleEditMenu = () => {
    this.setState({ openEditModal: !this.state.openEditModal });
  };
  getCities = () => {
    const { availableHubs } = this.props || {};
    return availableHubs ? availableHubs.map((hub) => hub.name) : [];
  };
  setLabel = (label) => {
    this.setState({ label: label });
  };
  trackLocalClickEventWithPax = (params) => {
    const { paxDetails, fromDateObj } = this.state || {};
    trackLocalClickEvent({
      ...params,
      paxRoomData: {
        paxDetails,
        roomDetails: this.roomDetails,
        fromDateObj,
      },
    });
  };

  trackFabClickEvents = (params) => {
    const { eventName = '', suffix = '' } = params || {};
    sendMMTGtmEvent({ eventName: eventName + suffix, data: { pageName: 'landing', branch: this?.holidayLandingData?.branch } });
    this.trackLocalClickEventWithPax(params);
  }
  togglePax = () => {
    logHolidaysLandingPDTEvents({ value: 'change_pax', actionType: PDT_EVENT_TYPES.buttonClicked });
    this.trackLocalClickEventWithPax({ eventName: 'change_pax' });
    this.setPopupState(popupEnum.TRAVELLER, true);
  };
  getEditSearchTrackingData = () => {
    let trackingData = {};
    trackingData.departureCity = this.userDepCity;
    trackingData.dest = this.destinationCity;
    trackingData.packageDate = this.state.fromDateObj
      ? fillDateAndTime(this.state.fromDateObj.selectedDate, 'DD-MM-YYYY')
      : undefined;
    return trackingData;
  };

  openDepartureCity = (label) => {
    this.setPopupState(popupEnum.DEP_DEST, true);
    this.setState({ label });
    if (label === TO) {
      this.trackClickEventLocal('change_destination', '', this.destinationCity);
    } else {
      this.trackClickEventLocal('change_hub', '', this.userDepCity);
    }
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: label === TO ? 'change_destination' : 'change_hub',
    });
  };

  hideDepartureCity = () => {
    this.setPopupState(popupEnum.DEP_DEST, false);
    this.setState({ fromImageSearch: false });
  };

  onCitySelect = async (city, branch, locusId, data = {}) => {
    if (this.state.label === TO) {
      this.destinationCity = city;
      if (!isEmpty(data)) {
        this.destinationCityData = data;
      } else {
        this.destinationCityData = { destinationCity: city };
      }
      setDataInStorage(KEY_USER_DEST_CITY, this.destinationCityData);
      this.holidayLandingData.destinationCity=city
      const { primaryCityObj, displayName = '', type = '' } = data || {};
      const { primaryCityName, primaryCityType, primarCitySearchType = '' } = primaryCityObj || {};

      // Tracking
      const toCitySelectData = {
        destcity_selected: city,
        destcity_selected_type: primaryCityType,
        destcity_selected_parent: primaryCityName,
      };
      const eventValue = `select_destination| ${primaryCityName} | ${primaryCityType} | ${displayName} | ${type}${
        primarCitySearchType ? ` | ${primarCitySearchType}` : ''
      }`;
      logHolidaysLandingPDTEvents({ actionType: PDT_EVENT_TYPES.valueSelected, value: eventValue });
      this.trackLocalClickEventWithPax({
        eventName: `select_destination_${city}`,
        prop1: `${primaryCityName} | ${primaryCityType} | ${displayName} | ${type}${
          primarCitySearchType ? ` | ${primarCitySearchType}` : ''
        }`,
        pdtExtraData: toCitySelectData,
      });
      sendMMTGtmEvent({ eventName: `select_destination_${city}`, data: { pageName: 'landing',branch: this?.holidayLandingData?.branch } });
    } else {
      const { citySearchType, citySelectionType, primaryCityObj, nearByCities } = data || {};
      const { primaryCityName } = primaryCityObj || {};
      this.setState({ selectedCityType: citySearchType, citySelectionType });
      this.userDepCity = city;
      this.setDataInLocalStorage(city, citySearchType, citySelectionType, locusId);
      this.props.updateDepCity(city);

      const nearByCityDataForPdt = getNearByCityDataForPdt(nearByCities);
      //Tracking.
      const fromCitySelectData = {
        fromcity_selected: city,
        fromcity_selected_type: citySelectionType,
        fromcity_search_type: citySearchType,
        fromcity_selected_parent: primaryCityName ? primaryCityName : city,
        fromcity_suggestions: nearByCityDataForPdt ? nearByCityDataForPdt : '',
      };
      logHolidaysLandingPDTEvents({
        actionType: PDT_EVENT_TYPES.valueSelected,
        value: `select_hub|${city} | ${citySearchType}`,
      });
      this.trackLocalClickEventWithPax({
        eventName: `select_hub_${city}`,
        prop1: `${city} | ${citySearchType}`,
        pdtExtraData: fromCitySelectData,
      });
      sendMMTGtmEvent({ eventName: `select_hub_${city}`, data: { pageName: 'landing', branch: this.holidayLandingData.branch } });
      this.refreshContent(this.props.userDepCity);
    }
    this.setPopupState(popupEnum.DEP_DEST, false);
    this.setState({ fromDateObj: null, filters: [] });
    await this.getMetaData();
  };
  getPopupEventName = (popupVisible) => {
    switch (popupVisible) {
      case popupEnum.DEP_DEST:
        return this.state.label === TO ? 'change_destination' : 'change_hub';
      default:
        return popupVisible;
    }
  };
  closePopup = () => {
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: `back|${this.getPopupEventName(this.state.popupVisible)}`,
    });
    if (this.state.popupVisible !== '') {
      this.setPopupState('');
      this.setState({
        fromImageSearch: false,
      });
    }
    return true;
  };

  setDataInLocalStorage = (city, citySearchType, citySelectionType, locusId) => {
    if (!isEmpty(city)) {
      setDataInStorage(KEY_USER_DEP_CITY, city);
    } else {
      removeDataFromStorage(KEY_USER_DEP_CITY);
    }

    if (!isEmpty(citySearchType)) {
      setDataInStorage(KEY_USER_CITY_SEARCH_TYPE, citySearchType);
    } else {
      removeDataFromStorage(KEY_USER_CITY_SEARCH_TYPE);
    }

    if (!isEmpty(citySelectionType)) {
      setDataInStorage(KEY_USER_CITY_SELECTION_TYPE, citySelectionType);
    } else {
      removeDataFromStorage(KEY_USER_CITY_SELECTION_TYPE);
    }

    if (!isEmpty(locusId)) {
      setDataInStorage(KEY_USER_CITY_LOCUS_ID, locusId);
    } else {
      removeDataFromStorage(KEY_USER_CITY_LOCUS_ID, locusId);
    }
  };

  toggleCalendar = () => {
    const popupName = this.state.popupVisible;
    if (popupName !== popupEnum.CALENDAR) {
      logHolidaysLandingPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: 'change_date',
      });
      this.trackClickEventLocal('change_date', '', this.getEditSearchTrackingData());
    } else {
      logHolidaysLandingPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: `back|${popupEnum.CALENDAR}`,
      });
    }
    this.setPopupState(popupEnum.CALENDAR, !popupName);
  };

  onDayClicked = (day) => {
    this.trackClickEventLocal('click_calendar_date');
  };

  onCalendarDone = (date) => {
    this.setPopupState(popupEnum.CALENDAR, false);
    this.setState({ fromDateObj: getDateObject(date) });
    const formattedDate = fecha.format(date, DATE_WITH_DAY_FORMAT);
    setDataInStorage(KEY_USER_TRAVEL_DATE, formattedDate);
    // Tracking
    const pdtExtraData = { ap_window: getAPWindow(date) };
    const dateSelected = fecha.format(date, 'YYYY-MM-DD');
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: `select_date|${dateSelected}|${getAPWindow(date)}`,
    });
    this.trackLocalClickEventWithPax({
      eventName: 'select_date',
      prop1: this.getEditSearchTrackingData(),
      pdtExtraData,
    });
  };

  trackMmtBlackClickEvent = ({ eventName = '', prop1 = '', mmtBlackBucketDetail = {} }) => {
    const {
      gcBucket = null,
      myCashBucket = null,
      effectivePriceBucket = null,
    } = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    this.trackClickEventLocal(
      eventName,
      '',
      prop1,
      {},
      { omniData: { [TRACKING_EVENTS.M_V46]: evar46 } },
    );
  };

  onKnowMorePressMmtBlackMembership = (bottomSheetDetails, mmtBlackBucketDetail, ctaText) => {
    const eventName = `GC_${ctaText.split(' ').join('_')}`;
    this.setPopupState(popupEnum.MMT_BLACK_BOTTOMSHEET, true);
    this.setState({
      mmtBlackBottomSheetDetails: {
        bottomSheetDetails: bottomSheetDetails,
        mmtBlackBucketDetail: mmtBlackBucketDetail,
      },
    });
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    const {
      gcBucket = null,
      myCashBucket = null,
      effectivePriceBucket = null,
    } = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    this.trackClickEventLocal(
      eventName,
      '',
      '',
      {},
      { omniData: { [TRACKING_EVENTS.M_V46]: evar46 } },
    );
  };

  handleCloseMmtBlackBottomSheet = () => {
    this.setPopupState(popupEnum.MMT_BLACK_BOTTOMSHEET, false);
    this.setState({ mmtBlackBottomSheetDetails: {} });
  };

  handleMmtBlackBottomSheetToggle = () => {
    handleMmtBlackClickEvents(
      '',
      'GC_Popup_Click_close',
      this.state.mmtBlackBottomSheetDetails?.mmtBlackBucketDetail,
      this.trackClickEventLocal,
      this.handleCloseMmtBlackBottomSheet,
    );
  };

  handleMmtBlackCtaBtnClick = () => {
    handleMmtBlackClickEvents(
      '',
      'GC_Popup_Click_got_it',
      this.state.mmtBlackBottomSheetDetails?.mmtBlackBucketDetail,
      this.trackClickEventLocal,
      this.handleCloseMmtBlackBottomSheet,
    );
  };

  handleTermConditionClick = (url) => {
    handleMmtBlackClickEvents(
      url,
      'GC_Popup_Click_t&c',
      this.state.mmtBlackBottomSheetDetails?.mmtBlackBucketDetail,
      this.trackClickEventLocal,
      this.handleCloseMmtBlackBottomSheet,
    );
  };

  getGroupingPageDataUpdated({ aff, cmp, date }) {
    const { campaign, destinationCity, filters = [] } = this.destinationCityData || {};
    const { selectedCityType, citySelectionType, filters: filtersFromState } = this.state || {};
    let groupingObj = {
      destinationCity,
      departureCity: this.userDepCity,
      aff: aff,
      filters: [...(filtersFromState || []), ...(filters || [])],
      rooms: createRoomDetailsFromRoomDataForPhoenix(this.state.paxDetails.roomData, {}),
      cmp,
      holCampaign: campaign,
      destinationCityData: this.destinationCityData,
      selectedCityType,
      citySelectionType,
      availableHubs: this.props.availableHubs,
    };
    if (date) {
      groupingObj.packageDate = date;
    }
    return groupingObj;
  }

  getListingPageDateUpdated({ aff, cmp, date }) {
    const { campaign, destinationCity, filters = [] } = this.destinationCityData || {};
    const { filters: filtersFromState, paxDetails } = this.state || {};
    let listingObj = {
      destinationCity,
      departureCity: this.userDepCity,
      dest: this.destinationCity,
      filters: [...(filtersFromState || []), ...(filters || [])],
      rooms: createRoomDetailsFromRoomDataForPhoenix(paxDetails.roomData, {}),
      aff: aff,
      cmp,
      campaign,
    };
    if (date) {
      listingObj.packageDate = date;
    }
    return listingObj;
  }

  trackEvent = (data) => {
    const { adult, child, infantCount, noOfRooms } = this.state.paxDetails || {};
    const trackObj = {
      pdtData: {
        destination: this.destinationCity,
        pax: `${adult}Adult | ${child}Child | ${infantCount}InfantCount | ${noOfRooms}Rooms `,
        travelDate: this.state?.fromDateObj?.selectedDate,
        filterSelected: this.state.filters,
        page: LANDING_TRACKING_PAGE_NAME,
        selectedCity: this.props.userDepCity,
        ...this.pdtObj,
        ...data,
      },
    };
    trackSearchWidgetV2Landing(trackObj);
  };
  onDone = async (fromSummary = false) => {
    const m_v98 = `${this.state.citySelectionType} | ${this.state.selectedCityType}`;
    const aff = this.holidayLandingData.aff;
    const cmp = this.holidayLandingData.cmp;
    const date = getDateObject(this.state?.fromDateObj?.selectedDate);
    const selectedDate = date?.selectedDate ? fecha.format(date?.selectedDate, 'YYYY-MM-DD') : null;
    const TOTAL_PAX =
      this.state?.paxDetails.adult +
      this.state?.paxDetails.child +
      this.state.paxDetails.infantCount;
    const hasMandatoryPaxDate = getPokusForMandatoryPaxDate();
    const { isDefaultRoomDetail } = this;
    if (
      hasMandatoryPaxDate &&
      (!selectedDate || isDefaultRoomDetail) &&
      !isEmpty(this.state.metaData)
    ) {
      if (fromSummary) {
        this.searchFilterRef.current.scrollToIndex({ index: 0 });
      }
      this.setState({
        searchFilterModalError: true,
      });

      const errorEventDate = !selectedDate ? 'Date' : '';
      const errorEventPax = this.isDefaultRoomDetail ? 'Pax' : '';
      const separator = errorEventDate && errorEventPax ? '_' : '';
      trackLocalClickEvent({
        eventName: 'Search_Error_' + errorEventDate + separator + errorEventPax,
      });
      return;
    } else {
      this.setState({
        searchFilterModalError: false,
      });
    }
    const AP_WINDOW = getAPWindow(selectedDate);
    const FILTER_FLAG = this.state.quickFilter === undefined ? 'N' : 'Y';
    //Tracking
    const pdtExtraData = {
      search_criteria: `FromCity: ${this.props.userDepCity}, Dest: ${getCityCampaignDisplayName(
        this.destinationCityData,
      )}, Pax: ${getPaxCountInfoForTotalRooms(
        this.roomDetails,
      )}, Date: ${selectedDate}, Filters: ${FILTER_FLAG}`,
    };
    const prop1Event = `SearchCriteria_${this.props.userDepCity}_${getCityCampaignDisplayName(
      this.destinationCityData,
    )}_${AP_WINDOW}_${TOTAL_PAX}_${FILTER_FLAG}`;
    const { paxDetails } = this.state || {};
    const { type, displayName } = this.destinationCityData || {};
    const destCityData = {
      id: 'holidays',
      countryName: 'INDIA',
      locusId: 'holidays',
      name: getCityCampaignDisplayName(this.destinationCityData),
    };
    const {  fromDateObj } =
      this.state || {};
    const searchContext = setSearchContextData({
      paxDetails,
      destCityData,
      tagDetails: { type, displayName },
    });
    const pdtObj = {
      ...getLandingPDTObj(),
      search_context: searchContext,
    };

    updateLandingPDTObj({ pdtObj });
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.searchClicked,
      value: 'search|' + prop1Event,
    });

    updatePDTJourneyIdOnSearchChange({
      depCity: this.props.userDepCity,
      destCity: getCityCampaignDisplayName(this.destinationCityData),
      paxDetails,
      startDate: selectedDate,
    });

    this.trackLocalClickEventWithPax({
      eventName: 'Search',
      prop1: prop1Event,
      omniData: { m_v98 },
      pdtExtraData,
    });
    sendMMTGtmEvent({ eventName: 'Search', data: { pageName: 'landing', branch: this?.holidayLandingData?.branch } });
    //Open grouping page.
    HolidayNavigation.push(
      HOLIDAY_ROUTE_KEYS.GROUPING,
      this.getGroupingPageDataUpdated({ aff, cmp, date: selectedDate }),
    );
    this.onSWCloseNew();
    if (fromSummary) {
      this.searchFilterRef.current.scrollToIndex({ index: 0 });
    }
  };

  handleTravellerChange = (roomData, packagePaxDetail, isBack = false) => {
    this.roomDetails = createRoomDetailsFromRoomDataForPhoenix(roomData, packagePaxDetail);
    if (!isBack) {
      this.isDefaultRoomDetail = false;
    }
    const { adult, child, noOfRooms, infantCount } = getPaxDetails({
      roomDetails: this.roomDetails,
    });
    const paxDetails = {
      adult,
      child,
      noOfRooms,
      roomData,
      infantCount,
    };
    this.setState({
      paxDetails,
    });
    this.setPopupState(popupEnum.TRAVELLER, false);
    setDataInStorage(KEY_USER_ROOMS_CONFIG, this.roomDetails);

    // Tracking
    const eventName = `changed_pax_adult_${adult}_child_${child}_infant_${infantCount}_noOfRooms_${noOfRooms}`;
    const prop1 = `changed_adult:${adult}_children:${child}_infant:${infantCount}`;
    const pdtExtraData = {
      pax_selected: `${adult}|${child}|${infantCount}|${adult + child + infantCount}`,
      pax_room_selected: getRoomDataForPdt(this.roomDetails),
    };
    const eventVal = isBack ? `back|${popupEnum.TRAVELLER}` : eventName;
    logHolidaysLandingPDTEvents({ actionType: PDT_EVENT_TYPES.buttonClicked, value: eventVal });
    this.trackLocalClickEventWithPax({ eventName, eventNameOmni: '', prop1, pdtExtraData });
  };
  getModalName = () => {
    const popupName = this.state.popupVisible;
    if (popupName) {
      return popupName;
    }
  };
  setFabText = () => {
    this.setState({
      fabTextShrinked: true,
    });
  };
  setWidgetState = (key, val) => {
    this.setState({
      showWidget: val ? key : '',
    });
  };
  setPopupState = (key, val) => {
    if (key === popupEnum.DEP_DEST && !val) {
      this.setState({
        fromImageSearch: false,
      });
    }
    this.setState({
      popupVisible: val ? key : '',
    });
  };

  setMenuActiveState = (value) => {
    this.setState({
      active: value,
    });
  };

  setLocatorState = () => {
    this.setPopupState(popupEnum.BRANCH, true);
  };

  toggleMenu = () => {
    this.closePopup();
    this.setMenuActiveState('');
  };

  renderContent = (isSuccess) => {
    if (!this.state.showWidget && !isSuccess) {
      this.showSearchWidget(false);
      return null;
    }
    const {
      masterData,
      availableHubs,
      filterIdMap,
      isSWSuccess,
      isSWLoading,
      isSWError,
      showLocationPopUp,
      searchWidgetData,
      userDepCity,
      searchHistoryResults,
      fabCta,
      updateDepCity,
      showFromCityPopup,
      landingMMTBlackData,
    } = this.props;
    this.landingResponse = [];
    const landingData = this.props.landingData || {};
    if (Array.isArray(landingData?.sections)) {
      this.landingResponse = orderBy(landingData?.sections, (row) => row.order, ['asc']);
    }
    if (this.props.headerMenu && this.props.headerMenu.length > 0) {
      this.headerMenu = checkMenuTabsData(this.props.headerMenu);
    }
    const aff = this.holidayLandingData.aff;
    const cmp = this.holidayLandingData.cmp;
    const { label, popupVisible, showGoingTo, showWidget, fromDateObj, metaData } =
      this.state || {};
    const shouldShowMyraChatIcon = getMyraChatIconPokus() && !this.isGdprRegion &&  !isNonMMTAffiliate(this.holidayLandingData.aff);
    const fabCtaDataValue = isRawClient() && shouldShowMyraChatIcon
      ? { showMyraChat: true, showFab: true }
      : this.props.fabCta;
    const modalName = this.getModalName();
    const showFabCta =
      fabCta &&
      fabCta.showFab &&
      popupVisible !== popupEnum.CALENDAR &&
      !showWidget &&
      popupVisible !== popupEnum.DEP_DEST;
    const isHeaderHide= this.isSearchFilter?.searchV2 && isGIAffiliate(this.holidayLandingData.aff) && isAppPlatform(this.state.deviceType)
    return (
      <View style={styles.holidayLaningParent}>
        {showWidget === popupEnum.SHOW_SEARCH_WIDGET && (
          <SearchWidget
            pageName={LANDING_TRACKING_PAGE_NAME}
            fetchSearchWidgetData={this.props.fetchSearchWidgetData}
            fetchSearchWidgetDataWithOldData={this.props.fetchSearchWidgetDataWithOldData}
            toggleLocationAutoComplete={this.props.toggleLocationAutoComplete}
            searchHistoryResults={searchHistoryResults}
            getSearchResults={this.getSearchResults}
            masterData={masterData}
            isWG={this.checkIfWG()}
            searchWidgetData={searchWidgetData}
            availableHubs={availableHubs}
            filterIdMap={filterIdMap}
            isSuccess={isSWSuccess}
            isLoading={isSWLoading}
            isError={isSWError}
            showLocationPopUp={showLocationPopUp}
            showGoingTo={showGoingTo}
            onSWClose={this.onSWClose}
            onSWDone={this.onSWDone}
            isLanding
            disallowRefresh={false}
            aff={aff}
            cmp={cmp}
            userDepCity={userDepCity}
            updateDepCity={updateDepCity}
            trackErrorClickEvent={trackLocalErrorClickEvent}
            trackClickEvent={this.trackClickEventLocal}
          />
        )}
        {showWidget === popupEnum.showFilterWidget && this.renderPhoenixSearchPage()}

        {!showWidget && (
          <React.Fragment>
            {this.state.popupVisible !== popupEnum.DEP_DEST && (
              <HolidayLandingHeader
                holidayLandingData={this.holidayLandingData}
                label={label}
                openDepartureCity={this.openDepartureCity}
                onCitySelect={this.onCitySelect}
                closeCitySelection={this.closePopup}
                setPdtTrackingData={this.setPdtTrackingData}
                onBackClick={this.onBackClick}
                onHeaderGoingToClicked={this.onHeaderGoingToClicked}
                trackClickEvent={this.trackClickEventLocal}
                isSearchFilter={this.isSearchFilter}
                onLogoutClick={this.onLogoutClick}
                isHeaderHide={isHeaderHide}
              />
            )}
            <HolidayLandingPopups
              popup={modalName}
              calendarPageData={{
                metaData: metaData,
                fromDateObj: fromDateObj,
                toggleCalendar: this.toggleCalendar,
                onCalendarDone: this.onCalendarDone,
              }}
              filterDestinationData={{
                label,
                availableHubs,
                fromImageSearch: this.state.fromImageSearch,
                onCitySelect: this.onCitySelect,
                setPdtTrackingData: this.setPdtTrackingData,
                openImageSearch: this.openImageSearch,
                isFromImageSearchBanner,
              }}
              close={this.closePopup}
              travellerPageData={{
                roomData: this.state?.paxDetails?.roomData,
                handleTravellerChange: this.handleTravellerChange,
                selectedDate:this.dateObj?.selectedDate,
                packagePaxDetail:this.state.metaData?.pax,
                trackClickEvent:this.trackClickEvent,
                fromLanding:true,
                handlePDT: ()=> {}
              }}
              menuData={{
                headerMenu: this.headerMenu,
                toggleMenu: this.toggleMenu,
                active: this.state.active,
                menuCardOnPressHandling: this.menuCardOnPressHandling,
              }}
              selectCityData={{
                onCitySelectedFromPopUp: this.onCitySelectedFromPopUp,
                selectCityPopupData: this.state.selectCityPopupData,
              }}
              trackClickEvent={this.trackClickEventLocal}
              isSearchFilter={this.isSearchFilter}
              mmtBlackBottomSheetData={{
                mmtBlackBottomSheetDetails: this.state.mmtBlackBottomSheetDetails,
                handleMmtBlackBottomSheetToggle: this.handleMmtBlackBottomSheetToggle,
                handleMmtBlackCtaBtnClick: this.handleMmtBlackCtaBtnClick,
                handleTermConditionClick: this.handleTermConditionClick,
              }}
            />
            {!showFromCityPopup && (
              <React.Fragment>
                <View
                  style={{
                    backgroundColor: holidayColors.white,
                    flex: 1,
                  }}
                >
                  <HolidayLandingContent
                    searchFilterRef={this.searchFilterRef}
                    holidayLandingData={this.holidayLandingData}
                    stateData={{ ...this.state }}
                    destinationCityData={this.destinationCityData}
                    onFilterClicked={this.handleFilterClick}
                    selectedQuickFilterIndex={this.state.selectedQuickFilterIndex}
                    setLabel={this.setLabel}
                    openDepartureCity={this.openDepartureCity}
                    toggleCalendar={this.toggleCalendar}
                    fromDateObj={fromDateObj}
                    search={this.onDone}
                    roomDetails={this.roomDetails}
                    togglePax={this.togglePax}
                    headerMenu={this.headerMenu}
                    setFabText={this.setFabText}
                    landingResponse={this.landingResponse}
                    refreshContent={this.refreshContent}
                    openMenuOverlay={this.openMenuOverlay}
                    openImageSearch={this.openImageSearch}
                    landingDataRefreshed={this.landingDataRefreshed}
                    closeQuery={this.closeQuery}
                    isDefaultRoomDetail={this.isDefaultRoomDetail}
                    trackClickEvent={trackLocalClickEvent}
                    sectionsListToBeSupressed={this.sectionsListToBeSupressed}
                    isSearchFilter={this.isSearchFilter}
                    isHeaderHide={isHeaderHide}
                    onKnowMorePressMmtBlackMembership={this.onKnowMorePressMmtBlackMembership}
                    trackMmtBlackClickEvent={this.trackMmtBlackClickEvent}
                    landingMMTBlackData={landingMMTBlackData}
                  />
                </View>
                {showFabCta && (
                  <HolidayFabAnimationContainer
                    ref={(ref) => {
                      if (this.props.registerFabAnimationRef) {
                        this.props.registerFabAnimationRef(ref);
                      }
                    }}
                    fabData={createLandingFabData({
                      holidayLandingData: this.holidayLandingData,
                    })}
                    pageName={HLD_PAGE_NAME.LANDING}
                    configId={HLD_PAGE_NAME.LANDING}
                    textShrinked={this.state.fabTextShrinked && !showFabAnimationExtended()}
                    fabCta={fabCtaDataValue}
                    unmountIntervention={this.props.unmountIntervention}
                    trackLocalClickEvent={this.trackFabClickEvents}
                    setLocatorState={this.setLocatorState}
                    containerBottomValue={40}
                    containerStyle={{ bottom: 40 }}
                    trackPDTV3Event={logHolidaysLandingPDTEvents}
                    travelPlexConfigData={{
                      page_context: getLandingPDTObj().page_context,
                      attr2: this.destinationCity,
                      attr4: this.travelPlexAttr4?.allocationDetail?.queueIdentifier,
                      hideInput: !showHolAgentOnLandingAndListingPage(),
                    }}
                  />
                )}
                <HolidayLandingPopups
                  popup={modalName}
                  calendarPageData={{
                    metaData: metaData,
                    fromDateObj: fromDateObj,
                    toggleCalendar: this.toggleCalendar,
                    onCalendarDone: this.onCalendarDone,
                  }}
                  filterDestinationData={{
                    label,
                    availableHubs,
                    fromImageSearch: this.state.fromImageSearch,
                    onCitySelect: this.onCitySelect,
                    setPdtTrackingData: this.setPdtTrackingData,
                    openImageSearch: this.openImageSearch,
                    isFromImageSearchBanner,
                  }}
                  close={this.closePopup}
                  travellerPageData={{
                    roomData: this.state?.paxDetails?.roomData,
                    handleTravellerChange: this.handleTravellerChange,
                    selectedDate: this.dateObj?.selectedDate,
                    packagePaxDetail: this.state.metaData?.pax,
                  }}
                  menuData={{
                    headerMenu: this.headerMenu,
                    toggleMenu: this.toggleMenu,
                    active: this.state.active,
                    menuCardOnPressHandling: this.menuCardOnPressHandling,
                  }}
                  selectCityData={{
                    onCitySelectedFromPopUp: this.onCitySelectedFromPopUp,
                    selectCityPopupData: this.state.selectCityPopupData,
                  }}
                  trackClickEvent={this.trackClickEventLocal}
                  isSearchFilter={this.isSearchFilter}
                  mmtBlackBottomSheetData={{
                    mmtBlackBottomSheetDetails: this.state.mmtBlackBottomSheetDetails,
                    handleMmtBlackBottomSheetToggle: this.handleMmtBlackBottomSheetToggle,
                    handleMmtBlackCtaBtnClick: this.handleMmtBlackCtaBtnClick,
                    handleTermConditionClick: this.handleTermConditionClick,
                  }}
                />
              </React.Fragment>
            )}
          </React.Fragment>
        )}
      </View>
    );
  };

  menuCardOnPressHandling = (url, eventName) => {
    this.trackLocalClickEventWithPax({ eventName });
    const deeplink = `${url}&lastPage=landing`;
    const shouldRemoveLastPage = removeLastPage();
    handleDeeplink({
      urlValue: deeplink,
      ...(shouldRemoveLastPage && { deeplink: url }), // used to avoid passing lastPage in htmlPage
      mapObject: {
        userDepCity: this.userDepCity,
        availableHubs: this.props.availableHubs,
        holidayLandingData: this.holidayLandingData,
        destinationCityData: this.destinationCityData,
      },
    });
  };

  openMenuOverlay = (item, index) => {
    const eventName = `Menu_Entry_${item.priority}_${item.name}`;
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: `Menu_Entry_${item.priority}|${item.name}`,
    });
    this.trackLocalClickEventWithPax({ eventName });
    this.setPopupState(popupEnum.MENU, true);
    this.setState({
      active: index,
    });
  };

  openImageSearch = ({ isFromImageSearchBanner = false } = {}) => {
    this.setState({
      fromImageSearch: true,
      label: TO,
    });
    setIsImageSearchFromBanner(isFromImageSearchBanner);
    this.setPopupState(popupEnum.DEP_DEST, true);
    this.trackLocalClickEventWithPax({
      eventName: `imagesearch_${isFromImageSearchBanner ? 'banner' : 'maincamera'}`,
    });
  };

  closeQuery = async (ticketId, showErrorModal, showThankYouModal) => {
    this.setState({ isLoading: true });
    const response = await closeQueryByTicketId(ticketId);
    this.setState({ isLoading: false });
    if (!response.ok && !response) {
      showErrorModal(true);
    } else {
      const responseBody = await response.json();
      if (responseBody?.success) {
        showThankYouModal(true);
      } else {
        showErrorModal(true);
      }
    }
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: 'close_query_presales',
    });
    this.trackLocalClickEventWithPax({ eventName: 'close_query_presales' }); //@todo add prop1 and evar41
  };
  onSWClose = (filtersApplied = false, isSearch = false) => {
    if (this.props.isError && !isSearch) {
      this.onBackPressed();
    } else {
      this.setWidgetState('');
    }
    if (!filtersApplied) {
      HolidayDataHolder.getInstance().setCurrentPage('holidaysLanding');
    }
    // this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
  };

  onSWCloseNew = (filtersApplied = false) => {
    this.setWidgetState('');
    if (!filtersApplied) {
      HolidayDataHolder.getInstance().setCurrentPage('holidaysLanding');
    }
  };

  onDoneNew = (nextProps) => {
    this.setWidgetState('');
    HolidayDataHolder.getInstance().setCurrentPage('holidaysLanding');
    this.setState({
      filters: nextProps?.holidayGroupingNewDto?.filters,
    });
  };
  getSearchWidgetDataMasterObj = () => ({
    request: {
      lob: REQUEST_LOB,
      filterSorterParam: true,
    },
    pageName: DEFAULT_PAGE_NAME,
    destinations: [],
    dateObj: {},
    isLanding: true,
  });

  renderProgressView = () => <ProgressIndicator loaderText={this.state.loaderText} />;

  onHeaderGoingToClicked = () => {
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: 'change_destination',
    });
    this.trackClickEventLocal('change_destination', '', this.getEditSearchTrackingData());
    this.showSearchWidget(true);
  };
  getSearchResults = () => {
    this.props.getSearchHistory();
  };

  showSearchWidget = (showGoingTo) => {
    this.setWidgetState(popupEnum.SHOW_SEARCH_WIDGET, true);
    this.setState({
      showGoingTo,
    });
    HolidayDataHolder.getInstance().setCurrentPage('holidaysSearchWidget');
  };

  renderPhoenixSearchPage = () => {
    const {
      masterData,
      availableHubs,
      filterIdMap,
      isSWSuccess,
      isSWLoading,
      isSWError,
      showLocationPopUp,
      searchHistoryResults,
    } = this.props;
    const isBottomSheet = false;
    const aff = this.holidayLandingData.aff;
    const dateObj = {
      // fromDate: this.holidayLandingGroupDto.fromDate,
      // toDate: this.holidayLandingGroupDto.toDate,
      // packageDate: this.holidayLandingGroupDto.packageDate,
      // selectedDate: this.holidayLandingGroupDto.selectedDate
    };
    const SearchPage = require('../../SearchWidget/Components/PhoenixSearchPage').default;
    if (isEmpty(this.masterMetaResponse)) {
      // only one time assignment is required
      this.masterMetaResponse = {
        listingFilters: this.state.metaData.filters,
        headerDetail: { totalPackageCount: 1, packagesCount: 1 },
      };
    }

    const extraProps = {
      trackClickEvent: this.trackLocalClickEventWithPax,
      masterMetaResponse: this.masterMetaResponse,
      loadPhoenixSearchPageData: true,
      setSearchWidgetRefreshRequired: () => {},
      searchWidgetRefreshRequired: true,
      campaign: this.holidayLandingData.campaign,
      quickFilter: this.state.quickFilter,
    };

    const searchPage = (
      <SearchPage
        pageName={LANDING_PAGE_NAME}
        trackingPageName={LANDING_TRACKING_PAGE_NAME}
        dateObj={dateObj}
        criterias={this.state.filters}
        destinationCity={'GOA'}
        cmpChannel={this.holidayLandingData.cmp}
        branch={this.holidayLandingData.branch}
        fetchSearchWidgetData={(request) => {}}
        fetchSearchWidgetDataWithOldData={(request) => {}}
        toggleLocationAutoComplete={this.props.toggleLocationAutoComplete}
        masterData={masterData}
        // searchWidgetData={}
        availableHubs={availableHubs}
        searchHistoryResults={searchHistoryResults}
        filterIdMap={filterIdMap}
        isSuccess={isSWSuccess}
        isLoading={isSWLoading}
        isError={isSWError}
        showLocationPopUp={showLocationPopUp}
        onSWClose={this.onSWCloseNew}
        onSWDone={this.onDoneNew}
        getSearchResults={this.getSearchResults}
        aff={aff}
        updateDepCity={this.props.updateDepCity}
        userDepCity={this.props.userDepCity}
        trackErrorClickEvent={this.trackErrorLocalClickEvent}
        {...extraProps}
        isBottomSheet={isBottomSheet}
        visibleOn={'LANDING'} //todo need to pass original Page
        isLanding={true}
        trackPDTV3Event={logHolidaysLandingPDTEvents}
      />
    );
    const totalPackageCount = this.masterMetaResponse?.headerDetail?.packagesCount;
    return !isBottomSheet ? (
      <Modal animationType={'slide'} onRequestClose={() => this.setWidgetState('')}>
        <SafeAreaView style={{ flex: 1 }}>{searchPage}</SafeAreaView>
      </Modal>
    ) : totalPackageCount > this.state.filterThreshold ? (
      <BottomSheet
        onBackPressed={() => {
          this.setState({ showFilterBottomsheet: false });
        }}
      >
        <View style={{ height: '100%', width: '100%' }}>{searchPage}</View>
      </BottomSheet>
    ) : null;
  };

  onBackClick = () => {
    const { popup } = this.state || {};
    if (this?.props?.leaveIntent?.toUpperCase() === 'Y' && !this.interventionPaused) {
      this?.props?.close(); /* to handle intervention on back press */
      return true;
    } else {
      if (popup !== '') {
        this.setPopupState('');
        this.setState({
          popup: '',
          active: null,
        });
        return true;
      }
      const eventName = 'back';
      this.trackLocalClickEventWithPax({ eventName });
      logHolidaysLandingPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
      });
      return this.onBackPressed();
    }
  };
  onBackPressed = () => {
    if (this.state.popup !== '') {
      this.setState({
        popup: '',
      });
      return;
    }
    if (this.state.showWGDestSelector) {
      this.setState({ showWGDestSelector: !this.state.showWGDestSelector });
      return true;
    }

    if (isRawClient()) {
      window.location.href = '//www.makemytrip.com';
    }
    if (isIosClient()) {
      ViewControllerModule.popViewController(this.props.rootTag);
    } else {
      BackHandler.exitApp();
    }
    return true;
  };
  onLoginEventReceived = (response) => {
    if (response && response.loggedIn) {
      const isWG = this.checkIfWG();
      this.props.fetchLandingData(
        this.holidayLandingData,
        isWG && !isEmpty(this.holidayLandingData.depCity) ? this.holidayLandingData.depCity : '',
        this.getGiTrackingData()
      );
      this.props.updateInterventionData({
        fromCity: this.props.userDepCity,
        cmp: this.holidayLandingData.cmp,
        branch: HOLIDAYS_BRANCH_NONE,
        referralCode: this.holidayLandingData.referralCode,
      });
    }
  };

  onLogoutClick = async () => {
    if (isRawClient()&& MobileLoginModule) {
      const MobileLogin = await MobileLoginModule;
      MobileLogin.doLogout().finally(() => {
        const isWG = this.checkIfWG();
        this.props.fetchLandingData(
          this.holidayLandingData,
          isWG && !isEmpty(this.holidayLandingData.depCity) ? this.holidayLandingData.depCity : '',
          this.getGiTrackingData()
        );
      });
    }
  };

  setPdtTrackingData = (obj) => {
    this.pdtObj = { ...this.pdtObj, ...obj };
  };
}
export default withBackHandler(withIntervention(HolidayLandingNew, LANDING_PAGE_NAME));
