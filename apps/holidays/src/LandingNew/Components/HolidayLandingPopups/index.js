import React, { Suspense } from 'react';
import { Mo<PERSON>, SafeAreaView, View } from 'react-native';;
import {today} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {addDays} from '@mmt/legacy-commons/Helpers/dateTimehelpers';
import { infoIcon } from '../../../HolidayConstants';
import { CALENDAR_SELECTED_DATE_DIFF, FROM } from '../../../SearchWidget/SearchWidgetConstants';
import { getCitySearchType } from '../../Utils/DestinationDepartureCityUtils';
import styles from '../HolidayLandingCss';
import { getGeoLocationPokus } from '../../Utils/HolidayLandingUtilsV2';
import { MenuCss } from '../phoenixCss';
import { popupEnum } from './PopupConstants';
import Loader from 'apps/holidays/src/SearchWidget/Components/Loader';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import { logHolidaysLandingPDTEvents } from '../../Utils/HolidayLandingPdtTrackingUtils';
import { showNewRoomAndGuest } from 'apps/holidays/src/utils/HolidaysPokusUtils';
import BottomSheet from 'apps/holidays/src/Common/Components/BottomSheet';
import MMTBlackBottomSheet from 'apps/holidays/src/Common/Components/Membership/BottomSheet';
import { TRACKING_EVENTS } from 'apps/holidays/src/HolidayTrackingConstants';
import {isMobileClient} from '../../../utils/HolidayUtils';

const MmtHolidayCalender = isMobileClient()
    ? React.lazy(() => import('../../../Calender/MmtHolidayCalender'))
    : require('../../../Calender/MmtHolidayCalender').default;

const BranchLocatorPage = isMobileClient()
    ? React.lazy(() => import('../../../BranchLocator/BranchLocatorPage'))
    : require('../../../BranchLocator/BranchLocatorPage').default;

const MenuSectionPage = isMobileClient()
    ? React.lazy(() => import('../MenuOverlay'))
    : require('../MenuOverlay').default;

const SelectCityPopUp = isMobileClient()
    ? React.lazy(() => import('apps/holidays/src/SearchWidget/Components/SelectCityPopUp'))
    : require('apps/holidays/src/SearchWidget/Components/SelectCityPopUp').default;

const TravellerPage = isMobileClient()
    ? React.lazy(() => import('../../../PhoenixDetail/Components/EditOverlay/TravellerPage/TravellerPage'))
    : require('../../../PhoenixDetail/Components/EditOverlay/TravellerPage/TravellerPage').default;

const getDateForCalendar = () => addDays(today(), CALENDAR_SELECTED_DATE_DIFF);
const HolidayLandingPopups = ({
  popup,
  calendarPageData,
  filterDestinationData,
  travellerPageData,
  close,
  menuData,
  selectCityData,
  trackClickEvent,
  isSearchFilter,
  mmtBlackBottomSheetData,
}) => {
  let FilterDestination;
  let TravellerPage;
  const enableNewGeoLocation = getGeoLocationPokus();
  const label = filterDestinationData?.label;
  if (
    (enableNewGeoLocation && label === FROM) ||
    (isSearchFilter?.searchV2 && label !== FROM)
  ) {
    FilterDestination = require('../../../SearchWidget/Components/DepartureDestinationSelector').default;
  } else {
    FilterDestination = require('../../../SearchWidget/Components/FilterDestinationSelector').default;
  }

  if(showNewRoomAndGuest()) {
    TravellerPage = require('../../../Common/Components/RoomAndGuest').default;
  } else {
    TravellerPage = require('../../../PhoenixDetail/Components/EditOverlay/TravellerPage/TravellerPage').default;
  }

  const onDayClickedTrackEvent = (day) => {
    const {dateString = ''} = day || {} ;
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.valueSelected,
      value: `click_calendar_date|${dateString}`,
    });
    trackClickEvent('click_calendar_date');
  };

  const trackMmtBlackClickEvent = ({eventName}) => {
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBottomSheetData?.mmtBlackBottomSheetDetails?.mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    trackClickEvent(eventName,'', '', {}, {omniData: {[TRACKING_EVENTS.M_V46]: evar46}});
  }

  const getData = (val) => {
    switch (val) {
      case popupEnum.MMT_BLACK_BOTTOMSHEET:
        return (
          <BottomSheet onBackPressed={mmtBlackBottomSheetData.handleMmtBlackBottomSheetToggle} containerStyle={{ padding: 0 }}>
            <MMTBlackBottomSheet
              togglePopup={mmtBlackBottomSheetData.handleMmtBlackBottomSheetToggle}
              bottomSheetDetail={mmtBlackBottomSheetData?.mmtBlackBottomSheetDetails?.bottomSheetDetails}
              ctaButtonClick={mmtBlackBottomSheetData.handleMmtBlackCtaBtnClick}
              handleTermConditionClick={mmtBlackBottomSheetData.handleTermConditionClick}
              mmtBlackPdtEvents={logHolidaysLandingPDTEvents}
              trackClickEvent={trackMmtBlackClickEvent}
              mmtBlackBucketDetail={mmtBlackBottomSheetData?.mmtBlackBottomSheetDetails?.mmtBlackBucketDetail}
            />
          </BottomSheet>
          );
      case popupEnum.CALENDAR:
        const { metaData, fromDateObj, toggleCalendar, onCalendarDone } = calendarPageData || {};
        return (
          <Modal animationType="slide" onRequestClose={close}>
          <SafeAreaView style={styles.calendar}>
            <Suspense fallback={<Loader />}>
              <MmtHolidayCalender
                selectedDate={fromDateObj?.selectedDate ?? getDateForCalendar()}
                onDone={onCalendarDone}
                onCalendarBack={toggleCalendar}
                availableDates={metaData?.dates?.availableDates}
                message={metaData?.dates?.msg}
                icon={infoIcon}
                onDayClicked={onDayClickedTrackEvent}
              />
            </Suspense>
          </SafeAreaView>
          </Modal>
        );
      case popupEnum.TRAVELLER:
        const { roomData, handleTravellerChange, selectedDate, packagePaxDetail } =
          travellerPageData || {};
        return (
          <Suspense fallback={<Loader />}>
            <Modal animationType="slide" onRequestClose={close}>
              <SafeAreaView style={styles.paxModal}>
                <TravellerPage
                  roomData={roomData}
                  handleTravellerChange={handleTravellerChange}
                  selectedDate={selectedDate}
                  packagePaxDetail={packagePaxDetail}
                  trackClickEvent={trackClickEvent}
                  fromLanding={true}
                  handlePDT={() => {}}
                  trackPDTV3Event={logHolidaysLandingPDTEvents}
                />
              </SafeAreaView>
            </Modal>
          </Suspense>
        );
      case popupEnum.DEP_DEST:
        const { label, availableHubs, onCitySelect, setPdtTrackingData, fromImageSearch = false, isFromImageSearchBanner = false} =
          filterDestinationData || {};
        return (
          <Modal animationType="slide" onRequestClose={close}>
            <View style={styles.filterDestination}>
              <FilterDestination
                autoCompleteObj={{ label, availableHubs }}
                forceShowInput
                showAdavanceSearch={false}
                citySelect={onCitySelect}
                onBack={close}
                scrollable
                showGetCurrLocation={label === FROM}
                fromImageSearch={fromImageSearch}
                showSearchWithImageWidget={true}
                style={styles.filter}
                availableHubs={availableHubs}
                trackClickEvent={trackClickEvent}
                setPdtTrackingData={setPdtTrackingData}
                searchType={getCitySearchType(label)}
                trackPDTV3Event={logHolidaysLandingPDTEvents}
                isFromImageSearchBanner={isFromImageSearchBanner}
              />
            </View>
          </Modal>
        );
      case popupEnum.BRANCH:
        return (
          <Modal animationType="slide" onRequestClose={close}>
          <Suspense fallback={<Loader />}>
            <BranchLocatorPage onBackBranchLocator={close} trackClickEvent={trackClickEvent} />;
          </Suspense>
          </Modal>
        );
      case popupEnum.MENU:
        const { headerMenu, toggleMenu, active, menuCardOnPressHandling } = menuData || {};
        return (
          <Modal animationType="slide" onRequestClose={close}>
          <Suspense fallback={<Loader />}>
            <MenuSectionPage
              headerMenu={headerMenu}
              toggleMenu={toggleMenu}
              style={MenuCss}
              active={active}
              menuCardOnPressHandling={menuCardOnPressHandling}
              trackLocalClickEvent={trackClickEvent}
            />
          </Suspense>
          </Modal>
        );
      case popupEnum.SELECT_CITY:
        const { onCitySelectedFromPopUp, selectCityPopupData = {} } = selectCityData || {};
        return (

          <Suspense fallback={<Loader />}>
            <SelectCityPopUp
              hideSelectCityPop={close}
              onCitySelectFromPopup={onCitySelectedFromPopUp}
              trackClickEvent={trackClickEvent}
              data={selectCityPopupData}
            />
          </Suspense>

        );
      default:
        return null;
    }
  };
  return getData(popup);
};
export default HolidayLandingPopups;
