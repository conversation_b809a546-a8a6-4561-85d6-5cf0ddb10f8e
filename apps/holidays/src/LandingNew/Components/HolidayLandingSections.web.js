import React, { Suspense, useCallback } from 'react';
import {NativeModules, StyleSheet, View} from 'react-native';
import { DEEPLINK_LISTING_GROUPING_PARAM, DEEPLINK_WG_PARAM } from '../../HolidayConstants';

import {
  fetchLandingData,
  fetchLandingDataFromStorage,
  setRefreshLandingForEditQuery,
} from '../Actions/HolidayLandingActions';
import HolidayLandingCategory from './HolidayLandingCategory';
import HolidayLandingLoginStrip from './HolidayLandingLoginStrip';
import HolidayLandingPromoSection from './HolidayLandingPromoSection';
import HolidayLandingReasonToBelieve from './HolidayLandingReasonToBelieve';
import HolidayLandingRecommendationBanner from './HolidayLandingRecommendationBanner';
import {
  genericOnPressHandling,
  handleGroupDeeplink,
  onCardClickDeeplinkHandling,
  onDynamicPackageItemClicked,
  trackLocalClickEvent,
} from '../Utils/HolidayLandingUtilsV2';
import { AdCss, HbCss, OcbCss } from './phoenixCss';
import styles from './HolidayLandingCss';
import { generateSectionKey, getMmtBlackDetailsNodes } from '../Utils/HolidayLandingUtils';
import { connect } from 'react-redux';
import { LANDING_TRACKING_PAGE_NAME, sectionCodes } from '../LandingConstants';
import { HOLIDAYS_BRANCH_NONE } from '../../utils/HolidayTrackingUtils';
import { doQuery, isNonMMTAffiliate } from '../../utils/HolidayUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { AdBannerPlacement } from '../../Common/Components/HolidayAdBanner/HolidayAdConstants';
import {
  enableSearchByImage,
  showMMTBlack,
  showNewCYSSection,
  showNewRVSSection,
  showRNESection,
} from '../../utils/HolidaysPokusUtils';
import { logHolidaysLandingPDTEvents } from '../Utils/HolidayLandingPdtTrackingUtils';
import { updatePDTJourneyIdOnSearchChange } from '../../utils/HolidayPDTTrackingV3';
import { defaultRoom, roomDefault } from '../../utils/RoomPaxUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

/* Components */
import HolidayAdBanner from '../../Common/Components/HolidayAdBanner';
import HolidayDidYouKnowBanner from '../../Common/Components/Phoenix/HolidayDidYouKnowBanner';
import SMEHorizontalSection from '../../SmeDetails/components/SMEHorizontalSection';
import HolidayLandingCarouselImageBanner from './HolidayLandingCarouselImageBanner';
import Loader from '../../SearchWidget/Components/Loader';
import HolidayCollectionBanner from '../../Common/Components/Phoenix/HolidayCollectionBanner';
import HolidayQuotesSection from './QuotesSection';
import HolidayLandingRecentlyPackageSection from './HolidayLandingRecentlyViewedPackages';
import RVSSectionV2 from '../../Common/Components/Sections/RVSV2';
import { SearchByImageBannerEntryPoint } from '../../Common/Components/SearchByImage';
import MembershipCard from '../../Common/Components/Membership/MembershipCard';
import { CONTENT_TYPES } from '../../Common/Components/Membership/utils/constants';
import { noop } from 'lodash';

//todo ashish handle lazy loading for ios and android. for web its not working.
/*const HolidayCollectionBanner = React.lazy(() => import('../../Common/Components/Phoenix/HolidayCollectionBanner'));
const HolidayQuotesSection = React.lazy(()=> import('./QuotesSection'));
const HolidayLandingRecentlyPackageSection = React.lazy(() => import('./HolidayLandingRecentlyViewedPackages'))*/

const HolidayLandingSections = (props) => {
  const {
    item,
    index,
    holidayLandingData,
    availableHubs,
    roomDetails,
    viewState,
    userDepCity,
    destinationCityData,
    isHindi,
    landingDataRefreshed,
    fabCta,
    closeQuery,
    recentlySeenPackagesData,
    loggedInUser,
    landingResponse,
    paxDetails,
    fromDateObj,
    sectionsListToBeSupressed,
    openImageSearch = () => {},
    onKnowMorePressMmtBlackMembership = noop,
    trackMmtBlackClickEvent,
  } = props || {};

  const refreshContent = () => {
    fetchLandingDataFromStorage(false);
    fetchLandingData({ ...holidayLandingData, userDepCity });
  };
  const onLoginClicked = () => {
    const { HolidayModule } = NativeModules;
    HolidayModule.onLoginUser();
    const eventName = 'Login Clicked';
    trackEvent({ eventName, eventNameOmni: eventName });
  };
  const trackEvent = ({ eventName, eventNameOmni }) => {
    trackLocalClickEvent({
      eventName,
      eventNameOmni,
      paxRoomData: { paxDetails: paxDetails, roomDetails: roomDetails, fromDateObj: fromDateObj },
    });
  };
  const editQuery = (ticketId) => {
    const queryDto = {};
    queryDto.pageName = LANDING_TRACKING_PAGE_NAME;
    queryDto.omniPageName = LANDING_TRACKING_PAGE_NAME;
    queryDto.funnelStep = LANDING_TRACKING_PAGE_NAME;
    queryDto.cmp = holidayLandingData.cmp;
    queryDto.aff = holidayLandingData.aff;
    queryDto.branch = HOLIDAYS_BRANCH_NONE;
    if (ticketId) {
      queryDto.ticketId = ticketId;
    }
    if (fabCta.formId) {
      queryDto.formId = fabCta.formId;
    }
    doQuery(queryDto);
    setRefreshLandingForEditQuery(true);
    logHolidaysLandingPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value : 'edit_query_presales',
    })
    trackEvent({ eventName: 'edit_query_presales' }); //@todo add prop1 and evar41
  };
  const onPromoItemClicked = (card, promoIndex) => {
    const deepLinkUrl = card.deepLinkUrl || '';

    if (!deepLinkUrl) return;
    const urlValue = `${deepLinkUrl}&lastPage=landing`;
    const eventName = 'promo_carousel';
    if (urlValue) {
      const urlParams = URL.parse(urlValue);
      const queryParams = queryString.parse(urlParams.query);
      if (queryParams && urlValue.includes(DEEPLINK_LISTING_GROUPING_PARAM)) {
        handleGroupDeeplink({
          queryParams,
          urlValue,
          redirectionpage: queryParams.redirectionpage,
          holidayLandingData,
          availableHubs,
        });
      } else {
        const { HolidayModule } = NativeModules;
        HolidayModule.openWebView({
          url: deepLinkUrl,
          event: eventName,
          cmp: holidayLandingData.cmp,
        });
      }
      trackEvent({
        eventName,
        eventNameOmni: `${eventName}_${promoIndex}`,
      });
    }
  };
  const trackCategoryEvent = useCallback((event) => {
    trackLocalClickEvent({ eventName: event });
  }, []);
  const onTncClicked = (cards, tncIndex) => {
    const urlValue = cards.tncUrl;
    const eventName = 'promo_tnc';
    if (urlValue) {
      const { HolidayModule } = NativeModules;
      HolidayModule.openWebView({
        url: urlValue,
        event: eventName,
        cmp: holidayLandingData.cmp,
      });
      trackLocalClickEvent({ eventName, eventNameOmni: `${eventName}_${tncIndex}` });
    }
  };
  const getMapObject = () => {
    return {
      holidayLandingData,
      availableHubs,
      userDepCity,
      destinationCityData,
      landingDataRefreshed,
    };
  };
  const genericOnPressHandlingLocal = useCallback((params) => {
    genericOnPressHandling({
      ...params,
      mapObject: getMapObject(),
    });
    updatePDTJourneyIdOnSearchChange({
      depCity: userDepCity,
      destCity: params?.card?.destination,
      paxDetails: defaultRoom,
    });
  }, []);
  const onCardClickDeeplinkHandlingLocal = (params) => {
    onCardClickDeeplinkHandling({ ...params, mapObject: getMapObject() });
  };
  const onDynamicPackageItemClickedLocal = (userPackageMeta, index) => {
    return onDynamicPackageItemClicked({
      userPackageMeta,
      packageIndex: index,
      landingDataRefreshed,
      landingResponse,
      holidayLandingData,
    });
  };

  const handleImageSearchBannerClick = () => {
    openImageSearch({ isFromImageSearchBanner: true});
  }
  const getSectionView = () => {
    let returnView = null;
    if (sectionsListToBeSupressed?.includes(item.sectionCode)) {
      return null;
    }
    switch (item.sectionCode) {
      case sectionCodes.MMT_BLACK:
        const { section, bottomSheet, mmtBlackPdtData } = getMmtBlackDetailsNodes(item?.cards?.[0]);
        if (!showMMTBlack() || !section || !section?.headerText || !section?.headerText.length) {
          returnView = [];
         break;
        }
        returnView = (
          <MembershipCard
            sectionDetails={section}
            containerStyles={styles.membershipCard}
            onKnowMorePress={() => onKnowMorePressMmtBlackMembership(bottomSheet, mmtBlackPdtData?.mmtBlackBucketDetail, section?.cta?.text)}
            mmtBlackPdtEvents={logHolidaysLandingPDTEvents}
            trackMemberShipLoadEvent={trackMmtBlackClickEvent}
            sectionOrder={item?.order}
            contentType={CONTENT_TYPES.LANDING}
            mmtBlackBucketDetail={mmtBlackPdtData?.mmtBlackBucketDetail}
            sendLoadEvents={false}
          />
        );
        break;
      case sectionCodes.PROMO:
        returnView = (
          <HolidayLandingPromoSection
            promoSections={item}
            onPromoItemClicked={onPromoItemClicked}
            onTncClicked={onTncClicked}
            viewState={viewState}
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.CATEGORY:
        returnView = (
          <HolidayLandingCategory
            item={item}
            onPress={genericOnPressHandlingLocal}
            trackEvent={trackCategoryEvent}
          />
        );
        break;
      case sectionCodes.HERO_BANNER:
        returnView = (
          <HolidayLandingCarouselImageBanner
            data={item}
            onPress={genericOnPressHandlingLocal}
            styles={StyleSheet.flatten(HbCss)}
            resizeMode="cover"
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.REASON_TO_BELIEVE_BANNER:
        returnView = (
          <HolidayLandingReasonToBelieve
            data={item}
            onPress={genericOnPressHandlingLocal}
            resizeMode="cover"
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.DID_YOU_KNOW_BANNER:
        returnView = (
          <HolidayDidYouKnowBanner
            data={item}
            onPress={genericOnPressHandlingLocal}
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.RECOMMENDATION_BANNER:
        returnView = (
          <HolidayLandingRecommendationBanner
            data={item}
            onPress={genericOnPressHandlingLocal}
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.PRESALES:
        if (showNewRVSSection()) {
          returnView = null;
        } else {
          returnView = (
            <Suspense fallback={<Loader />}>
              <HolidayQuotesSection
                data={item}
                onPress={genericOnPressHandlingLocal}
                key={generateSectionKey(item)}
                editQuery={editQuery}
                closeQuery={closeQuery}
                trackClickEvent={trackLocalClickEvent}
                refreshContent={refreshContent}
              />
            </Suspense>
          );
        }
        break;
      case sectionCodes.OCCASION_BANNER:
        returnView = (
          <HolidayLandingCarouselImageBanner
            data={item}
            onPress={genericOnPressHandlingLocal}
            styles={StyleSheet.flatten(OcbCss)}
            resizeMode="cover"
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.COLLECTION_BANNER:
        returnView = (
          <Suspense fallback={<Loader />}>
            <HolidayCollectionBanner
              data={item}
              onPressCard={genericOnPressHandlingLocal}
              onPressExplore={onCardClickDeeplinkHandlingLocal}
              key={generateSectionKey(item)}
            />
          </Suspense>
        );
        break;
      case sectionCodes.SME_SECTION:
        returnView = (
          <SMEHorizontalSection
            key={generateSectionKey(item)}
            data={item.cards}
            header={item.header}
            subHeader={item.subHeader}
            onCardClick={(profileId) =>
              HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.SME_DETAIL, { profileId })
            }
            style={{ paddingVertical: 12, backgroundColor: '#f6f6f6' }}
          />
        );
        break;
      case sectionCodes.IMAGE_SEARCH:
        const { landingEntryPoint = false } = enableSearchByImage();
        if(landingEntryPoint) {
          returnView = (
            <SearchByImageBannerEntryPoint
              handleUploadImageClick={handleImageSearchBannerClick}
            />
          );
        } else {
          returnView = null
        }
        break;
      case sectionCodes.RECENTLY_VIEWED_PACKAGES:
        if (showNewRVSSection()) {
          returnView = null;
        } else {
          returnView = (
            <Suspense fallback={<Loader />}>
              <HolidayLandingRecentlyPackageSection
                header={item?.header}
                subHeader={item?.subHeader}
                latestData={recentlySeenPackagesData}
                onPress={onDynamicPackageItemClickedLocal}
                notMMTAff={!isNonMMTAffiliate(holidayLandingData.aff)}
                isNotWG={true}
                viewState={viewState}
                sectionKey={generateSectionKey(item)}
                key={generateSectionKey(item)}
                trackClickEvent={trackLocalClickEvent}
                order={item.order}
              />
            </Suspense>
          );
        }
        break;
      case sectionCodes.LOGIN:
        returnView = (
          <HolidayLandingLoginStrip
            notMMTAff={!isNonMMTAffiliate(holidayLandingData.aff)}
            loggedInUser={loggedInUser}
            onPress={onLoginClicked}
            key={generateSectionKey(item)}
          />
        );
        break;
      case sectionCodes.AD:
        returnView = (
          <HolidayAdBanner
            pageName={AdBannerPlacement.HOLIDAY_LANDING_PAGE}
            containerStyle={AdCss.container}
          />
        );
        break;
      case sectionCodes.RNE:
        if (false) {
          returnView = (
            <ReferAndEarnEntryPoint
              data={item}
              loggedInUser={loggedInUser}
              trackClickEvent={trackLocalClickEvent}
            />
          );
        } else {
          returnView = null;
        }
        break;
      case sectionCodes.CONTINUE_YOUR_SEARCH:
        if (showNewCYSSection()) {
          returnView = <ContinueYourSearch data={item} pageName={LANDING_TRACKING_PAGE_NAME} />;
        } else {
          returnView = null;
        }
        break;
      case sectionCodes.PAYMENT_DROP_OFF:
        if (showNewRVSSection()) {
          returnView = (
            <RVSSectionV2
              data={item}
              itemData={recentlySeenPackagesData}
              pageName={LANDING_TRACKING_PAGE_NAME}
              trackLocalClickEvent={trackLocalClickEvent}
              trackPDTV3Event={logHolidaysLandingPDTEvents}
            />
          );
        } else {
          returnView = null;
        }
        break;
      default:
        returnView = null;
    }
    return returnView;
  };
  return getSectionView();
};
const mapStateToProps = (state) => ({
  ...state.holidaysLanding,
});
export default connect(mapStateToProps, null)(HolidayLandingSections);
