import { formatClickEventName, GI_PAGE_NAMES, sendGIEvents, formatPageLoadEventName } from '../../utils/ThirdPartyUtils'
const pageName = GI_PAGE_NAMES.LANDING;
export const trackLandingGILoadEvent = ({isError = false, giData = {}} = {}) => {
  sendGIEvents({
    eventName: formatPageLoadEventName({pageName}),
    data: {
      pageName: `${pageName}${isError ? '_error' : ''}`,
      googleTagParams : {...giData, "Page_Name": pageName},
    }
  })
}
export const trackLandingGIClickEvent = ({ eventName, ...rest } = {}) => {
  sendGIEvents({
    eventName: formatClickEventName({ string: eventName, pageName }),
    data: {
      pageName,
      ...rest,
    }
  })
}
