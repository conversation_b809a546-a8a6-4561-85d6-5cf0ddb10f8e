/* eslint-disable import/no-unresolved */
import React from 'react';
import PropTypes from 'prop-types';
import {Image, Platform, View, StyleSheet, Text} from 'react-native';
import fecha from 'fecha';
import {statusBarHeightForIphone, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {addDays, today, convertDateObject, compareDates} from '@mmt/legacy-commons/Helpers/dateHelpers';
import calendarImg from '@mmt/legacy-assets/src/calendar.webp';
import Card from '@mmt/legacy-commons/Common/Components/Card/index';
import searchWidgetStyles from '../SearchWidget/Components/SearchWidgetCss';
import {
  TOTAL_MONTHS,
  CALENDAR_MIN_DATE_DIFF,
} from '../SearchWidget/SearchWidgetConstants';
import HoldiaysMessageStrip from '../Common/Components/HolidaysMessageStrip';
import { isEmpty } from 'lodash';
import PageHeader from '../Common/Components/PageHeader';
import { paddingStyles } from '../Styles/Spacing';
import { holidayColors, iconColors } from '../Styles/holidayColors';
import { fontStyles } from '../Styles/holidayFonts';
import CalendarComponent from './CalendarComponent';
import PrimaryButton from '../Common/Components/Buttons/PrimaryButton';
import { getMonthMmm } from '@mmt/legacy-commons/Common/utils/dateTimeUtil';
import { DAYS, SUB_PAGE_NAMES } from '../HolidayConstants';
import HolidayDataHolder from '../utils/HolidayDataHolder';

class MmtHolidayCalender extends React.Component {
  constructor(props) {
    super(props);
    const {selectedDate, availableDates, isDefaultSelected} = props || {};
    const todayDate = today();
    const yearDifference = (selectedDate.getFullYear() - todayDate.getFullYear());
    const monthDifference = (yearDifference * TOTAL_MONTHS) + (selectedDate.getMonth() - todayDate.getMonth());
    this._monthDifference = monthDifference;
    this._minDate = fecha.format(addDays(todayDate, CALENDAR_MIN_DATE_DIFF), 'YYYY-MM-DD');
    this._maxDate = fecha.format(addDays(todayDate, 365), 'YYYY-MM-DD');
    this._selectedDate = fecha.format(selectedDate, 'YYYY-MM-DD');
    this._isDefaultSelected = isDefaultSelected;
    this.availableDates = availableDates;
    this.endDate = '';
    this.unavailableDates = [];

    if (this.availableDates && this.availableDates.length > 0) {
      const minStartDate = (this.availableDates
        .map(row => convertDateObject(row.start))
        .sort((a, b) => a - b))[0];
      const maxEndDate = (this.availableDates
        .map(row => convertDateObject(row.end))
        .sort((a, b) => b - a))[0];


      this.startDate = fecha.format(minStartDate, 'YYYY-MM-DD');
      this.endDate = fecha.format(maxEndDate, 'YYYY-MM-DD');
      this.filterUnavailableDates();
    } else {
      this.startDate = this._minDate;
    }

    this.startDate = this.props.minDate || this.startDate;

    // eslint-disable-next-line react/no-did-mount-set-state
    this.state = {
      isDefaultSelected: this._isDefaultSelected,
      minDate: this._minDate,
      selectedDate: this._selectedDate,
      selectedDateObj: props.selectedDate,
    };
    this._cellWidth = 40;
  }

  filterUnavailableDates = () => {
    const startDate = convertDateObject(this.startDate);
    const endDate = convertDateObject(this.endDate);
    for (let date = startDate; date <= endDate; date = addDays(date, 1)) {
      if (this.isDateExistsInRange(date) === false) {
        this.unavailableDates.push(fecha.format(date, 'YYYY-MM-DD'));
      }
    }
  }

  isDateExistsInRange = (date) => {
    let isSelectedDateValid = false;
    for (let i = 0; i < this.availableDates.length; i += 1) {
      const startDate = convertDateObject(this.availableDates[i].start);
      const endDate = convertDateObject(this.availableDates[i].end);

      if (compareDates(startDate, date) !== 1 && compareDates(date, endDate) !== 1) {
        isSelectedDateValid = true;
        break;
      }
    }
    return isSelectedDateValid;
  }

  _onDaySelected = () => {
  };

  _onDone = () => {
    const {
      isDefaultSelected,
    } = this.state;
    if (!isDefaultSelected) {
      this.props.onDone(this.state.selectedDateObj);
    }
  };

  onDateSelection = (dayObj) => {
    const constructDate = {
      year: dayObj.getFullYear(),
      month: dayObj.getMonth(),
      day: dayObj.getDate(),
    };
    this?.props?.onDayClicked?.(constructDate);
    this.setState({
      isDefaultSelected: false,
      selectedDate: fecha.format(dayObj, 'YYYY-MM-DD'),
      selectedDateObj: dayObj
    });
  }
  componentDidMount() {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.CALENDAR)
  }
  componentWillUnmount() {
    HolidayDataHolder.getInstance().clearSubPageName()
  }

  render() {
    const {
      selectedDate,
      isDefaultSelected,
      selectedDateObj,
    } = this.state;
    const {onCalendarBack,message } = this.props;


    let markedDateProps = {
      [selectedDate]: {
        customStyles: {
          container: {
            backgroundColor: holidayColors.primaryBlue,
            borderRadius: 4,
            margin: 0,
            padding: 0,
          },
          text: {
            ...fontStyles.labelSmallBold,
            color: holidayColors.white,
          },
        },
      },
    };
    if (isDefaultSelected) {
      markedDateProps = {};
    }


    this.unavailableDates?.forEach(row => markedDateProps[row] = {disabled: true, disableTouchEvent: true});
    const monthName = getMonthMmm(selectedDateObj);
    return (<View style={searchWidgetStyles.calendarContainer}>
      <Card style={{
        ...Platform.select({
          ios: {
            marginTop: -statusBarHeightForIphone,
            paddingTop: statusBarHeightForIphone,
            marginBottom: 0
          },
          android: {
            marginVertical: 0,
            padding: 0,
          },
          web: {
            marginVertical: 0,
            padding: 0,
          },
        }),
        marginHorizontal: 0,
      }}
      elevation = {0}
      >
        <PageHeader
            showBackBtn
            showShadow
            title={`Select ${this.props.headerTitle || 'Date'}`}
            onBackPressed={onCalendarBack}
            containerStyles={paddingStyles.pt2}
            headerWrapperStyle={styles.headerWrapperStyle}
          />
          {!isEmpty(message) && (
            <HoldiaysMessageStrip
              message={message}
              shouldShow
              iconUrl
              containerStyles={styles.stripContainerStyles}
              icon={this.props?.icon}
              iconStyle={styles.stripIconStyle}
            />
          )}
      </Card>

      <CalendarComponent
        selectedDate={selectedDate}
        startDate={this.startDate}
        onDaySelection={this.onDateSelection}
        unavailableDates={this.unavailableDates}
        markedDateProps={markedDateProps}
        endDate={this.endDate}
        maxDate={this._maxDate}
        isDefaultSelected={isDefaultSelected}
      />

      <View style={[styles.footerContainer, (isDefaultSelected ? {backgroundColor: holidayColors.white} : {})]}>
        <View style={styles.footerSection}>
          <View style={styles.dateInput}>
            <Image style={styles.image} source={calendarImg} />
            <View style={styles.textContainer}>
              <Text style={styles.title}>STARTING DATE</Text>
              <View style={styles.dateContainer}>
                {isDefaultSelected ?
                  <Text style={styles.addDateText}>Add travel date</Text> :
                  <>
                    <Text style={styles.dayText}>{selectedDateObj?.getDate()} {monthName}</Text>
                    <Text style={styles.yearText}>{DAYS?.[selectedDateObj?.getDay()]}, {selectedDateObj?.getFullYear()}</Text>
                  </>
                }
              </View>
            </View>
          </View>
          <PrimaryButton
            buttonText="DONE"
            handleClick={this._onDone}
            btnContainerStyles={styles.accept}
            isDisable={isDefaultSelected}
          />
        </View>
      </View>
    </View>);
  }
}

const styles = StyleSheet.create({
  footerContainer: {
    width: "100%",
  },
  footerSection: {
    backgroundColor: holidayColors.white,
    width: "100%",
    ...paddingStyles.pa16,
    elevation: 8,
    height: 140,
  },
  dateInput: {
    flexDirection: 'row',
    borderColor: holidayColors.midLightBlue,
    height: 55,
    borderWidth: 1,
    borderRadius: 8,
    ...paddingStyles.pl16,
    alignItems: 'center',
    marginBottom: 16,
  },
  headerWrapperStyle: {
    shadowColor: 'transparent',
    shadowOffset: {
      width: 0,
      height: 0
    },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  stripContainerStyles: {
    backgroundColor: holidayColors.fadedYellow,
    marginTop: 0
  },
  stripIconStyle: {
    width: 17,
    height: 15
  },
  image: {
    width: 15,
    height: 15,
    marginRight: 15,
    tintColor: iconColors.searchFilterIcons,
  },
  title: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    fontFamily: fonts.regular,
  },
  dateContainer: {
    flexDirection: "row",
    marginTop: 3,
    alignItems : "flex-end"
  },
  dayText: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    fontFamily: fonts.regular,
  },
  yearText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    fontFamily: fonts.regular,
    marginLeft: 4,
  },
  addDateText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    fontFamily: fonts.regular,
  },
});

MmtHolidayCalender.propTypes = {
  selectedDate: PropTypes.instanceOf(Date).isRequired,
  onDone: PropTypes.func.isRequired,
};

export default MmtHolidayCalender;
