export const overlayActionTypes = {
  SHOW_OVERLAYS: 'show_overlays',
  HIDE_OVERLAYS: 'hide_overlays',
  CLEAR_OVERLAYS:'clear_overlays',
};


export const showReviewOverlay = (key, data = {}) => dispatch => {
  dispatch({
    type: overlayActionTypes.SHOW_OVERLAYS,
    key: key,
    data: data,
  });
};

export const hideReviewOverlays = (keys = []) => dispatch => {
  dispatch({
    type: overlayActionTypes.HIDE_OVERLAYS,
    overlays: keys,
  });
};
export const clearReviewOverlays = () => dispatch => {
  dispatch({
    type: overlayActionTypes.CLEAR_OVERLAYS,
  });
};
