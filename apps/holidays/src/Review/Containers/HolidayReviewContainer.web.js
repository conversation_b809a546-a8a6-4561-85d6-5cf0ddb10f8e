import React from 'react';
import {connect} from 'react-redux';
import {
  fetchReviewData,
  validateCoupon,
  fetchPersuasionDataActions,
  selectPaymentOption,
  doPrePaymentDynamic,
  validateZC,
  toggleComponentFailure,
} from '../Actions/HolidayReviewActions';
import PhoenixReviewContainer from '../../PhoenixReview/Container/PhoenixReviewContainer';
const mapStateToProps = state => ({
  ...state.holidaysReview,
});

const mapDispatchToProps = dispatch => ({
  fetchReviewData: (holidayReviewData, roomDetails) =>
    dispatch(fetchReviewData(holidayReviewData, roomDetails)),
  validateCoupon: (coupon, isSelected, action, couponToBeRemoved) => dispatch(validateCoupon(coupon, isSelected, action, couponToBeRemoved)),
  fetchPersuasionDataActions: () => dispatch(fetchPersuasionDataActions()),
  selectPaymentOption: option => dispatch(selectPaymentOption(option)),
  doPrePaymentDynamic: (userDetails, user,presales) => dispatch(doPrePaymentDynamic(userDetails, user,presales)),
  validateZC:(dynamicPackageId, action, zcType) => dispatch(validateZC(dynamicPackageId, action, zcType)),
  toggleComponentFailure:(componentFailureData) => dispatch(toggleComponentFailure(componentFailureData)),
});

class HolidaysReviewContainer extends React.Component {
  ReviewPageView;
  constructor(props) {
    super(props);
    this.ReviewPageView = require('../../PhoenixReview/Container/PhoenixReviewContainer').default;
  }
  render() {
    return <PhoenixReviewContainer {...this.props} />;
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(HolidaysReviewContainer);
