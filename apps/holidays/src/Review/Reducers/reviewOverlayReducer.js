import { overlayActionTypes } from '../Actions/reviewOverlayActions';

const initialState = {
  overlayMap: {},
  overlayDataMap: {},
};

const holidaysReviewOverlays = (state = initialState, action) => {
  let newOverlayMap = { ...state.overlayMap };
  let newOverlayDataMap = { ...state.overlayDataMap };
  switch (action.type) {
    case overlayActionTypes.SHOW_OVERLAYS:
      newOverlayMap[action.key] = true;
      newOverlayDataMap[action.key] = action.data;
      return {
        overlayMap: newOverlayMap,
        overlayDataMap: newOverlayDataMap,
      };
    case overlayActionTypes.HIDE_OVERLAYS:
      action.overlays.forEach((item, index) => {
        delete newOverlayMap[item];
        delete newOverlayDataMap[item];
      });
      return {
        overlayMap: newOverlayMap,
        overlayDataMap: newOverlayDataMap,
      };
    case overlayActionTypes.CLEAR_OVERLAYS:
      return initialState;
    default:
      return state;
  }
};

export default holidaysReviewOverlays;
