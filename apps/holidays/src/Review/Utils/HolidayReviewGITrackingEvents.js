import { formatClickEventName, GI_PAGE_NAMES, sendGIEvents, formatPageLoadEventName } from '../../utils/ThirdPartyUtils'

export const trackReviewGILoadEvent = ({ isError = false, isFPHReview = false, giData = {} } = {}) => {
  const pageName = isFPHReview ? GI_PAGE_NAMES.FPHREVIEW : GI_PAGE_NAMES.REVIEW
  sendGIEvents({
    eventName: formatPageLoadEventName({pageName}),
    data: {
      pageName: `${pageName}${isError ? '_error' : ''}`,
      googleTagParams : {...giData, "Page_Name": pageName},
    }
  })
}
export const trackReviewGIClickEvent = ({ eventName, isFPHReview = false, ...rest } = {}) => {
  const pageName = isFPHReview ? GI_PAGE_NAMES.FPHREVIEW : GI_PAGE_NAMES.REVIEW

  sendGIEvents({
    eventName: formatClickEventName({ string: eventName ,pageName }),
    data: {
      pageName,
      ...rest,
    }
  })
}
