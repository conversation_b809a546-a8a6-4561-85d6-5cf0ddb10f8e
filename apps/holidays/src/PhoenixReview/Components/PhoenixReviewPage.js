import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeviceEventEmitter,
  findNodeHandle,
  FlatList,
  Image,
  NativeModules,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import LinearGradient from 'react-native-linear-gradient';
import _, { add, cloneDeep, isEmpty } from 'lodash';
import fecha from 'fecha';
import { colors, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  fetchReviewErrorMessage,
  getAddonCode,
  getDefaultRoom,
  getPackagePrice,
  getPriceChangeDifference,
  isPanRequired,
  isPanWithTCSRequired,
  showPriceChange as showPriceChangeUtil,
  trackReviewErrorLocalChatClickEvent,
  trackReviewLocalClickEvent,
} from '../../Review/Utils/HolidayReviewUtils';
import { fetchReviewContent, getMmtBlackReviewPagePopupFlag, setMmtBlackReviewPagePopupFlag } from '../../utils/HolidayNetworkUtils';
import {
  createChatID,
  createRandomString,
  getPageName,
  getPaxConfig,
  isAndroidClient,
  isLuxeFunnel,
  isMobileClient,
  isRawClient,
  openGenericDeeplink,
  saveHolMeta,
  startReactChat,
} from '../../utils/HolidayUtils';
import {
  CLOSE_INTERVENTION,
  PART_PAYMENT_OPTION,
  PDTConstants,
  REVIEW_PAGE_NAME,
  REVIEW_PDT_PAGE_NAME,
  SPECIAL_REQUEST_OVERLAY,
} from '../../Review/HolidayReviewConstants';
import {
  getEvar108ForReview,
  trackDeeplinkRececived,
  trackReviewPageLoadEvent,
} from '../../utils/HolidayTrackingUtils';
import {
  createReviewFabData,
} from '../../Common/Components/Widgets/FabAnimation/FabAnimationUtils';
import {
  DOM_BRANCH,
  EVENTS,
  FUNNEL_ENTRY_TYPES,
  HLD_PAGE_NAME,
  HOL_REQUEST_TYPE,
  PDT_PAGE_EXIT_EVENT,
  PDT_RAW_EVENT,
  TAX_COLLECT_TCS_TYPES,
  AFFILIATES,
  FUNNELS,
} from '../../HolidayConstants';
import memoize from 'memoize-one';
import { createSightSeeingDayPlanDataMap, calculateToDateTime } from '../../PhoenixDetail/Utils/PhoenixDetailUtils';
import { deepLinkParams, itineraryUnitTypes, OBT_BRANCH } from '../../PhoenixDetail/DetailConstants';
import { getUserDetails, isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import {
  addTravellerFormLeaf,
  checkIfFlightIsOvernight,
  createSavedTravellerInformation,
  formDynamicApiDataContact,
  getPackageAddonsProp1,
  getTotalTravellers,
  initializeTravellerFormData,
} from '../Utils/HolidayReviewUtils';
import ReviewSection, { STATUS_COMPLETE, STATUS_DONE, STATUS_INCOMPLETE } from './ReviewSection';
import {
  ADD_TRAVELLER_ERROR,
  DEFAULT_BOTTOM,
  FAILURE_TOAST_BOTTOM,
  FORM_FILL_STATUS,
  INFORMATION_STRIP_BOTTOM,
  MMT_BLACK_ATTACH_CARD_CALL,
  PDTConstantsNew,
  PRICE,
  PRICE_CHANGE_CARD_BOTTOM,
  REVIEW_FOOTER_BTN_TEXT,
  SECTIONS,
  SECTIONS_DATA,
  TCS_VERSIONS,
} from '../Utils/HolidayReviewConstants';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { sectionCodes } from '../../LandingNew/LandingConstants';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import { setFirebaseTrackingForReview } from '../../utils/FirebaseUtils/FirebaseTracking';
import { fetchQueueIdentifier } from '../../utils/HolidayNetworkUtils';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { DATE_FORMAT_FLIGHT } from '../../PhoenixDetail/Utils/FlightUtils';
import {
  getPhoenixReviewSectionsExpanded,
  getPhoenixReviewSectionsOrder,
  getPokusforReviewTcsV2Section,
  getPokusforReviewUpdateSection,
  getReviewpagePopupInterval,
  showHolAgentOnDetailAndReviewPage,
  showInsuranceSection,
  showMMTBlack,
  showTravelPlex,
} from '../../utils/HolidaysPokusUtils';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import {
  getPokusConfigWaitingPromise,
  initAbConfigUsingPokusHLD,
} from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import url from 'url';
import { clearReviewComponentVisit, sectionTrackingPageNames } from '../Utils/ReviewVisitTracking';
import { getHolidayDetailObjectForMimaPreSales } from '../../MimaPreSales/utils/MimaPreSalesUtils';

import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import BranchIOTracker from '../../utils/HolidayBranchSDKEventTracker';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import HolidayDeeplinkParser, { ReviewParams } from '../../utils/HolidayDeeplinkParser';


/* Components */
import AddRequestOverlayDynamic from '../../PhoenixReview/Components/AddRequestOverlayDynamic';
import ComponentFailureToastMessage from '../../Common/Components/ComponentFailurePopUp/ComponentFailureToastMessage';
import ComponentFailurePopUp from '../../Common/Components/ComponentFailurePopUp';
import HolidayDetailLoader from '../../PhoenixDetail/Components/HolidayDetailLoader';
import PhoenixReviewOverlays from '../Components/ReviewOverlays';

/* Icons */


/* Components */
import InformationStrip from './InformationStrip';
import PageHeader from '../../Common/Components/PageHeader';
import FilterLoader from '../../SearchWidget/Components/FilterLoader';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import PackageInfo from './PackageInfo';
import PackageAddOns from './PackageAddons';
import CouponsOffers from './Coupons/CouponsOffers';
import Cancellation from './Cancellation';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import HolidayReviewError from './HolidayReviewError';
import HolidayFabAnimationContainer from '../../Common/Components/Widgets/FabAnimation/FabAnimationContainer';
import PackageUpdate from './PackageUpdate';
import PriceChangeCard from './PriceChangeCard';
import PackageInclusions from './PackageInclusions';
import ReviewFooter from './PageFooter';
import TravellerDetails from './TravellerSection/TravellerDetails';
import TravellerDetailsSection from './TravellerDetails';
import TCSbannerStrip from './TCSbannerStrip';
import TCSbannerBox from './TCSbannerBox';
import withIntervention from '../../Common/Components/Interventions/withIntervention';
import BookingInformation from './BookingInformation';
import ViatorMessageStrip, { VIATOR_MESSAGE_STATE } from './ViatorMessageStrip';
import TravelInsuranceCard from '../../Travelnsurance/reviewcard/TravelInsuranceCard';
import NotOptedBottomSheet from '../../Travelnsurance/bottomsheets/NotOptedBottomSheet';
import OldTravellerBottomSheet from '../../Travelnsurance/bottomsheets/OldTravellerBottomSheet';
import HolidaysReviewTcs from './TCS';
import TcsPanWidget from './TCS/TcsPanWidget';
import {
  PAN_TCS_WIDGET_TRACKING,
  TCS_AMOUNT,
  TCS_AMOUNT_CALCULATED,
  TCS_PAN_WIDGET_TAG,
} from './TCS/TcsPanConstants';
import {
  createPDTProductObject,
  getTravellerInfo,
  initReviewPDTObj,
  logHolidayReviewPDTClickEvents,
} from '../Utils/HolidayReviewPDTTrackingUtils';
import { showFabAnimationExtended } from '../../utils/HolidaysPokusUtils';
import { EVENT_NAMES , PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import VPPCard from '../../Common/Components/VisaProtectionPlan/VPPCard';
import VPPBottomSheet from '../../Common/Components/VisaProtectionPlan/VPPBottomSheet';
import { handleCtaClick, vppCaptureReviewClickEvents } from '../../Common/Components/VisaProtectionPlan/VPPUtils';
import {
  VPP_TRACKING_ACTIONS,
  VPP_TRACKING_EVENT,
  VPP_TRACKING_PAGES,
} from '../../Common/Components/VisaProtectionPlan/VPPConstant';
import MMTBlackBottomSheet from '../../Common/Components/Membership/ReviewBottomSheet';
import BottomSheet from '../../Common/Components/BottomSheet';
import PhoenixReviewPageToastV2 from '@mmt/holidays/src/PhoenixReview/Components/PhoenixReviewPageToastV2';
import DownloadApp from '../../Common/Components/DownloadApp';
import { sendMMTGtmEvent } from '../../utils/ThirdPartyUtils';
import { hideCommonOverlay, showCommonOverlay } from '../../Common/Components/CommonOverlay/CommonOvelayAction';
import CommonOverlay from '../../Common/Components/CommonOverlay';
import { hideReviewOverlays, showReviewOverlay } from '../../Review/Actions/reviewOverlayActions';
import withBackHandler from '../../hooks/withBackHandler';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { getFabBorderStyle, LUXE_CTA_GRADIENT_COLORS } from '../../utils/CtaUtils';
import {
  calculateFabPosition,
} from '../Utils/FabPositioningUtils';
import { getReviewPDTObj } from '../Utils/HolidayReviewPDTDataHolder';
import HolidayPDTMetaIDHolder from '../../utils/HolidayPDTMetaIDHolder';
import ActionBottomsheet from '../../Common/Components/VisaProtectionPlan/VPPPopus/ActionBottomsheet';
export const LOGIN_EVENT_REVIEW = 'login_event_review';
const downIcon = require('@mmt/legacy-assets/src/double-down-arrow.webp');
const travellerPillWidth = {
 withPersuasion : PRICE_CHANGE_CARD_BOTTOM ,
    //isAndroidClient() ? 130 : 55, will uncomment once persuasion is done from backend
 withoutPersuasion : DEFAULT_BOTTOM,
};

/* Listners Variables */
let paymentEventReviewListener;
let loginEventReviewListener;
let widgetTCSListener;
class PhoenixReviewPage extends BasePage {
  constructor(props) {
    super(props);
    PerformanceMonitorModule.start('PhoenixReviewPage');
    this.timeNow = 0;
    this.closeTravellerDetails = React.createRef();
    this.initUserData();
    this.hasTrackedLoad = false;
    if (this.props[ReviewParams.fromReviewDeeplink]) {
      this.holidayReviewData =
        HolidayDeeplinkParser.parseReviewPageDeeplink(
          this.props.query, // add query in native code
          true,
        ) || {};

      this.holidayReviewData.roomDetails = getDefaultRoom();
      if (this.holidayReviewData.cmp) {
        trackDeeplinkRececived({ [TRACKING_EVENTS.M_V81]: this.holidayReviewData.cmp });
      }
    } else if (this.props.requestId) {
      this.holidayReviewData = {};
      this.holidayReviewData.quoteRequestId = this.props.requestId;
      this.holidayReviewData.roomDetails = getDefaultRoom();
      this.holidayReviewData.pt = this.props.pt;
      this.holidayReviewData.aff = this.props.aff;
      this.holidayReviewData.docId = this.props?.docId || ''
    } else if (this.props.dynamicPackageId) {
      this.holidayReviewData = {};
      this.holidayReviewData.dynamicPackageId = this.props.dynamicPackageId;
      this.holidayReviewData.roomDetails = getDefaultRoom();
      this.holidayReviewData.pt = this.props.pt;
      this.holidayReviewData.aff = this.props.aff;
      this.holidayReviewData.requestType = this.props?.requestType || '';
      this.holidayReviewData.docId = this.props?.docId || ''
    } else if (this.props.reviewPackageId) {
      this.holidayReviewData = {};
      this.holidayReviewData.reviewPackageId = this.props.reviewPackageId;
      this.holidayReviewData.roomDetails = getDefaultRoom();
      this.holidayReviewData.pt = this.props.pt;
      this.holidayReviewData.aff = this.props.aff;
      this.holidayReviewData.docId = this.props?.docId || ''
    } else {
      this.holidayReviewData = cloneDeep(this.props.holidayReviewData);
    }

    this.state = {
      showPriceChange: true,
      listData: [],
      formSectionResponses: [],
      userDetails: null,
      userDetailsDynamic: this.getInitialUserDetailsDynamic(),
      savedTravellerList: [],
      completedTravellersList: [],
      showTCSErrorStrip: false,
      tcsTnCAcknowledged: this.showReviewTCSv2,
      showComponentFailureToast: false,
      popup: '',
      selectedCollapsedForm: {},
      collapsedFormData: {},
      isSelectedCheckboxTCS: false,
      showOvernightPopup: false,
      paymentPageData: {},
      errorsList: {
        [ADD_TRAVELLER_ERROR]: false,
      },
      travellerPill: true,
      showOnce: false,
      isConfigAvailable: false,
      showTcsBottomSheet: false,
      showPanError: false,
      fareBreakupVisibilityForTcs: false,
      tncCheckBoxSelected: true,
      fabTextShrinked: false,
      showTravellerDetailPage: false,
      isActionBottomSheetVisible:false,
      activeCardData:[]
    };
    this.TCSbannerStripRef = null;
    this.maxSteps = 5;
    // this.showTcsBanner = true; //true shows TCS Banner
    this.isLoggedInUser = false;
    this.roomDetails = this?.holidayReviewData?.roomDetails || {};
    this.isReloaded = false;
    this.isStartPaymentAfterInsurance = false;
    this.isShowErrorPopupAfterInsurance = false;

    this.travellerFormData = {};
    this.travellerSection = null;
    this.travellerList = [];
    this.showReviewTCSv2 = getPokusforReviewTcsV2Section();
    this.overnightCondition = checkIfFlightIsOvernight({
      detailData: this.props.reviewData?.reviewDetail,
    });
    this.holidayReviewData.trackingData =
      this.props.reviewData?.holidayReviewData?.trackingData || {};
    this.reviewBottomSheet = false;
    saveHolMeta(this.holidayReviewData.isWG, this.holidayReviewData.aff, this.holidayReviewData.pt, this.holidayReviewData.cmp);
    this.travelPlexAttr4 = null;
  }
  getInitialUserDetailsDynamic = () => ({ above18CheckboxValue: true, activeRadio: 0 });
  callbackHandler = (type, params) => {
    switch (type) {
      case CLOSE_INTERVENTION:
        this.props.unmountIntervention();
        break;
      default:
        break;
    }
  };

  toggleTncCheckBox = () => {
    this.setState((prevState) => ({
      tncCheckBoxSelected: !prevState.tncCheckBoxSelected,
    }));
  };

  goBack = () => {
    const { popup, showTcsBottomSheet, fareBreakupVisibilityForTcs } = this.state || {};
    if (showTcsBottomSheet) {
      this.setTcsBottomSheetVisibility(false);
      return true;
    }

    if (fareBreakupVisibilityForTcs) {
      this.setFareBreakupVisibilityForTcs(false);
      return true;
    }

    if (this?.props?.leaveIntent?.toUpperCase() === 'Y' && popup === '') {
      this?.props?.close();
      {
        /* to handle intervention on back press */
      }
      return true;
    } else {
      const quoteReqId =
        this.holidayReviewData.quoteRequestId && this.holidayReviewData?.fromDeeplink;
      if (this.props.requestId || this.props.reviewPackageId || quoteReqId) {
        if (Platform.OS === 'ios') {
          ViewControllerModule.popViewController(this.props.rootTag);
        } else {
          BackHandler.exitApp();
        }
        return true;
      } else if (this.props.requestType === HOL_REQUEST_TYPE.DROP_OFF) {
        const detailDeeplink =
          this?.props?.reviewData?.reviewDetail?.metadataDetail?.backActionURL || '';
        const holidaysDetailData = HolidayDeeplinkParser.parseDetailPageDeeplink(detailDeeplink);
        HolidayNavigation.pop();
        if(!isEmpty(holidaysDetailData)){
          HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, { holidaysDetailData })
        }
        return true;
      } else if (this.props.DL && !isRawClient()) {
        BackHandler.exitApp();
        return true;
      } else {
        this.props.toggleComponentFailure(null);
        HolidayNavigation.pop();
        trackReviewLocalClickEvent(PDTConstants.BACK, '', this.props.reviewData);
      }
      return true;
    }
  }
  setTckAcknowledgedValue = () => {
    const isTCSAcknowledged = this.showReviewTCSv2 || this.getIsTCSAcknowledged();
    this.setState({
      tcsTnCAcknowledged: isTCSAcknowledged,
    })
  }
  componentDidUpdate(prevProps,prevState){
    if (!prevProps?.reviewData && this.props?.reviewData){
      this.setState({
        listData: this.checkIsPackageAddonSection(),
      });
      this.isReloaded = true;
      this.setTckAcknowledgedValue();
    } else if (
      prevProps?.reviewData &&
      this.props?.reviewData &&
      prevProps?.reviewData != this.props?.reviewData
    ) {
      this.overnightCondition = checkIfFlightIsOvernight({ detailData : this.props.reviewData?.reviewDetail });
      this.setTckAcknowledgedValue();
      this.setState({
        listData: this.checkIsPackageAddonSection(),
      });
      this.isReloaded = true;
    }
    if (this.isReloaded) {
      this.isReloaded = false;
      const countdownEndTime =
        this?.props?.reviewData?.reviewDetail?.metadataDetail?.countdownEndTime;
      if (countdownEndTime) {
        this.timeNow = getNewDate(new Date()).getTime();
        const counttime = fecha.parse(countdownEndTime, DATE_FORMAT_FLIGHT).getTime();
        const timeLeft = counttime - this.timeNow;
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
          //this.reloadReview() {to-do undo comment}
        }, timeLeft);
      }
    }
    if (prevProps?.packagePersuasionSection != this.props.packagePersuasionSection) {
      this.setState({
        showPriceChange: true,
      });
    }
    if(showInsuranceSection()){
    if(prevProps.validateResponse !== this.props?.validateResponse){
      const validateResponseObj = this.props?.validateResponse;
      const isToShowOldTravellerBottomSheet = this.props?.insuranceAddonDetail && this.props?.insuranceAddonDetail?.addons?.length > 0 && this.props?.validateResponse?.addonAction === "REMOVE_ADDON"

      if (isToShowOldTravellerBottomSheet) {
        const insurance = {
          id: validateResponseObj?.addonId,
          addonSubType: validateResponseObj?.addonSubtype,
          isSelected: true,
        };
        this.selectInsurance(insurance, false, true);
        this.isShowErrorPopupAfterInsurance = true;
      } else {
        this.setState({
          isToShowInsuranceAddOnBottomSheet:
            this.props?.insuranceAddonDetail &&
            this.props?.insuranceAddonDetail?.addons?.length > 0 &&
            this.props?.validateResponse?.addonAction === 'VIEW_ADDON',
          isToShowOldTravellerBottomSheet: isToShowOldTravellerBottomSheet,
        });
      }
    }

    if(prevProps.insuranceAddOn !== this.props.insuranceAddOn){
      this.selectInsurance(this.props.insuranceAddOn, false, true)
    }

      if (
        prevProps.validateResponse !== this.props?.validateResponse &&
        this.props?.validateResponse?.addonAction === 'CONTINUE'
      ) {
        this.setState({
          reviewPopUp: true,
        });
      }
      if (
        this.isStartPaymentAfterInsurance &&
        prevProps.insuranceAddonDetail !== this.props?.insuranceAddonDetail
      ) {
        this.setState({
          reviewPopUp: true,
        });
        this.isStartPaymentAfterInsurance = false;
      } else if (
        this.isShowErrorPopupAfterInsurance &&
        prevProps.insuranceAddonDetail !== this.props?.insuranceAddonDetail
      ) {
        this.setState({
          isToShowOldTravellerBottomSheet: true,
        });
        this.isShowErrorPopupAfterInsurance = false;
      }
    }
    if(!this.hasTrackedLoad && !isEmpty(this.props?.reviewData)){
      setTimeout(() => {
      this.trackPDTLoadAndExitEvent(PDT_EVENT_TYPES.pageRenedered, EVENT_NAMES.PAGE_RENEDERED);
    }, 500);
      this.hasTrackedLoad = true;
    }
  }
  onBackClick=()=>{
    return this.goBack();
  }

  trackPDTLoadAndExitEvent = (type,eventValue) => {
    const pdtProductObject = createPDTProductObject(this.props);
    logHolidayReviewPDTClickEvents({
      actionType: type,
      value: eventValue,
      compData: pdtProductObject.event_detail,
      addonsDetails: pdtProductObject.addon_details,
    });
  };

  componentWillMount() {
    this.updateMmtBlackBottomSheetCacheValue();
    paymentEventReviewListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(EVENTS.REVIEW.PAYMENT_EVENT_REVIEW, this.onPaymentThankyou);
  }

  updateMmtBlackBottomSheetCacheValue = async () => {
    const showAutoMmtBlackPopup = await getMmtBlackReviewPagePopupFlag();
    const popupInterval = getReviewpagePopupInterval();
    if (showAutoMmtBlackPopup && showMMTBlack() && this.props.mmtBlackDetail?.bottomSheet) {
      this.popuptimer= setTimeout(() => {
        this.setState({
          showReviewPopUp: true,
        });
      }, popupInterval);

    }
    setMmtBlackReviewPagePopupFlag(false);
  };
  componentWillMount() {
    this.updateMmtBlackBottomSheetCacheValue();
    BackHandler.addEventListener(HARDWARE_BACK_PRESS, this.goBack);
    paymentEventReviewListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(EVENTS.REVIEW.PAYMENT_EVENT_REVIEW, this.onPaymentThankyou);
  }

  onPaymentThankyou = (paymentResp) => {
    if (paymentResp && paymentResp.data) {
      const { reviewDetail = {} } = this.props.reviewData || {};
      const { metadataDetail = {} } = reviewDetail || {};
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.THANK_YOU, {
        paymentResponse: isAndroidClient()
          ? JSON.parse(JSON.parse(paymentResp.data).response)
          : JSON.parse(paymentResp.data),
        pageDataMap: this.props.reviewData.pageDataMap,
        isWG: this.holidayReviewData.isWG,
        tagDestination: this.holidayReviewData.tagDestination,
        source: this.holidayReviewData.source,
        ticketSource: metadataDetail?.trackingInfo?.ticketSource || '',
        pdtProductObject: createPDTProductObject(this.props),
      });
    }
  };

  updateUserDetailsMasterDynamic = (userDetails, userDetailsDynamic) => {
    this.setState({
      userDetails,
      userDetailsDynamic,
    });
  };

  reloadReview = async () => {
    //hide toast and error messages before reload
    this.setState({ showComponentFailureToast: false });
    this.props.toggleComponentFailure(null);
    await this.loadReview();
  };

  /* Function to check if TCS section needs to been shown or not in both old or new view */
  getShowTCSSection = (version) => {
    const { reviewData, additionalDetail } = this.props;
    const branch = reviewData?.reviewDetail?.metadataDetail?.branch;
    const { showTcsSection = false } = additionalDetail?.tcsSection || {};
    if (!branch || !showTcsSection || branch !== OBT_BRANCH) {
      return false;
    }
    let showConditionForTCSVersion = false;
    switch (version) {
      case TCS_VERSIONS.V1:
        showConditionForTCSVersion = !this.showReviewTCSv2;
        break;
      case TCS_VERSIONS.V2:
        showConditionForTCSVersion = this.showReviewTCSv2;
        break;
      default:
        showConditionForTCSVersion = false;
    }
    return showConditionForTCSVersion;
  };

  /* Function return values to tcsACK to be true if we are not showing ACK Section in TCS Section */
  getIsTCSAcknowledged = () => {
    const { required = false } = this.props.additionalDetail?.tcsSection?.acknowledgement || {};
    return !required;
  };
  async initAb() {
    if (isMobileClient()) {
      try {
        await getPokusConfigWaitingPromise(5000);
      } catch (error) {
        console.log('Pokus error');
      }
    } else {
      await initAbConfigUsingPokusHLD();
    }
    this.isSectionsExpanded = getPhoenixReviewSectionsExpanded();
    this.showReviewTCSv2 = getPokusforReviewTcsV2Section();
    this.showNewUpdateSection = getPokusforReviewUpdateSection();
    const isTCSAcknowledged = this.showReviewTCSv2 || this.getIsTCSAcknowledged();
    this.setState({
      tcsTnCAcknowledged: isTCSAcknowledged,
      isConfigAvailable: true,
    });
  }

  async componentDidMount() {
    super.componentDidMount();
    await this.initAb();
    await HolidayDataHolder.getInstance().setSubFunnel();
    HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.ONLINE);
    initReviewPDTObj({ reviewData: this.props.reviewData });
    this.props.toggleComponentFailure(null);
    loginEventReviewListener = DeviceEventEmitter && DeviceEventEmitter.addListener(LOGIN_EVENT_REVIEW, this.onLoginEventReceived);
    widgetTCSListener = DeviceEventEmitter.addListener(PAN_TCS_WIDGET_TRACKING, this.handleTCSWidgetTracking);

    // Fetch attr4 data for TravelPlex
    try {
      this.travelPlexAttr4 = await fetchQueueIdentifier('review');
    } catch (error) {
      console.log('Error fetching TravelPlex attr4 data:', error);
    }

   await HolidayPDTMetaIDHolder.getInstance().setPdtId();
   logHolidayReviewPDTClickEvents({
    actionType: PDT_EVENT_TYPES.pageEntry,
    value: EVENT_NAMES.PAGE_ENTRY,
    shouldTrackToAdobe:false
  });
    if (!this.props.fromDetails) {
      await this.loadReview();
      const { reviewData = {} } = this.props || {};
      const { reviewDetail = {} } = reviewData || {};
      const body = {
        packageDetail: {
          dynamicId: this?.holidayReviewData?.dynamicPackageId,
          packageInclusionsDetail: reviewDetail?.inclusionsDetail,
        },
      };
      if (!this.props.fromOldDetailPage) {
        await this.props.fetchPackageContent(body, true);
      }
      this.memoizedPrepareDayPlanSections(this.props.packageContent);
    } else if (isEmpty(this?.props?.reviewData?.reviewDetail?.metadataDetail)) {
      this.reloadReview();
      this.fetchFlightDetails();
      this.memoizedPrepareDayPlanSections(this.props.packageContent);
    } else {
      PerformanceMonitorModule.stop();
      this.setState({
        listData: this.checkIsPackageAddonSection(),
      });

      this.timeNow = getNewDate(new Date()).getTime();
      const countdownEndTime =
        this?.props?.reviewData?.reviewDetail?.metadataDetail?.countdownEndTime;
      if (countdownEndTime) {
        const counttime = fecha.parse(countdownEndTime, DATE_FORMAT_FLIGHT).getTime();
        const timeLeft = counttime - this.timeNow;
        this.isReloaded = false;
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
          this.reloadReview();
        }, timeLeft);
      }
      this.fetchFlightDetails();

      this.memoizedPrepareDayPlanSections(this.props.packageContent);
    }

    let cmpValue = this.props?.reviewData?.cmp ? this.props?.reviewData?.cmp : undefined;
    if (!cmpValue) {
      if (this.props?.reviewData?.holidayReviewData) {
        cmpValue = this.props?.reviewData?.holidayReviewData?.initId
          ? this.props?.reviewData?.holidayReviewData?.initId
          : '';
      } else {
        cmpValue = '';
      }
    }
    const { reviewDataFromDetail, reviewData } = this?.props || {};
    const { campaign } = reviewDataFromDetail || {};
    const { reviewDetail } = reviewData || {};
    this.setTckAcknowledgedValue();
    this.props.updateInterventionData({
      packageId: reviewDetail?.id,
      branch: this.props.detailData?.branch || DOM_BRANCH,
      destinationCity: this.holidayReviewData.tagDestination,
      cmp: cmpValue,
      departureDate: reviewDetail?.departureDetail?.departureDate,
      roomData: this.roomDetails,
      campaign,
    });
    setFirebaseTrackingForReview({
      reviewData: reviewDetail || {},
      roomDetails: this.roomDetails || {},
      branch: this.props.holidayReviewData?.branch || DOM_BRANCH,
    });
    BranchIOTracker.trackPageView({
      pageName: BranchIOTracker.PAGE.REVIEW,
      [BranchIOTracker.KEYS.EXTRA_DATA]: {},
    });
  }
  componentWillUnmount() {
    if (paymentEventReviewListener.remove) {
      paymentEventReviewListener.remove();
      // DeviceEventEmitter && DeviceEventEmitter.removeListener(EVENTS.REVIEW.PAYMENT_EVENT_REVIEW, this.onPaymentThankyou);
    }
    if (loginEventReviewListener.remove) {
      loginEventReviewListener.remove();
      // DeviceEventEmitter && DeviceEventEmitter.removeListener(LOGIN_EVENT_REVIEW, this.onLoginEventReceived);
    }
    if (widgetTCSListener?.remove) {
      widgetTCSListener.remove();
    }
    clearTimeout(this.timer);
    clearReviewComponentVisit(sectionTrackingPageNames.PHOENIX_REVIEW_PAGE);
    this.props?.clearGiftCardData()
    this.props?.removeMmtBlackReviewData()
    this.props?.removeGiftCardStatus();
    this.trackPDTExitEvent();
    if(this.holidayReviewData.quoteRequestId){
      HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.PSM)
    }
  }
  trackPageExit = () => {
    trackReviewPageLoadEvent({
      logOmni: false,
      pdtData: {
        pageDataMap: this.props?.holidayReviewData?.pageDataMap || {},
        interventionDetails: {},
        eventType: PDT_RAW_EVENT,
        activity: PDT_PAGE_EXIT_EVENT,
        requestId: createRandomString(),
        branch: this.props?.holidayReviewData?.branch || DOM_BRANCH,
      },
    });
  };
  trackPDTExitEvent(){
    this.trackPDTLoadAndExitEvent(PDT_EVENT_TYPES.pageExit,EVENT_NAMES.PAGE_EXIT);
    if(!this.holidayReviewData.quoteRequestId){
    HolidayDataHolder.getInstance().clearQueryDetailForPSM()
    }
  }
  async initUserData() {
    this.isLoggedInUser = await isUserLoggedIn();
    const userDetailsObj = await getUserDetails();
    if (userDetailsObj) {
      this.setState({
        userDetails: userDetailsObj,
        userDetailsDynamic: this.getInitialUserDetailsDynamic(),
      });
    }
  }
  onLoginClicked = () => {
    const { HolidayModule } = NativeModules;
    if (isMobileClient()) {
      HolidayModule.onLoginUserReview();
    } else {
      HolidayModule.onLoginUserReview(this.getReturnUrl());
    }
    this.captureReviewClickEventsInComponent({ name: PDTConstants.PHOENIX_REVIEW_LOGIN });
  };
  getReturnUrl = () => {
    if (!isRawClient()) return null;
    const { quoteRequestId, dynamicPackageId, reviewType, reviewSubtype} = this.holidayReviewData || {};
    let urlObj = url.parse(window.location.href);
    if(!isEmpty(quoteRequestId)) {
      urlObj = {
        ...urlObj,
        query: {
          requestId: quoteRequestId,
          dynamicPackageId: dynamicPackageId,
          reviewType,
          reviewSubtype,
        }
      };
    } else {
      urlObj = {
        ...urlObj,
        query: {
          dynamicPackageId: dynamicPackageId,
        }
      };
    }
    return url.format(urlObj);
  };


  onLoginEventReceived = (response) => {
    if (response && response.loggedIn) {
      this.initUserData();
      this.loadReview();
    }
  };

  startChat = () => {
    this.props.unmountIntervention();
    let cmpValue = this.props.reviewData.cmp ? this.props.reviewData.cmp : undefined;
    if (!cmpValue) {
      if (this.props.reviewData.holidayReviewData) {
        cmpValue = this.props.reviewData.holidayReviewData.initId
          ? this.props.reviewData.holidayReviewData.initId
          : '';
      } else {
        cmpValue = '';
      }
    }
    const chatIdentifier = createChatID();
    const { reviewData = '' } = this.props || {};
    const { trackingData = {}, source = '', reviewType = '' } = reviewData?.holidayReviewData || {};
    const { categoryTrackingEvent = '' } = trackingData || {};
    const chatDto = {
      destinationCity: this.props.reviewData.reviewDetail?.tagDestination.name,
      branch: this.holidayReviewData.branch,
      travelDate: this.props.reviewData.pageDataMap.otherDetails.travel_start_date,
      packageId: `${this.props.reviewData.reviewDetail?.id}`,
      dynamicPackageId: this.props.reviewData.reviewDetail?.dynamicId,
      paxConfig: getPaxConfig(this.props.reviewData.pageDataMap),
      cmp: cmpValue,
      pageName: REVIEW_PDT_PAGE_NAME,
      chatId: chatIdentifier,
      categoryTrackingEvent,
      eventData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForReview({
          source,
          pageName: REVIEW_PDT_PAGE_NAME,
          reviewType,
        }),
      },
    };
    startReactChat(chatDto);
    const eventName = PDTConstants.CONTACT_ICON;
    trackReviewLocalClickEvent(eventName, PDTConstants.CHAT_SUFFIX, this.props.reviewData, false, {
      prop66: chatIdentifier,
    });
    sendMMTGtmEvent({eventName : eventName+ PDTConstants.CHAT_SUFFIX, data: {
      pageName: 'holidayReview',
      branch: this.holidayReviewData.branch,
    }})
    this.trackPageExit();
  };

  startNoPkChat = () => {
    const { code = '', message = ''} = this.props.error || {};
    const chatIdentifier = createChatID();
    const chatDto = {
      destinationCity: this.holidayReviewData.tagDestination,
      branch: this.holidayReviewData.branch,
      chatId: chatIdentifier,
      pageName: REVIEW_PDT_PAGE_NAME,
    };
    trackReviewErrorLocalChatClickEvent({
      eventName: 'chat_clicked',
      suffix: chatIdentifier,
      evar22: `${code ? `${code}:` : ''}${message || ''}`,
    });
    startReactChat(chatDto);
  };
  loadReview = () => {
    this.travellerFormData = {};
    const { listData } = this.state;
    if (listData && listData[SECTIONS.TRAVELLERS]?.status) {
      listData[SECTIONS.TRAVELLERS].status = 'mandatory';
    }
    this.setState({
      userDetailsDynamic: this.getInitialUserDetailsDynamic(),
      completedTravellersList: [],
      popup: '',
      listData,
      showTravellerDetailPage: false,
    });
    HolidayPDTMetaIDHolder.getInstance().setPdtId();
    this.props.fetchReviewData(this.holidayReviewData, this.roomDetails || {}, false, false, () => {
      this.isReloaded = true;
    });
  };
  onScroll = (event) => {
    // Handle FAB button animation here
    const scrollIndex = event.nativeEvent.contentOffset.y;
    // When a user scrolls down more than 50 px, shrink the FAB text
    if (scrollIndex > 50 && !this.state.fabTextShrinked) {
      this.setState({
        fabTextShrinked: true,
      });
    } else if (scrollIndex <= 50 && this.state.fabTextShrinked) {
      this.setState({
        fabTextShrinked: false,
      });
    }
  };
  hidePriceChangeBanner = () => {
    this.setState({
      showPriceChange: false,
    });
  };
  onContactFormError = (field) => {
    // This handling is required to specially handling scroll to error  fields with in a FlatList
    UIManager.measureLayout(
      findNodeHandle(field),
      findNodeHandle(this.flatList),
      () => {},
      (left, top, width, height) => {
        setTimeout(() => {
          this.flatList.scrollToOffset({ animated: true, offset: top });
        }, 500);
      },
    );
  };
  handleSkip = (currentSection) => {
    if (currentSection < SECTIONS_DATA.length) {
      const { listData } = this.state;
      // since current section value is flatlist index + 1, therefore to open next section just pass current sections step number
      let stepId = listData[currentSection].id;
      this.gotoSection(currentSection - 1, stepId);
    }
  };

  gotoSection = (prevSectionIndex, nextSectionIndex, showError) => {
    const { listData } = this.state;
    listData?.map((item) => {
      if (!this.isSectionsExpanded) {
        item.expanded = false;
      }
    });
    let index = this.getIndexOfSection(nextSectionIndex);
    listData[index].expanded = true;
    this.setState({ listData }, () => {
      this.scrollList(index);
      if (showError) {
        setTimeout(() => {
          if (this.travellerSection) {
            this.travellerSection.validateBookerForm();
          }
        }, 500);
      }
    });
  };

  getIndexOfSection = (sectionIndex) => {
    const { listData } = this.state;
    let index = listData.findIndex((el) => el.id == sectionIndex) || 0;
    if (index < 0) {
      index = 0;
    }
    return index;
  };

  scrollList = (index) => {
    setTimeout(
      function () {
        if (this.flatList) {
          this.flatList.scrollToIndex({ animated: true, index });
        }
      }.bind(this),
      200,
    );
  };

  scrollToOffSetForTcsWidget = (index) => {
    if (this.flatList) {
      const ITEM_HEIGHT = 900;
      const flatListHeight = this.flatListHeight || 0; // Use default value of 0 if undefined or null
      setTimeout(() => {
        const offset = (index + 1) * ITEM_HEIGHT - flatListHeight;
        this.flatList.scrollToOffset({ animated: true, offset });
      }, 100);
    }
  };

  prepareDayPlanSections = (packageContent) => {
    const reviewDetail = this.props?.reviewData?.reviewDetail;
    const { departureDetail } = reviewDetail || {};
    const { dayItinerariesV2: dayItineraries = [] } = reviewDetail?.itineraryDetail || {};
    const dayItineraries2 = cloneDeep(dayItineraries);
    const sightSeeingMap = createSightSeeingDayPlanDataMap(packageContent, departureDetail);
    if (dayItineraries2 && dayItineraries2.length > 0) {
      dayItineraries2.forEach((data, index) => {
        let indexItem = data.itineraryUnits.findIndex((unit, index) => {
          const { itineraryUnitType } = unit;
          return (
            itineraryUnitType === itineraryUnitTypes.TRANSFERS ||
            itineraryUnitType === itineraryUnitTypes.CAR
          );
        });

        if (Object.keys(sightSeeingMap).length > 0 && indexItem === -1) {
          if (sightSeeingMap[data.day]) {
            data.itineraryUnits.push(sightSeeingMap[data.day]);
          }
        } else if (Object.keys(sightSeeingMap).length > 0 && indexItem >= 0) {
          if (sightSeeingMap[data.day]) {
            data.itineraryUnits.splice(indexItem + 1, 0, sightSeeingMap[data.day]);
          }
        }
      });
      return dayItineraries2;
    }
    return;
  };
  memoizedPrepareDayPlanSections = memoize(this.prepareDayPlanSections); //for sightseeing data

  openTravellerDetailPage = (item, index) => {
    const PDT_ACTION =
      item?.status === FORM_FILL_STATUS.UNFILLED
        ? `${PDTConstantsNew.ADD_TRAVELLER}_${index}`
        : `${PDTConstantsNew.UPDATE_PAX}_${index}`;
    this.captureReviewClickEventsInComponent({name: PDT_ACTION, suffix : '_1' })
    const { showTravellerDetailPage, savedTravellerList } = this.state;
    const { reviewData } = this.props;
    const { travellerFormAutofillerDetail } = reviewData || {};
    let savedTravellers = savedTravellerList;
    if (isEmpty(savedTravellerList) && travellerFormAutofillerDetail) {
      savedTravellers = createSavedTravellerInformation(travellerFormAutofillerDetail);
    }
    if (!showTravellerDetailPage) {
      this.setState({
        showTravellerDetailPage: true,
        savedTravellerList: savedTravellers,
        travellerIndex: index,
      });
    }
  };

  async fetchFlightDetails() {
    const responseBody = await fetchReviewContent(this.holidayReviewData.dynamicPackageId);
    if (!responseBody) {
      return;
    }

    const { flightContent, carItineraryContent } = responseBody ?? {};
    if (flightContent?.baggageInfoMap && carItineraryContent?.carContents) {
      this.setState({
        baggageInfo: flightContent?.baggageInfoMap,
        carContents: carItineraryContent?.carContents,
      });
    } else if (flightContent?.baggageInfoMap) {
      this.setState({
        baggageInfo: flightContent?.baggageInfoMap,
      });
    } else if (carItineraryContent?.carContents) {
      this.setState({
        carContents: carItineraryContent?.carContents,
      });
    }
  }

  onFormFilled = (completedTravellersList) => {
    this.setState(
      {
        completedTravellersList: completedTravellersList,
      },
      () => {
        this.updateSectionStatus();
        this.onSubmitHitSubmitTravellerAPI();
      },
    );
  };

  onSubmitHitSubmitTravellerAPI = () => {
    const { dynamicFormData } = this.props.reviewData || {};
    const { completedTravellersList } = this.state;
    const travellerForm = initializeTravellerFormData(dynamicFormData);
    if (completedTravellersList && completedTravellersList.length > 0) {
      for (let i = 0; i < completedTravellersList.length; i++) {
        addTravellerFormLeaf(travellerForm, completedTravellersList[i]);
      }
    }
    this.travellerFormData = travellerForm;
  };

  handleTcsBannerAckSelection = (value) => {
    this.setState({ tcsTnCAcknowledged: value, showTCSErrorStrip: !value }, () => {
      this.updateSectionStatus();
    });
  };

  setBookerInfoRef = (element) => {
    this.bookerInfoRef = element;
  };
  updateSectionStatus = () => {
    const { listData } = this.state;
    const { completedTravellersList } = this.state || {};
    const uncompletedList = this.getUncompletedList();
    const {
      metadataDetail: { branch },
    } = this.props?.reviewData?.reviewDetail || {};
    let index = listData.findIndex((el) => el.id === SECTIONS.TRAVELLERS) || 0;
    if (index < 0) {
      index = 0;
    }
    const newList = _.cloneDeep(listData);
    if (completedTravellersList?.length > 0 && uncompletedList?.length <= 0) {
      if (branch === OBT_BRANCH && !this.state.tcsTnCAcknowledged) {
        newList[index].status = STATUS_INCOMPLETE;
      } else {
        newList[index].status = STATUS_DONE;
      }
    } else {
      newList[index].status = STATUS_INCOMPLETE;
    }
    this.setState({
      listData: newList,
    });
  };

  componentFailureChangeButtonHandling = () => {
    this.setState({ showComponentFailureToast: false });
    this.props.toggleComponentFailure(null);
    HolidayNavigation.pop();
    if (this.props.openForwardFlowFromReviewPage) {
      this.props.openForwardFlowFromReviewPage();
    }
  };

  componentFailureCancelButtonHandling = () => {
    this.setState({ showComponentFailureToast: true });
  };
  getPSMDeeplinkDetail = () => {
    const {holidayReviewData} = this.props || {}
    const {quoteRequestId, fromDeeplink} = holidayReviewData || {}
    if (quoteRequestId && fromDeeplink) {
      const {metadataDetail} = this.props?.reviewData?.reviewDetail || {}
      const {reviewMessageMap} = metadataDetail || {}
      return reviewMessageMap;
    }
    return {}
  }

  openPSMDetailPage = (deepLink) => {
    if (isEmpty(deepLink)) {
      return
    }

    try {
      const q = URL.parse(deepLink, true);
      const queryParams = q.query;
      let quoteRequestId = queryParams['quoteRequestId'];
      if(!isEmpty(quoteRequestId)) {
        const {departureDetail, destinationDetail, roomDetails} = this.holidayReviewData || {}
        const itineraryDetail = {departureDetail, destinationDetail, rooms: roomDetails}
        const quote = {quoteRequestId}
        const holidaysDetailData = getHolidayDetailObjectForMimaPreSales(itineraryDetail, quote)
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
          from_quotes_deeplink: true,
          from_detail_deeplink: true,
          openMimaPreSales: true,
          query: deepLink,
          holidaysDetailData
        });
      } else {
        const {HolidayModule} = NativeModules;
        HolidayModule.openWebView({url:deepLink});
      }
    } catch (e) {
      console.log(e)
    }
  }
  openListingPage = () => {
    // from detail page deeplink
    this.props.toggleComponentFailure(null);
    if (this.props[deepLinkParams.deepLink]) {
      const { pt, aff, departureDetail = {}, destinationDetail = {} } = this.holidayReviewData;
      // HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.LISTING, {
      //   holidaysListingData: {
      //     dest: destinationDetail.tagDestination,
      //     destinationCity: destinationDetail.tagDestination,
      //     departureCity: departureDetail.departureCity,
      //     packageDate: departureDetail.departureDate,
      //     cmp: this.holidayReviewData.cmp,
      //     pt,
      //     aff,
      //     fromDeepLink: true,
      //   },
      // });
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, {
        dest: destinationDetail.tagDestination,
        destinationCity: destinationDetail.tagDestination,
        departureCity: departureDetail.departureCity,
        packageDate: departureDetail.departureDate,
        cmp: this.holidayReviewData.cmp,
        pt,
        aff,
        fromDeepLink: true,
      });
    } else {
      if (getPageName()) {
        HolidayNavigation.navigate(getPageName());
      } else {
        HolidayNavigation.pop();
      }
    }
  };
  togglePopup = (popupName) => {
    this.setState({
      popup: popupName,
    });
  };
  closePopUp = () => {
    this.togglePopup('');
    return true;
  };
  addSpecialRequest = (collapsed) => {
    this.captureReviewClickEventsInComponent({ name: PDTConstants.ADD_SPECIAL_REQUEST });
    this.togglePopup(SPECIAL_REQUEST_OVERLAY);
    this.setState({ selectedCollapsedForm: collapsed });
  };
  updateCollapsedData = (formData, sectionId) => {
    this.captureReviewClickEventsInComponent({ name: PDTConstants.SUBMIT_SPECIAL_REQUEST });
    this.setState({
      collapsedFormData: {
        ...this.state.collapsedFormData,
        [sectionId]: formData,
      },
    });
  };
  updateTravellerList = (savedTravellersSet) => {
    this.setState({
      savedTravellerList: savedTravellersSet,
    });
  };

  handleReviewSectionToggle = (sectionIndex, isExpanded) => {
    const { listData } = this.state;
    let index = listData.findIndex((el) => el.id == sectionIndex) || 0;
    if (index < 0) {
      index = 0;
    }
    listData[index].expanded = isExpanded;
    this.setState({
      listData,
    });
  };
  closeAll = () => {
    const { listData } = this.state;
    listData.map((el) => {
      el.expanded = false;
    });
    this.setState({
      listData,
    });
  };

  renderViatorSection = () => {
    const { reviewData = {} } = this.props;
    const { metadataDetail: { pickUpPointMessage = {} } = {} } = reviewData?.reviewDetail || {};
    return (
      <ViatorMessageStrip viatorInfo={pickUpPointMessage} state={VIATOR_MESSAGE_STATE.COLLPASED} />
    );
  };

  onLayout = (event) => {
    this.flatListHeight = event.nativeEvent.layout.height;
  }

  handleVPPCtaClicks = (item) => {
    const onClickHandler = handleCtaClick[item.onClick]
    const { addonType = '', addonSubType = '' } = this.props.vppAddonDetail?.addons[0] || {};
    const vppDetailPageProps = {
      vppAddonDetail: this.props.vppAddonDetail,
      onSelect: this.selectInsurance,
      trackLocalClickEvent: this.trackReviewLocalClickEventInComponent,
      addonType,
      addonSubType,
      showOverlay: this.props.showReviewOverlay,
      hideOverlays: this.props.hideReviewOverlays,
    }
    const vppBottomSheetProps = {
      toggleBottomSheet: this.setVPPBottomSheetVisibility,
    }
    this.captureReviewClickEventsInComponent({
      name: VPP_TRACKING_EVENT,
      suffix: item.key,
      prop1: VPP_TRACKING_PAGES.BASE_CARD
    })
    if (onClickHandler) {
      onClickHandler({ item, vppBottomSheetProps, vppDetailPageProps });
    }
  }

  handleTCSWidgetTracking = (details) => {
    if (!isPanRequired(this.props.pricingDetail)) {
      return;
    }
    const { pan_tcs_tracking_data = '' } = details || {};
    if (pan_tcs_tracking_data.startsWith(TCS_AMOUNT)) {
      this.trackReviewLocalClickEventInComponent(TCS_AMOUNT_CALCULATED, '', { prop1: pan_tcs_tracking_data });
      return;
    }
    this.trackReviewLocalClickEventInComponent(pan_tcs_tracking_data, '');
  };
  renderItem = ({ item, index }) => {
    const {
      reviewData,
      validateZC,
      validateCoupon,
      dealDetail,
      couponData,
      getEmiOptions,
      emiOptions,
      collapsedForm,
      pricingDetail,
      penaltyDetail = {},
      additionalDetail = {},
      emiDetail = {},
    } = this.props;
    const { baggageInfo = {}} = this.state || {};
    const { metadataDetail: { branch }, roomDetail = {} } = reviewData?.reviewDetail || {};
    const insuranceAddonDetail = this?.props?.insuranceAddonDetail;
    const vppAddonDetail =  this?.props?.vppAddonDetail

    switch (item.id) {
      case SECTIONS.TRAVELLERS:
        return ( <>
          <ReviewSection
            contentStyle={{ paddingHorizontal: 0 }}
            title={item.title}
            subTitle={undefined}
            stepId={(index + 1)}
            maxSteps={this.maxSteps}
            status={item.status}
            expanded={item.expanded}
            isSectionsExpanded={this.isSectionsExpanded}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            onExpand={expanded => this.handleReviewSectionToggle(SECTIONS.TRAVELLERS, expanded)}
            onSkip={this.handleSkip}
            updateSectionStatus ={this.updateSectionStatus}
            closeAll= {this.closeAll}
          >
            <View>
              <TravellerDetailsSection
                ref={ref => this.travellerSection = ref}
                  isLoggedInUser = {this.isLoggedInUser}
                reviewData={reviewData}
                userDetails={this.state.userDetails}
                onLoginClicked={this.onLoginClicked}
                travellerList={this.travellerList}
                onAddTraveller ={this.openTravellerDetailPage}
                showTcsBanner={this.getShowTCSSection(TCS_VERSIONS.V1)} //{this.showTcsBanner}
                handleTcsBannerAckSelection= {this.handleTcsBannerAckSelection}
                showTCSErrorStrip={this.state.showTCSErrorStrip}
                contactFormIntialValue={this.state.userDetailsDynamic}
                updateUserDetailsMasterDynamic={this.updateUserDetailsMasterDynamic}
                setTravellerErrorRef={this.setTravellerErrorRef}
                collapsedForm={collapsedForm}
                addSpecialRequest={this.addSpecialRequest}
                onContactFormError={this.onContactFormError}
                isSelected={this.state.tcsTnCAcknowledged}
                showError={this.state.errorsList?.[ADD_TRAVELLER_ERROR] || false}
                  psmDetail={this.getPSMDeeplinkDetail()}
                  hanldeDeeplink={this.openPSMDetailPage}
                additionalDetail ={additionalDetail}
                trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
              />
            </View>
          </ReviewSection>
            { isPanRequired(pricingDetail) &&
              <TcsPanWidget
                type={TAX_COLLECT_TCS_TYPES.PAN}
                containerStyle={this.state.showPanError ? styles.tcsWidgetContainerError : styles.tcsWidgetContainer}
                pricingDetail={pricingDetail}
              />
            }

        </>
        );
      case SECTIONS.ADD_ONS:
        return (
          <ReviewSection
            title={item.title}
            subTitle={undefined}
            stepId={index + 1}
            maxSteps={this.maxSteps}
            status={undefined}
            expanded={item.expanded}
            onExpand={(expanded) => this.handleReviewSectionToggle(SECTIONS.ADD_ONS, expanded)}
            onSkip={this.handleSkip}
            isSectionsExpanded={this.isSectionsExpanded}
            updateSectionStatus={this.updateSectionStatus}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            closeAll={this.closeAll}
          >
            <PackageAddOns
              reviewData={reviewData}
              penaltyDetail={penaltyDetail}
              validateZC={validateZC}
              trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            />
             {insuranceAddonDetail &&
            insuranceAddonDetail?.addons?.length > 0 &&
            showInsuranceSection() ? (
              <TravelInsuranceCard
                insuranceAddonDetail={insuranceAddonDetail}
                trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
                onSelect={(insuranceAddOn) => {
                  this.selectInsurance(insuranceAddOn,false,false);
                }}
              />
            ) : null}
             {vppAddonDetail && vppAddonDetail?.addons?.length > 0 && (
              <VPPCard
                vppAddonDetail={vppAddonDetail}
                onSelect={(vppAddOn) => {
                  this.selectInsurance(vppAddOn, false,false);
                }}
                handleCtaClick={this.handleVPPCtaClicks}
                isSelected={vppAddonDetail?.addons[0].isSelected}
                basePageCard={true}
                trackLocalClickEvent={this.trackReviewLocalClickEventInComponent}
                inclusionMessage={vppAddonDetail?.addonHeader?.inclusionMessage}
              />
            )}
          </ReviewSection>
        );
      case SECTIONS.ITENERARY:
        return (
          <ReviewSection
            title={item.title}
            subTitle={undefined}
            stepId={index + 1}
            maxSteps={this.maxSteps}
            status={undefined}
            expanded={item.expanded}
            onSkip={this.handleSkip}
            isSectionsExpanded={this.isSectionsExpanded}
            onExpand={(expanded) => this.handleReviewSectionToggle(SECTIONS.ITENERARY, expanded)}
            updateSectionStatus={this.updateSectionStatus}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            closeAll={this.closeAll}
            renderSubComponent={this.renderViatorSection}
          >
            <PackageInclusions
              reviewData={reviewData}
              carContents={this.state.carContents}
              roomCount={roomDetail?.rooms?.length}
              baggageInfo={baggageInfo}
              additionalDetail={additionalDetail}
            />
          </ReviewSection>
        );
      case SECTIONS.CANCELLATION:
        return (
          <ReviewSection
            title={item.title}
            subTitle={undefined}
            stepId={index + 1}
            maxSteps={this.maxSteps}
            status={undefined}
            expanded={item.expanded}
            onExpand={(expanded) => this.handleReviewSectionToggle(SECTIONS.CANCELLATION, expanded)}
            onSkip={this.handleSkip}
            isSectionsExpanded={this.isSectionsExpanded}
            updateSectionStatus={this.updateSectionStatus}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            closeAll={this.closeAll}
          >
            <Cancellation
              reviewData={reviewData}
              penaltyDetail={penaltyDetail}
              listData={this.state.listData}
              gotoSection={this.gotoSection}
            />
          </ReviewSection>
        );
      case SECTIONS.COUPONS:
        return (
          <ReviewSection
            title={item.title}
            subTitle={undefined}
            stepId={index + 1}
            maxSteps={this.maxSteps}
            status={undefined}
            expanded={item.expanded}
            onSkip={this.handleSkip}
            isSectionsExpanded={this.isSectionsExpanded}
            onExpand={(expanded) => this.handleReviewSectionToggle(SECTIONS.COUPONS, expanded)}
            updateSectionStatus={this.updateSectionStatus}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            closeAll={this.closeAll}
          >
            <CouponsOffers
              dealDetail={dealDetail}
              validateCoupon={validateCoupon}
              emiDetails={emiDetail}
              couponData={couponData}
              getEmiOptions={() => getEmiOptions(this?.holidayReviewData?.dynamicPackageId)}
              emiOptions={emiOptions}
              fromPresalesDetail={this.holidayReviewData.reviewType === sectionCodes.PRESALES}
              reviewAdditionalPricingDetail={pricingDetail?.reviewAdditionalPricingDetail}
              trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
              fromAmendment={this.holidayReviewData.fromAmendment}
            />
          </ReviewSection>
        );
      default:
        return <View />;
    }
  };
  onTCSReferenceAvailable = (ref) => {
    this.TCSbannerStripRef = ref;
  };

  showReviewPopup = () => {
    this.setState({ showReviewPopUp: true });
    this.setState({isGifVisible: false});
    this.captureReviewClickEventsInComponent({name:`GC_${this.props.mmtBlackDetail?.section?.cta?.text?.split(' ').join('_')}`});
    this.setState({retryCount:0});
    this.props.removeGiftCardStatus();
  }

  renderListHeader = () => {
    const { reviewData, additionalDetail, packageUpdateSection } = this.props || {};
    const { sections = [] } = packageUpdateSection || {};
    const { tcsSection = {} } = additionalDetail || {};
    const showTCSV2Section = this.getShowTCSSection(TCS_VERSIONS.V2);


    // const packageAddonDetail = this?.props?.reviewData?.reviewDetail?.packageAddonsDetail?.insuranceAddonDetail

    return (
      <>
        {/*{packageAddonDetail ? <TravelInsuranceCard insuranceAddonDetail={packageAddonDetail}/> : null}*/}

        <PackageInfo
          reviewDetail={reviewData?.reviewDetail}
          showReviewPopup={this.showReviewPopup}
          trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
        />
        {this.showNewUpdateSection && sections?.length ? (
          <PackageUpdate
            sections={sections}
            gotoSection={this.gotoSection}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            priceChangeAction={this.priceChangePersuasion?.[0]?.action}
          />
        ) : null}
        {showTCSV2Section && (
          <View style={styles.tcsBannerStripWrapper}>
            <TCSbannerStrip
              tcsSection={tcsSection}
              showAckSection={false} // by default assume ack to be true
              onReferenceAvailable={this.onTCSReferenceAvailable}
              handleTcsBannerAckSelection={this.handleTcsBannerAckSelection}
              showTCSErrorStrip={this.state.showTCSErrorStrip}
              handleTcsBannerToggle={() => {}}
              isSelected={this.state.tcsTnCAcknowledged}
              trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            />
          </View>
        )}
      </>
    );
  };
  renderListFooter = () => {
    const { additionalDetail } = this.props || {};
    const isDomesticPackage =
      this.props?.reviewData?.reviewDetail?.metadataDetail?.branch !== 'OBT';
    const { tcsSection = {} } = additionalDetail || {};
    const { footer, showTcsSection = false  } = tcsSection || {};
    const isShowTcsSection = showTcsSection && !isDomesticPackage && !this.showReviewTCSv2
    const showTCSFooter = this.getShowTCSSection(TCS_VERSIONS.V1);
    const BookingInfoComp = (
      <BookingInformation
        timerStarted={this.timeNow}
        affiliate = {this.holidayReviewData.aff}
        countdownEndTime={this.props.reviewData?.reviewDetail?.metadataDetail?.countdownEndTime}
        callbackHandler={(type, params) => this.callbackHandler(type, params)}
        toggleTncCheckBox={this.toggleTncCheckBox}
        tncCheckBoxSelected={this.state.tncCheckBoxSelected}
      />
    );
    return (
      <View style={isShowTcsSection ? styles.footerWithoutTCS : styles.footerWithTCS}>
        {isShowTcsSection ? <TCSbannerBox footer={footer} /> : null}
        <View style={{ marginTop: 12 }}>{BookingInfoComp}</View>
      </View>
    );
  };
  renderItemSeparator = () => <View style={marginStyles.mv6} />;

  setShowOvernightPopupState = (value) => {
    this.setState({
      showOvernightPopup: value,
    });
  };

  validateAddOns = ()=>{
    setTimeout(() => { // putting wait to ensure that we aquire section reference for validation
      this.validatePaxDynamic(true);
    }, 250);
  }

  startPayment = () => {
    this.updateSectionStatus();
    const { listData } = this.state;
    listData[SECTIONS.TRAVELLERS].expanded = true; //expand traveller section to get reference of the sections
    this.setState({listData, showTCSErrorStrip: false,reviewPopUp:false}, () => {
      setTimeout(() => {
        this.validatePaxDynamic(false);
      }, 250);
    });
  };
  getUncompletedList = () => {
    const { completedTravellersList } = this.state || {};
    return completedTravellersList?.filter((item) => {
      return item.status !== STATUS_COMPLETE;
    });
  };

 handlePrePayment = () => {
   const { paymentPageData } = this.state || {};
   const { userDetails, object, isPresalesEditPage } = paymentPageData || {};
   const formData = formDynamicApiDataContact(object);
   const travellerInfo = getTravellerInfo(object);
   logHolidayReviewPDTClickEvents({
    actionType : PDT_EVENT_TYPES.buttonClicked,
    value : 'PRE_PAYMENT_API_CALLED',
    travellerInfo,
   })
    this.props.doPrePaymentDynamic(formData, userDetails, isPresalesEditPage);
  };

  setTcsBottomSheetVisibility = show => this.setState({ showTcsBottomSheet: show });
  setFareBreakupVisibilityForTcs = show => this.setState({ fareBreakupVisibilityForTcs: show });
  setVPPBottomSheetVisibility = show => this.setState({ showVPPBottomSheet : show });

  handleBackPressedFromTCS = () => {
    this.setTcsBottomSheetVisibility(false);
    this.setFareBreakupVisibilityForTcs(false);
  };

  goToPaymentPage = () => {
    if (this.state.showOvernightPopup) {
      this.setShowOvernightPopupState(false);
    }
    const panWithTcsApplicable = isPanWithTCSRequired(this.props?.pricingDetail);
    if (panWithTcsApplicable) {
      this.setTcsBottomSheetVisibility(true);
    } else {
      this.handlePrePayment();
    }
  };

  validateAddsOns = ({ userDetails, object, isPresalesEditPage })=>{
     const formData = formDynamicApiDataContact(object)
     formData.dynamicPackageId = this.props.reviewData?.reviewDetail.dynamicId
     this.props.validateAddOns(formData);
  };

  goToPaymentAfterOvernightCheck = ({ userDetails, object, isPresalesEditPage }) => {
    // The callback provided to this.setState ensures that the later state update or method call happens after the initial state update is complete.
    this.setState({ paymentPageData: { userDetails, object, isPresalesEditPage } }, () => {
      if (this.overnightCondition) {
        this.setState({
          showOvernightPopup: true,
        });
      } else {
        this.goToPaymentPage();
      }
    });
  };

  isTcsPanHandled = async () => {
    try {
      //Below code will check if PAN widget is present on review page or not.
      if (!isPanRequired(this.props.pricingDetail) ){
        return true;
      }
      const { HolidayModule } = NativeModules;
      //This should only be executed when PAN widget has been rendered on review page else it will give exception.
      const panStatus = await HolidayModule.getPanStatus({ tag: TCS_PAN_WIDGET_TAG });
      const isPanError = !panStatus;

      //Set state for pan error, this will add red border to pan input widget.
      this.setState({ showPanError: isPanError });

      //Scroll to the pan widget
      if (isPanError) {
        this.scrollToOffSetForTcsWidget(1);
        return false;
      }
      return true;
    } catch (e) {
      console.log('Error in getting PAN status', e);
      return true
    }
  };

  validatePaxDynamic = async(isToValidateAddOns) => {
    const { completedTravellersList, tncCheckBoxSelected } = this.state || {};
    if (this.travellerSection) {
      const isValidBookerForm = this.travellerSection.validateBookerForm();
      const { reviewData } = this.props;
      const {
        metadataDetail: { branch },
      } = reviewData.reviewDetail;
      if (isValidBookerForm && branch === OBT_BRANCH && !this.state.tcsTnCAcknowledged) {
        // Show TCS banner error
        this.setState({ showTCSErrorStrip: true }, () => {
          if (this.travellerSection.getTCSStripRef()) {
            this.onContactFormError(this.travellerSection.getTCSStripRef());
          }
        });
        return;
      }
      const { userDetailsDynamic } = this.state;
      const uncompletedList = this.getUncompletedList();
      if (
        isValidBookerForm &&
        completedTravellersList?.length > 0 &&
        uncompletedList?.length <= 0 &&
        userDetailsDynamic
      ) {
        const { formSections, travellerFormId, collapsedForm } = this.props;
        const collapsedFormData = this.state.collapsedFormData;
        const object = {
          travellerFormData: { ...this.travellerFormData },
          userDetailsDynamic,
          formSections,
          travellerFormId,
          collapsedForm,
          collapsedFormData,
        };
        const userDetails = {
          ...this?.state?.userDetails,
          phone:this?.state?.userDetails?.mobileNumber || this?.state?.userDetails?.mobile?.mobileNumber,
        };
        const isPresalesEditPage =
          this.holidayReviewData.reviewType === sectionCodes.PRESALES &&
          this.holidayReviewData.reviewSubtype === sectionCodes.PRESALES_EDIT;

        if (!await this.isTcsPanHandled()) {
          return;
        }

        if (!tncCheckBoxSelected){
          this.flatList.scrollToEnd({ animated: true });
          return;
        }

        //Removed Overnight Popup check for insurance

        if(isToValidateAddOns){
          this.validateAddsOns({ object, userDetails, isPresalesEditPage });
        }else{
          this.goToPaymentAfterOvernightCheck({ object, userDetails, isPresalesEditPage });
        }

        // this.props.doPrePaymentDynamic(
        //   formDynamicApiDataContact(object),
        //   userDetails,
        //   isPresalesEditPage ? true : false,
        // );

        const { reviewData = {}, pricingDetail = {} } = this.props;
        const { reviewDetail = {} } = reviewData || {};
        const {
          name = '',
          dynamicId = '',
          departureDetail = {},
          destinationDetail = {},
          metadataDetail = {},
        } = reviewDetail || {};
        BranchIOTracker.trackCommerceEvent({
          [BranchIOTracker.KEYS.EVENT_NAME]: BranchIOTracker.EVENT.PURCHASE,
          [BranchIOTracker.KEYS.PAGE_NAME]: BranchIOTracker.PAGE.REVIEW,
          [BranchIOTracker.KEYS.CANONICAL_IDENTIFIER]: dynamicId,
          [BranchIOTracker.KEYS.TITLE]: name,
          [BranchIOTracker.KEYS.AFFILIATE]: this.holidayReviewData.aff,
          [BranchIOTracker.KEYS.CUSTOM_META_DATA]: {
            [BranchIOTracker.KEYS.PRODUCT_NAME]: name,
            [BranchIOTracker.KEYS.PRICE]: pricingDetail.price,
            [BranchIOTracker.KEYS.CURRENCY]: pricingDetail.currencyCode,
          },
          [BranchIOTracker.KEYS.CUSTOM_DATA]: {
            ...departureDetail,
            ...destinationDetail,
            ...metadataDetail,
          },
        });
      } else {
        if (uncompletedList.length >= 0) {
          this.setState({
            errorsList: {
              [ADD_TRAVELLER_ERROR]: true,
            },
          });
        }
        this.goTo();
      }
    } else {
      this.goTo(true);
    }
  };
  goTo(showError) {
    const { listData } = this.state;
    let index = listData.findIndex((el) => el.id === SECTIONS.TRAVELLERS) || 0;
    this.gotoSection(null, index, showError);
  }

  handleActionBottomSheetUpdate = () => {
    this.setState({ isActionBottomSheetVisible: false });
    const { activeCardData } = this.state;
    const { addonSubType = '', action = '' } = activeCardData || {};

    this.props.selectInsurance(activeCardData);
    //Tracking
    if (!isEmpty(addonSubType)) {
      vppCaptureReviewClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        eventName: getAddonCode(addonSubType),
        suffix: action,
        subPageName: '',
        prop1: VPP_TRACKING_PAGES.REVIEW,
      });
    }
  };

  handleActionBottomSheetClose=()=>{
  this.setState({
    isActionBottomSheetVisible:false
   })
  }
  trackReviewLocalClickEventInComponent = (name, suffix, { prop1 = '', prop66 = '', currOmniData={}} = {}) => {
    const {
      gcBucket = null,
      myCashBucket = null,
      effectivePriceBucket = null,
    } = this.props?.mmtBlackDetail?.mmtBlackPdtData?.mmtBlackBucketDetail || {};
    const omniData = {
      ...currOmniData,
      [TRACKING_EVENTS.M_C1]: prop1,
      [TRACKING_EVENTS.M_C66]: prop66,
    };
    const evar46Data = {
      [TRACKING_EVENTS.M_V46]: `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`,
    };
    const trackingData = this.props?.mmtBlackDetail ? { ...omniData, ...evar46Data } : omniData;
    trackReviewLocalClickEvent(name, suffix, this.props.reviewData, false, {
      omniData: trackingData,
    });
  };
  trackFabClickEvents = (params) => {
    const {
      eventName,
      suffix,
      prop1,
      prop66,
      omniData,
  }=params || {}
    this.trackReviewLocalClickEventInComponent(eventName, suffix,  { prop1, prop66, currOmniData: omniData  });
  };

  captureReviewClickEventsInComponent = ({ name = '', suffix = '', actionType = {}, prop1 = '', prop66 = '' }) => {
    logHolidayReviewPDTClickEvents({
      actionType : !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value : name + suffix,
      shouldTrackToAdobe:isEmpty(actionType)
    })
    this.trackReviewLocalClickEventInComponent(name, suffix, { prop1, prop66 });
  };

  trackInformationStripClickEvent = (name) => {
    this.captureReviewClickEventsInComponent({ name: 'ReviewUpdate_Price', suffix: name });
  };
  handleExpert = () => {
    HolidayNavigation.pop();
    if (this.props.handleContactExpertClick) {
      this.props.handleContactExpertClick();
    }
  };

  onViewableItemsChanged = ({ viewableItems }) => {
    if (viewableItems.length > 0 && !this.state.showOnce) {
      const isTravellersSectionVisible = viewableItems.some(
        (item) => item.index === SECTIONS.TRAVELLERS && item?.isViewable,
      );
      if (isTravellersSectionVisible) {
        this.setState({ travellerPill: false, showOnce: true });
      }
    } else if (viewableItems.length === 0 && !this.state.travellerPill && !this.state.showOnce) {
      this.setState({ travellerPill: true, showOnce: true });
    }
  };

  getTravellerPill = () => {
    const pillpress = () => {
      this.gotoSection(null, SECTIONS.TRAVELLERS);
      this.trackReviewLocalClickEventInComponent('scroll_traveller_form');
    };
    const { showPriceChange } = this.state || {};
    const gradientColors = isLuxeFunnel() ? LUXE_CTA_GRADIENT_COLORS : [colors.lavender, colors.governorBay]
    const luxeTextStyle = isLuxeFunnel() &&  {
      color: holidayColors.black,
    };
    const luxIconStyle = isLuxeFunnel() && {
      tintColor: holidayColors.black
    }
    const bottomWidth =
      this.priceChange && showPriceChange
        ? travellerPillWidth.withPersuasion
        : travellerPillWidth.withoutPersuasion;
    return (
      <TouchableOpacity
        style={[styles.travellerPillContainer, { bottom: bottomWidth }]}
        onPress={pillpress}
      >
        <LinearGradient
          start={{
            x: 0.0,
            y: 0.0,
          }}
          end={{
            x: 1.0,
            y: 1.0,
          }}
          colors={gradientColors}
          style={[styles.travellerPillContainerNew, getFabBorderStyle()]}
        >
          <Image source={downIcon} style={[styles.travellerpillIcon, luxIconStyle]} />
          <Text style={[styles.pillText, luxeTextStyle]}>Scroll to add traveller details</Text>
        </LinearGradient>
      </TouchableOpacity>
    );
  };
  initiateGiftCardAttachment = async () => {
    await this.props.attachGiftCard();
  };

  /**
   * Handles the click event for the gift card button.
   * This method closes the review popup, then either initiates the gift card attachment process
   * or captures a "Got It" click event, depending on the button type.
   */
  handleGiftCardButtonClick = () => {
    const { mmtBlackDetail } = this.props;
    const isAttachCardButton = mmtBlackDetail.bottomSheet?.ctaButton?.type === MMT_BLACK_ATTACH_CARD_CALL;

    this.setState({ showReviewPopUp: false });

    if (isAttachCardButton) {
      this.initiateGiftCardAttachment();
      this.captureReviewClickEventsInComponent({ name: PDTConstants.GC_POPUP_CLICK_ADD });
    } else {
      this.captureReviewClickEventsInComponent({ name: PDTConstants.GC_POPUP_CLICK_GOT_IT });
    }
  };

  handleReviewPopupClose = () => {
    this.setState({ showReviewPopUp: false });
    this.captureReviewClickEventsInComponent({ name: PDTConstants.GC_POPUP_CLICK_CLOSE});
  }
  handleReviewContinueWithoutClaimClose = () => {
    this.setState({ showReviewPopUp: false });
    this.captureReviewClickEventsInComponent({ name: PDTConstants.GC_POPUP_CLICK_WITHOUT_CLAIM});
  }
  handleTermConditionClick = (url) => {
    this.setState({ showReviewPopUp: false });
    this.captureReviewClickEventsInComponent({ name: PDTConstants.GC_POPUP_CLICK_TC});
    openGenericDeeplink({ url})
  }

  tryAgainCount =()=>{
      this.setState({retryCount:this.state.retryCount+1});
  }
  renderContent() {
    const {
      showPriceChange,
      listData,
      completedTravellersList,
      showOvernightPopup,
      paymentPageData,
      travellerPill,
      showComponentFailureToast,
      isConfigAvailable,
    } = this.state;
    const {
      reviewLoading,
      reviewData,
      dealDetail,
      validateCoupon,
      couponData,
      validateTravellerForm,
      reviewPackageLoading,
      unfilledTravellerList,
      dynamicFormList,
      validateZC,
      travellerFormOptionsDetails,
      paymentScheduleOptions,
      pricingDetail,
      selectPaymentOption,
      packageReviewDifferenceDetail = {},
      additionalDetail,
      packagePersuasionSection,
      emiDetail,
      fabCta = {},
    } = this.props || {};
    const { infos } = packagePersuasionSection || {};
    this.priceChangePersuasion = infos?.filter((el) => el.infoType === PRICE);
    const { dynamicFormData } = reviewData;
    this.priceChange = showPriceChangeUtil(reviewData?.reviewDetail);
    this.priceChangeValue = getPriceChangeDifference(reviewData?.reviewDetail);
    this.travellerList =
      completedTravellersList && completedTravellersList.length > 0
        ? completedTravellersList
        : unfilledTravellerList;

    const countdownEndTime = reviewData?.reviewDetail?.metadataDetail?.countdownEndTime;
    const roomDetail = reviewData?.reviewDetail?.roomDetail || {};
    const { noOfAdults, children } = getTotalTravellers(roomDetail);
    const totalTravellers = noOfAdults + children;
    const paymentSchedules = paymentScheduleOptions?.paymentSchedules;
    const partPayment =
      paymentSchedules?.length > 0
        ? paymentSchedules?.find((e) => e.paymentType === PART_PAYMENT_OPTION)
        : [];
    const reservePrice = partPayment?.installmentDetails?.find(
      (e) => !e.partPaymentDate,
    )?.partPaymentValue;
    const pricePersuaion = this.priceChangePersuasion?.[0];

    const insuranceAddonDetail = this?.props?.insuranceAddonDetail

    // Calculate FAB position using utility function
    const fabContainerStyle = calculateFabPosition({
      showComponentFailureToast,
      showNewUpdateSection: this.showNewUpdateSection,
      showPriceChange,
      priceChange: this.priceChange,
      isConfigAvailable,
      packageReviewDifferenceDetail,
      travellerPill,
    });

    return (
      <View style={[AtomicCss.flex1, styles.viewContainer]}>
          <FilterLoader
            showCenterLoader={true}
            loadingFirstTime={false}
            show={Boolean(reviewLoading || reviewPackageLoading)}
            style = {this.reviewBottomSheet ? {backgroundColor: 'rgba(0,0,0,0)'} : {}}/>
        <DownloadApp />
          <PageHeader
            showBackBtn
            showShadow
            title={'Review Page'}
            subTitle={reviewData?.reviewDetail?.name}
            onBackPressed={this.goBack}
            containerStyles={styles.reviewHeaderStyle}
        />
          <FlatList
            ref={(ref) => (this.flatList = ref)}
            style={paddingStyles.pa16}
            data={listData}
            renderItem={this.renderItem}
            onLayout={this.onLayout}
            onScroll={this.onScroll}
          keyExtractor={(item) => item.id}
            ListHeaderComponent={this.renderListHeader}
            ListFooterComponent={this.renderListFooter}
            ItemSeparatorComponent={this.renderItemSeparator}
            viewabilityConfig={{ viewAreaCoveragePercentThreshold: 20 }}
            onViewableItemsChanged={this.onViewableItemsChanged}
            maxToRenderPerBatch={100}
            windowSize={1}
            initialNumToRender={100}
          />
        {travellerPill && this.getTravellerPill()}


        {this.state.showTravellerDetailPage && isRawClient() ?
          <View style={styles.showTravellerDetailPageStyle}>
            <TravellerDetails
              onFormFilled={this.onFormFilled}
              savedTravellerList={this.state.savedTravellerList}
              travellerList={this.travellerList}
              dynamicFormList={dynamicFormList}
              formIndex={this.state.travellerIndex}
              dynamicFormData={dynamicFormData}
              onBackPress={() => this.setState({ showTravellerDetailPage: false })}
              validateTravellerForm={validateTravellerForm}
              trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
              listData={this.state.listData}
              closeTravellerDetails={this.closeTravellerDetails}
              updateTravellerList={this.updateTravellerList}
              fromAmendment={this.holidayReviewData.fromAmendment}
            />
          </View> : []}
          {this.state.showTravellerDetailPage && !isRawClient() ?
            (<View style={styles.travellerDetailPageContainer}>
            <TravellerDetails
              onFormFilled={this.onFormFilled}
              savedTravellerList={this.state.savedTravellerList}
              travellerList={this.travellerList}
              dynamicFormList={dynamicFormList}
              formIndex={this.state.travellerIndex}
              dynamicFormData={dynamicFormData}
              onBackPress={() => this.setState({ showTravellerDetailPage: false })}
              validateTravellerForm={validateTravellerForm}
              trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
              listData={this.state.listData}
              closeTravellerDetails={this.closeTravellerDetails}
              updateTravellerList={this.updateTravellerList}
              fromAmendment={this.holidayReviewData.fromAmendment}
            />
          </View>
        ) : (
          []
        )}
        {this.props.componentFailureData && (
          <ComponentFailurePopUp
            componentFailureData={this.props.componentFailureData}
            onChangePress={this.componentFailureChangeButtonHandling}
            onCancelPress={this.componentFailureCancelButtonHandling}
            openListingPage={this.openListingPage}
            fromDeeplink={!!this.props[ReviewParams.fromReviewDeeplink]}
            componentAccessRestriction={this.props.componentAccessRestriction}
            fromPresalesDetail={
              this.holidayReviewData.reviewType === sectionCodes.PRESALES ? true : false
            }
            handleContactExpertClick={this.handleExpert}
          />
        )}
        {this.state.showComponentFailureToast && (
          <ComponentFailureToastMessage
            componentFailureData={this.props.componentFailureData}
            onButtonPress={this.componentFailureChangeButtonHandling}
            openListingPage={this.openListingPage}
            componentAccessRestriction={this.props.componentAccessRestriction}
            fromDeeplink={!!this.props[ReviewParams.fromReviewDeeplink]}
            fromPresalesDetail={
              this.holidayReviewData.reviewType === sectionCodes.PRESALES
            }
            handleContactExpertClick={this.handleExpert}
          />
        )}
        {this.state.showTcsBottomSheet &&(
          <HolidaysReviewTcs
            setTcsBottomSheetVisibility={this.setTcsBottomSheetVisibility}
            setFareBreakupVisibilityForTcs={this.setFareBreakupVisibilityForTcs}
            fareBreakupVisibilityForTcs={this.state.fareBreakupVisibilityForTcs}
            handlePrePayment={this.handlePrePayment}
            onBackPressed={this.handleBackPressedFromTCS}
            listData={listData}
            validateZC={validateZC}
            holidayReviewData={this.holidayReviewData}
            gotoSection={this.gotoSection}
            pricingDetail={pricingDetail}
            emiDetail={emiDetail}
            trackReviewLocalClickEventInComponent={this.trackReviewLocalClickEventInComponent}
          />
        )}
        {_.isEmpty(this.state.popup) && !this.state.showTravellerDetailPage && (
          <View style={styles.footerWrapper}>
            {/*This showTcsBottomSheet was added due
             to bug introduced in holidays due to travelplex */}
            {!this.state.showTcsBottomSheet && !this.state.showTravellerDetailPage && (
              this.showNewUpdateSection
                ? showPriceChange &&
                  !isEmpty(pricePersuaion) && (
                    <PriceChangeCard
                      newPrice={this.priceChangeValue}
                      onClose={this.hidePriceChangeBanner}
                      persuasionData={this.priceChangePersuasion?.[0]}
                      trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
                    />
                  )
                : this.state?.isConfigAvailable && (
                    <InformationStrip
                      headerInfo={packageReviewDifferenceDetail.headerInfo}
                      sectionInfo={packageReviewDifferenceDetail.sectionInfo}
                      shouldShow={true}
                      change={packageReviewDifferenceDetail?.change}
                      trackClickEvent={this.trackInformationStripClickEvent}
                    />
                  )
            )}

            {/*This showTcsBottomSheet was added due
             to bug introduced in holidays due to travelplex */}
            {!this.state.showReviewPopUp && !this.state.showTcsBottomSheet && !this.state.showTravellerDetailPage && (
              <PhoenixReviewPageToastV2
                ref={this.snackbarRef}
                trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
                addGiftCard={this.initiateGiftCardAttachment}
                attachGiftCardStatus={this.props.attachGiftCardStatus}
                attachGiftCardData={this.props.attachGiftCardData}
                retryCount={this.state.retryCount}
                tryAgainCount={this.tryAgainCount}
              />
            )}
            {/*This showTcsBottomSheet was added due
             to bug introduced in holidays due to travelplex */}
            {!this.state.showTcsBottomSheet && !this.state.showTravellerDetailPage && (
              <ReviewFooter
                key={JSON.stringify(pricingDetail)}
                btnTxt={
                  paymentScheduleOptions?.paymentSchedules.length > 1
                    ? REVIEW_FOOTER_BTN_TEXT.BOOKING_OPTIONS
                    : REVIEW_FOOTER_BTN_TEXT.CONTINUE
                }
                reviewData={reviewData}
                dealDetail={dealDetail}
                validateCoupon={validateCoupon}
                couponData={couponData}
                timerStarted={this.timeNow}
                validateAddOns={this.validateAddOns}
                startPayment={this.startPayment}
                countdownEndTime={countdownEndTime}
                disableBookingButton={!isEmpty(this.props.componentFailureData)}
                listData={this.state.listData}
                gotoSection={this.gotoSection}
                validateZC={validateZC}
                trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
                pricingDetail={pricingDetail}
                paymentScheduleOptions={paymentScheduleOptions}
                selectPaymentOption={selectPaymentOption}
                totalTravellers={totalTravellers}
                fromAmendment={this.holidayReviewData.fromAmendment}
                callbackHandler={(type, params) => this.callbackHandler(type, params)}
                showOvernightPopup={showOvernightPopup}
                goToPaymentPage={this.goToPaymentPage}
                setShowOvernightPopupState={this.setShowOvernightPopupState}
                updateReviewFooter={this.updateReviewFooter}
                holidayReviewData={this.holidayReviewData}
                reviewPopUp={this.state.reviewPopUp}
                pageName={REVIEW_PAGE_NAME}
                insuranceAddonDetail={insuranceAddonDetail}
                fareBreakUpCardData={this.props.mmtBlackDetail.bottomSheet || {}}
              />
            )}
          </View>
        )}

        {this.state.showTcsBottomSheet && !this.state.showTravellerDetailPage && (
          <HolidaysReviewTcs
            setTcsBottomSheetVisibility={this.setTcsBottomSheetVisibility}
            setFareBreakupVisibilityForTcs={this.setFareBreakupVisibilityForTcs}
            fareBreakupVisibilityForTcs={this.state.fareBreakupVisibilityForTcs}
            handlePrePayment={this.handlePrePayment}
            onBackPressed={this.handleBackPressedFromTCS}
            listData={listData}
            validateZC={validateZC}
            holidayReviewData={this.holidayReviewData}
            gotoSection={this.gotoSection}
            pricingDetail={pricingDetail}
            emiDetail={emiDetail}
            trackReviewLocalClickEventInComponent={this.trackReviewLocalClickEventInComponent}
          />
        )}
        {this.state.popup === SPECIAL_REQUEST_OVERLAY && (
          <AddRequestOverlayDynamic
            closePopUp={this.closePopUp}
            form={this.state.selectedCollapsedForm}
            data={this.state.collapsedFormData}
            options={travellerFormOptionsDetails}
            updateCollapsedData={this.updateCollapsedData}
          />
        )}
        <PhoenixReviewOverlays />
        {this.state.isToShowInsuranceAddOnBottomSheet &&
          <NotOptedBottomSheet
            insuranceAddonDetail={insuranceAddonDetail}
            onCancel={()=>{
              this.setState({
                isToShowInsuranceAddOnBottomSheet :false
              })
            }}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            onPositiveClick={(insuranceAddon) => {
              this.selectInsurance(insuranceAddon, true);
              this.setState({ isToShowInsuranceAddOnBottomSheet :false });
              this.trackPDTExitEvent();
            }}
            onNegativeClick={() => {
              this.setState({
                isToShowInsuranceAddOnBottomSheet :false,
                reviewPopUp:true,
              })
              this.trackPDTExitEvent();
            }} />
        }
        {
          this.state.isToShowOldTravellerBottomSheet &&
          <OldTravellerBottomSheet
            promptMessage={this.props?.validateResponse?.promptMessage}
            insuranceAddonDetail={insuranceAddonDetail}
            trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
            price={getPackagePrice(pricingDetail)}
            onPositiveClick={() => {
                this.setState({
                  reviewPopUp:true,
                  isToShowOldTravellerBottomSheet: false
              })
          }}
            onCancel={()=>{
              this.setState({
                isToShowOldTravellerBottomSheet :false
              })
            }}
          />
        }
         {this.state.showReviewPopUp && showMMTBlack() && this.props.mmtBlackDetail?.bottomSheet && (
          <BottomSheet
            containerStyle={styles.bottomSheetContainer}
            onBackPressed={(this.props.mmtBlackDetail?.bottomSheet?.ctaButton?.type === MMT_BLACK_ATTACH_CARD_CALL && !this.props.attachGiftCardData?.success) ? noop : this.handleReviewPopupClose}
          >
            <MMTBlackBottomSheet
              trackReviewLocalClickEvent={this.trackReviewLocalClickEventInComponent}
              ctaButtonClick={this.handleGiftCardButtonClick}
              togglePopup={this.handleReviewPopupClose}
              bottomSheetDetail={this.props.mmtBlackDetail.bottomSheet}
              attachGiftCardData={this.props.attachGiftCardData}
              isGifVisible={this.state.isGifVisible}
              handleTermConditionClick={this.handleTermConditionClick}
              handleContinueWithoutClaim={this.handleReviewContinueWithoutClaimClose}
            />
          </BottomSheet>
        )}
        {this.state.showVPPBottomSheet &&
          <VPPBottomSheet
           addonType={this.props?.vppAddonDetail?.addons[0]?.addonType}
           addonSubType={this.props?.vppAddonDetail?.addons[0]?.addonSubType}
           toggleBottomSheet={this.setVPPBottomSheetVisibility}
           trackLocalClickEvent={this.trackReviewLocalClickEventInComponent}
          />
        }

        {this.holidayReviewData.reviewType !== sectionCodes.PRESALES
          && !this.state.showReviewPopUp
          && !this.state.showTcsBottomSheet
          && !this.state.showTravellerDetailPage &&
          (
          <HolidayFabAnimationContainer
            ref={(ref) => {
              if (this.props.registerFabAnimationRef) {
                this.props.registerFabAnimationRef(ref);
              }
            }}
            fabCta={fabCta}
            pageName={REVIEW_PDT_PAGE_NAME}
            configId={HLD_PAGE_NAME.REVIEW}
            textShrinked={this.state.fabTextShrinked && !showFabAnimationExtended()}
            containerStyle={{ bottom: fabContainerStyle.baseBottom }}
            fabData={createReviewFabData(this.props.reviewData)}
            startChat={this.startChat}
            unmountIntervention={this.props.unmountIntervention}
            trackLocalClickEvent={this.trackFabClickEvents}
            trackPageExit={this.trackPageExit}
            trackPDTV3Event={logHolidayReviewPDTClickEvents}
            travelPlexConfigData={{
              ...getReviewPDTObj(),
              dynamicPackageId: this.holidayReviewData.dynamicPackageId,
              to_date_time: calculateToDateTime(
                this.props.reviewData?.reviewDetail?.departureDetail?.departureDate,
                this.holidayReviewData.duration
              ),
              product: [{
                id: this.holidayReviewData.dynamicPackageId,
                name: this.props.reviewData?.reviewDetail?.name || this.holidayReviewData.packageName || '',
              }],
              attr1: this.props.holidayReviewData?.branch || DOM_BRANCH,
              attr2: this.props.reviewData.reviewDetail?.tagDestination.name,
              attr4: this.travelPlexAttr4?.allocationDetail?.queueIdentifier,
              hideInput: !showHolAgentOnDetailAndReviewPage(),
            }}
          />
        )}
        <FilterLoader
          showCenterLoader={true}
          loadingFirstTime={false}
          show={Boolean(reviewLoading || reviewPackageLoading)}
          style={this.reviewBottomSheet ? { backgroundColor: 'rgba(0,0,0,0)' } : {}}
        />
         {this.state.isActionBottomSheetVisible && !isEmpty(this.state.activeCardData) && (
                <ActionBottomsheet
                  isVisible={this.state.isActionBottomSheetVisible}
                  popUpValue={this.state.activeCardData.addonSubType}
                  onUpdateClick={this.handleActionBottomSheetUpdate}
                  goBackToInitialState={this.handleActionBottomSheetClose}
                />)
              }
      </View>
    );
  }

  updateReviewFooter = (value) => {
    this.reviewBottomSheet = value;
    this.setState({
      reviewPopUp:false,
    })
  }
  renderProgressView = () => (
    <View style={styles.loader}>
      <HolidayDetailLoader loadingText="Loading ..." />
    </View>
  );
  checkIsPackageAddonSection = () => {
    const { penaltyDetail = {} } = this.props || {};
    const addons = penaltyDetail?.zcOptions;
    const sectionsOrder = getPhoenixReviewSectionsOrder(SECTIONS);
    const hideAddons =
      addons?.length === 0 ||
      (addons?.length > 0 &&
        !addons.some((addon) => addon.available === true) &&
        (this.props?.vppAddonDetail === undefined) &&
        ((this.props?.insuranceAddonDetail === undefined) || !showInsuranceSection()));

    let sectionsData = [];
    let finalSectionData = [];

    /* Store Sections in array according to the sequence given by pokus variable */
    sectionsOrder.map((sectionId) => {
      const section = SECTIONS_DATA.find((item) => item.id === sectionId);
      if (section) {
        sectionsData.push(section);
        finalSectionData.push(section);
      }
    });

    /* Add Sections which are not present in pokus */

    // filter out all the sections which were not present in pokus call
    let leftOutSections = [];
    [...Object.values(SECTIONS)]
      .filter((x) => ![...sectionsOrder].includes(x))
      .forEach((sectionId) => {
        const sectionData = SECTIONS_DATA.find((item) => item.id === sectionId);
        if (sectionData) {
          leftOutSections.push(sectionData);
        }
      });

    sectionsData.push(...leftOutSections);
    finalSectionData.push(...leftOutSections);

    /* Add Sections which are not present in pokus */

     if (hideAddons) {
      finalSectionData = sectionsData.filter((accordian) => accordian.id !== SECTIONS.ADD_ONS);
    } else {
      // send event that package addon is visible in review page
      this.captureReviewClickEventsInComponent({
        name: 'Viewed_section_addon',
        prop1: getPackageAddonsProp1(
          this.props.penaltyDetail?.zcOptions?.addons,
          this.props?.insuranceAddonDetail,
          this.props?.vppAddonDetail,
        ),
        actionType: PDT_EVENT_TYPES.contentSeen,
      });
    }

    if(this.holidayReviewData.quoteRequestId) {
      finalSectionData = finalSectionData.filter(accordian => (accordian.id !== SECTIONS.COUPONS));
    }
    if(this.holidayReviewData.quoteRequestId) {
      finalSectionData = finalSectionData.filter(accordian => (accordian.id !== SECTIONS.COUPONS));
    }

    /* If pokus value is true, sections should remain expanded at all times */
    if (this.isSectionsExpanded) {
      finalSectionData.map((section) => {
        section.expanded = true;
      });
    }
    this.maxSteps = [...finalSectionData].filter((item) => !item?.isIgnoreInSteps)?.length || 0;
    finalSectionData[SECTIONS.TRAVELLERS].expanded = true;
    return finalSectionData;
  };

  selectInsurance = (insuranceAddOn,withPayment, skipPopup = true) =>{
    if(insuranceAddOn?.id && this.props.reviewData?.reviewDetail?.dynamicId){
      const { addonSubType = '', isSelected } = insuranceAddOn || {};
      const action = isSelected ? 'REMOVE' : 'SELECT';
      const requestBody = {
        "action": insuranceAddOn.isSelected ?"REMOVE" : "SELECT",
        "addonId": insuranceAddOn.id,
        "addonSubType":insuranceAddOn.addonSubType,
        "dynamicPackageId": this.props.reviewData?.reviewDetail.dynamicId
      }
      this.props.selectInsurance(requestBody);
      if(withPayment){
        this.isStartPaymentAfterInsurance = true;
      }
    }
  }

  render() {
    return (
      <View style={{ flex: 1 }}>
        {(this.props.isLoading || this.props.reviewPackageLoading) && this.renderProgressView()}
        {this.props.isError && this.renderError()}
        {this.props.isSuccess && this.renderContent()}
      </View>
    );
  }
  renderError = () => {
    const { errorMessage, codeValue } = fetchReviewErrorMessage(this.props.error);
    return (
      <HolidayReviewError
        startChat={this.startNoPkChat}
        duration={this.holidayReviewData.duration}
        onBackPressed={this.goBack}
        packageName={this.holidayReviewData.packageName || 'Holidays Review'}
        errorMessage={errorMessage}
        codeValue={codeValue}
      />
    );
  };
}
const styles = StyleSheet.create({
  progressContainer: {
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
    width: '100%',
    height: '100%',
    backgroundColor: holidayColors.red,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loader: {
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  viewContainer: {
    backgroundColor: holidayColors.lightGray2,
  },
  travellerPillContainer: {
    backgroundColor: 'transparent',
    ...holidayBorderRadius.borderRadius16,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    flexDirection: 'row',
    left: '25%',
    zIndex: 1,
    ...AtomicCss.alignCenter,
  },
  travellerPillContainerNew: {
    backgroundColor: 'transparent',
    ...holidayBorderRadius.borderRadius16,
    height: 30,
    ...paddingStyles.ph14,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  priceChange: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 70,
    zIndex: 15,
  },
  scrollSection: {
    ...paddingStyles.pa16,
  },
  footerWrapper: {
    position: 'relative',
    elevation: 13,
    zIndex: 15,
  },
  footerWithTCS: {
    ...marginStyles.mt12,
    marginBottom: 60,
  },
  footerWithoutTCS: {
    marginBottom: 60,
  },
  travellerpillIcon :{
    height:15,
    width:15,
    tintColor:holidayColors.white,
    ...marginStyles.mr6,
  },
  pillText:{
    color: holidayColors.white,
    ...fontStyles.labelSmallRegular,
  },
  tcsBannerStripWrapper : {
    ...marginStyles.mb10,
    backgroundColor:colors.white,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  travellerDetailPageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  reviewHeaderStyle: {
    zIndex: 2
  },
  tcsWidgetContainer: {
    ...marginStyles.mt16,
  },
  tcsWidgetContainerError: {
    ...marginStyles.mt16,
    borderColor: holidayColors.red,
  },
  showTravellerDetailPageStyle : {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex:1000,
    background: holidayColors.white,
    height: '100%',
    width: '100%'
  },
});

const mapDispatchToProps = {
  showReviewOverlay,
  hideReviewOverlays,
};
export default withBackHandler(withIntervention(connect(null, mapDispatchToProps)(PhoenixReviewPage,REVIEW_PDT_PAGE_NAME)));
