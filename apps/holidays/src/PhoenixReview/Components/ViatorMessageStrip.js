import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import HolidayImageHolder from '../../Common/Components/HolidayImageHolder';
import { isEmpty } from 'lodash';

export const VIATOR_MESSAGE_STATE = {
  EXPANDED: 'expaned',
  COLLPASED: 'collapsed',
};
const ViatorMessageStrip = ({ viatorInfo = {}, state = VIATOR_MESSAGE_STATE.COLLPASED }) => {
  const {
    collapsedValue = '',
    iconUrl = '',
    expandedValue = '',
  } = viatorInfo || {};
  if( isEmpty(viatorInfo)) {
    return null
  }
  return state === VIATOR_MESSAGE_STATE.COLLPASED ? (
    <View style={styles.container}>
      <View style={styles.row}>
        <HolidayImageHolder imageUrl={iconUrl} style={styles.image} />
        <Text style={styles.submessage}>{collapsedValue}</Text>
      </View>
    </View>
  ) : (
    <Text style={styles.expandedMessage}>{expandedValue}</Text>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.fadedYellow,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pa16,
    ...marginStyles.mh16,
    ...marginStyles.mb16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'base-line',
  },
  image: {
    width: 23,
    height: 20,
    ...marginStyles.mr8,
    ...marginStyles.mt4,
    // Add your image styles here
  },
  title: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  submessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flex: 1,
  },
  expandedMessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.yellow,
  },
});

export default ViatorMessageStrip;
