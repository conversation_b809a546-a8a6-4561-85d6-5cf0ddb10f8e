import { useEffect } from 'react';

/**
 * Custom hook to handle back button press - Web version (no-op since web doesn't have hardware back button)
 * @param {Function} onBackPress - Callback function to handle back press
 * @returns {void}
 */
const useBackHandler = (onBackPress) => {
  // Web doesn't have hardware back button, so this is essentially a no-op
  // We could potentially listen to browser back button events here if needed
  useEffect(() => {
    // No-op for web - hardware back button doesn't exist
  }, [onBackPress]);
};

export default useBackHandler;
