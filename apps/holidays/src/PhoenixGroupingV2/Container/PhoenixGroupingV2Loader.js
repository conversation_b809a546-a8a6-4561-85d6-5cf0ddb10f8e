import React from 'react';
import HolidayPageLoader from '../../Common/Components/PageLoader';
import { getCityCampaignDisplayName } from '../../LandingNew/Utils/DestinationDepartureCityUtils';
import * as DateUtils from 'mobile-holidays-react-native/src/utils/HolidayDateUtils';
import { connect } from 'react-redux';
import { isEmpty } from 'lodash';

const PhoenixGroupingV2Loader = ({ userDepCity = '', holidayLandingGroupDto }) => {
  const {
    userDepCity: userDepartureCity = '',
    destinationCityData = {},
    campaign = '',
    selectedDate = '',
    packageDate = '',
    fromDate = '',
    toDate = '',
  } = holidayLandingGroupDto || {};

  const getLoaderDataObject = () => {
    return {
      departureCity:
        !isEmpty(userDepartureCity) && typeof userDepartureCity !== 'object'
          ? userDepartureCity
          : typeof userDepCity !== 'object'
          ? userDepCity
          : '',
      destinationCity: getCityCampaignDisplayName(destinationCityData),
      campaign: campaign,
      departureDate: DateUtils.getFormattedDateWithoutDayName(
        DateUtils.getDateObject(selectedDate || packageDate || fromDate || toDate),
      ),
      showFilters: true,
    };
  };

  return (
    <HolidayPageLoader
      showDateText={false}
      changeAction
      loadingText="Loading Packages..."
      {...getLoaderDataObject()}
    />
  );
};

const mapStateToProps = (state) => {
  return {
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
  };
};

export default connect(mapStateToProps)(PhoenixGroupingV2Loader);
