import { PDT_PAGE_VIEW } from '../../HolidayConstants';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { trackHolidayGroupingLoadEvent } from '../../utils/HolidayGroupingTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { fetchFabCtaV2 } from '../../utils/NetworkUtils/commonApis';
import { roomDefault } from '../../utils/RoomPaxUtils';
import { sendMMTGtmEvent } from '../../utils/ThirdPartyUtils';
import { logHolidaysGroupingPDTEvents } from '../Utils/PhoenixGroupingV2PDTTrackingUtils';
import {
  createPhoenixGroupingV2PdtData,
  getPhoenixGroupingV2PageName,
  populateCommonOnmiData,
} from '../Utils/PhoenixGroupingV2TrackingUtils';
import { FAB_CTA_ACTION_TYPES } from './PhoenixGroupingV2ActionTypes';
export const fetchFabCtaAction = ({
  destinationCity,
  showFabFromDeeplink,
  cmp,
  pageName,
  holidayLandingGroupDto = {},
  evar22 = '',
}) => async (dispatch) => {
  const fabCta = await fetchFabCtaV2({
    data: {
      destinationCity: destinationCity,
      showFabFromDeeplink: showFabFromDeeplink,
      cmp: cmp,
    },
    pageName,
    packageId: '',
  });

  let eventName = 'load_fab_none';
  const { isListing } = holidayLandingGroupDto || {};
  if (fabCta?.showFab) {
    eventName = `load_${fabCta?.showCall ? 'C' : ''}${fabCta?.showQuery ? 'Q' : ''}${
      fabCta?.showChat ? 'Ch' : ''
    }${fabCta?.branchLocator ? 'B' : ''}`;
  }
  logHolidaysGroupingPDTEvents({
    actionType: PDT_EVENT_TYPES.pageRenedered,
    value: eventName,
    shouldTrackToAdobe:false
  })
  trackHolidayGroupingLoadEvent({
    logOmni: true,
    omniPageName: getPhoenixGroupingV2PageName({ isListing }),
    omniData: {...populateCommonOnmiData({
      holidayLandingGroupDto,
      roomDetails: holidayLandingGroupDto?.rooms || [roomDefault],
    }),
    [TRACKING_EVENTS.M_V22]: evar22,
  },
    pdtData: {
      ...createPhoenixGroupingV2PdtData({
        groupingData: holidayLandingGroupDto,
        fabCta,
      }),
      activity: eventName,
      eventType: PDT_PAGE_VIEW,
    },
  });
  sendMMTGtmEvent({ eventName, data: { pageName: 'listing' } });

  dispatch(fabCtaSuccess(fabCta));
};

export const fabCtaSuccess = (fabCtaDetail) => ({
  type: FAB_CTA_ACTION_TYPES.FAB_CTA_DATA_SUCCESS,
  fabCtaDetail,
});
