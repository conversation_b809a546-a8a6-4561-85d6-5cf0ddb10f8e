import { cloneDeep, isEmpty } from 'lodash';
import {
  createRandomString,
  getDepartureCity,
  isRawClient,
  setCategoryTrackingEvent,
} from '../../utils/HolidayUtils';
import { fetchGroupingSections, getAvailableHubsCities } from '../../utils/NetworkUtils/commonApis';
import {
  checkIsListingRedirection,
  getFiltersFromQueryParams,
  getFirstGroupWithPackages,
  updateObjectWithLocusInformation,
} from '../Utils/PhoenixGroupingV2Utils';
import { fetchGroupMetaDataDetail, setSelectedGroupKey } from './groupMetaActions';
import {
  fetchListingPackages,
  listingPackagesLoading,
  recentPackageLoading,
  recentPackageSuccess,
  resetListingPackages,
} from './listingPackagesActions';
import {
  fetchMetaDataDetail,
  isMetaDataLoading,
  metaSuccess,
  resetMetaIfNoPackage,
} from './metaDataActions';
import {
  HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE,
  PHOENIX_GROUPING_V2_ACTION_TYPES,
} from './PhoenixGroupingV2ActionTypes';
import { fetchRecentlySeenPackages } from '../../utils/HolidayNetworkUtils';
import {
  setAvailableHubs,
  updateDepCity,
} from '../../SearchWidget/Actions/HolidaySearchWidgetActions';
import { USER_DEFAULT_CITY } from '../../HolidayConstants';
import { getAbortController, resetAbortController } from '../../utils/NetworkUtils/AbortController';
import {
  createPhoenixGroupingV2LoggingMap,
  getPhoenixGroupingV2PageName,
} from '../Utils/PhoenixGroupingV2TrackingUtils';
import { fetchFabCtaAction } from './fabCtaActions';
import { PHOENIX_GROUPING_V2_SECTIONS } from '../Contants';
import { getHolRecentSearchExpireDays, showNewRVSSection } from '../../utils/HolidaysPokusUtils';
import { logHolidaysGroupingPDTEvents } from '../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { updateRecentSearchHistoryForCommon } from '../../LandingNew/Utils/HolidayLandingUtils';
const store = isRawClient() ? require('web/webStore').default : require('@mmt/legacy-commons/AppState/Store').store;

export const updateGroupDtoByMeta = ({
  holidayLandingGroupDto = {},
  metaDataResponse = {},
  isFromDeeplink = false,
}) => {
  const categoryTrackingEvent = setCategoryTrackingEvent({
    campaign: holidayLandingGroupDto?.campaign || null,
    filters: holidayLandingGroupDto?.filters || [],
    destinationThemes: metaDataResponse?.headerDetail?.destinationThemes || [],
  });

  let filters = holidayLandingGroupDto?.filters || [];

  /* Update filters is page is opened from deep link */
  if (Object.keys(holidayLandingGroupDto?.queryParams || {})?.length > 0 && filters.length < 1) {
    filters = getFiltersFromQueryParams(
      metaDataResponse?.listingFilters,
      holidayLandingGroupDto.queryParams,
    );
  }

  const pageDataMap = createPhoenixGroupingV2LoggingMap({
    holidayLandingGroupDto,
    metaResponse: metaDataResponse,
    filters, // using filters created above, since we are updating holidayLandingGroupDTO here with new filters
  });

  const updatedData = {
    ...holidayLandingGroupDto,
    ...updateObjectWithLocusInformation(holidayLandingGroupDto, metaDataResponse),
    masterListingFilters: metaDataResponse?.listingFilters,
    listingSorters: metaDataResponse?.listingSorters,
    filters,
    branch: metaDataResponse?.details?.[0]?.branch || '',
    trackingData: { categoryTrackingEvent },
    pageDataMap,
    requestId: createRandomString(),
    isListing: checkIsListingRedirection({
      holidayLandingGroupDto,
      listingRedirection: metaDataResponse?.listingRedirection,
    }),
  };

  return updatedData;
};

export const loadListingAction = ({
  holidayLandingGroupDto,
  metaDataResponse,
  selectedGroupCollection,
  offset = 0,
  isLoadMore,
  abortController,
  sendPackageIds,
  noPackageFoundResetFilter = false,
}) => async (dispatch) => {
  if (!isLoadMore) {
    await dispatch(resetListingPackages());
    dispatch(listingPackagesLoading());
  }
  await dispatch(
    fetchListingPackages({
      holidayLandingGroupDto,
      metaDataDetail: metaDataResponse,
      selectedGroupCollection,
      offset,
      abortController,
      sendPackageIds, // to pass package Id's if found in metadata/v2
      noPackageFoundResetFilter, // if there was no package found for specific group + user filter, remove group filter
    }),
  );
};

export const trackSectionDataRenderedEvents = ({ eventName, trackLocalClickEvent = () => {} }) => {
  logHolidaysGroupingPDTEvents({
    actionType: PDT_EVENT_TYPES.pageRenedered,
    value: eventName,
    shouldTrackToAdobe:false
  });
  trackLocalClickEvent({ eventName });
};

export const loadRecentyViewewdPackages =
  ({ holidayLandingGroupDto, checkPokusForRVS, trackLocalClickEvent = () => {} }) =>
  async (dispatch) => {
    try {
      dispatch(recentPackageLoading);
      // call personalization/userpackages/fetch to get recently seen packages
      const recentPackages = await fetchRecentlySeenPackages(holidayLandingGroupDto, {
        checkPokusForRVS,
        sectionTypes: [PHOENIX_GROUPING_V2_SECTIONS.PAYMENT_DROP_OFF],
      });
      if (recentPackages?.cards?.length > 0 && checkPokusForRVS && showNewRVSSection()) {
        trackSectionDataRenderedEvents({ eventName: 'RENDERED_SECTION_PDO', trackLocalClickEvent });
      }
      if (recentPackages?.length > 0 && !showNewRVSSection()) {
        trackSectionDataRenderedEvents({ eventName: 'RENDERED_SECTION_RVS', trackLocalClickEvent });
      }
      dispatch(recentPackageSuccess(recentPackages));
    } catch (e) {
      console.log('*** error while fetching recently viewed packages ***', e);
    }
  };
export const loadGroupAndListingAction = ({
  holidayLandingGroupDto,
  metaDataResponse,
  searchQuery = '',
  selectedGroup = null,
  resetMetaIfNoPackageValue = false,
  didResetFilter = false,
}) => async (dispatch) => {
  try {
    if (!didResetFilter && resetMetaIfNoPackageValue) {
      dispatch(resetMetaIfNoPackage(false));
    }
    // decision condition to show page with group collection tabs or show all packages in a list
    const isListingRedirection = checkIsListingRedirection({
      holidayLandingGroupDto,
      listingRedirection: metaDataResponse?.listingRedirection,
    });
    if (isListingRedirection) {
      // make listing call here without groupKey
      await dispatch(
        loadListingAction({
          holidayLandingGroupDto,
          metaDataResponse,
          abortController: getAbortController(),
          sendPackageIds: holidayLandingGroupDto?.packageIds?.length > 0,
        }),
      );
    } else {
      const groupMetaResponse = await dispatch(
        fetchGroupMetaDataDetail({
          holidayLandingGroupDto,
          searchQuery,
          newKeyRequired: true,
          offset: 0,
        }),
      );
      const selectedGroupCollection = getFirstGroupWithPackages({
        groupList: groupMetaResponse?.groupDetailsList,
        selectedGroup,
      });

      dispatch(setSelectedGroupKey(selectedGroupCollection?.groupKey));
      resetAbortController();
      dispatch(
        // made a listing call with group id firstgroupKeyWithPackages
        loadListingAction({
          holidayLandingGroupDto,
          metaDataResponse,
          selectedGroupCollection,
          abortController: getAbortController(),
        }),
      );
    }
  } catch (error) {
    console.log(error, '**** load grouping and listing action error ****');
  }
};

export const initialLoadAction = ({
  holidayLandingGroupDto,
  searchQuery,
  resetIfPackageNotFound = false,
  isFromDeeplink = false,
  trackLocalClickEvent = () => {}
}) => async (dispatch) => {
  try {
    let updatedHolidayLandingGroupDto = {};
    // store holidayLandingGroupDto in redux store for easy access and updation
    dispatch(isMetaDataLoading());
    const metaDataResponse = await dispatch(
      fetchMetaDataDetail({
        holidayLandingGroupDto,
        setPersonalisedData: true,
        resetIfPackageNotFound,
      }),
    );

    // If meta data api failed -> stop further calls and create error page
    if (!metaDataResponse || (metaDataResponse?.error?.code && metaDataResponse?.error?.message)) {
      const { showFab, cmp, destinationCity, isListing = false } = holidayLandingGroupDto || {};
      const storeData = store.getState();
      const error = storeData?.holidaysPhoenixGroupingV2?.metaDataDetail?.error
      const errorMessage = error?.code ? `HLD:${error.code}:${error?.message || ''}` : '';
      dispatch(
        fetchFabCtaAction({
          destinationCity,
          showFabFromDeeplink: showFab,
          holidayLandingGroupDto: holidayLandingGroupDto, // to get udpated state for tracking event
          cmp,
          pageName: getPhoenixGroupingV2PageName({
            isMetaDataError: true,
            isListing: isListing,
          }),
          evar22: errorMessage ,
        }),
      );
      return null;
    }
    const recentSearchData = {
      ...holidayLandingGroupDto,
      apWindow: metaDataResponse?.destinationMeta?.apWindow,
    }
    updateRecentSearchHistoryForCommon(recentSearchData, getHolRecentSearchExpireDays(),metaDataResponse);
    if (metaDataResponse.didResetFilter) {
      updatedHolidayLandingGroupDto = {
        ...cloneDeep(holidayLandingGroupDto),
        filters: [],
        ...(Object.keys(holidayLandingGroupDto?.queryParams || {})?.length > 0 && {
          queryParams: {},
        }),
      };
    }

    // update HolidayLandingGroupDto with response of metaDataV2 to populate filters
    updatedHolidayLandingGroupDto = updateGroupDtoByMeta({
      holidayLandingGroupDto: isEmpty(updatedHolidayLandingGroupDto)
        ? cloneDeep(holidayLandingGroupDto)
        : updatedHolidayLandingGroupDto,
      metaDataResponse,
      isFromDeeplink,
    });
    dispatch(holidayLandingGroupDtoUpdate(updatedHolidayLandingGroupDto));
    dispatch(metaSuccess(metaDataResponse));
    dispatch(
      fetchFabCtaAction({
        destinationCity: updatedHolidayLandingGroupDto.destinationCity,
        showFabFromDeeplink: updatedHolidayLandingGroupDto.showFab,
        holidayLandingGroupDto: updatedHolidayLandingGroupDto, // to get udpated state for tracking event
        cmp: updatedHolidayLandingGroupDto.cmp,
        pageName: getPhoenixGroupingV2PageName({
          isListing: updatedHolidayLandingGroupDto.isListing,
        }),
      }),
    );
    // call section/fetch to get inline sections
    fetchGroupingSections({ holidayLandingGroupDto }).then((sections) => {
      if (sections?.length > 0) {
        dispatch(groupSectionFetchSuccess(sections));
      }
    });

    dispatch(loadRecentyViewewdPackages({ holidayLandingGroupDto, checkPokusForRVS: true, trackLocalClickEvent }));
    await dispatch(
      loadGroupAndListingAction({
        holidayLandingGroupDto: updatedHolidayLandingGroupDto,
        metaDataResponse,
        searchQuery,
        didResetFilter: metaDataResponse?.didResetFilter,
      }),
    );
  } catch (e) {
    console.log({ e }, '** initialLoadAction **');
  }
};

export const removeFilterAndReload = ({ holidayLandingGroupDto, selectedGroup }) => async (
  dispatch,
) => {
  try {
    let updatedHolidayLandingGroupDto = {};
    // store holidayLandingGroupDto in redux store for easy access and updation
    const metaDataResponse = await dispatch(
      fetchMetaDataDetail({
        holidayLandingGroupDto,
      }),
    );

    // If meta data api failed -> stop further calls and create error page
    if (!metaDataResponse || (metaDataResponse?.error?.code && metaDataResponse?.error?.message)) {
      return null;
    }

    // update HolidayLandingGroupDto with response of metaDataV2 to populate filters
    updatedHolidayLandingGroupDto = updateGroupDtoByMeta({
      holidayLandingGroupDto: isEmpty(updatedHolidayLandingGroupDto)
        ? cloneDeep(holidayLandingGroupDto)
        : updatedHolidayLandingGroupDto,
      metaDataResponse,
    });
    dispatch(holidayLandingGroupDtoUpdate(updatedHolidayLandingGroupDto));
    await dispatch(
      loadGroupAndListingAction({
        holidayLandingGroupDto: updatedHolidayLandingGroupDto,
        metaDataResponse,
        selectedGroup,
        didResetFilter: metaDataResponse?.didResetFilter,
      }),
    );
  } catch (e) {
    console.log({ e }, '** removeFilterAndReload **');
  }
};

export const fetchAvailableHubs = () => async (dispatch) => {
  try {
    const availableHubs = [];
    const isLanding = false; // this api is being called from PhoenixGroupingV2
    var result = await getAvailableHubsCities(isLanding);
    for (let cityIndex = 0; cityIndex < result.length; cityIndex++) {
      if (result[cityIndex].id !== -1) {
        if (result[cityIndex].name === USER_DEFAULT_CITY) {
          result[cityIndex].isActive = true;
        } else {
          result[cityIndex].isActive = false;
        }
        availableHubs.push(result[cityIndex]);
      }
    }
    dispatch(setAvailableHubs(availableHubs));
    return availableHubs;
  } catch (e) {
    console.log({ e }, '** fetchAvailableHubs **');
  }
};

export const holidayLandingGroupDtoUpdate = (holidayLandingGroupDto) => ({
  type: HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE.HOLIDAY_LANDING_GROUP_DTO_UPDATE,
  holidayLandingGroupDto,
});

export const groupSectionFetchSuccess = (sections) => ({
  type: HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE.GROUP_SECTION_FETCH_SUCCESS,
  sections,
});

export const resetPhoenixGroupingV2Data = () => ({
  type: PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE,
});

export const resetPhoenixGroupingV2DataAfterEdit = () => ({
  type: PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE_AFTER_EDIT,
});

export const loadSavedPhoenixGroupingV2Data = () => async (dispatch, getState) => {
  if (isEmpty(getState().holidaysSearchWidget.userDepCity)) {
    // update departure city in all reducers in case screen is opening from deeplink
    const departureCity = await getDepartureCity();
    dispatch(updateDepCity(departureCity));
  }
};
