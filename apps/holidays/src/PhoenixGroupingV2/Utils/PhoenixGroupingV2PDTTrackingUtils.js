import { isEmpty } from 'lodash';
import {
  getComponentsForPDT,
  initializeCampaignDetails,
  initializeSearchContext,
  logHolidaysEventToPDT,
  populateExperimentalDetails,
  populatePageContext,
  populateSearchContext,
  updatePDTJourneyIdOnSearchChange,
} from '../../utils/HolidayPDTTrackingV3';
import {
  createChildAgeArrayFromApi,
  createRoomDataFromRoomDetailsPhoenix,
  defaultRoom,
  roomDefault,
} from '../../utils/RoomPaxUtils';
import { getGroupingPDTObj, updateGroupingPDTObj } from './PhoenixGroupingV2PDTDataHolder';
import { getPhoenixGroupingV2PageName } from './PhoenixGroupingV2TrackingUtils';
import { OBT_BRANCH } from '../../HolidayConstants';
import { updatePDTRequestId } from '../../utils/PdtDataHolder';
import { HOLIDAYS, LOB_CATEGORIES } from '../../utils/HolidayPDTConstants';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from '../Contants';
import { getPaxDetails } from '../../utils/HolidayUtils';

const DEFAULT_LOCUS_TYPE = 'CITY';

export const logHolidaysGroupingPDTEvents = ({ value, actionType, errorDetails = {}, subPageName = '', category = '' , compData={} ,queryDetail={},shouldTrackToAdobe=true}) => {
  let pdtObj = {
    ...getGroupingPDTObj(),
    event_detail: {
      components: getComponentsForPDT({
        category,
      })
    },
    ...(isEmpty(errorDetails) || isEmpty(errorDetails?.code) ? {} : { error_details_list: [errorDetails] }),
  }
  logHolidaysEventToPDT({
    pdtObj,
    value,
    actionType,
    subPageName,
    compData,
    queryDetail,
    shouldTrackToAdobe
  })
};

export const initializeGroupingPDTData = ({ isListing, branch, isMetaDataError }) => {
  let pdtData = getGroupingPDTObj();
  return {
    ...JSON.parse(JSON.stringify(pdtData)),
    experiment_details: populateExperimentalDetails(),
    page_context: setPageContextData({ isListing, branch, isMetaDataError }),
    search_context: initializeSearchContext(),
    campaign_details: initializeCampaignDetails(),
  };
};

export const setPageContextData = ({ isListing, branch, isMetaDataError = false }) => {
  const pageName = getPhoenixGroupingV2PageName({ isListing, isMetaDataError });
  const lobCategory =
    branch === OBT_BRANCH ? LOB_CATEGORIES.INT_HOLIDAYS : LOB_CATEGORIES.DOM_HOLIDAYS;
  const pageContext = populatePageContext({
    funnelStep: PHOENIX_GROUPING_V2_PAGE_NAMES.LISTING__PAGE_NAME,
    pageName,
    lobCategory,
  });

  // Ensure lob field is always present
  if (!pageContext.lob) {
    pageContext.lob = HOLIDAYS;
  }

  return pageContext;
};

export const initGroupingPDTObj = ({ holidayLandingGroupDto, isMetaDataError }) => {
  const { isListing,destinationCityData } = holidayLandingGroupDto || {};
  const {branch = ""} = destinationCityData || {}
  const pdtObj = initializeGroupingPDTData({ isListing, branch, isMetaDataError });
  updateGroupingPDTObj({ pdtObj });
};

export const getSearchContextData = ({ holidayLandingGroupDto }) => {
  const {
    destinationLocusCode = '',
    destinationCity = '',
    departureLocusCode = '',
    userDepCity = '',
    destinationCityData = {},
    packageDate = '',
    rooms = [roomDefault],
    destinationMeta={}
  } = holidayLandingGroupDto || {};

  const pdtObj = getGroupingPDTObj();
  const { search_context = {}} = pdtObj;
  const {departureCity={},destinationCities=[]}=destinationMeta
  let childAgeArray = [];
  if (rooms) {
    childAgeArray = createChildAgeArrayFromApi(rooms);
  }
  const paxDetails = {
    ...getPaxDetails({ roomDetails: rooms }),
    roomData: createRoomDataFromRoomDetailsPhoenix(rooms, childAgeArray),
  };

  const { type = '' } = destinationCityData || {};
  const destCity = {
    locusId: destinationLocusCode,
    name: destinationCity,
    type: type || DEFAULT_LOCUS_TYPE,
    countryName:destinationCities[0]?.locusDetails?.countryName,
    LocationDetails:destinationCities[0]?.locationDetails,
    locus_v2:destinationCities[0]?.locusDetailsV2
  };
  const deptCity = {
    locusId: departureLocusCode,
    name: userDepCity,
    type: DEFAULT_LOCUS_TYPE,
    countryName:departureCity?.locusDetails?.countryName,
    LocationDetails:departureCity?.locationDetails,
    locus_v2:departureCity?.locusDetailsV2
  };
  return populateSearchContext({
    deptCity,
    destCity,
    packageDate,
    paxDetails,
    prevSearchContext: search_context,
  });
};

export const onUpdateSearchWidgetPDT = ({ holidayLandingGroupDto }) => {
  const {
    userDepCity = '',
    destinationCity = '',
    packageDate = '',
    rooms = [],
  } = holidayLandingGroupDto || {};

  // update search context in PDT obj
  const searchContext = getSearchContextData({
    holidayLandingGroupDto,
  });
  let pdtObj = { ...getGroupingPDTObj(), search_context: searchContext };
  updateGroupingPDTObj({ pdtObj });

  //update journery id
  updatePDTJourneyIdOnSearchChange({
    depCity: userDepCity,
    destCity: destinationCity,
    startDate: packageDate,
    paxDetails: !isEmpty(rooms) ? getPaxDetails({ roomDetails: rooms }) : defaultRoom,
  });
};
