import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import isEmpty from 'lodash/isEmpty';
import trim from 'lodash/trim';
import url from 'url';
import ReactNative, {
  ActivityIndicator,
  Alert,
  BackHandler,
  DeviceEventEmitter,
  Image,
  NativeModules,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import PriceChangeCard from '../../Review/Components/PriceChangeCard';
import OwnCouponCode from '../../Review/Components/OwnCouponCode';
import BlackFooter from '../../Review/Components/BlackFooter';
import FlightListing from './Weekender/FlightListing/index';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import HolidayReviewError from '../../Review/Components/HolidayReviewError';
import BookerInfo from '../../Review/Components/BookerInfo';
import BookerInfoDynamic from '../../Review/Components/BookerInfoDynamic';
import TravellerDynamicForm from '../../Review/Components/TravellerDynamicForm';
import AddTraveller from '../../Review/Components/AddTraveller';
import PriceSection from '../../Review/Components/PriceSection';
import FPHBottomOverlay from './FPHBottomOverlay';
import HotelFPH from './Weekender/HotelFPH';
import FPHOffer from './FPHOffer';
import TabFPH from './TabFPH';
import {
  addPersuasionToReviewData,
  fetchReviewErrorMessage,
  fetchReviewPersuasionDetail,
  formDynamicApiData,
  getCashMsg,
  getDefaultRoom,
  getPriceChangeDifference,
  getPriceChangeInclusion,
  isBookingAllowed,
  showPriceChange,
  trackReviewLocalClickEvent,
} from '../../Review/Utils/HolidayReviewUtils';
import HolidayAcknowledgeCard from '../../Common/Components/HolidayAcknowledgeCard';
import {
  createRandomString,
  hasOnBoardingCuesLastVisit,
  isOnBoardingCuesDelayOver,
  removeCuesStepsShown,
  createCuesSteps,
  getExperimentValue,
  isAndroidClient,
  isMobileClient,
  isNotNullAndEmptyCollection,
  isRawClient,
  saveHolMeta,
  startReactChat, isIosClient,
} from '../../utils/HolidayUtils';
import {
  createFlightChangeRequest,
  createHotelChangeRequest,
  fetchFlightBaggageInfo,
} from '../../utils/HolidayNetworkUtils';
import AbConfigKeyMappings from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import { getUserDetails, isUserLoggedIn } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';
import { getDataFromStorage, KEY_USER_DETAILS, setDataInStorage } from '@mmt/legacy-commons/AppState/LocalStorage';
import {
  ADD_ALL_TRAVELLER_MSG,
  ADD_TRAVELLER_OVERLAY,
  BOOKER_INFO_REF,
  CANCELLATION_OVERLAY,
  CANCELLATION_SECTION_REF,
  CHILD_MAX_AGE, COUNTRY_CODE_OVERLAY,
  EXTRA_INFO_OVERLAY,
  FARE_BREAKUP_REF,
  FIGHT_SECTION_REF,
  FPH_CONFIRMATION_OVERLAY,
  FPH_REVIEW_PDT_PAGE_NAME,
  HOTEL_SECTION_REF,
  INVALID_CHILD_AGE_MESSAGE,
  MINIMUM_ADULT_AGE_MESSAGE,
  OWN_CODE_OVERLAY,
  PAX_TYPE_ADULT,
  PAX_TYPE_CHILD,
  PDTConstants,
  SAVED_TRAVELLER_OVERLAY,
  SPECIAL_REQUEST_LABEL,
  SPECIAL_REQUEST_OVERLAY,
  TNC_LABEL,
  TRAVELLER_REF,
  VIEW_MORE_COUPONS_OVERLAY,
} from '../../Review/HolidayReviewConstants';
import ReviewPersuasion from '../../Review/Components/ReviewPersuasion';
import Toast from '../../Review/Components/Toast';
import Header from '../../Review/Components/Header';
import Advantages from '../../Review/Components/Advantages';
import ExtraInfoOverlay from '../../Review/Components/ExtraInfoOverlay';
import TCSbannerStrip from '../../Review/Components/TCSbannerStrip';
import ViewMoreCoupon from '../../Review/Components/ViewMoreCoupon';
import TCSbannerBox from '../../Review/Components/TCSbannerBox';
import AddRequestOverlay from '../../Review/Components/AddRequestOverlay';
import SavedTravellers from '../../Review/Components/SavedTravellers';
import AddRequestOverlayDynamic from '../../Review/Components/AddRequestOverlayDynamic';
import HolidayCancellationOverlay, {ZC_TAB_INDEX} from '../../PhoenixDetail/Components/HolidayCancellationOverlay';
import {
  componentImageTypes,
  DETAILS_REVIEW_EXPIRY_MSG,
  OBT_BRANCH,
  overlays,
  packageActions,
} from '../../PhoenixDetail/DetailConstants';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {
  addChooseTravellerComponent,
  addPaxAtLocation,
  addSavedTravellersComponent,
  areAllRoomsFilled,
  areAllTravellersAdded,
  atleastOneTraveller,
  disableCoTraveller,
  findLocationAndAddPax,
  findNextPlaceToAddPax,
  getChooseTravellerFooter,
  getDefaultTraveller,
  getErrorStateComponent,
  getHeaderForTravellerSection,
  getMandateObject,
  getPaxCountMap,
  initializePaxData,
  isChildValid,
  modifyRoomDetailsWithCoTravellers,
  populateCoTravellersAccToType,
  removePaxAndUpdateRemovedIndexesArray,
  showRoomsFilledToast,
  validateUser,
} from '../../Review/Utils/ChooseTravellerUtil';
import {
  isValidEmail,
  isValidIntlMobile,
  isValidMobileCode,
} from '@mmt/legacy-commons/Helpers/validationHelpers.js';
import { DOM_BRANCH, EVENTS, PDT_PAGE_VIEW, WEEKEND_GETAWAY_PAGE_TYPE, HLD_CUES_POKUS_KEYS } from '../../HolidayConstants';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import EditOverlay from '../../PhoenixDetail/Components/editBasePage';
import {
  calculateFlightsCount,
  calculateTravellersCount,
  createRoomDetailsFromRoomData,
  getActionData,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import VisaModule from '@mmt/legacy-commons/Native/VisaModule';
import HolidaysCountryCodeList from '../../HolidaysCountryCodeList';
import { trackReviewClickEvent, trackReviewLoadEvent } from '../../utils/HolidayTrackingUtils';
import { initAbConfigUsingPokusHLD } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import HolidayDeeplinkParser, { ReviewParams } from '../../utils/HolidayDeeplinkParser';
import { HolidayNavigation, HOLIDAY_ROUTE_KEYS } from '../../Navigation';

const iconArrow = require('@mmt/legacy-assets/src/iconArrowRight.webp');

export const LOGIN_EVENT_REVIEW = 'login_event_review';
export const CHANGE_HOTEL_EVENT_DETAIL = 'change_hotel_event_detail';
export const HOLIDAY_FPH_REVIEW_PAGE = 'HOLIDAY_FPH_REVIEW_PAGE';

const CURRENT_SCENE_NAME = 'holidaysFPHReview';
const TAB_HEIGHT = 80;

let loginEventReviewListener;
let changeHotelEventDetail;
class HolidaysFPHReview extends BasePage {
  constructor(props) {
    super(props);
    if (this.props[ReviewParams.fromReviewDeeplink]) {
      this.holidayReviewData = HolidayDeeplinkParser.parseReviewPageDeeplink(this.props.query, true);
      this.holidayReviewData.roomDetails = getDefaultRoom();
    }
    this.state = {
      popup: '',
      userDetails: {},
      userDetailsDynamic: {},
      travellerFormDetail: null,
      travellerDetailDynamic: {},
      dynamicTravellerIntialValue: {},
      collapsedFormData: {},
      nextActiveObj: {},
      validatePax: false,
      showDatePicker: false,
      baggageInfo: {},
      selectedRequest: '',
      userRequest: '',
      showAddTravellerError: false,
      priceChange: true,
      showTcsMsg: true,
      showCashMsg: true,
      travellerError: [],
      coTravellersData: [],
      roomDetails: this.holidayReviewData.roomDetails,
      countryCodeList: [],
      showCoachMarks: false,
      tcsTnCAcknowledged: false,
      showTCSErrorStrip: false,
    };
    this.isLoggedInUser = false;
    this.roomDetails = this.holidayReviewData.roomDetails;
    this.holidayReviewData.isWG = this.holidayReviewData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    saveHolMeta(this.holidayReviewData.isWG, this.holidayReviewData.aff, this.holidayReviewData.pt, this.holidayReviewData.pt);
    this.showFPHEditOverlay = false;
    this.bookerInfoDynamicForm = React.createRef();
    this.bookerInfoOldForm = React.createRef();
  }

  async componentDidMount() {
    super.componentDidMount();
    await initAbConfigUsingPokusHLD();


    this.showFPHEditOverlay = getExperimentValue(
      AbConfigKeyMappings.showFPHEditOverlay,
      false
    );


    this.showFromCityFPH = getExperimentValue(
      AbConfigKeyMappings.showFromCityFPH,
      true
    );

    this.showPriceChangeFPH = getExperimentValue(
      AbConfigKeyMappings.showPriceChangeFPH,
      true
    );

    this.showNewOverlays = getExperimentValue(
      AbConfigKeyMappings.showNewOverlays_FPH_MOB,
      true
    );

    this.initUserData();
    this.loadReview();
    this.fetchCountryCodeData();
  
    if (DeviceEventEmitter) {
      loginEventReviewListener = DeviceEventEmitter.addListener(
        LOGIN_EVENT_REVIEW,
        this.onLoginEventReceived,
      );
      changeHotelEventDetail = DeviceEventEmitter.addListener(
        CHANGE_HOTEL_EVENT_DETAIL,
        this.onChangeHotelEvent,
      );
    }
  }

  onChangeHotelEvent = (response) => {
    if (response) {
      if (response.changed) {
        this.actionLoadingText = 'Updating selected hotel';
        this.props.fetchReviewData(
          this.holidayReviewData,
          this.roomDetails,
          this.updateRoomDetailData,
          false,
          null,
          () => {
            this.fetchFlightDetails();
          }
        );
        this.togglePopup('');
        if (response.from === 'change_hotel') {
          HolidayNavigation.pop();
        }
      } else if (response.reload) {
        this.onApiError(DETAILS_REVIEW_EXPIRY_MSG, false, true);
        this.togglePopup('');
        if (response.from === 'change_hotel') {
          HolidayNavigation.pop();
        }
      }
    }
  };

  onPackageComponentToggled = (response) => {
    if (response) {
      this.props.fetchReviewData(
        this.holidayReviewData,
        this.roomDetails,
        this.updateRoomDetailData,
        false,
        null,
        () => {
          this.fetchFlightDetails();
        }
      );
    }
  }

  getDynamicId = () => {
    const {reviewData} = this.props;
    const {reviewDetail} = reviewData || {};
    const {dynamicId = ''} = reviewDetail || {};
    return dynamicId;
  }

  onFlightSelected = async (actionRequest) => {
    actionRequest.dynamicPackageId = this.getDynamicId();
    actionRequest = await createFlightChangeRequest(actionRequest);
    this.actionLoadingText = 'Updating selected flight';
    this.props.changePackageComponent(actionRequest, this.onPackageComponentToggled, this.onApiError);
  };

  onPackageComponentToggle = (add, packageComponent) => {
    let action = {};
    action.action = packageActions.TOGGLE;
    action.dynamicPackageId = this.getDynamicId();
    const actionData = getActionData(add, packageComponent);
    this.actionLoadingText = actionData.actionLoadingText;
    this.props.togglePackageComponent(action, this.onPackageComponentToggled, this.onApiError, packageComponent);
  }

  onHotelSelected = async (actionRequest) => {
    actionRequest.dynamicPackageId = this.getDynamicId();
    actionRequest = await createHotelChangeRequest(actionRequest);
    this.actionLoadingText = 'Updating selected hotel';
    this.props.changePackageComponent(actionRequest, this.onPackageComponentToggled, this.onApiError);
  }

  onComponentChange = (actionRequest, componentType) => {
    switch (componentType) {
      case componentImageTypes.FLIGHT:
        this.onFlightSelected(actionRequest);
        break;
      case componentImageTypes.HOTEL:
        this.onHotelSelected(actionRequest);
        break;
      default: break;
    }
  }

  localPageLoadEvent = (event, logOmni, pageName) => {
    const {reviewData} = this.props;
    trackReviewLoadEvent(
      logOmni, pageName,
      reviewData.pageDataMap,
      {},
      PDT_PAGE_VIEW,
      event,
      reviewData.requestId,
      reviewData.branch
    );
  }

  localClickEvent = (eventName, suffix, pageName) => {
    const {reviewData} = this.props;
    trackReviewClickEvent(
      pageName,
      eventName + suffix,'',
      reviewData && reviewData.pageDataMap
        ? reviewData.pageDataMap
        : {},
      {},
      PDTConstants.PDT_RAW_EVENT,
      eventName,
      createRandomString(),
      reviewData ? reviewData.branch : OBT_BRANCH
    );
  }

  async fetchCountryCodeData() {
    if (isAndroidClient()) {
      const list = await GenericModule.getCountryList('countries_list.json');
      if (list) {
        this.setState({
          countryCodeList: list,
        });
      }
    } else if (isIosClient()) {
      const list = await VisaModule.getCountriesCodeList();
      if (list) {
        this.setState({
          countryCodeList: list,
        });
      }
    } else {
      // mWeb
      const {HolidayModule} = NativeModules;
      const list = await HolidayModule.getCountriesCodeList();
      if (list) {
        this.setState({
          countryCodeList: list,
        });
      }
    }
  }

    onApiError = (msg, alert, reload) => {
      if (msg) {
        if (alert) {
          Alert.alert('', msg);
        } else {
          showShortToast(msg);
        }
      }
      // if (reload) {
      //   this.reloadOnError();
      // }
    };

    componentWillUnmount() {
      if (loginEventReviewListener.remove) {
        // DeviceEventEmitter.removeListener(
        //   LOGIN_EVENT_REVIEW,
        //   this.onLoginEventReceived
        // );
        loginEventReviewListener();
      }
      if(changeHotelEventDetail.remove) {
        changeHotelEventDetail.remove();
        // DeviceEventEmitter.removeListener(
        //   EVENTS.REVIEW.PAYMENT_EVENT_REVIEW,
        //   this.onPaymentThankyou
        // );
      }
      BackHandler.removeEventListener(HARDWARE_BACK_PRESS, this.goBack);
    }

  onPaymentThankyou = (paymentResp) => {
    if (paymentResp && paymentResp.data) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.THANK_YOU,{
        paymentResponse: isAndroidClient()
          ? JSON.parse(JSON.parse(paymentResp.data).response)
          : JSON.parse(paymentResp.data),
        pageDataMap: this.props.reviewData.pageDataMap,
        isWG: this.holidayReviewData.isWG,
        tagDestination: this.holidayReviewData.tagDestination,
      });
    }
  };

  enableTravellers = () => {
    const {coTravellersData} = this.state;
    if (coTravellersData && coTravellersData.length > 0) {
      coTravellersData.forEach((item) => {
        item.disabled = false;
      });
      this.setState({
        coTravellersData,
      });
    }
  }

  refreshFetchContent = (checkinDate, departureCity, roomData, checkoutDate) => {
    trackReviewLocalClickEvent(
      PDTConstants.DONE_INTENT,
      '',
      this.props.reviewData,
      true
    );
    const rooms = createRoomDetailsFromRoomData(roomData);
    this.roomDetails = rooms;
    this.togglePopup('');
    this.props.fetchReviewData(
      this.holidayReviewData,
      this.roomDetails,
      this.updateRoomDetailData,
      true,
      {
        departureCity,
        checkinDate,
        checkoutDate,
        rooms,
      },
      () => {
        this.enableTravellers();
        this.fetchFlightDetails();
      }
    );
  };

  goBack = () => {
    if (this.props[ReviewParams.fromReviewDeeplink]
      && !isRawClient()) {
      if (isIosClient()) {
        ViewControllerModule.popViewController(this.props.rootTag);
      } else {
        BackHandler.exitApp();
      }
    } else if (this.state.popup !== '') {
      this.togglePopup('');
    } else {
      HolidayNavigation.pop();
    }
    return true;
  };

  loadReview = () => {
    this.togglePopup('');
    this.props.fetchReviewData(
      this.holidayReviewData,
      this.roomDetails,
      true,
      null,
      () => {
        if (this.showFPHEditOverlay) {
          this.togglePopup(FPH_CONFIRMATION_OVERLAY);
        }
        this.fetchFlightDetails();
      }
    );
  };

  updateRoomDetailData = (coTravellersData, reviewData) => {
    if (
      reviewData &&
      reviewData.reviewDetail.roomDetail &&
      reviewData.reviewDetail.roomDetail.rooms
    ) {
      initializePaxData(reviewData.reviewDetail.roomDetail.rooms);
      if (coTravellersData) {
        const roomDetailsClone = reviewData.reviewDetail.roomDetail.rooms;
        const coTravellersDataClone = _.cloneDeep(coTravellersData);
        const modifiedObj = modifyRoomDetailsWithCoTravellers(
          roomDetailsClone,
          coTravellersDataClone,
          this.addOrEditTraveller,
          this.props.reviewData
        );
        this.setState({
          roomDetails: modifiedObj.roomDetails,
          coTravellersData: modifiedObj.coTravellersData,
        });
      } else {
        this.setState({
          roomDetails: reviewData.reviewDetail.roomDetail.rooms,
        });
      }
    }
  };

  async fetchFlightDetails() {
    const baggageInfoMap = await fetchFlightBaggageInfo(this.holidayReviewData.dynamicPackageId);
    if (baggageInfoMap) {
      this.setState({
        baggageInfo: baggageInfoMap,
      });
    }
  }

  onLoginEventReceived = (response) => {
    if (response && response.loggedIn) {
      this.isLoggedInUser = true;
      this.updateUserContactInfo(
        response.loggedInEmail,
        response.loggedInPhone
      );
      this.loadReview();
    }
  };

  async initUserData() {
    this.isLoggedInUser = await isUserLoggedIn();
    const userDetails = await getDataFromStorage(KEY_USER_DETAILS);
    this.setState({
      userDetails,
    });
    const userDetailsObj = await getUserDetails();
    if (userDetailsObj) {
      const phoneNumber = isAndroidClient()
        ? userDetailsObj.mobile && userDetailsObj.mobile.mobileNumber
          ? userDetailsObj.mobile.mobileNumber
          : ''
        : '';
      this.updateUserContactInfo(userDetailsObj.email, phoneNumber);
    }
  }

  updateUserContactInfo = (email, phone) => {
    let userDetailsObj = this.state.userDetails;
    if (userDetailsObj) {
      if (!userDetailsObj.email && email) {
        userDetailsObj.email = email;
      }
      if (!userDetailsObj.phone && phone) {
        userDetailsObj.phone = phone;
      }
    } else {
      userDetailsObj = {};
      if (email) {
        userDetailsObj.email = email;
      }
      if (phone) {
        userDetailsObj.phone = phone;
      }
    }
    this.setState({
      userDetails: userDetailsObj,
    });
  };

  togglePopup = (popupName) => {
    this.setState({popup: popupName});
  };

  closePopUp = () => {
    this.togglePopup('');
    return true;
  };


  setScrollViewRef = (element) => {
    this.scrollViewRef = element;
  };

  setCancellationViewRef = (element) => {
    this.cancellationViewRef = element;
  };

  static navigationOptions = {header: null};

  setBookerInfoRef = (element) => {
    this.bookerInfoRef = element;
  };

  setFareBreakUpRef = (element) => {
    this.fareBreakUpRef = element;
  };

  setFlightSectionRef = (element) => {
    this.flightSectionRef = element;
  };

  setHotelSectionRef = (element) => {
    this.hotelSectionRef = element;
  };

  setTravellerErrorRef = (element) => {
    this.travellerErrorRef = element;
  };

  updateUserDetailsMaster = (userDetails) => {
    this.setState({
      userDetails,
    });
  };

  updateUserDetailsMasterDynamic = (userDetails, userDetailsDynamic) => {
    this.setState({
      userDetails,
      userDetailsDynamic,
    });
  };

  updateRequest = (selectedReq, userReq) => {
    this.setState({
      selectedRequest: selectedReq,
      userRequest: userReq,
    });
  };

  addPax = (person) => {
    const isUserValid = validateUser(person, this.props.reviewData);
    if (!person.disabled && isUserValid) {
      const {roomDetails, coTravellersData} = this.state;
      let {popup} = this.state;
      const paxType =
        person.age > CHILD_MAX_AGE ? PAX_TYPE_ADULT : PAX_TYPE_CHILD;
      let paxAdded = false;
      if (this.nextLocationToAddPax) {
        if (paxType === PAX_TYPE_CHILD) {
          const validChild = isChildValid(
            person,
            roomDetails,
            this.nextLocationToAddPax
          );
          if (validChild) {
            paxAdded = addPaxAtLocation(
              paxAdded,
              roomDetails,
              paxType,
              person,
              this.nextLocationToAddPax
            );
          } else {
            showShortToast(INVALID_CHILD_AGE_MESSAGE);
            return;
          }
        } else {
          paxAdded = addPaxAtLocation(
            paxAdded,
            roomDetails,
            paxType,
            person,
            this.nextLocationToAddPax
          );
        }
        this.nextLocationToAddPax = null;
      } else {
        paxAdded = findLocationAndAddPax(
          paxAdded,
          roomDetails,
          paxType,
          person
        );
      }
      if (paxAdded) {
        disableCoTraveller(coTravellersData, person);
      }
      const allRoomsFilled = areAllRoomsFilled(roomDetails);
      popup = allRoomsFilled ? '' : popup;
      this.popupCoTravellersData = [];
      const nextActiveObj = findNextPlaceToAddPax(this.state.roomDetails);
      if (nextActiveObj) {
        populateCoTravellersAccToType(
          coTravellersData,
          nextActiveObj.type,
          this.popupCoTravellersData
        );
      }
      this.setState({
        roomDetails,
        coTravellersData,
        popup,
        nextActiveObj,
      });
    } else if (!isUserValid) {
      this.traveller = person;
      this.setState({
        popup: ADD_TRAVELLER_OVERLAY,
        showAddTravellerError: true,
      });
    }
  };

  removePax = (person) => {
    const {roomDetails, coTravellersData, popup} = this.state;
    let {nextActiveObj} = this.state;
    removePaxAndUpdateRemovedIndexesArray(roomDetails, person);
    for (let index = 0; index < coTravellersData.length; index += 1) {
      if (coTravellersData[index].id === person.id) {
        coTravellersData[index].disabled = false;
      }
    }
    if (popup !== '') {
      nextActiveObj = findNextPlaceToAddPax(roomDetails);
    }

    this.setState({
      roomDetails,
      coTravellersData,
      nextActiveObj,
    });
  };

  getTravellerComponent = (popupName) => {
    const travellerComponents = [];
    const {roomDetails, errorState, nextActiveObj} = this.state;
    const paxCountMap = getPaxCountMap(roomDetails);
    travellerComponents.push(getHeaderForTravellerSection(
      paxCountMap.noOfAdults,
      paxCountMap.noOfChildren,
      this.setTravellerErrorRef,
      2
    ));
    addChooseTravellerComponent(
      roomDetails,
      travellerComponents,
      popupName,
      this.removePax,
      this.updateNextLocationAndTogglePopup,
      errorState,
      nextActiveObj
    );
    if (errorState) {
      travellerComponents.push(getErrorStateComponent(this.errorMessage));
    }
    travellerComponents.push(getChooseTravellerFooter(this.updateNextLocationAndTogglePopup));
    return travellerComponents;
  };

  updateNextLocationAndTogglePopup = (popupName, type, index, roomDetail) => {
    const allRoomsFilled = areAllRoomsFilled(this.state.roomDetails);
    let nextActiveObj = {};
    if (allRoomsFilled) {
      showRoomsFilledToast();
      return;
    }
    if (type && roomDetail) {
      this.nextLocationToAddPax = {
        roomNumber: roomDetail.roomNumber,
        type,
        index,
      };
      nextActiveObj = {
        roomNumber: roomDetail.roomNumber,
        type,
        index,
      };
    } else {
      nextActiveObj = findNextPlaceToAddPax(this.state.roomDetails);
      this.nextLocationToAddPax = null;
    }
    const {coTravellersData} = this.state;
    this.popupCoTravellersData = [];
    if (type) {
      populateCoTravellersAccToType(
        coTravellersData,
        type,
        this.popupCoTravellersData
      );
    } else {
      populateCoTravellersAccToType(
        coTravellersData,
        nextActiveObj.type,
        this.popupCoTravellersData
      );
    }
    nextActiveObj = popupName === '' ? {} : nextActiveObj;
    this.setState({
      popup: popupName,
      errorState: false,
      nextActiveObj,
    });
  };

  getSavedTravellerPopup = (coTravellersData) => {
    const savedTravellerPopup = [];
    addSavedTravellersComponent(
      this.addPax,
      coTravellersData,
      this.updateNextActiveAndTogglePopup,
      this.addOrEditTraveller,
      savedTravellerPopup
    );
    return savedTravellerPopup;
  };

  updateNextActiveAndTogglePopup = () => {
    this.setState({
      popup: '',
      nextActiveObj: {},
    });
  };

  renderProgressView = () => (
    <View style={styles.progressContainer}>
      <ActivityIndicator
        styleAttr="Inverse"
        style={styles.indicator}
        size="large"
        color="#008cff"
      />
    </View>
  );

  removeTraveller = (deleteSessionId) => {
    const {travellerDetailDynamic, travellerError} = this.state;
    let {coTravellersList} = this.state;
    const newTravellerDetailDynamic = {};
    for (const sessionId in travellerDetailDynamic) {
      if (deleteSessionId != sessionId) {
        newTravellerDetailDynamic[sessionId] =
          travellerDetailDynamic[sessionId];
      }
    }

    coTravellersList = coTravellersList.map(coTravller => ({
      ...coTravller,
      disabled:
        deleteSessionId === coTravller.sessionId ? false : coTravller.disabled,
      sessionId:
        deleteSessionId === coTravller.sessionId ? null : coTravller.sessionId,
    }));
    this.setState({
      travellerDetailDynamic: {...newTravellerDetailDynamic},
      coTravellersList,
    });
    this.setState({
      travellerError: travellerError.filter(errorSessionId => deleteSessionId != errorSessionId),
    });
  };

  render() {
    return (
      <View style={{flex: 1}}>
        {(this.props.isLoading || this.props.reviewLoading) && this.renderProgressView()}
        {!this.props.reviewLoading && this.props.isError && this.renderError()}
        {!this.props.reviewLoading && this.props.isSuccess && this.renderContent()}
      </View>
    );
  }
  renderCoachMarks = () => {
    if (this.finalCuesSteps && this.finalCuesSteps.length) {
      const CoachMarks = require('@mmt/legacy-commons/Common/Components/CoachMarks').default;
      return (
        <CoachMarks
          steps = {this.finalCuesSteps}
          onDone ={this.hideCoachMarksOverlay}
          onSkip = {this.hideCoachMarksOverlay}
          onStart = {(step)=> this.handleScrollForCoachMarks(step)}
          onStepChange = {(step)=> this.handleScrollForCoachMarks(step)}
          pageName = {HLD_CUES_POKUS_KEYS.FPH}
          trackEvent = {this.handlePDT}
        />
      );
    }
    return null;
  }

  startNoPkChat = () => {
    const chatDto = {
      destinationCity: this.holidayReviewData.tagDestination,
      branch: this.holidayReviewData.branch,
      pageName: FPH_REVIEW_PDT_PAGE_NAME,
    };
    startReactChat(chatDto);
  };

  renderError = () => {
    const errorMessage = fetchReviewErrorMessage(this.props.error);
    return (
      <HolidayReviewError
        startChat={this.startNoPkChat}
        duration={this.holidayReviewData.duration}
        onBackPressed={this.goBack}
        packageName={this.holidayReviewData.packageName}
        errorMessage={errorMessage}
        aff={this.holidayReviewData.aff}
      />
    );
  };

  isPaxInfoValid = () =>
    this.state.userDetails &&
    this.state.userDetails.email &&
    isValidEmail(this.state.userDetails.email) &&
    this.state.userDetails.phone &&
    isValidIntlMobile(this.state.userDetails.phone) &&
    this.state.userDetails.phoneCode &&
    isValidMobileCode(this.state.userDetails.phoneCode) &&
    !isEmpty(trim(this.state.userDetails.city)) &&
    this.state.userDetails.state;

  validatePax = () => {
    this.setState({validatePax: true});
    if (this.isPaxInfoValid()) {
      const userDetails = this.saveUserDetails();
      let paxRemarks = '';
      if (this.state.selectedRequest) {
        paxRemarks = this.state.selectedRequest;
      } else if (this.state.userRequest) {
        paxRemarks = this.state.userRequest;
      }
      const allTravellersAdded = areAllTravellersAdded(this.state.roomDetails);
      if (allTravellersAdded) {
        if (atleastOneTraveller(this.state.roomDetails)) {
          this.props.doPrePayment(
            this.state.roomDetails,
            userDetails,
            paxRemarks
          );
        } else {
          showShortToast(MINIMUM_ADULT_AGE_MESSAGE);
        }
      } else {
        this.errorMessage = ADD_ALL_TRAVELLER_MSG;
        this.setState(
          {
            errorState: true,
          },
          () => {
            this.scrollToItem(TRAVELLER_REF);
          }
        );
      }
    } else {
      this.scrollToItem(BOOKER_INFO_REF);
    }
  };

  addDirectDynamicTraveller = (travellerData, coTravellerIndex) => {
    this.setState({travellerAutoSave: true});
    this.addDynamicTraveller(travellerData, coTravellerIndex);
  };

  addDynamicTraveller = (travellerData, coTravellerIndex = false) => {
    this.setState({
      dynamicTravellerIntialValue: {},
      coTravellerIndex,
      openTravellerForm: true,
    });
    const {coTravellersList} = this.state;
    if (travellerData) {
      // coTravellerIndex: check whearth traveller is already saved or not
      let savedSessionId = null;
      if (coTravellerIndex !== false) {
        const {sessionId: coTravellerSessionId} = coTravellersList[
          coTravellerIndex
        ];
        if (coTravellerSessionId) {
          savedSessionId = coTravellerSessionId;
        }
      }
      // if yes then save into same traveller info
      if (savedSessionId) {
        // update the data and
        const {
          formSections: roomSection,
        } = this.props.formSectionsTraveller[0];
        for (let roomIndex = 0; roomIndex < roomSection.length; roomIndex++) {
          const {formSections: personSections} = roomSection[roomIndex];
          for (
            let personIndex = 0;
            personIndex < personSections.length;
            personIndex++
          ) {
            const {sectionId: personSessionId} = personSections[personIndex];
            // not saved then open traveller list
            if (savedSessionId === personSessionId) {
              const {fieldValues} = travellerData;
              const data = {};
              for (const name in fieldValues) {
                data[`${personSessionId}#${name}`] = fieldValues[name][0];
              }
              this.setState({
                dynamicTravellerIntialValue: data,
                travellerFormDetail: personSections[personIndex],
              });
              break;
            }
          }
        }
      } else {
        // it is not saved then update according to click traveller
        const {travellerFormDetail} = this.state;
        const {sectionId} = travellerFormDetail;
        const {fieldValues} = travellerData;
        const data = {};
        for (const name in fieldValues) {
          data[`${sectionId}#${name}`] = fieldValues[name][0];
        }
        this.setState({
          dynamicTravellerIntialValue: data,
        });
      }
    }
  };

  openTravellerList = (formSections) => {
    const {travellerFormAutofillerDetail} = this.props;
    let {coTravellersList} = this.state;
    if (
      !coTravellersList &&
      travellerFormAutofillerDetail[formSections.autoFiller]
    ) {
      coTravellersList = travellerFormAutofillerDetail[
        formSections.autoFiller
      ].fieldValues.map(traveller => ({
        ...traveller,
        name: `${traveller.fieldValues.FIRST_NAME} ${traveller.fieldValues.LAST_NAME}`,
        age:
          traveller.fieldValues.DOB && traveller.fieldValues.DOB.length > 0
            ? this.getAge(traveller.fieldValues.DOB[0])
            : '',
        disabled: false,
      }));
    } else if (!coTravellersList) {
      coTravellersList = [];
    }
    if (coTravellersList.length > 0) {
      this.setState({
        popup: SAVED_TRAVELLER_OVERLAY,
        coTravellerIndex: false,
      });
    } else {
      this.setState({openTravellerForm: true});
    }
    this.setState({travellerFormDetail: formSections, coTravellersList});
  };

  validateTravellerDetail = () => {
    const {formSectionsTraveller} = this.props;
    const {travellerDetailDynamic} = this.state;
    const {formSections} = formSectionsTraveller[0];

    const errorList = [];
    formSections.map((roomDetail) => {
      roomDetail.formSections.map((paxDetail) => {
        const {sectionId} = paxDetail;
        if (!travellerDetailDynamic[sectionId]) {
          errorList.push(sectionId);
        }
      });
    });
    return errorList;
  };

  validatePaxDynamic = () => {
    const userDetailsDynamic = this.bookerInfoDynamicForm.current.validateBookerInfo();
    if (userDetailsDynamic) {
      const {
        formSections,
        formSectionsTraveller,
        travellerFormId,
        collapsedForm,
      } = this.props;
      const {travellerDetailDynamic, collapsedFormData} = this.state;

      const object = {
        formSections,
        formSectionsTraveller,
        userDetailsDynamic,
        travellerDetailDynamic,
        travellerFormId,
        collapsedForm,
        collapsedFormData,
      };

      this.saveUserDetails();

      const errorList = this.validateTravellerDetail();
      if (errorList.length === 0) {
        this.props.doPrePaymentDynamic(
          formDynamicApiData(object),
          this.state.userDetails
        );
      } else {
        this.errorMessage = ADD_ALL_TRAVELLER_MSG;
        this.setState(
          {
            travellerError: errorList,
          },
          () => {
            this.scrollToItem(TRAVELLER_REF);
          }
        );
      }
    } else {
      this.scrollToItem(BOOKER_INFO_REF);
    }
  };

  startPayment = () => {
    this.setState({showTCSErrorStrip: false});
    const {reviewData} = this.props;
    const {metadataDetail: {branch}} = reviewData.reviewDetail;
    if (branch === OBT_BRANCH) {
      if (this.state.tcsTnCAcknowledged) {
        this.dynamicForm ? this.validatePaxDynamic() : this.validatePax();
      } else {
        this.scrollToItem(TRAVELLER_REF);
        this.setState({showTCSErrorStrip: true});
      }
    } else {
      this.dynamicForm ? this.validatePaxDynamic() : this.validatePax();
    }
  };

  onPageLayoutComplete = (layout) => {
    // this function gets called when page is having all views height and width
    // this is required since we can't scroll to exact position till root view is not having all views height
    // now show coach marks and scroll to the required step
    // we'll wait for 3 secs for rendering of entire views before we show coach marks
    if (this.cuesTiemout) {
      clearTimeout(this.cuesTiemout);
    }
    this.cuesTiemout = setTimeout(this.handleCoachMarkOverlay, 3000);
  }
  handleCoachMarkOverlay = async() => {
    const hasPageVisitTime = await hasOnBoardingCuesLastVisit(HLD_CUES_POKUS_KEYS.FPH);
    if (hasPageVisitTime) {
      const delayOver = await isOnBoardingCuesDelayOver(HLD_CUES_POKUS_KEYS.FPH);
      if (delayOver) {
        //  clean page keys, since we need to show it again
        await removeCuesStepsShown(HLD_CUES_POKUS_KEYS.FPH);
        await this.showCoachMarksOverlay();
      }
    } else {
      await this.showCoachMarksOverlay();
    }
  }
  showCoachMarksOverlay = async()=>{
    const cuesConfig = getExperimentValue(AbConfigKeyMappings.cuesConfig, {});
    const localSteps = require('./HolidayFPHReviewPageCoachMarks.json');
    this.finalCuesSteps = await createCuesSteps(cuesConfig[HLD_CUES_POKUS_KEYS.FPH], localSteps);
    this.setState({showCoachMarks: true, priceChange: false});
  }
  hideCoachMarksOverlay = () => {
    this.scrollViewRef.scrollTo({y:0});
    this.setState({showCoachMarks: false});
  }
  handleScrollForCoachMarks = step => {
    let refType;
    switch (step.key) {
      case 'hotel':
        refType = HOTEL_SECTION_REF;
        break;
      case 'traveller':
        refType = TRAVELLER_REF;
        break;
      case 'policy':
        refType = CANCELLATION_SECTION_REF;
        break;
      case 'coupon':
        refType = FARE_BREAKUP_REF;
        break;
      default:
        refType = null;
        break;
    }
    if (refType) {
      this.scrollToItem(refType, TAB_HEIGHT);
    }
  }
  scrollToItem = (refType, tabHeight) => {
    let ref;
    switch (refType) {
      case FARE_BREAKUP_REF:
        ref = this.fareBreakUpRef;
        break;
      case CANCELLATION_SECTION_REF:
        ref = this.cancellationViewRef;
        break;
      case FIGHT_SECTION_REF:
        ref = this.flightSectionRef;
        break;
      case HOTEL_SECTION_REF:
        ref = this.hotelSectionRef;
        break;
      case TRAVELLER_REF:
        ref = this.travellerErrorRef;
        break;
      case BOOKER_INFO_REF:
        ref = this.bookerInfoRef;
        break;
      default:
        ref = null;
    }
    if (ref) {
      ref.measureLayout(
        ReactNative.findNodeHandle(this.scrollViewRef),
        (x, y) => {
          if (isRawClient()) {
            y += document.querySelector('.reviewScroll').scrollTop;
          }
          this.scrollViewRef.scrollTo({
            x: 0,
            y: tabHeight ? (y - tabHeight) : y,
            animated: true,
          });
        }
      );
    }
  };

  saveUserDetails = () => {
    setDataInStorage(KEY_USER_DETAILS, this.state.userDetails);
    return this.state.userDetails;
  };

  saveTraveller = (userDetails) => {
    const userInfoComplete = validateUser(userDetails, this.props.reviewData);
    if (userInfoComplete) {
      userDetails.name = `${userDetails.firstName} ${userDetails.lastName}`;
      const {coTravellersData} = this.state;
      let toInsert = true;
      for (let index = 0; index < coTravellersData.length; index += 1) {
        if (userDetails.id === coTravellersData[index].id) {
          coTravellersData[index] = userDetails;
          toInsert = false;
        }
      }
      if (toInsert) {
        coTravellersData.push(userDetails);
      }
      this.setState({
        coTravellersData,
        popup: '',
        showAddTravellerError: false,
      });
    } else {
      this.setState({
        showAddTravellerError: true,
      });
    }
  };

  getReturnUrl = () => {
    if (!isRawClient()) {
      return null;
    }
    let urlObj = url.parse(window.location.href);
    urlObj = {
      ...urlObj,
      query: {
        dynamicPackageId: this.holidayReviewData.dynamicPackageId,
      },
    };
    return url.format(urlObj);
  };

  updateCollapsedData = (formData, sectionId) => {
    this.setState({
      collapsedFormData: {
        ...this.state.collapsedFormData,
        [sectionId]: formData,
      },
    });
  };
  onLoginClicked = () => {
    const {HolidayModule} = NativeModules;
    if (isMobileClient()) {
      HolidayModule.onLoginUserReview();
    } else {
      HolidayModule.onLoginUserReview(this.getReturnUrl());
    }
  };

   hidePriceDiff = () => {
     this.setState({
       priceChange: false,
     });
   };

   hideTcsMsg = () => {
     this.setState({showTcsMsg: false});
   }


  hideCashMsg = () => {
    this.setState({
      showCashMsg: false,
    });
  };

  pageName = () => {
    if (this.props.branch === DOM_BRANCH) {
      return 'mob:funnel:DOM holidays: fphReview';
    }
    return 'mob:funnel:OBT holidays: fphReview';
  };

  handlePDT = (eventName) => {
    trackReviewLocalClickEvent(
      eventName,
      '',
      this.props.reviewData,
      true
    );
  };

  addOrEditTraveller = (traveller) => {
    let {showAddTravellerError} = this.state;
    if (!traveller) {
      this.traveller = getDefaultTraveller();
    } else {
      this.traveller = traveller;
      showAddTravellerError = true;
    }
    this.setState({
      popup: ADD_TRAVELLER_OVERLAY,
      nextActiveObj: {},
      showAddTravellerError,
    });
  };

  onEditClick = () => {
    this.togglePopup(overlays.EDIT_OVERLAY);
  };

  handleEditClick = () => {
    this.togglePopup('');
    this.togglePopup(overlays.EDIT_OVERLAY);
  }

  onCountryCodeSelection = (phoneCode) => {
    if (this.bookerInfoOldForm && this.bookerInfoOldForm.current) {
      this.bookerInfoOldForm.current.handlePhoneCodeChange(phoneCode);
    }
  }
  handleTcsBannerAckSelection = value => {
    this.setState({tcsTnCAcknowledged: value, showTCSErrorStrip: !value});
  };

  handleTcsBannerToggle = show => {
    const eventText = show ? 'TCS_more' : 'TCS_less';
    trackReviewLocalClickEvent(eventText, '', this.props.reviewData);
  }

  renderContent() {
    const {
      persuasionData,
      reviewData,
      travellerFormOptionsDetails,
      formSections,
      formSectionsTraveller,
      collapsedForm,
    } = this.props;
    this.travellerComponent = this.getTravellerComponent(this.state.popup);
    const savedTravellerComponent = this.getSavedTravellerPopup(this.popupCoTravellersData);
    const mandateObj = getMandateObject(reviewData);
    const {
      metadataDetail: {branch},
    } = reviewData.reviewDetail;
    let reviewPersuasionData = {};
    let isTopPersuasion = false;
    let isEmiPersuasion = false;
    let isBottomPersuasionDataList = false;
    if (persuasionData) {
      addPersuasionToReviewData(reviewData, persuasionData);
      reviewPersuasionData = fetchReviewPersuasionDetail(persuasionData);
      isTopPersuasion = !!(reviewPersuasionData && reviewPersuasionData.topPersuasionData);
      isEmiPersuasion = !!(reviewPersuasionData && reviewPersuasionData.emiPersuasionData);
      isBottomPersuasionDataList = !!(reviewPersuasionData
        && isNotNullAndEmptyCollection(reviewPersuasionData.bottomPersuasionDataList));
    }

    const priceChange = showPriceChange(reviewData.reviewDetail);
    const showCashMsg = reviewData.reviewDetail.offlineDetail.cashPaymentFlow;
    const cashMsg = getCashMsg(reviewData.reviewDetail.offlineDetail);
    const bookingAllowed = isBookingAllowed(reviewData.reviewDetail.offlineDetail);
    const showTcsMsg = branch === OBT_BRANCH;
    const priceChangeInclusion = getPriceChangeInclusion(reviewData.reviewDetail);
    const priceChangeValue = getPriceChangeDifference(reviewData.reviewDetail);

    return (
      <View style={[AtomicCss.flex1, styles.viewContainer]}>
        <Header
          title="Your Flight + Hotel Combo offer"
          onBackPressed={this.goBack}
        />
        <ScrollView
          style={styles.scrollSection}
          scrollEventThrottle={500}
          onScroll={this.pageScroll}
          ref={this.setScrollViewRef}
          stickyHeaderIndices={[0]}
        >
          <TabFPH reviewData={reviewData} onTabSelect={tab => this.scrollToItem(tab, TAB_HEIGHT)} />
          {priceChange && this.showPriceChangeFPH && (
          <PriceChangeCard newPrice={priceChangeValue} />
          )}
          <View style={styles.fphBg} onLayout={event => this.onPageLayoutComplete(event.nativeEvent.layout)}>
            <FPHOffer
              reviewDetail={reviewData.reviewDetail}
              onEditClick={() => {
                trackReviewLocalClickEvent(
                    PDTConstants.EDIT_INTENT,
                    '',
                    reviewData,
                    true
                  );

                this.onEditClick();
                }}
            />
            <View style={styles.wrapper}>
              {isTopPersuasion && (
                <ReviewPersuasion
                  item={reviewPersuasionData.topPersuasionData}
                  togglePopup={this.togglePopup}
                />
            )}
            </View>
            <View style={styles.separator} />
            {reviewData?.reviewDetail?.hotelDetail?.hotels.length ?
            reviewData.reviewDetail.hotelDetail.hotels.map((item, index) => (
              <HotelFPH
                setHotelSectionRef={this.setHotelSectionRef}
                hotelData={item}
                reviewData={reviewData}
                showNewOverlays={this.showNewOverlays}
                roomDetails={this.roomDetails}
                lastPageName={CURRENT_SCENE_NAME}
                onComponentChange={this.onComponentChange}
              />)
              ) : null}
            {reviewData.reviewDetail.flightDetail ? (
              <FlightListing
                setFightSectionRef={this.setFlightSectionRef}
                roundTrip
                baggageInfo={this.state.baggageInfo}
                showNewOverlays={this.showNewOverlays}
                roomDetails={this.roomDetails}
                lastPageName={CURRENT_SCENE_NAME}
                reviewData={reviewData}
                onComponentChange={this.onComponentChange}
                onPackageComponentToggle={this.onPackageComponentToggle}
                localClickEvent={this.localClickEvent}
                localPageLoadEvent={this.localPageLoadEvent}
              />) : null}
          </View>
            <BookerInfoDynamic
              index={1}
              updateUserDetailsMaster={this.updateUserDetailsMasterDynamic}
              userDetails={this.state.userDetails}
              setBookerInfoRef={this.setBookerInfoRef}
              ref={this.bookerInfoDynamicForm}
              formSections={formSections}
              options={travellerFormOptionsDetails}
            />
            <TravellerDynamicForm
              formSections={formSectionsTraveller}
              setTravellerErrorRef={this.setTravellerErrorRef}
              travellerDetailDynamic={this.state.travellerDetailDynamic}
              openTravellerList={this.openTravellerList}
              removeTraveller={this.removeTraveller}
              errors={this.state.travellerError}
            />
          {branch === OBT_BRANCH ? (
            <TCSbannerStrip
              handleTcsBannerAckSelection={this.handleTcsBannerAckSelection}
              showTCSErrorStrip={this.state.showTCSErrorStrip}
              handleTcsBannerToggle={this.handleTcsBannerToggle}
            />
          ) : null}
          <View style={styles.btnSection}>
            {collapsedForm.map(collapsed => (
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={styles.reviewBtnContainer}
                  onPress={() => {
                    this.togglePopup(SPECIAL_REQUEST_OVERLAY);
                    this.setState({selectedCollapsedForm: collapsed});
                  }}
                  key={collapsed.sectionId}
                >
                  <Text style={styles.reviewText}>{collapsed.displayName}</Text>
                  <Image source={iconArrow} style={styles.reviewIconArrow} />
                </TouchableOpacity>
              ))}
            {(reviewData.reviewDetail.additionalDetail.tnc ||
              reviewData.reviewDetail.additionalDetail.exclusions) && (
              <View>
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={styles.reviewBtnContainer}
                  onPress={() => this.togglePopup(EXTRA_INFO_OVERLAY)}
                >
                  <Text style={styles.reviewText}>{TNC_LABEL}</Text>
                  <Image source={iconArrow} style={styles.reviewIconArrow} />
                </TouchableOpacity>
              </View>
            )}
            {reviewData.reviewDetail.penaltyDetail && (
              <View ref={this.setCancellationViewRef}>
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={styles.reviewBtnContainer}
                  onPress={() => this.togglePopup(CANCELLATION_OVERLAY)}
                >
                  <Text style={styles.reviewText}>Cancellation Policy</Text>
                  <Image source={iconArrow} style={styles.reviewIconArrow} />
                </TouchableOpacity>
              </View>
            )}
          </View>
          <Advantages />
          {isBottomPersuasionDataList && (
            <ReviewPersuasion
              item={reviewPersuasionData.bottomPersuasionDataList[0]}
              togglePopup={this.togglePopup}
            />
          )}
          <PriceSection
            setFareBreakUpRef={this.setFareBreakUpRef}
            reviewData={reviewData}
            basicPrice={this.props.basicPrice}
            extraChargesBreakUps={this.props.extraChargesBreakUps}
            discountedPrice={this.props.discountedPrice}
            gst={this.props.gst}
            packagePrice={this.props.packagePrice}
            reviewAdditionalPricingDetail={
              this.props.reviewAdditionalPricingDetail
            }
            reviewWalletDetail={this.props.reviewWalletDetail}
            quoteRequestId={this.holidayReviewData.quoteRequestId}
            couponData={this.props.couponData}
            couponAvailable={this.props.couponAvailable}
            validateCoupon={this.props.validateCoupon}
            selectPaymentOption={this.props.selectPaymentOption}
            paymentScheduleOptions={this.props.paymentScheduleOptions}
            togglePopup={this.togglePopup}
            isEmiPersuasion={isEmiPersuasion}
            emiPersuasionData={reviewPersuasionData.emiPersuasionData}
            emiDetails={this.props.emiDetails}
          />
          {branch === OBT_BRANCH ? (
            <TCSbannerBox />
          ) : null}
          <HolidayAcknowledgeCard />
        </ScrollView>
        <BlackFooter
          basicPrice={this.props.basicPrice}
          packagePrice={this.props.packagePrice}
          startPayment={this.startPayment}
          scrollToFareBreakUp={this.scrollToItem}
          bookingAllowed={bookingAllowed}
          disableBookingButton={false}
        />
        {this.state.popup === EXTRA_INFO_OVERLAY && (
          <ExtraInfoOverlay
            closePopUp={this.closePopUp}
            tnc={reviewData.reviewDetail.additionalDetail.tnc}
            exclusions={reviewData.reviewDetail.additionalDetail.exclusions}
            togglePopup={this.togglePopup}
          />
        )}
        {priceChange && this.state.priceChange && (
          <Toast
            inclusion={priceChangeInclusion}
            hideToast={this.hidePriceDiff}
            bottom={150}
            duration={1000}
            showCashMsg={false}
            marginBottom={showCashMsg && this.state.showCashMsg ? 15 : -185}
          />
        )}
        {showCashMsg && this.state.showCashMsg && (
          <Toast
            cashMsg={cashMsg}
            hideToast={this.hideCashMsg}
            bottom={150}
            duration={1000}
            showCashMsg
          />
        )}
        {!priceChange && showTcsMsg && this.state.showTcsMsg && (
        <Toast
          cashMsg="Please keep your PAN card handy for the next steps."
          hideToast={this.hideTcsMsg}
          bottom={150}
          duration={1000}
          showCashMsg
        />
        )}
        {this.state.popup === OWN_CODE_OVERLAY && (
          <OwnCouponCode
            togglePopup={this.togglePopup}
            isLoggedInUser={this.isLoggedInUser}
            onLoginClicked={this.onLoginClicked}
            validateCoupon={this.props.validateCoupon}
            aff={this.holidayReviewData.aff}
          />
        )}
        {this.state.popup === overlays.EDIT_OVERLAY && (
          <EditOverlay
            togglePopup={this.togglePopup}
            pageName={this.pageName()}
            handlePDT={this.handlePDT}
            departureDate={
              reviewData.reviewDetail.departureDetail.departureDate
            }
            checkoutDate={this.holidayReviewData.checkoutDate}
            categoryId={reviewData.reviewDetail.categoryDetail.id}
            departureCity={reviewData.reviewDetail.departureDetail.cityName}
            flightCount={calculateFlightsCount(reviewData.reviewDetail.flightDetail)}
            packageId={reviewData.reviewDetail.id}
            cityId={reviewData.reviewDetail.departureDetail.cityId}
            roomDetails={this.roomDetails}
            refreshFetchContent={this.refreshFetchContent}
            onBackPressed={this.onBackPressed}
            hideFromCity={!this.showFromCityFPH}
            startDateTxt="START DATE"
            endDateTxt="RETURN DATE"
          />
        )}
        {this.state.popup === ADD_TRAVELLER_OVERLAY && (
          <AddTraveller
            showDatePicker={this.showDatePicker}
            reviewData={reviewData}
            date={this.state.selectedDate}
            userDetails={this.traveller}
            togglePopup={this.togglePopup}
            saveTraveller={this.saveTraveller}
            mandateObj={mandateObj}
            closePopUp={this.closePopUp}
            showError={this.state.showAddTravellerError}
          />
        )}
        { this.state.popup === CANCELLATION_OVERLAY && reviewData.reviewDetail.penaltyDetail && (
          <HolidayCancellationOverlay
            togglePopup={this.togglePopup}
            details={reviewData.reviewDetail.penaltyDetail}
            activeTabIndex={ZC_TAB_INDEX}
            isPerPerson={false}
            activeTab= {ZC_TAB_INDEX}
            travellerCount={calculateTravellersCount(reviewData.reviewDetail.roomDetails)}
            cancellationPolicyData={{ 'statusCode': 1, 'success': true, 'penaltyDetail': reviewData.reviewDetail.penaltyDetail }}
            dynamicId={reviewData.reviewDetail.dynamicId}
            pageName={HOLIDAY_FPH_REVIEW_PAGE}
            trackClickEvent={(event, suffix) => this.trackReviewLocalClickEvent(event, suffix, reviewData, true)}
            pageName={FPH_REVIEW_PDT_PAGE_NAME}
          />
        )}
        {this.state.popup === FPH_CONFIRMATION_OVERLAY && (
        <FPHBottomOverlay
          reviewData={reviewData}
          closePopUp={this.closePopUp}
          handleEditClick={this.handleEditClick}
          startDate={reviewData.reviewDetail.departureDetail.departureDate}
          returnDate={this.holidayReviewData.checkoutDate}
        />
        )}
        {this.state.popup === SPECIAL_REQUEST_OVERLAY && (
          <AddRequestOverlayDynamic
            closePopUp={this.closePopUp}
            form={this.state.selectedCollapsedForm}
            data={this.state.collapsedFormData}
            options={travellerFormOptionsDetails}
            updateCollapsedData={this.updateCollapsedData}
          />
        )}
        {this.state.popup === SAVED_TRAVELLER_OVERLAY && (
          <SavedTravellers
            coTravellersData={this.state.coTravellersList}
            addOrEditTraveller={this.addDynamicTraveller}
            addPax={this.addDirectDynamicTraveller}
            updateNextActiveAndTogglePopup={() => {
              this.setState({
                popup: '',
                travellerFormDetail: null,
              });
            }}
          />
        )}
        {this.state.popup === COUNTRY_CODE_OVERLAY && (
          <View style={styles.countryCodeList}>
            <HolidaysCountryCodeList
              countryCodeSelected={this.onCountryCodeSelection}
              countriesListData={this.state.countryCodeList}
              onBackPress={this.closePopUp}
            />
          </View>
        )}
        {this.state.showCoachMarks && this.renderCoachMarks()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  impInfoWrapper: {backgroundColor: '#fff', paddingTop: 24},
  fphBg: {backgroundColor: '#f8fcff'},
  scrollSection: {
    backgroundColor: '#fff',
  },
  progressContainer: {
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
    width: '100%',
    height: '100%',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnSection: {
    paddingHorizontal: 15,
    backgroundColor: '#fff',
  },
  reviewBtnContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderColor: '#ccc',
    paddingBottom: 16,
    paddingTop: 16,
    alignItems: 'center',
    position: 'relative',
  },
  reviewText: {
    color: '#008cff',
    fontFamily: 'Lato-Bold',
    fontSize: 14,
    lineHeight: 18,
    flex: 1,
    paddingRight: 5,
  },
  reviewIconArrow: {
    width: 24,
    height: 24,
  },
  separator: {
    height: 10,
    backgroundColor: '#f2f2f2',
  },
  viewContainer: {
    ...Platform.select({
      android: {
        marginTop: StatusBar.currentHeight,
      },
    }),
    backgroundColor: '#fff',
  },
  countryCodeList: {
    justifyContent: 'center',
    zIndex: 10,
    elevation: 10,
    alignItems: 'center',
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: '#fff',
  },
});

HolidaysFPHReview.propTypes = {
  fetchReviewData: PropTypes.func.isRequired,
  fetchPersuasionDataActions: PropTypes.func.isRequired,
  holidayReviewData: PropTypes.object.isRequired,
};
export default HolidaysFPHReview;


