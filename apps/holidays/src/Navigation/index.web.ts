import NavigationUtil from './NavigationUtil';
import {history} from '../../../../../web/webStore';
import { isNumber } from 'lodash';

export { HOLIDAY_ROUTE_KEYS } from './holidayPageKeys';

export const HolidayNavigation = {
    canGoBack() {
        return true;
    },
    navigate(key: string, params: object) {
        const data = NavigationUtil.getInstance().resolveUrlAndState(key, params);
        history.push(data.url, data.state);
    },
    push(key: string, params: object) {
        const data = NavigationUtil.getInstance().resolveUrlAndState(key, params);
        history.push(data.url, data.state);
    },
    goBack() {
        history.goBack();
    },
    pop() {
        history.goBack();
    },
    popTo(params: object) {
        if (isNumber(params) && params < 0)
            {history.go(params);}
    },
    replace(key: string, params: object) {
        const data = NavigationUtil.getInstance().resolveUrlAndState(key, params);
        history.replace(data.url, data.state);
    },
};
