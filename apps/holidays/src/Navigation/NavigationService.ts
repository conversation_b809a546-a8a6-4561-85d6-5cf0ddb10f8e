import { NavigationContainerRef, StackActions } from '@react-navigation/native';
import * as React from 'react';

export const navigationRef = React.createRef<NavigationContainerRef<any>>();

export function navigate(name: string, params?: object) {
    console.log('Navigating to:', name, params);
    navigationRef.current?.navigate(name, params);
}

export function push(name: string, params?: object) {
    navigationRef.current?.dispatch(StackActions.push(name, params));
}

export function goBack() {
    navigationRef.current?.goBack();
}
