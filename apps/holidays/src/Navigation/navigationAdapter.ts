export interface NavigationAdapter {
    navigate: (screenName: string, options?: Object) => void;
    push: (screenName: string, options?: Object) => void;
    popToPage: (stackNo: number) => void;
    pop: () => void;
    replace: (screenName: string, options?: Object) => void;
    goBack: () => void;
    canGoBack: () => boolean;
    reset: (routes: { name: string; params?: any }[], index: number) => void;
    getCurrentRoute: () => string;
    getPreviousRoute: () => string;
    getCurrentState: () => any;
}

interface TransitionOptions {
    shallow?: boolean;
    locale?: string | false;
    scroll?: boolean;
    unstable_skipClientCache?: boolean;
}

type Url = string;
export interface WebNavigationAdapter {
    reload(): void;
    back(): void;
    push(url: Url, as?: Url, options?: TransitionOptions): Promise<boolean>;
    events: {
        on(
            type:
                | 'routeChangeStart'
                | 'beforeHistoryChange'
                | 'routeChangeComplete'
                | 'routeChangeError'
                | 'hashChangeStart'
                | 'hashChangeComplete',
            handler: (...evts: any[]) => void
        ): void;
    };
}
