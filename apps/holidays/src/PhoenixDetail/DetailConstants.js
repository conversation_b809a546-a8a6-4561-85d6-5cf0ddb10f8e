export const detailActionTypes = {
  STATE_LOADING: 'DETAIL_STATE_LOADING',
  STATE_IDLE: 'DETAIL_STATE_IDLE',
  STATE_LOADING_WITH_CHANGE_DATE: 'STATE_LOADING_WITH_CHANGE_DATE',
  STATE_ERROR: 'DETAIL_STATE_ERROR',
  STATE_SUCCESS: 'DETAIL_STATE_SUCCESS',
  STATE_ACTIVITY_VALIDATION_PEEK: 'STATE_ACTIVITY_VALIDATION_PEEK',
  STATE_PERS_SUCCESS: 'DETAIL_STATE_PERS_SUCCESS',
  STATE_SHORT_LIST_SUCCESS: 'DETAIL_SHORT_LIST_SUCCESS',
  CONTENT_STATE_SUCCESS: 'CONTENT_STATE_SUCCESS',
  STATE_SIMILAR_PKGS_SUCCESS: 'STATE_SIMILAR_PKGS_SUCCESS',
  STATE_CTA_SUCCESS: 'DETAIL_CTA_SUCCESS',
  STATE_HOTEL_DETAIL_LOADING: 'STATE_HOTEL_DETAIL_LOADING',
  STATE_HOTEL_DETAIL_SUCCESS: 'STATE_HOTEL_DETAIL_SUCCESS',
  STATE_TRAVEL_PLAN_LOADING: 'STATE_TRAVEL_PLAN_LOADING',
  STATE_OFFER_LOADING: 'STATE_OFFER_LOADING',
  STATE_TRAVEL_PLAN_SUCCESS: 'STATE_TRAVEL_PLAN_SUCCESS',
  STATE_REVIEW_LOADING: 'STATE_REVIEW_LOADING',
  STATE_DETAIL_CANCELLATION_POLICY_SUCCESS: 'STATE_DETAIL_CANCELLATION_POLICY_SUCCESS',
  REVIEW_COMPONENT_FAILURE_ERROR_DATA: 'REVIEW_COMPONENT_FAILURE_ERROR_DATA',
  COMPONENT_COUNT: 'COMPONENT_COUNT',
  SET_OFFER_SECTION_DATA: 'SET_OFFER_SECTION_DATA',
  STATE_OFFER_SUCCESS: 'STATE_OFFER_SUCCESS',
  COUPON_ERROR: 'COUPON_ERROR',
  STATE_REVIEW_EMI_OPTIONS: 'STATE_REVIEW_EMI_OPTIONS',
  UPDATE_PAX_DATA:'UPDATE_PAX_DATA',
  CLEAR_DETAIL_DATA: 'CLEAR_DETAIL_DATA',
  OPEN_SLOT_TIMING_OVERLAY: 'OPEN_SLOT_TIMING_OVERLAY',
  CHANGE_SLOT_TIMING_OVERLAY_TO_DEFAULT: 'OPEN_SLOT_TIMING_OVERLAY_TO_DEFAULT',
  REMOVE_MMT_BLACK_DETAIL: 'REMOVE_MMT_BLACK_DETAIL',
};
export const itineraryUnitTypes = {
    FLIGHT: 'FLIGHT',
    HOTEL: 'HOTEL',
    CAR: 'CAR',
    ACTIVITY: 'ACTIVITY',
    TRANSFERS: 'TRANSFERS',
    ADD_ACTIVITY: 'ADD_ACTIVITY',
    MEAL: 'MEAL',
    MEALS: 'MEALS',
    SIGHTSEEING : 'SIGHTSEEING',
    COMMUTE : 'COMMUTE',
    ADD_TRANSPORT_OPTION: 'ADD_TRANSPORT_OPTION',
};
export const componentImageTypes = {
    FLIGHT: 'FLIGHT',
    HOTEL: 'HOTEL',
    CAR: 'CAR',
    ACTIVITY: 'ACTIVITY',
    TRANSFERS: 'TRANSFERS',
    COMMUTE: 'COMMUTE',
};
export const errorCodesPrefix = {
  FLIGHT: 'FLIGHT',
  HOTEL: 'HOTEL',
  CAR: 'CAR',
  ROUTE: 'ROUTE',
  PACKAGE: 'PACKAGE',
};
export const packageErrorCodes = {
  PACKAGE1: 'PACKAGE001',
  PACKAGE2: 'PACKAGE002',
  PACKAGE3: 'PACKAGE003',
  PACKAGE4: 'PACKAGE004',
};
export const errorMessages = {
  FLIGHT: 'Seems like flights are not available for these date. Please try changing the date.',
  HOTEL: 'Seems like hotels are not available for these date. Please try changing the date.',
  CAR_AND_ROUTE: 'Oops! Something went wrong. Why don’t you try some other package?',
  PACKAGE_MAIN: 'It looks like our servers are sleeping. Why don’t you refresh them?',
  PACKAGE_CITY: 'This package is not available from the selected city. Please try a different city.',
  PACKAGE_DATE: 'This package is not available on the selected date. Please try a different date.',
  PACKAGE_DEFAULT: 'Seems like something is wrong with the package. Please try another package.',
  NON_LOGIN: 'You are not logged in. Please login.',
  DEFAULT: 'It looks like our servers took too long.',
};
export const itineraryUnitSubTypes = {
  FLIGHT_ARRIVE: 'FLIGHT_ARRIVE',
  FLIGHT_DEPART: 'FLIGHT_DEPART',
  CHECKIN: 'CHECKIN',
  CHECKOUT: 'CHECKOUT',
  BREAKFAST: 'BREAKFAST',
  DINNER: 'DINNER',
  LUNCH:'LUNCH',
  SIGHTSEEING: 'SIGHTSEEING',
  TRANSFER_ATH: 'TRANSFER_ATH',
  TRANSFER_HTA: 'TRANSFER_HTA',
  CAR_SIGHTSEEING: 'CAR_SIGHTSEEING',
  CAR_INTERCITY: 'CAR_INTERCITY',
  ADD_ACTIVITY: 'ADD_ACTIVITY',
  COMMUTE_ARRIVE: 'COMMUTE_ARRIVE',
  COMMUTE_DEPART: 'COMMUTE_DEPART',
  ACT_TRANSFER: 'TRANSFER',
  ACT_MEALS: 'MEALS',
};
export const flightDetailTypes = {
  DOM_ONWARDS: 'DOM_ONWARDS',
  DOM_RETURN: 'DOM_RETURN',
  OBT: 'OBT',
};
export const visaTypes = {
  INCLUDED: 'INCLUDED',
  VISA_ASSISTANCE: 'VISA_ASSISTANCE',
};

export const overlays = {
  NONE :'',
  IMAGE_OVERLAY: 'ImageOverlay',
  OFFER_OVERLAY: 'OfferOverlay',
  FLIGHTS_OVERLAY: 'FlightsOverlay',
  VISA_OVERLAY: 'VisaOverlay',
  EXTRA_INFO_OVERLAY: 'ExtraInfoOverlay',
  ACTIVITY_OVERLAY: 'ActivityOverlay',
  SIGHTSEEING_OVERLAY: 'SightseeingOverlay',
  TRANSFER_OVERLAY: 'TransferOverlay',
  HOTEL_OVERLAY: 'HotelOverlay',
  PRICE_OVERLAY: 'PriceOverlay',
  TOOLTIP_OVERLAY: 'ToolTipOverlay',
  EDIT_OVERLAY: 'EditOverlay',
  PLACES_OVERLAY: 'PlacesOverlay',
  CHANGE_CITY_OVERLAY: 'change_from_city',
  CHANGE_DATE_OVERLAY: 'change_Date',
  CHANGE_PAX_OVERLAY: 'change_pax',
  CHANGE_FLIGHT_OVERLAY: 'change_flight',
  CHANGE_TRANSFER_OVERLAY: 'change_transfer',
  ADD_ACTIVITY_OVERLAY: 'add_activity',
  DETAIL_ACTIVITY_OVERLAY: 'detail_activity',
  COST_OVERLAY: 'CostOverlay',
  BRANCH_OVERLAY: 'branch_locator',
  MENU_OVERLAY: 'MenuOverlay',
  PACKAGE_TYPE: 'PACKAGE_TYPE',
};

export const extraInfoHeadings = {
  EXCLUSIONS: 'EXCLUSIONS',
  TNC: 'TERMS AND CONDITIONS',
  CANCELLATION_POLICY: 'CANCELLATION POLICIES',
  MAIN_HEADING: 'Cancellation and other Policies',
};

export const extraInfoRefs = {
  EXCLUSIONS: 'EXCLUSIONS',
  TNC: 'TNC',
  CANCELLATION_POLICY: 'CANCELLATION_POLICY',
  FREE_CANCELLATION_BANNER: 'FREE_CANCELLATION_BANNER',
};

export const visaOverlayHeadings = {
  IMPORTANT_INFO: 'Important Information',
  REQUIRED_DOCS: 'Required Documents',
  PROCESSING_TIME: 'Processing Time',
};
export const PDTConstants = {
  PDT_RAW_EVENT: 'rawEvent',
  INCLUSION_PREFIX: 'inclusion',
  SHARE: 'share',
  SHORTLIST: 'shortlist',
  IMAGE_CAROUSAL: 'image_carousal',
  DAY_PLAN: 'day_plan',
  BOOK: 'book',
  BACK: 'back',
  FAB_STRIP: 'fab',
  POLICY: 'policy',
  CHEAPER_DATE_CHECK: 'cheaper_date_check',
  COST_PERS: 'what_it_costs',
  ITINERARY: 'itinerary',
  EXPAND: 'expand',
  COLLAPSE: 'collapse',
  LOGIN: 'login',
  CONTACT_ICON_CHATGPT_SHOWN: 'chatgpt_shown',
  CONTACT_ICON: 'contact_icon',
  CALL_SUFFIX: '_call',
  QUERY_SUFFIX: '_query',
  CHAT_SUFFIX: '_chat',
  CHATGPT_SUFFIX: '_chatgpt',
  BRANCH_LOCATOR_SUFFIX: '_branchLocator',
  CROSS: 'cross',
  CHANGE_FLIGHT: 'change_flight',
  FLIGHT_UPGRADE: 'flight_upgrade',
  CHANGE_HOTEL: 'change_hotel',
  REMOVE_FLIGHT: 'remove_flight',
  ADD_FLIGHT: 'add_flight',
  REMOVE_TRANSFER: 'remove_transfer',
  ADD_TRANSFER: 'add_transfer',
  CHANGE_TRANSFER: 'change_transfer',
  ADD_ACTIVITY: 'add_activity',
  ADDED_ACTIVITY: 'added_activity',
  REMOVE_ACTIVITY: 'remove_activity',
  CHANGE_ACTIVITY: 'change_activity',
  DETAILS_ACTIVITY: 'details_activity',
  REMOVE_ACTIVITY_ALL: 'remove_activity_all',
  CHOOSE_OPTIONS: 'choose_options',
  VIEW_HOTEL: 'view_hotel',
  ACTIVITIES_SUFFIX: '_activities',
  SIGHTSEEING_SUFFIX: '_sightseeing',
  TRANSFERS_SUFFIX: '_transfers',
  HOTELS_SUFFIX: '_hotels',
  FLIGHTS_SUFFIX: '_flights',
  CHANGE_PAX: 'change_pax',
  CHANGE_DATE: 'change_date',
  EDIT_INTENT: 'edit_intent',
  VIEW_PLACES: 'view_all_places',
  SIMILAR_PACKAGE: 'similar_package',
  BRANCH_LOCATOR_BACK: 'branchLocator_back',
  BRANCH_LOCATOR_CALL: 'branchLocator_call',
  BRANCH_LOCATOR_DIRECTIONS: 'branchLocator_directions',
  BRANCH_LOCATOR_SHARE: 'branchLocator_share',
  BRANCH_LOCATOR_DETAILS: 'branchLocator_detail',
  BRANCH_LOCATOR_DETAILS_CLOSE: 'branchLocator_detail_close',
  CHANGE_FROM_CITY: 'change_hub',
  SELECT_FROM_CITY: 'select_hub',
  SEEN: 'Seen_',
  CHANGE: 'Change_',
  CLOSE: 'Close_',
  FLIGHT_SOLD_OUT: 'flight_soldout_error',
  HOTEL_SOLD_OUT: 'hotel_soldout_error',
  ACTIVITY_SOLD_OUT: 'Message_shown_Activity_Unavailable',
  VISA_SOLD_OUT: 'visa_error',
  GENERIC_FORWARD_FLOW_ERROR: 'generic_error',
  CANCELLATION_POLICY:'cancellation_policy',
  DATE_CHANGE_POLICY:'date_change_policy',
};
export const deepLinkParams = {
  id: 'id',
  fromCity: 'fromCity',
  fromCityCode: 'fromCityCode',
  pkgType: 'pkgType',
  depDate: 'depDate',
  category: 'category',
  listingClassId: 'listingClassId',
  cmp: 'cmp',
  deepLink: 'from_detail_deeplink',
  dynamicPackageId: 'dynamicPackageId',
  savePackageId: 'savePackageId',
  fphSavePackageId: 'fphSavePackageId',
  story: 'story',
  pt: 'pt',
  aff: 'aff',
  query: 'query',
  initId: 'intid',
  fromSeo: 'fromSeo',
  quoteRequestId:'quoteRequestId',
  rooms: 'room',
  source: 'source',
  variantId: 'variantId',
  banner: 'banner',
  requestType: 'requestType',
  docId: 'docId',
  activeConversationId: 'activeConversationId',
  openTravelPlex: 'openTravelPlex',
};

export const packageActions = {
  TOGGLE: 'TOGGLE',
  CHANGE: 'CHANGE',
  REMOVE: 'REMOVE',
  ADD: 'ADD',
  MODIFY: 'MODIFY',
};

export const HOTEL_CHANGE_TYPE = {
  ROOM_CHANGE:'ROOM_CHANGE',
  HOTEL_CHANGE:'HOTEL_CHANGE',
};

export const packageActionComponent = {
  FLIGHT: 'FLIGHT',
  HOTEL: 'HOTEL',
  CAR: 'CAR_ITINERARY',
  ACTIVITY: 'ACTIVITY',
  TRANSFER: 'AIRPORT_TRANSFER',
  VISA: 'VISA',
  COMMUTE: 'COMMUTE',
};

export const DETAIL_MAIN_IMAGE_SIZE = '_360x203';
export const DETAIL_GALLERY_IMAGE_SIZE = '_360x203';
export const MAX_DAY_IMG_SIZE = 5;
export const MAX_DAY_HOTEL_IMG_SIZE = 1;
export const MAX_DAY_ACT_IMG_SIZE = 1;
export const PERSUASION_PAGE_NAME_DETAIL = 'DETAILS';
export const PERSUASION_PAGE_NAME_PSM_DETAIL = 'PRESALES_DETAIL';
export const DETAIL_TRACKING_PAGE_NAME = 'detail';
export const DETAIL_TRACKING_ERROR_PAGE_NAME = 'detail:error';
export const PERS_INDEX_TO_SHOW_AS_TAG = 1;
export const DISCOUNT_TYPE_INSTANT = 'Instant';
export const INCLUSIONS = 'INCLUSIONS';
export const EXCLUSIONS = 'EXCLUSIONS';
export const DESCRIPTION_TITLE = 'ABOUT THE ACTIVITY';
export const DEFAULT_ACTIVITY_OVERLAY_HEADING = 'Activity included in this package';
export const DEFAULT_ACTIVITIES_OVERLAY_HEADING = 'Activities included in this package';
export const ACTIVITY_OVERLAY_APPEND_TEXT = 'Activity in ';
export const ACTIVITITIES_OVERLAY_APPEND_TEXT = 'Activities in ';
export const DEFAULT_HOTEL_OVERLAY_HEADING = 'Hotels included in this package';
export const DEFAULT_FLIGHT_OVERLAY_HEADING = 'Flights included in this package';
export const HOTEL_OVERLAY_APPEND_TEXT = 'Hotel in ';
export const TRANSFER_OVERLAY_HEADING = 'Transfers included in this package';
export const SIGHTSEEING_OVERLAY_HEADING = 'Sightseeing on the way';
export const ADD_ACTIVITY_HEADING = 'Spend the day at your own leisure.';
export const PRIVATE = 'PRIVATE';
export const GROUP = 'GROUP';
export const SHARED = 'SHARED';
export const TRANSFER_TYPE = 'TRANSFER TYPE';
export const VEHICLE_TYPE = 'VEHICLE TYPE';
export const PRIVATE_TRANSFER = 'Private Transfer';
export const GROUP_TRANSFER = 'Group Transfer';
export const DAY = 'DAY';
export const NO_ACTIVITIES_INCLUDED = 'No activities included';
export const ACTIVITIES = 'Activities';
export const ACTIVITY = 'Activity';
export const ENTRY_FEE_INCLUDED_KEY = 'entryFeeIncluded';
export const MEAL_INCLUDED_KEY = 'mealIncluded';
export const WATER_BOTTLE_INCLUDED_KEY = 'waterBottleIncluded';
export const ENTRY_FEE_INCLUDED_VALUE = 'Entry fee';
export const MEAL_INCLUDED_VALUE = 'Meal';
export const WATER_BOTTLE_INCLUDED_VALUE = 'Water bottle';
export const TRANSFER_AIRPORT_TO_HOTEL = 'TRANSFER_ATH';
export const TRANSFER_HOTEL_TO_AIRPORT = 'TRANSFER_HTA';
export const CAR_INTERCITY = 'CAR_INTERCITY';
export const CAR_SIGHTSEEING = 'CAR_SIGHTSEEING';
export const AIRPORT_TRANSFER = 'AIRPORT_TRANSFER';
export const CAR_ITR_AIRPORT_TRANSFER = 'CAR_ITR_AIRPORT_TRANSFER';
export const AIRPORT_TO_HOTEL = 'AIRPORT_TO_HOTEL';
export const HOTEL_TO_AIRPORT = 'HOTEL_TO_AIRPORT';
export const SEDAN = 'Sedan';
export const MULTI_SEATER_COACH = 'Multi seater coach';
export const TIPS = 'TIPS';
export const DETAIL_INTERVENTION_PAGE_NAME = 'detail';
export const DETAIL_INTERVENTION_ERROR_PAGE_NAME = 'detail_error';
export const DETAIL_QUERY_PAGE_NAME = 'detail';
export const DETAIL_LOCAL_NOTIFICATION_PAGE_NAME = 'detail';
export const COUPON_CODE_SUCCESS = 'success';
export const COUPON_CODE_FAILED = 'failed';
export const OBT_BRANCH = 'OBT';
export const HOL_TYPE_TAG_NAME = 'HolidayType';
export const OVERLAY_ANIMATE_DURATION = 600;
export const OVERLAY_ANIMATE_DELAY = 200;
export const OVERLAY_FULL_BOTTOM = 700;
export const OVERLAY_HALF_BOTTOM = 450;
export const VIEW_MORE = 'View More';
export const DEFAULT_TRAVELLER_COUNT = 2;
export const MIN_CHILD_AGE = 2;
export const MAX_CHILD_AGE = 11;
export const MAX_COMPULSORY_BED_CHILD_AGE = 5;
export const FLIGHT_PKG_MAX_TRAVELLER_LIMIT = 9;
export const MAX_ROOM_TRAVELLER_LIMIT = 4;
export const MAX_ROOM_LIMIT = 6;
export const MAX_TRAVELLER_COUNT = 20;
export const DFIT_PKG_TYPE = 'DFIT';
export const PRICE_TOOL_TIP = 'price';
export const ERROR_DETAIL_RELOAD = 'PACKAGE012';
export const DETAILS_REVIEW_EXPIRY_MSG = 'Seems like you\'ve been idle for too long. Please retry now.';
export const DETAILS_HOTEL_CHANGE_ERROR_MSG = 'Hotel could not be changed. Please try again later';
export const DETAILS_FLIGHT_CHANGE_ERROR_MSG = 'Sorry! Flight change unsuccessful. Try again later';
export const DETAILS_TRANSFER_CHANGE_ERROR_MSG = 'Sorry! Transfer change unsuccessful. Try again later';
export const REMOVE_FLIGHT_CHANGE_ERROR_MSG = 'Sorry! Flight could not be removed. Try again later';
export const DETAILS_ACTIVITY_DETAILS_ERROR_MSG = 'Sorry! Activity details unavailable. Try again later';
export const ADD_ACTIVITY_DETAILS_ERROR_MSG = 'Sorry! Activities could not be updated. Please try again later.';
export const LOAD_ACTIVITY_DETAILS_ERROR_MSG = 'Sorry! Activities could not be loaded. Try again later';
export const REMOVE_ACTIVITY_DETAILS_ERROR_MSG = 'Sorry! Activity could not be removed. Try again later';
export const DETAIL_REVIEW_ERROR_MSG = 'Oops! Some unknown error has occurred. Please try again.';
export const ATH_INCL_TEXT = 'Airport-hotel drop is included';
export const HTA_INCL_TEXT = 'Hotel-airport drop is included';
export const AH_BOTH_INCL_TEXT = 'Airport-hotel and hotel-airport drop included';
export const HOTEL_ROOM_ONLY_MEAL_PLAN = 'EP';
export const DETAIL_REMOVE = 'FLIGHT016'; //to-do by Pujasvi
export const detailReviewFailure = {
  REVIEW_FAILED_TYPE_HOTEL: 'HOTEL',
  REVIEW_FAILED_TYPE_ACTIVITY: 'ACTIVITY',
  REVIEW_FAILED_TYPE_ADDON_INCLUSION: 'ADDON_INCLUSION',
  REVIEW_FAILED_TYPE_TRANSFER: 'TRANSFERS',
  REVIEW_FAILED_TYPE_VISA: 'VISA',
  REVIEW_FAILED_TYPE_FLIGHT: 'FLIGHT',
  REVIEW_FAILED_TYPE_GENERIC: 'GENERIC',
  PREPAYMENT_FAILED_TYPE: 'PRE_PAYMENT',
};

export const HOLIDAY_PACKAGE_TYPE = {
  FLEXI_PACKAGE : 'FIT',
  GROUP_PACKAGE : 'FD',
  FLEXI:'Flexi Package',
};
export const SIGHT_SEEING_INCLUDED = 'Sightseeing Included';
export const SECTIONS_LIST = ['DETAIL_COUPON_LIST', 'MMT_BLACK', 'PERSONALIZATION'];
export const SELECT_ACTIONS = {
  APPLY: 'APPLY',
  REMOVE: 'REMOVE',
};
export const INVALID_COUPON_MESSAGE = 'Sorry, this coupon code is not valid';
export const SUCCESSFULLY_APPLIED_MESSAGE = 'Coupon Applied Successfully';
export const COUPON_BOX_PLACEHOLDER_TEXT = {
  FOCUSED: 'Enter Coupon code',
  UNFOCUSED: 'Have a Coupon Code?',
};

//TODO not being used anywhere can remove
export const OFFER_SECTION_CONSTANTS = {
  DETAILS_OFFER_SECTION_API: '/package/section/fetch',
  DETAILS_MODIFY_OFFER_SECTION_API: '/package/detail/coupon/select',
};
export const INCLUSIONS_TYPE = {
  FLIGHTS: 'flights',
  ACTIVITIES: 'activities',
  HOTELS: 'hotels',
  VISA: 'visa',
  SIGHTSEEING: 'sightSeeing',
  AIRPORT_TRANSFER: 'airportTransfers',
  CAR_ITINERARY: 'carItinerary',
  CITY_DROPS: 'cityDrops',
  CAB_ITINERARY:'cabItinerary',
};

export const FEATURE_EDIT_HEADING = 'Addons included in this package';

export const VISA_TYPES = {
  visaSc: 'Assistance',
  visaCharges: 'Included',
  onArrival: 'Included',
};

export const ADDON_TYPES = {
  ADDON: 'ADDON',
  TRIP_MONEY: 'TRIP_MONEY'
};

export const ADDON_SUBTYPES = {
  INSURANCE: 'INSURANCE',
  VISA_PROTECTION_PLAN: 'VISA_PROTECTION_PLAN'
};
export const ADDON_LOBNAME = {
  INSURANCE: 'Insurance',
  VISA_PROTECTION_PLAN: 'Visa Protection Plan'
};
