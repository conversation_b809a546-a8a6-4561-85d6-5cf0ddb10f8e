import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useRef } from 'react';
import { isNumber } from 'lodash';
// @ts-ignore
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { getBaseVisaCondition, isSummaryTabDefaultOpen } from '../../../utils/HolidayUtils';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import { getFontFamily } from 'apps/holidays/src/theme';
import { FONT_KEYS } from 'apps/holidays/src/theme/font';import { getShowNewActivityDetail } from '../../../utils/HolidaysPokusUtils';

interface SorterProps {
  toggleDayPlan: any;
  currentActivePlanOnItinerary: any;
  detailData: any;
  errorType: [];
  sightseeingCount: Number;
  fromPreSales: boolean;
  index: any;
  onPressHandler: any;
}

export enum TABS {
  DAY = 'DAY',
  FLIGHT = 'FLIGHT',
  HOTEL = 'HOTEL',
  TRANSFER = 'TRANSFER',
  ACTIVITIES = 'ACTIVITY',
  VISA = 'VISA',
  COMMUTE = 'COMMUTE',
  MEALS = 'MEALS',
  SUMMARY = 'SUMMARY'
}

// Exported variable, Check all references.
export const getTabNames = () => [
  'Day Plan',
  'Commute',
  'Flights',
  'Hotels',
  'Transfers',
  'Activities',
  ... getShowNewActivityDetail() ? ['Meals'] : [], //Note to show activity tab only when pokus is true
  ... getShowNewActivityDetail() ? [] : ['Visa'], //Note this to remove VISA tab when show New UI wrt to pokus value
];

export const Sorter = ({
  toggleDayPlan,
  currentActivePlanOnItinerary,
  detailData,
  errorType,
  componentCount,
  fromPreSales,
  sightseeingCount,
  index,
  onPressHandler
}: SorterProps) => {
  const { packageDetail } = detailData || {};
  const { packageInclusionsDetail, basePackageInclusionsDetail = {} } = packageDetail || {};
  let flatList = useRef();

  const {
    flights,
    hotels,
    meals,
    carItinerary,
    cabItinerary,
    cityDrops,
    activities,
    airportTransfers,
    visa,
    sightSeeing,
  } = packageInclusionsDetail || {};

  const isVisa = getBaseVisaCondition(basePackageInclusionsDetail);

  const showSummaryTabFirst = isSummaryTabDefaultOpen({ fromPreSales });

  const TABS_MAP = new Map([
    ['Day Plan', 'DAY'],
    ['Summary', 'SUMMARY'],
    ['Commute', 'COMMUTE'],
    ['Flights', 'FLIGHT'],
    ['Hotels', 'HOTEL'],
    ['Transfers', 'TRANSFER'],
    ['Activities', 'ACTIVITY'],
    ['Meals', 'MEALS'],
    ['Visa', 'VISA'],
  ]);

  const onTabPress = (tab: TABS) => {
    // Scroll to top on reset tab.
    onPressHandler(index + 1);
    toggleDayPlan(tab);
  };

  useEffect(() => {
    if (flatList && flatList.current) {
      let tabsData = getTabNames();
      if(showSummaryTabFirst) {
         tabsData = [...tabsData, 'Summary']
      }
      const scrollIndex = tabsData.findIndex(
        (item) => TABS_MAP.get(item) === currentActivePlanOnItinerary,
      );
      if (scrollIndex >= 0) {
        flatList.current.scrollToIndex({ animated: true, index: scrollIndex });
      }
    }
  }, [currentActivePlanOnItinerary]);

  const TAB = React.memo(({ item, count }) => {
    const isActive = currentActivePlanOnItinerary === TABS_MAP.get(item);
    const isError = errorType.includes(item);
    const bgStyle = isActive ? (isError ? styles.sorterActiveError : styles.sorterActive) : {};
    const textStyle = isActive ? (isError ? styles.sorterTextActiveError : styles.sorterTextActive) : isError ?  styles.sorterTextActiveError : {};

    return (
      <TouchableOpacity
        style={[styles.sorter, bgStyle]}
        onPress={() => onTabPress(TABS_MAP.get(item))}
      >
        <View style={{ flexDirection: 'row' }}>
          {count > 0 && <Text style={[styles.countTextStyle, textStyle ,{fontFamily: getFontFamily(FONT_KEYS.TEXT_BOLD)}]}>{count} </Text>}
          <Text style={[styles.sorterText, textStyle]}>{item}</Text>
        </View>
      </TouchableOpacity>
    );
  });
  const JOINTTAB = React.memo(({ item1, count1, item2, count2, item }) => {
    const isActive = currentActivePlanOnItinerary === TABS_MAP.get(item);
    const isError = errorType.includes(item);
    const bgStyle = isActive ? (isError ? styles.sorterActiveError : styles.sorterActive) : {};
    const textStyle = isActive
      ? isError
        ? styles.sorterTextActiveError
        : styles.sorterTextActive
      : isError
      ? styles.sorterTextActiveError
      : {};

    return (
      <TouchableOpacity
        style={[styles.sorter, bgStyle]}
        onPress={() => onTabPress(TABS_MAP.get(item))}
      >
        <View style={{ flexDirection: 'row' }}>
          {!!count1 && (
            <Text>
              <Text style={[styles.countTextStyle, textStyle]}>{count1} </Text>
              <Text style={[styles.sorterText, textStyle]}>{item1}</Text>
            </Text>
          )}
          {!!(count1 && count2) && (
            <Text>
              {' '}
              <Text style={[styles.sorterText, textStyle]}> + </Text>
            </Text>
          )}
          {!!count2 && (
            <Text>
              <Text style={[styles.countTextStyle, textStyle]}>{count2} </Text>
              <Text style={[styles.sorterText, textStyle]}>{item2}</Text>
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  });


  const renderListItem = (item: string, index: number) => {
    if (
      (item === 'Hotels' && hotels) ||
      (item === 'Activities' && (activities || sightSeeing)) ||
      item === 'Day Plan' ||
      (item === 'Visa' && (visa || isVisa)) ||
      (item === 'Meals' && meals ) || 
      (item === 'Summary')
    ) {
      let count = componentCount && isNumber(componentCount[item]) ? componentCount[item] : 0;
      if (item === 'Activities') {
        count = count + sightseeingCount;
      }
      if (item === 'Summary' && showSummaryTabFirst ) {
        return (
          <DynamicCoachMark cueStepKey="summaryTab" offsetHeight = {70} offsetWidth = {70}>
            <TAB item={item} count={count}/>
          </DynamicCoachMark>
        );
      }
      else
      return <TAB item={item} count={count} />;
    } else if (
      item === 'Commute' &&
      (carItinerary || cabItinerary || cityDrops || airportTransfers || flights)
    ) {
      let count1 =
        componentCount && isNumber(componentCount['Flights']) ? componentCount['Flights'] : 0;
      let count2 =
        componentCount && isNumber(componentCount['Transfers']) ? componentCount['Transfers'] : 0;
      if ((count1 + count2) > 0) {
      return (
        <JOINTTAB
          item1={'Flights'}
          count1={count1}
          item2={'Transfers'}
          count2={count2}
          item={item}
        />
      );
      } else {
      return [];
      }
    } else {
      return [];
    }
  };
  let tabsData = getTabNames();
  if(showSummaryTabFirst) {
    tabsData = [...tabsData, 'Summary']
  }
  return (
    <View style={styles.sorterWrapper}>
      <FlatList
        horizontal={true}
        data={tabsData}
        renderItem={({ item, index }) => renderListItem(item, index)}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        onScrollToIndexFailed={() => {}}
        contentContainerStyle={{ paddingHorizontal: 15 }}
        ref={flatList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  sorterWrapper: {
    ...paddingStyles.pv6,
    flexDirection: 'row',
  },
  sorter: {
    height: 34,
    backgroundColor: holidayColors.white,
    ...marginStyles.ma8,
    ...marginStyles.ml2,
    alignItems: 'center',
    justifyContent: 'center',
    ...paddingStyles.ph10,
    ...holidayBorderRadius.borderRadius8,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  countTextStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  sorterText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  sorterActive: {
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
  },
  sorterTextActive: {
    fontFamily: fonts.bold,
    color: holidayColors.primaryBlue,
  },
  //to change error color
  sorterActiveError: {
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.red,
    borderWidth: 1,

  },
  //to change error color
  sorterTextActiveError: {
    fontFamily: fonts.bold,
    color: holidayColors.red,
  },
});
