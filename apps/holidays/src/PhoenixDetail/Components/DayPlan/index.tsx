import React from 'react';
import { StyleSheet, View } from 'react-native';
import { isEmpty } from 'lodash';
import {
  ComponentAccessRestriction,
  DayItinerary,
  ItineraryUnit,
  PackageDetail,
  DayStripItem,
  RenderCardProps,
  DayWiseAddActivityRestriction,
  staticDataFDInterface,
  Destination,
  ActivityDetailObject,
} from '../../Types/PackageDetailApiTypes';
import {
  itineraryUnitTypes,
  itineraryUnitSubTypes,
  packageActionComponent,
} from '../../DetailConstants';
import { TABS } from './Sorter';
import {
  AddDayStripItems,
  createDayPlanData,
  createSubtitleData,
  getActivityExtraData,
  getSelectedItemIndexOnOverlay,
} from '../../Utils/PhoenixDetailUtils';
import { ActivityReqParams } from '../../Types/PhoenixActivityApiTypes';
import { getPackagePrice, onRemoveActivityPress } from '../../Utils/ActivityOverlayUtils';
import { FlightListingRequest } from '../../Types/FlightListingApiTypes';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { mealSubUnitTypes } from '../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { getShowNewActivityDetail } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import { isMobileClient } from '../../../utils/HolidayUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';
import { getCollapsedState, updateOnActivityDetailPage } from '../ItineraryV2/ItineraryV2Utils';

/* Components */
import MealRowV2 from '../../Components/ItineraryV2/Meals/MealRow';
import HotelRowV2 from '../../Components/ItineraryV2/Hotel/HotelRow';
import FlightRowV2 from '../../Components/ItineraryV2/Flight/FlightRow';
import TransferRowV2 from '../../Components/ItineraryV2/Transfer/TransferRow';
import SightSeeingRowV2 from '../../Components/ItineraryV2/SightSeeing/SightSeeingRow';
import ActivityRowV2 from '../ItineraryV2/Activity/ActivityRow';
import StaticItineraryComponentV2 from '../ItineraryV2/StaticItinerary/StaticItineraryRow';
import Commute from './commute/commute';
import AddActivity from './AddActivity';
import StaticItinerary from './StaticItineraryComponent';
import SightSeeingRow from './SightSeeingRow';
import MealRow from './MealRow';
import AddTravelTidbits from './AddTravelTidbits';
import ActivityRow from './activityRow';
import DayPlanHeader from './dayPlanHeader';
import FlightRow from './Flight/FlightRow';
import HotelRow from './hotelRow';
import TransferRow from './transferRow';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import { logPhoenixDetailPDTEvents } from '../../../utils/PhoenixDetailPDTTrackingUtils';

interface DayPlanProps {
  data: DayItinerary;
  roomDetails: any;
  staticData: staticDataFDInterface[];
  bundled: boolean;
  destinationMap: Map<number, Destination>;
  index: number;
  isVisible: boolean;
  packageDetail: PackageDetail;
  currentActivePlanOnItinerary: string;
  onComponentChange: () => void;
  onPackageComponentToggle: (b: boolean, FLIGHT: string) => void;
  packageDetailDTO: any;
  flightReqParams: FlightListingRequest;
  branch: string;
  showOverlay: (key: string, data: object) => {};
  hideOverlays: (keys: string[]) => {};
  clearOverlays: () => {};
  failedHotels: any;
  ifFlightGroupFailed: boolean;
  lastPageName: string;
  isLoading: boolean;
  detailData: any;
  hotelDetailLoading: boolean;
}

export const DayPlan = (dayPlanProps: DayPlanProps) => {
  const {
    failedHotels = [],
    ifFlightGroupFailed = false,
    data,
    roomDetails,
    staticData,
    bundled,
    destinationMap,
    index,
    totalDays,
    isVisible,
    packageDetail,
    currentActivePlanOnItinerary,
    onComponentChange,
    onPackageComponentToggle,
    packageDetailDTO,
    packageContent,
    flightReqParams,
    branch,
    trackLocalClickEvent,
    trackLocalPageLoadEvent,
    showOverlay,
    hideOverlays,
    clearOverlays,
    lastPageName,
    isLoading,
    detailData,
    hotelDetailLoading,
    fromPresales = false,
    updateMeal = () => {},
  } = dayPlanProps || {};

  if (!isVisible || !data) {
    return null;
  }

  const {
    flightDetail,
    hotelDetail,
    activityDetail,
    packageConfigDetail,
    pricingDetail,
    departureDetail,
    destinationDetail,
  }: PackageDetail = packageDetail || {};
  const activityMap: ActivityDetailObject | undefined = activityDetail
    ? activityDetail.activityMap
    : undefined;
  const accessRestriction: ComponentAccessRestriction | undefined | null = packageConfigDetail
    ? packageConfigDetail?.componentAccessRestriction
    : null;
  const { day, itineraryUnits, city, date = '' }: DayItinerary = data;
  const activityReqParams: ActivityReqParams = getActivityExtraData(packageDetail, day);
  const subtitleData = createSubtitleData(departureDetail, roomDetails, { date });
  const destination: Destination | undefined =
    destinationMap && destinationMap.has(day) ? destinationMap.get(day) : undefined;
  const destinationName = destination ? destination.name : null;
  const allData = createDayPlanData(packageDetail, packageContent, false);
  const { lobsData = [] } = allData || {};

  const capturePhoenixDetailClickEvents = ({
    eventName = '',
    suffix = '',
    prop1 = '',
    value = '',
  }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1,
    });
  };
  const captureClickEvents = ({ eventName = '', value = '' }) => {
    logPhoenixDetailPDTEvents({
      value: value || eventName,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });

    trackLocalClickEvent(eventName, '');
  };
  const openVideDetailOverlay = (
    pos: number,
    hotelSellableId: any,
    day: any,
    lastPageName: string,
    lob: string,
  ) => {
    // selectedItemPosition is the initial position of selected card.
    // This the position of the selected card clicked on Holiday Detail Page
    let selectedItemPosition = getSelectedItemIndexOnOverlay(lobsData, day, hotelSellableId, pos);
    if (selectedItemPosition < 0) {
      selectedItemPosition = 0;
    }
    capturePhoenixDetailClickEvents({
      eventName: 'view_',
      suffix: `${lob}_${destinationName}_${day}`,
      prop1: `base:${lob}`,
      value: `view|${lob}|${destinationName}|${day}`,
    });
    if (fromPresales) {
      captureClickEvents({
        eventName: `details_${selectedItemPosition}_${lob}_${destinationName}_${hotelSellableId}`,
        value: `details|${selectedItemPosition}|${lob}|${destinationName}|${hotelSellableId}`,
      });
    }
    const detailProps = {
      roomDetails: roomDetails,
      onComponentChange,
      onPackageComponentToggle,
      packageDetailDTO,
      packageDetail,
      selectedItemPosition,
      subtitleData,
      branch,
      lastPageName,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      showOverlay,
      hideOverlays,
      clearOverlays,
      failedHotels,
      ifFlightGroupFailed,
      isLoading,
      detailData,
      hotelDetailLoading,
      packageContent,
    };

    if(isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_OVERLAY, {
        props: detailProps,
      })
    } else {
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_OVERLAY,
      overlayKey: Overlay.VIEW_DETAILS,
      hideOverlays,
      showOverlay,
      props: detailProps,
    });
    }
  };

  const openActivityListingPage = (activityItem = null) => {
    capturePhoenixDetailClickEvents({
      eventName: 'add_activity_',
      suffix: `${day}`,
      prop1: `base:${itineraryUnitTypes.ACTIVITY}`,
      value: `add|activity|${day}`,
    });

    if (showNewActivityDetail) {
      const activityProductType =
        activityItem?.itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER
          ? itineraryUnitSubTypes.ACT_TRANSFER
          : activityItem?.itineraryUnitSubType === itineraryUnitTypes.MEALS
          ? itineraryUnitTypes.MEAL
          : itineraryUnitTypes.ACTIVITY;

      return openTravelTidbits({ activityProductType });
    }

    const activityListingProps = {
      activityReqParams,
      day: day,
      dynamicId: packageDetailDTO.dynamicPackageId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      subtitleData: subtitleData,
      lastPage: HOLIDAY_ROUTE_KEYS.DETAIL,
      branch,
      packageDetailDTO,
      roomDetails,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      showOverlay,
      hideOverlays,
    };
    isMobileClient()
      ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_LISTING, activityListingProps)
      : showOverlay(Overlay.ACTIVITY_LISTING_PAGE, activityListingProps);
  };

  const openTravelTidbits = ({ activityProductType = '' } = {}) => {
    // this suggests that we are coming from Add to Day Click and not Change click
    if (isEmpty(activityProductType)) {
      capturePhoenixDetailClickEvents({
        eventName: `${currentActivePlanOnItinerary || 'itinerary'}_${day}_addActivity_${city}`,
        value: `${currentActivePlanOnItinerary || 'itinerary'}|${day}|addActivity|${city}`,
      });
    }
    const travelTibitsProps = {
      departureDetail,
      activityReqParams,
      day: day,
      itineraryUnitDate: date,
      dynamicId: packageDetailDTO.dynamicPackageId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      subtitleData: subtitleData,
      lastPage: HOLIDAY_ROUTE_KEYS.DETAIL,
      branch,
      packageDetailDTO,
      roomDetails,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      activityProductType,
      currentActivePlanOnItinerary,
      showOverlay,
      hideOverlays,
    }
    if(isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRAVEL_TIDBITS, {
        props: travelTibitsProps,
      });
    } else {
      holidayNavigationPush({
        props: travelTibitsProps,
        pageKey: HOLIDAY_ROUTE_KEYS.TRAVEL_TIDBITS,
        overlayKey: Overlay.TRAVEL_TIBITS,
        showOverlay,
        hideOverlays,
      });
    }
  };

  const removeActivity = (activityCode: string) => {
    capturePhoenixDetailClickEvents({
      eventName: 'remove_',
      suffix: `${itineraryUnitTypes.ACTIVITY}_${day}_click_${destinationName}`,
      prop1: `base:${itineraryUnitTypes.ACTIVITY}`,
      value: `remove|${itineraryUnitTypes.ACTIVITY}|${day}|click|${destinationName}`,
    });
    onRemoveActivityPress(
      activityReqParams,
      activityCode,
      packageDetailDTO.dynamicPackageId,
      day,
      onComponentChange,
    );
  };

  const openFlightListingPage = (flightSequence: number) => {
    const { flightSelections, overnightDelays }: FlightListingRequest = flightReqParams;
    const requestParams: FlightListingRequest = {};
    if (flightSequence) {
      requestParams.listingFlightSequence = flightSequence;
    }
    if (flightSelections && flightSelections.length > 0) {
      requestParams.flightSelections = flightSelections;
    }
    if (overnightDelays) {
      requestParams.overnightDelays = overnightDelays;
    }
    trackLocalClickEvent('change_', `${itineraryUnitTypes.FLIGHT}_${destinationName}_${day}`);
    const flightOverlayProps = {
      flightRequestObject: requestParams,
      dynamicId: packageDetailDTO.dynamicPackageId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      onPackageComponentToggle: onPackageComponentToggle,
      subtitleData: subtitleData,
      accessRestriction: accessRestriction,
      lastPage: HOLIDAY_ROUTE_KEYS.DETAIL,
      packageDetailDTO,
      roomDetails,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      showOverlay,
      hideOverlays,
      clearOverlays,
    };
    showOverlay(Overlay.FLIGHT_OVERLAY, flightOverlayProps);
  };

  const removeFlights = () => {
    capturePhoenixDetailClickEvents({
      eventName: 'remove_',
      suffix: `${itineraryUnitTypes.FLIGHT}_${day}_click_${destinationName}`,
      prop1: `base:${itineraryUnitTypes.FLIGHT}`,
      value: `remove|${itineraryUnitTypes.FLIGHT}|${day}|click|${destinationName}`,
    });
    onPackageComponentToggle(false, packageActionComponent.FLIGHT);
  };

  const openComboPage = () => {
    captureClickEvents({
      eventName: 'Click_transportOptions',
    });
    const comboProps = {
      departureDetail,
      destinationDetail,
      trackLocalClickEvent,
      dynamicPackageId: packageDetailDTO.dynamicPackageId,
      onComponentChange,
    };
    if(isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.COMBO, {
        props: comboProps,
      });
    } else {
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.COMBO,
      overlayKey: Overlay.COMMUTE_VIEW_OPTIONS,
      hideOverlays,
      showOverlay,
      props: comboProps,
    });
    }
  };

  const openTransferAthAsActivityDetail = ({ item, unit }) => {
    const { itineraryUnitSubType = '' } = unit || {};
    capturePhoenixDetailClickEvents({
      eventName: 'view_',
      suffix: `${itineraryUnitSubType}_${destinationName}_${day}`,
      value: `view|${itineraryUnitSubType}|${destinationName}|${day}`,
    });

    const packagePrice = getPackagePrice(pricingDetail);
    const { privateTransfer = {}, groupTransfer = {}, staySequence = ''  } = item || {};
    const { metaData = {}, recheckKey = '' } = isEmpty(privateTransfer) ? groupTransfer : privateTransfer;
    const { activityList = [] } = activityReqParams || {};
    const addActivityRestricted = accessRestriction
      ? accessRestriction.addActivityRestricted
      : false;
    const ratePlanRestricted = accessRestriction ? accessRestriction.ratePlanRestricted : true;

    const blackStripData = {
      day: day,
      numberOfActivities: activityList.length,
      activityPrice: 0,
      packagePrice: pricingDetail?.categoryPrices?.[0]?.discountedPrice || packagePrice,
      showUpdate: false,
      addonPrice:pricingDetail?.categoryPrices?.[0]?.addonsPrice 
    };

    const onUpdatePress = ({ recheckKey: newRecheckKey = '' } = {}) => {
      updateOnActivityDetailPage({
        day,
        lastPageName,
        dynamicPackageId: packageDetailDTO?.dynamicPackageId,
        activityMetaData: metaData,
        newRecheckKey,
        onComponentChange,
        activityReqParams,
        hideOverlays,
      });
    };

    const onRemovePress = () => {
      removeActivity(metaData?.code);
    };
    const activityDetailProps = {
      blackStripData: blackStripData,
      packagePrice: packagePrice,
      staySequence,
      day,
      selected: true,
      activityCode: metaData?.code,
      selectedRecheckKey: recheckKey,
      modifyActivityDetail: () => {},
      onUpdatePress,
      onRemovePress,
      onChangePress: openActivityListingPage,
      isActivityDetailFirstPage: true,
      subtitleData: subtitleData,
      branch,
      packageDetailDTO,
      roomDetails,
      addActivityRestricted,
      ratePlanRestricted,
      hideOverlays,
      showOverlay,
      dynamicPackageId: packageDetailDTO?.dynamicPackageId,
    }
    if(isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2, {
        props: activityDetailProps,
      });
    } else {
    holidayNavigationPush({
      props: activityDetailProps,
      pageKey: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2,
      overlayKey: Overlay.ACTIVITY_DETAIL_V2,
      hideOverlays,
      showOverlay,
    });
    }
  };

  const showNewActivityDetail = getShowNewActivityDetail();
  const HotelRowComponent = showNewActivityDetail ? HotelRowV2 : HotelRow;
  const FlightRowComponent = showNewActivityDetail ? FlightRowV2 : FlightRow;
  const TransferRowComponent = showNewActivityDetail ? TransferRowV2 : TransferRow;
  const SightSeeingRowComponent = showNewActivityDetail ? SightSeeingRowV2 : SightSeeingRow;
  const ActivityRowComponent = showNewActivityDetail ? ActivityRowV2 : ActivityRow;

  const renderCard = (
    unit: ItineraryUnit,
    index: number,
    failedHotels: any,
    ifFlightGroupFailed: boolean,
  ): JSX.Element | null => {
    const {
      itineraryUnitType,
      itineraryUnitSubType,
      commuteCta,
      commuteHeader,
      isCommuteChangeable,
    }: RenderCardProps = unit;
    const commuteCtaObj = {
      commuteCta,
      commuteHeader,
      isCommuteChangeable,
      handleCtaClick: openComboPage,
    };

    const defaultCollapsedState = getCollapsedState({ unit, currentActivePlanOnItinerary });
    switch (itineraryUnitType) {
      case itineraryUnitTypes.HOTEL:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.CHECKIN:
          case itineraryUnitSubTypes.CHECKOUT:
          case itineraryUnitSubTypes.BREAKFAST:
          case itineraryUnitSubTypes.DINNER:
            if (
              !(
                currentActivePlanOnItinerary === TABS.DAY ||
                (currentActivePlanOnItinerary === TABS.HOTEL &&
                  itineraryUnitSubType === itineraryUnitSubTypes.CHECKIN)
              ) ||
              mealSubUnitTypes.includes(itineraryUnitSubType)
            ) {
              return null;
            }
            return (
              <HotelRowComponent
                bundled={bundled}
                failedHotels={failedHotels}
                day={day}
                city={city}
                hotelDetail={hotelDetail}
                roomDetails={roomDetails}
                itineraryUnit={unit}
                accessRestriction={accessRestriction}
                onViewDetailPress={openVideDetailOverlay}
                packageDetailDTO={packageDetailDTO}
                onComponentChange={onComponentChange}
                destinationName={destinationName}
                showOverlay={showOverlay}
                hideOverlays={hideOverlays}
                lastPageName={lastPageName}
                detailData={detailData}
                fromPresales={fromPresales}
                packageDetail={packageDetail}
                defaultCollapsedState={defaultCollapsedState}
              />
            );
          default:
            return null;
        }
      case itineraryUnitTypes.FLIGHT:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.FLIGHT_ARRIVE:
          case itineraryUnitSubTypes.FLIGHT_DEPART:
            if (
              !(
                currentActivePlanOnItinerary === TABS.DAY ||
                currentActivePlanOnItinerary === TABS.FLIGHT ||
                currentActivePlanOnItinerary === TABS.COMMUTE
              )
            ) {
              return null;
            }
            return (
              <FlightRowComponent
                ifFlightGroupFailed={ifFlightGroupFailed}
                day={day}
                city={city}
                flightDetail={flightDetail}
                itineraryUnit={unit}
                accessRestriction={accessRestriction}
                flightReqParams={flightReqParams}
                onViewDetailPress={openVideDetailOverlay}
                packageContent={packageContent}
                subtitleData={subtitleData}
                pricingDetail={pricingDetail}
                packageDetailDTO={packageDetailDTO}
                removeFlights={removeFlights}
                trackLocalClickEvent={trackLocalClickEvent}
                onComponentChange={onComponentChange}
                onPackageComponentToggle={onPackageComponentToggle}
                destinationName={destinationName}
                lastPageName={lastPageName}
                roomDetails={roomDetails}
                openFlightListingPage={openFlightListingPage}
                trackLocalPageLoadEvent={trackLocalPageLoadEvent}
                fromPresales={fromPresales}
                defaultCollapsedState={defaultCollapsedState}
                showOverlay={showOverlay}
                hideOverlays={hideOverlays}
                clearOverlays={clearOverlays}
              />
            );
          default:
            return null;
        }
      case itineraryUnitTypes.COMMUTE:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.COMMUTE_ARRIVE:
          case itineraryUnitSubTypes.COMMUTE_DEPART:
            if (
              !(
                currentActivePlanOnItinerary === TABS.DAY ||
                currentActivePlanOnItinerary === TABS.COMMUTE
              )
            ) {
              return null;
            }
            return (
              <Commute
                ifFlightGroupFailed={ifFlightGroupFailed}
                day={day}
                city={city}
                flightDetail={flightDetail}
                itineraryUnit={unit}
                accessRestriction={accessRestriction}
                flightReqParams={flightReqParams}
                onViewDetailPress={openVideDetailOverlay}
                packageContent={packageContent}
                subtitleData={subtitleData}
                pricingDetail={pricingDetail}
                packageDetailDTO={packageDetailDTO}
                removeFlights={removeFlights}
                trackLocalClickEvent={trackLocalClickEvent}
                onComponentChange={onComponentChange}
                onPackageComponentToggle={onPackageComponentToggle}
                destinationName={destinationName}
                lastPageName={lastPageName}
                roomDetails={roomDetails}
                trackLocalPageLoadEvent={trackLocalPageLoadEvent}
                fromPresales={fromPresales}
                commuteCtaObj={commuteCtaObj}
                packageDetail={packageDetail}
                detailData={detailData}
                showOverlay={showOverlay}
                hideOverlays={hideOverlays}
                clearOverlays={clearOverlays}
                defaultCollapsedState={defaultCollapsedState}
              />
            );
          default:
            return null;
        }
      case itineraryUnitTypes.TRANSFERS:
      case itineraryUnitTypes.CAR:
        if (
          !(
            currentActivePlanOnItinerary === TABS.DAY ||
            currentActivePlanOnItinerary === TABS.TRANSFER ||
            currentActivePlanOnItinerary === TABS.COMMUTE
          )
        ) {
          return null;
        }
        return (
          <TransferRowComponent
            day={day}
            city={city}
            itineraryUnit={unit}
            roomDetails={roomDetails}
            packageDetail={packageDetail}
            accessRestriction={accessRestriction}
            onViewDetailPress={openVideDetailOverlay}
            packageDetailDTO={packageDetailDTO}
            onComponentChange={onComponentChange}
            destinationName={destinationName}
            lastPageName={lastPageName}
            detailData={detailData}
            defaultCollapsedState={defaultCollapsedState}
            fromPresales={fromPresales}
            showOverlay={showOverlay}
            hideOverlays={hideOverlays}
            openTransferAthAsActivityDetail={openTransferAthAsActivityDetail}
          />
        );
      case itineraryUnitTypes.ACTIVITY:
        /* Note: We will not show meal activity and transfer activity in the activity tab.
        Also show Meal Activity in Meal Tab and Transfer Activity in Transfer Tab
         */
        const isMealActivity = itineraryUnitSubType === itineraryUnitTypes.MEALS;
        const isTransferActivity = itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER;
        if (
          !(
            currentActivePlanOnItinerary === TABS.DAY ||
            (currentActivePlanOnItinerary ===
              TABS.ACTIVITIES /* Condition to hide Meal and Transfer Activity in Activity Tab */ &&
              !isMealActivity &&
              !isTransferActivity) ||
            (currentActivePlanOnItinerary ===
              TABS.MEALS /* Condition to show Activity Meal on Meal Tab */ &&
              itineraryUnitSubType === itineraryUnitTypes.MEALS) ||
            (currentActivePlanOnItinerary ===
              TABS.COMMUTE /* Condition to show Activity Transfer on Transfer/Commute Tab */ &&
              itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER)
          )
        ) {
          return null;
        }
        return (
          <ActivityRowComponent
            day={day}
            city={city}
            lastPageName={lastPageName}
            pricingDetail={pricingDetail}
            activityReqParams={activityReqParams}
            packageDetailDTO={packageDetailDTO}
            subtitleData={subtitleData}
            branch={branch}
            roomDetails={roomDetails}
            packageDetail={packageDetail}
            activityDetail={activityMap}
            itineraryUnit={unit}
            destinationName={destinationName}
            accessRestriction={accessRestriction}
            onViewDetailPress={openVideDetailOverlay}
            onComponentChange={onComponentChange}
            removeActivity={removeActivity}
            changeActivity={openActivityListingPage}
            fromPresales={fromPresales}
            showOverlay={showOverlay}
            hideOverlays={hideOverlays}
            defaultCollapsedState={defaultCollapsedState}
          />
        );
      case itineraryUnitTypes.SIGHTSEEING:
        if (
          !(
            currentActivePlanOnItinerary === TABS.DAY ||
            currentActivePlanOnItinerary === TABS.ACTIVITIES
          )
        ) {
          return null;
        }
        return (
          <SightSeeingRowComponent
            day={day}
            city={city}
            destinationName={destinationName}
            packageDetail={packageDetail}
            activityDetail={activityDetail}
            itineraryUnit={unit}
            accessRestriction={accessRestriction}
            onViewDetailPress={openVideDetailOverlay}
            subtitleData={subtitleData}
            packageDetailDTO={packageDetailDTO}
            fromPresales={fromPresales}
            showOverlay={showOverlay}
            hideOverlays={hideOverlays}
            defaultCollapsedState={defaultCollapsedState}
          />
        );
      case itineraryUnitTypes.MEALS:
        if (
          !(
            currentActivePlanOnItinerary === TABS.DAY || currentActivePlanOnItinerary === TABS.MEALS
          )
        ) {
          return null;
        }
        if (showNewActivityDetail) {
          return (
            <MealRowV2
              day={day}
              city={city}
              itineraryUnit={unit}
              updateMeal={updateMeal}
              defaultCollapsedState={defaultCollapsedState}
            />
          );
        }
        return <MealRow itineraryUnit={unit} />;
      default:
        return null;
    }
  };

  const AddActivityCard = (cards: (JSX.Element | null)[]) => {
    let isItemRestricted = false;
    if (
      currentActivePlanOnItinerary === TABS.DAY ||
      currentActivePlanOnItinerary === TABS.ACTIVITIES ||
      ((currentActivePlanOnItinerary === TABS.MEALS ||
        currentActivePlanOnItinerary === TABS.COMMUTE) &&
        getShowNewActivityDetail())
    ) {
      if (accessRestriction) {
        const {
          dayWiseAddActivityRestrictions,
          psmAddActivityRestricted,
        }: ComponentAccessRestriction = accessRestriction;
        if (dayWiseAddActivityRestrictions && dayWiseAddActivityRestrictions.length > 0) {
          const item: DayWiseAddActivityRestriction | undefined =
            dayWiseAddActivityRestrictions.find((x) => x.day === day);
          if (item) {
            isItemRestricted = item.restricted;
          }
        }
        if (psmAddActivityRestricted) {
          isItemRestricted = true;
        }
      }
      if (!isItemRestricted) {
        const card = showNewActivityDetail ? ( //TODO fix condition before going live
          <AddTravelTidbits
            openTravelTidbits={openTravelTidbits}
            currentActivePlanOnItinerary={currentActivePlanOnItinerary}
          />
        ) : (
          <AddActivity openActivityListingPage={openActivityListingPage} />
        );

        cards.push(card);
      }
    }
  };

  const AddStaticDataForFD = (cards: (JSX.Element | null)[]) => {
    // Check both bundled and useStaticItinerary flags
    if (packageDetail?.metadataDetail?.bundled || packageDetail?.itineraryDetail?.staticItinerary) {
      if (day <= staticData.length && day > 0) {
        const StaticItineraryComponent = showNewActivityDetail
          ? StaticItineraryComponentV2
          : StaticItinerary;
        const StaticComponent = (
          <StaticItineraryComponent
            staticData={staticData[day - 1]}
            isOverlay={false}
            city={city}
            destinationMap={destinationMap}
            defaultCollapsedState={getCollapsedState({
              unit: staticData[day - 1],
              currentActivePlanOnItinerary,
            })}
          />
        );
        cards.splice(0, 0, StaticComponent);
      }
    }
  };

  if (itineraryUnits) {
    let dayStripMap: Map<string, DayStripItem> = new Map<string, DayStripItem>();
    let cards = itineraryUnits.map((unit, indexes) => {
      AddDayStripItems(dayStripMap, unit, currentActivePlanOnItinerary);
      return renderCard(unit, indexes, failedHotels, ifFlightGroupFailed);
    });
    cards = cards.filter((x) => x !== null);
    if (currentActivePlanOnItinerary === TABS.DAY) {
      AddStaticDataForFD(cards);
    }
    if (index < totalDays) {
      AddActivityCard(cards);
    }
    if (cards && cards.length > 0) {
      return (
        <View style={styles.dayPlanContainer}>
          <DayPlanHeader
            index={data.day}
            dayStripMap={dayStripMap}
            itineraryUnits={itineraryUnits}
            packageDetail={packageDetail}
            currentActivePlanOnItinerary={currentActivePlanOnItinerary}
            destination={data.city}
          />
          {cards}
        </View>
      );
    }
  }
  return [];
};

const styles = StyleSheet.create({
  dayPlanContainer: {
    // paddingHorizontal: 15,
    backgroundColor: holidayColors.white,
  },
});

export default DayPlan;
