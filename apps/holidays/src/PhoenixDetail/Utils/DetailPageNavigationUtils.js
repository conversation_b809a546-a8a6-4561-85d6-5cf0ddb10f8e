import { HolidayNavigation } from '../../Navigation';
import { isRawClient } from '../../utils/HolidayUtils';

export const holidayNavigationPush = ({
  props,
  pageKey,
  overlayKey,
  showOverlay,
  hideOverlays,
  navigationFunction = null,
} = {}) => {
  if (isRawClient()) {
    const data = { ...props, overlayKey, hideOverlays, showOverlay };
    showOverlay(overlayKey, data);
  } else {
    if (navigationFunction) {
      navigationFunction(pageKey, props);
    } else {
      HolidayNavigation.push(pageKey, props);
    }
  }
};

export const holidayNavigationPop = ({
  overlayKeys = [],
  hideOverlays = null,
  navigationFunction = null,
  navigationFunctionProps = {},
} = {}) => {
  if (isRawClient() && hideOverlays) {
    hideOverlays(overlayKeys);
  } else {
    if (navigationFunction) {
      navigationFunction(navigationFunctionProps);
    } else {
      HolidayNavigation.pop();
    }
  }
};

export const holidayNavigationClearScreens = ({ clearOverlays }) => {
  if (isRawClient()) {
    clearOverlays();
  } else {
    HolidayNavigation.pop();
  }
};
