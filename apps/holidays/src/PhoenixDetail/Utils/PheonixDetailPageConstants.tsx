export const DATE_HOTEL = 'ddd, DD MMM YYYY';
export const DATE_FORMAT_FLIGHT_TIME = 'HH:mm';

export const DAY_STRIP_COMPONENTS = {
  FLIGHT: 'Flight',
  HOTEL: 'Hotel',
  CAR: 'Transfer',
  ACTIVITY: 'Activity',
  ACTIVITIES: 'Activities',
  TRANSFERS: 'Transfer',
  TRANSFER: 'Transfer',
  SIGHTSEEING: 'Sightseeing',
  MEALS: 'Meals',
};

export const TAB_AND_ITINERARY_MAP: {[key: string]: string} = {
  ACTIVITY: 'ACTIVITY',
  TRANSFERS: 'TRANSFER',
  CAR: 'TRANSFER',
  HOTEL: 'HOTEL',
  MEALS: 'MEALS',
  FLIGHT: 'FLIGHT',
  SIGHTSEEING : 'SIGHTSEEING',
  COMMUTE: 'COMMUTE',
};

export const OVERLAY_CAROUSAL_POSITION: { [key: string]: number } = {
  FLIGHT: 0,
  HOTEL: 1,
  ACTIVITY: 2,
  TRANSFERS: 3,
  SIGHTSEEING: 4,
  MEALS: 5,
};

export const OVERLAY_CAROUSAL_TYPE = {
  0: 'FLIGHT',
  1: 'HOTEL',
  2: 'ACTIVITY',
  3: 'CAR',
  4: 'SIGHTSEEING',
  5: 'MEALS',
};

export const HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE: { [key: string]: string } = {
  BASE_DETAIL_PAGE: 'BASE_DETAIL_PAGE',
  OVERLAY_PAGE: 'OVERLAY_PAGE',
  HOTEL_LISTING_PAGE: 'HOTEL_LISTING_PAGE',
  HOLIDAY_HOTEL_DETAIL_PAGE : 'HOLIDAY_HOTEL_DETAIL_PAGE',
};

export const FD_FEATURE_TYPE = {
  VARIANT: 'VARIANT',
  GROUP_SIZE: 'GROUP_SIZE',
  MEAL: 'MEAL',
  ADDON_INCLUSION: 'ADDON_INCLUSION',
};

// TODO: @kv FD features
export const FD_VARIANT_DATA = {
  title: 'Package Type',
};

export const MAX_STAR_COUNT: number = 5;

export const flightListingActionTypes = {
  LOADING: 'flight_listing_loading',
  SUCCESS: 'flight_listing_success',
  ERROR: 'flight_listing_error',
};

export const flightsPriceMapActionTypes = {
  LOADING: 'flights_price_map_loading',
  SUCCESS: 'flights_price_map_success',
  ERROR: 'flights_price_map_error',
};

export const activityListingActionTypes = {
  LOADING: 'activity_listing_loading',
  SUCCESS: 'activity_listing_success',
  ERROR: 'activity_listing_error',
};

export const activityDetailActionTypes = {
  LOADING: 'activity_detail_loading',
  SUCCESS: 'activity_detail_success',
  ERROR: 'activity_detail_error',
};

export const mealSubUnitTypes = ['BREAKFAST','LUNCH','DINNER'];

export const ACTIVITY_MAP_PARAMS = {
  BASE_URL: 'https://maps.googleapis.com/maps/api/staticmap',
  SIZE: '153x93',
  ZOOM: 7,
  SCALE: 2,
  MAP_TYPE: 'roadmap',
  MARKER_SIZE: 'mid',
  MARKER_COLOR: 'blue',
};

export const PHOENIX_DETAIL_OVERLAY = 'detail overlay';
export const HOLIDAYS_FLIGHT_OVERLAY = 'change flight';
export const HOLIDAYS_FLIGHT_DETAILS = 'flight details';
export const PHOENIX_ACTIVITY_LISTING = 'add activities';
export const PHOENIX_ACTIVITY_DETAIL = 'activity details';

export const HOLIDAYS_FLIGHT_OVERLAY_LISTING = 'flight overlay:listing';
export const HOLIDAYS_FLIGHT_OVERLAY_DETAIL = 'flight overlay: detail';
export const HOLIDAYS_FLIGHT_OVERLAY_PAGE = 'flight overlay';
export const HOLIDAYS_ACTIVITY_OVERLAY_LISTING = 'activity overlay:lisitng';
export const HOLIDAYS_ACTIVITY_OVERLAY_DETAIL  = 'activity overlay:detail';
export const HOLIDAYS_ACTIVITY_OVERLAY_DETAIL_RATE_PLAN  = 'activity overlay:rateplan';
export const HOLIDAYS_ACTIVITY_OVERLAY = 'activity overlay';
export const HOLIDAYS_HOTEL_OVERLAY = 'hotel overlay';
export const HOLIDAYS_HOTEL_OVERLAY_LISTING = 'hotel overlay:listing';
export const HOLIDAYS_HOTEL_OVERLAY_DETAIL = 'hotel overlay:detail';
export const HOLIDAYS_TRANSFER_OVERLAY = 'transfer overlay';
export const HOLIDAYS_TRANSFER_OVERLAY_LISTING = 'transfer overlay:listing';
export const HOLIDAYS_TRANSFER_OVERLAY_DETAIL = 'transfer overlay:detail';
export const HOLIDAYS_AIRPORT_OVERLAY_LISTING = 'aiport overlay:listing';
export const HOLIDAYS_AIRPORT_OVERLAY_DETAIL = ' airport overlay:detail';
export const MORE_TRANSPORT_OPTION = 'details:moreTransportOptions';
export const MORE_TRANSPORT_OPTION_DETAILS = 'details:detailmoretransportoptions';

export const VISA_ASSISTANCE_AND_TOUR_GUIDE = 'Visa Assistance and Tour Guide';
export const PACKAGE_FEATURES = {
  MEAL: 'MEAL',
  MEAL_DAY_WISE: 'MEAL_DAY_WISE',
  VISA_INCLUDED: 'VISA_INCLUDED',
  VISA: 'VISA',
  ADDON_INCLUSION: 'ADDON_INCLUSION',
};

export const VIEW_STATE = {
  LOADING: 'loading',
  ERROR: 'error',
  SUCCESS: 'success',
};

export const VISA_API_STATUS_CODE = {
  SUCCESS: 1,
  ERROR: 0,
};

export const ACTIVITY_TYPE = {
  ACTIVITY: 'Activity',
  MEALS: 'Meals',
  TRANSFERS: 'Transfers',
};
const ACTIVITY_TYPE_V2 = {
ACTIVITY:'ACTIVITY',
MEAL:'MEAL',
TRANSFER:'TRANSFER'
}

export const TABS_MAPPING = {
  activityListingData: { key: ACTIVITY_TYPE.ACTIVITY, title: ACTIVITY_TYPE.ACTIVITY ,type:ACTIVITY_TYPE_V2.ACTIVITY},
  mealListingData: { key: ACTIVITY_TYPE.MEALS, title: ACTIVITY_TYPE.MEALS ,type:ACTIVITY_TYPE_V2.MEAL},
  transferListingData: { key: ACTIVITY_TYPE.TRANSFERS, title: ACTIVITY_TYPE.TRANSFERS,type:ACTIVITY_TYPE_V2.TRANSFER },
};
export const ACTIVITY_RESPONSE_SECTIONS = ['activityListingData', 'transferListingData', 'mealListingData'];

export const ACTIVITY_NOT_AVAILABLE = 'This activity is not available for selection';
