import type { ReactNode } from 'react';
import React from 'react';
import holidayRouteConfig, { setNavigation } from './Navigation/holidayRouteConfig';
import type { HolidayModuleDependencies } from './types/holidayModule';
import ErrorBoundary from './Common/Components/ErrorBoundary';
import holidaysStore from './holidaysStore';

export function holidayModuleProvider() {
    const onBootstrap = () => {
        console.log('On bootstrap called for New Holiday.');
    };

    const decorator = (children: ReactNode) => {
        return <ErrorBoundary>{children}</ErrorBoundary>;
    };

    const injectDependencies = (dependencies: HolidayModuleDependencies) => {
        if (!dependencies.rootTag) {
            console.warn('rootTag is missing in dependencies for Holiday module.');
            return;
        }

        setNavigation(dependencies);
    };

    const onMount = async (props: object) => {
        console.log('On mount called for New Holiday:', props);
    };

    const onUnmount = (props: object) => {
        console.log('On unmount called for New Holiday:', props);
    };

    const useDedicatedStore = true;

    return {
        id: '@mmt-rn/holidays',
        routeConfig: holidayRouteConfig,
        onBootstrap,
        decorator,
        injectDependencies,
        onMount,
        onUnmount,
        store: holidaysStore,
        useDedicatedStore,
    };
}
