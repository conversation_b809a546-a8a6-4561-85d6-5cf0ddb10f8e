import React, { useMemo } from 'react';

import { Platform, useWindowDimensions } from 'react-native';

import { useSafeAreaInsets, type EdgeInsets } from 'react-native-safe-area-context';

export const IS_ANDROID_15_AND_ABOVE = Platform.OS === 'android' && Platform.Version >= 35;

export interface SafeAreaDimensions {
  width: number;
  height: number;
}

export function useSafeAreaDimensions(): SafeAreaDimensions {
  const insets: EdgeInsets = useSafeAreaInsets();
  const windowDimensions = useWindowDimensions();
  return useMemo<SafeAreaDimensions>(
    () =>
      ({
        width: windowDimensions.width,
        height: Platform.select({
          ios: windowDimensions.height - insets.top - insets.bottom,
          android: IS_ANDROID_15_AND_ABOVE
            ? windowDimensions.height - insets.top - insets.bottom - 47 * 1.1 - 24 * 1.1
            : windowDimensions.height - insets.top - insets.bottom,
          default: windowDimensions.height,
        }),
      } as SafeAreaDimensions),
    [insets, windowDimensions],
  );
}
