import React, { useState, useEffect, useRef, useMemo } from 'react';
import { getAppData } from "../utils/providerUtils";
import {RNAppDataProvider} from 'core-rn-ui-factory';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { Platform, SafeAreaView } from 'react-native';
import store from '../storeManager';
import {
  broadcastRoutingEventsToNative,
  navigationRef,
  getQueryParamsFromUrl,
  saveNavigationState,
} from './routerUtils';
import { createStackNavigator, TransitionPresets } from '@react-navigation/stack';
import { navigation } from '@mmt/navigation';
import { generateUUID } from '@mmt/navigation/src/util';
import withRedux from 'packages/navigation/src/withRedux';
import { getTheme, ThemeProvider } from '../theme/theme.context';
import { getUserProfileType } from '@mmt/legacy-commons/AppState/AppUserConfig';
import bootstrapModule from '@mmt/legacy-commons/Common/ModuleWrapper';
import postSalesModule from '../postSales.module';
import EnvVariable from 'core-ui-lib/envVariable';

const Stack = createStackNavigator();

const screenOptions = {
  cardStyle: {
    backgroundColor: 'transparent',
  },
  headerShown: false,
  ...TransitionPresets.SlideFromRightIOS,
};

const PostSalesRouter = (props) => {
  const [isReady, setIsReady] = useState(false);
  const [initialState, setInitialState] = useState(null);
  let routerId = useRef(generateUUID()).current;
  useEffect(()=>{
    EnvVariable.setEnvVariable({brand:'MMT', platform:Platform.OS});
  },[]);
  useEffect(() => {
    const restoreState = () => {
      try {
        const pageData = props['@nav/pageData'];
        if (pageData) {
          const pageDataObj = JSON.parse(pageData);
          if (typeof pageDataObj === 'object') {
            setInitialState(pageDataObj);
          }
        }
      } catch (e) {
        // Nothing
      } finally {
        setIsReady(true);
        broadcastRoutingEventsToNative(props.page, props.rootTag);
      }
    };

    if (!isReady) {
      restoreState();
    }
  }, []);

  useEffect(() => {
    return () => {
      navigation.popNavigationRef(routerId);
    };
  }, [routerId]);

  const isNavRefSet = useRef(false);
  const bootstrapRegistry = useMemo(() => ({}), []);
  const postSaleModule = useMemo(() => bootstrapModule(postSalesModule, bootstrapRegistry), []);
  useEffect(() => {
    return () => {
      // Unmount logic
      Object.values(bootstrapRegistry).forEach((route) => {
        route && route.onUnmount && route.onUnmount(props);
      });
    };
  }, [bootstrapRegistry]);

  if (!isReady) {
    return null;
  }

  // not sure why react native navigation is not passing the deeplink params to the component
  // so manually extracting from props and spreading into the page below.
  const deepLinkParams = props.deep_link_intent_url
    ? getQueryParamsFromUrl(props.deep_link_intent_url)
    : {};

  const userProfileType = getUserProfileType();
  return (
    <RNAppDataProvider appData={getAppData()}>
      <ThemeProvider initial={getTheme(userProfileType)}>
        <NavigationContainer
          initialState={initialState}
          ref={navigationRef}
          onStateChange={(state) => {
            if (state) {
              /** everytime page changes broadcast to iOS native to disable swipe gesture on react-view-controller
               *  and use gestures of react-navigation */
              broadcastRoutingEventsToNative(state.routes[state.index].name, props.rootTag);
            }
            saveNavigationState(props['@nav/pageId'], state);
          }}
        >
          <Provider store={store}>
            <Stack.Navigator screenOptions={screenOptions} initialRouteName={props?.page}>
              {postSaleModule.routeConfig.map((routeConfig) => {
                const {
                  key,
                  component,
                  reduxOptions,
                  rnScreenOptions = {},
                  initialProp = {},
                  isMigratedRoute = false,
                  safeAreaStyle = {},
                  options
                } = routeConfig;

                const Page = withRedux(component(), isMigratedRoute, reduxOptions);
                return (
                  <Stack.Screen
                    name={key}
                    key={key}
                    listeners={() => ({
                      state: (e) => {
                        // Prevent default action
                        // e?.preventDefault();
                        routeConfig.onNavigationStateChange && routeConfig.onNavigationStateChange(e);
                      },
                    })}
                  >
                    {(navProps) => {
                      if (!isNavRefSet.current) {
                        isNavRefSet.current = true;
                        navigation.setNavigationRef(navProps.navigation, routerId);
                      }
                      const componentProps = {
                        ...initialProp,
                        ...props,
                        ...navProps,
                        ...navProps.route?.params,
                        ...deepLinkParams,
                      };
                      return (
                        <SafeAreaView style={[safeAreaStyle, { flex: 1, backgroundColor:  options.backgroundColor || "white" }]}>
                          <Page {...componentProps} navigation={navigation} newNavigation={navProps.navigation} />
                        </SafeAreaView>
                      );
                    }}
                  </Stack.Screen>
                );
              })}
            </Stack.Navigator>
          </Provider>
        </NavigationContainer>
      </ThemeProvider>
    </RNAppDataProvider>
  );
};

export default React.memo(PostSalesRouter);
